# 接口清理记录

## 清理说明

根据业务需求，移除了发货需求和发运订单中不必要的接口，简化API结构。

## 发货需求接口清理

### 移除的接口

#### 1. 新增发货需求
```
POST /api/shipping-demand/add
```
**移除原因**: 发货需求由发运订单自动生成，不需要手动新增

#### 2. 更新发货需求
```
PUT /api/shipping-demand/update
```
**移除原因**: 发货需求状态由WMS系统管理，不需要手动编辑

#### 3. 更新发货需求状态
```
PUT /api/shipping-demand/{id}/status
```
**移除原因**: 状态变更由WMS系统自动处理

#### 4. 取消发货需求
```
PUT /api/shipping-demand/{id}/cancel
```
**移除原因**: 取消操作由WMS系统处理

#### 5. 完结发货需求
```
PUT /api/shipping-demand/{id}/complete
```
**移除原因**: 完结操作由WMS系统自动处理

#### 6. 确认发货需求
```
PUT /api/shipping-demand/{id}/confirm
```
**移除原因**: 确认操作由WMS系统处理

### 保留的接口

#### 1. 分页查询发货需求列表
```
POST /api/shipping-demand/page
```
**保留原因**: 用于查看发货需求列表

#### 2. 获取发货需求详情
```
GET /api/shipping-demand/{id}
```
**保留原因**: 用于查看发货需求详细信息

#### 3. 根据订单号查询发货需求列表
```
GET /api/shipping-demand/order/{orderNo}
```
**保留原因**: 用于查看特定订单的发货需求

#### 4. 导出发货需求列表
```
POST /api/shipping-demand/export
```
**保留原因**: 用于导出发货需求数据

## 发运订单接口清理

### 移除的接口

#### 1. 更新发运订单状态
```
PUT /api/shipping-order/{id}/status
```
**移除原因**: 状态变更由业务流程自动处理

#### 2. 完结发运订单
```
PUT /api/shipping-order/{id}/complete
```
**移除原因**: 完结操作由系统自动处理

### 保留的接口

#### 1. 分页查询发运订单列表
```
POST /api/shipping-order/page
```
**保留原因**: 用于查看发运订单列表

#### 2. 获取发运订单详情
```
GET /api/shipping-order/{id}
```
**保留原因**: 用于查看发运订单详细信息

#### 3. 新增发运订单
```
POST /api/shipping-order/add
```
**保留原因**: 用于创建新的发运订单

#### 4. 更新发运订单
```
PUT /api/shipping-order/update
```
**保留原因**: 用于编辑发运订单信息

#### 5. 取消发运订单
```
PUT /api/shipping-order/{id}/cancel
```
**保留原因**: 用于手动取消订单

#### 6. 导出发运订单列表
```
POST /api/shipping-order/export
```
**保留原因**: 用于导出发运订单数据

#### 7. 获取SKU类型下拉框数据
```
GET /api/shipping-order/sku-types
```
**保留原因**: 用于获取产品类型选项

## 清理后的API结构

### 发货需求模块
```
发货需求管理 (/api/shipping-demand)
├── POST   /page              # 分页查询列表
├── GET    /{id}              # 获取详情
├── GET    /order/{orderNo}   # 根据订单号查询
└── POST   /export            # 导出列表
```

### 发运订单模块
```
发运订单管理 (/api/shipping-order)
├── POST   /page              # 分页查询列表
├── GET    /{id}              # 获取详情
├── POST   /add               # 新增订单
├── PUT    /update            # 更新订单
├── PUT    /{id}/cancel       # 取消订单
├── POST   /export            # 导出列表
└── GET    /sku-types         # 获取SKU类型
```

## 业务流程说明

### 发货需求生命周期
```
1. 发运订单创建 → 自动生成发货需求
2. 发货需求同步到WMS → 状态：待发货
3. WMS确认发货 → 状态：已发货
4. 发货完成 → 状态：已完结
5. 异常情况 → 状态：已取消
```

### 发运订单生命周期
```
1. 手动创建订单 → 状态：待发货
2. 生成发货需求 → 同步到WMS
3. WMS发货 → 状态：发货中
4. 客户签收 → 状态：已完结
5. 手动取消 → 状态：已取消
```

## 数据同步机制

### 运营平台 → WMS
- 发运订单创建时自动生成发货需求
- 发货需求信息同步到WMS系统

### WMS → 运营平台
- 发货需求状态变更同步到运营平台
- 发运订单状态根据发货需求状态自动更新

## 权限控制

### 发货需求
- **查询权限**: 运营人员可查看所有发货需求
- **操作权限**: 只有WMS系统可以变更状态

### 发运订单
- **查询权限**: 运营人员可查看所有发运订单
- **创建权限**: 运营人员可创建新订单
- **编辑权限**: 运营人员可编辑待发货状态的订单
- **取消权限**: 运营人员可取消订单

## 注意事项

1. **状态一致性**: 发运订单和发货需求的状态必须保持同步
2. **数据完整性**: 删除接口不影响现有数据的完整性
3. **向后兼容**: 移除的接口如果被前端调用，需要相应调整前端代码
4. **错误处理**: 确保移除接口后的错误处理机制正常工作
5. **文档更新**: 需要更新API文档，移除已删除的接口说明

## 影响评估

### 正面影响
- **简化API**: 减少不必要的接口，降低维护成本
- **业务清晰**: API结构更符合实际业务流程
- **安全性**: 减少状态变更接口，降低误操作风险

### 潜在风险
- **前端调整**: 前端代码可能需要相应调整
- **测试更新**: 相关测试用例需要更新
- **文档同步**: API文档需要及时更新

## 后续工作

1. **前端适配**: 通知前端开发人员调整相关代码
2. **测试验证**: 验证移除接口后的功能完整性
3. **文档更新**: 更新Swagger文档和接口说明
4. **监控告警**: 监控是否有对已删除接口的调用
5. **版本发布**: 在版本发布说明中注明接口变更
