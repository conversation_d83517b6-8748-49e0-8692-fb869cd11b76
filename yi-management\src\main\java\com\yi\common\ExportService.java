package com.yi.common;

import com.yi.utils.ExcelUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 通用导出服务基类
 */
public class ExportService {

    /**
     * 通用导出方法
     *
     * @param response     HTTP响应
     * @param fileNameBase 文件名基础部分
     * @param sheetName    工作表名称
     * @param dataList     原始数据列表
     * @param converter    数据转换器，将原始数据转换为导出VO
     * @param exportClass  导出VO的Class对象
     * @param <T>          原始数据类型
     * @param <E>          导出VO类型
     * @throws IOException IO异常
     */
    public static <T, E> void export(HttpServletResponse response, String fileNameBase, String sheetName,
                                     List<T> dataList, Function<T, E> converter, Class<E> exportClass) throws IOException {
        
        // 转换数据
        List<E> exportList = dataList.stream()
                .map(converter)
                .collect(Collectors.toList());
        
        // 导出Excel
        ExcelUtils.exportExcelWithTimestamp(response, fileNameBase, sheetName, exportClass, exportList);
    }

    /**
     * 简化的导出方法（当原始数据就是导出VO时）
     *
     * @param response     HTTP响应
     * @param fileNameBase 文件名基础部分
     * @param sheetName    工作表名称
     * @param exportList   导出数据列表
     * @param exportClass  导出VO的Class对象
     * @param <E>          导出VO类型
     * @throws IOException IO异常
     */
    public static <E> void exportDirect(HttpServletResponse response, String fileNameBase, String sheetName,
                                        List<E> exportList, Class<E> exportClass) throws IOException {
        ExcelUtils.exportExcelWithTimestamp(response, fileNameBase, sheetName, exportClass, exportList);
    }
}
