package com.yi.service;

/**
 * 单号生成服务接口
 */
public interface OrderNumberService {

    /**
     * 生成出库单号
     * 格式：F + 年月日 + 4位递增序列号
     * 例如：F202412010001
     *
     * @return 出库单号
     */
    String generateOutboundOrderNo();

    /**
     * 生成入库单号（与出库单号一致）
     * 格式：F + 年月日 + 4位递增序列号
     * 例如：F202412010001
     *
     * @return 入库单号
     */
    String generateInboundOrderNo();

    /**
     * 根据指定日期生成单号
     *
     * @param dateKey 日期键（YYYYMMDD）
     * @return 单号
     */
    String generateOrderNoByDate(String dateKey);

    /**
     * 获取当日已生成的单号数量
     *
     * @return 当日单号数量
     */
    Integer getTodayOrderCount();

    /**
     * 获取指定日期已生成的单号数量
     *
     * @param dateKey 日期键（YYYYMMDD）
     * @return 指定日期单号数量
     */
    Integer getOrderCountByDate(String dateKey);
}
