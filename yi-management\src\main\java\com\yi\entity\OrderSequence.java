package com.yi.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 单号序列表
 */
@Data
@TableName("t_order_sequence")
@ApiModel(value = "OrderSequence对象", description = "单号序列表")
public class OrderSequence implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 日期键（YYYYMMDD）
     */
    @ApiModelProperty(value = "日期键（YYYYMMDD）")
    @TableField("date_key")
    private String dateKey;

    /**
     * 当日序列号
     */
    @ApiModelProperty(value = "当日序列号")
    @TableField("sequence_value")
    private Integer sequenceValue;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField("created_time")
    private LocalDateTime createdTime;

    /**
     * 最后修改时间
     */
    @ApiModelProperty(value = "最后修改时间")
    @TableField("last_modified_time")
    private LocalDateTime lastModifiedTime;
}
