package com.yi.controller.shippingorder.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 发运订单日志响应
 */
@Data
@ApiModel(value = "ShippingOrderLogResponse", description = "发运订单日志响应")
public class ShippingOrderLogResponse {

    @ApiModelProperty(value = "动作名称")
    private String actionName;

    @ApiModelProperty(value = "操作人")
    private String operator;

    @ApiModelProperty(value = "操作时间")
    private String operateTime;

    @ApiModelProperty(value = "备注")
    private String remark;
}
