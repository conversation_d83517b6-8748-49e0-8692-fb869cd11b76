package com.yi.enums;

/**
 * 出库类型枚举
 */
public enum OutboundTypeEnum {
    
    SALES(1, "销售出库"),
    TRANSFER(2, "调拨出库");

    private final Integer code;
    private final String desc;

    OutboundTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据类型码获取描述
     *
     * @param code 类型码
     * @return 类型描述
     */
    public static String getDescByCode(Integer code) {
        if (code == null) {
            return "";
        }
        for (OutboundTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type.getDesc();
            }
        }
        return "";
    }

    /**
     * 根据类型码获取枚举
     *
     * @param code 类型码
     * @return 枚举对象
     */
    public static OutboundTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (OutboundTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 检查类型是否有效
     *
     * @param code 类型码
     * @return 是否有效
     */
    public static boolean isValid(Integer code) {
        return getByCode(code) != null;
    }
}
