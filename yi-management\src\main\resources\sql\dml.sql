-- =============================================
-- DML (Data Manipulation Language) 数据操作语言
-- 用于插入、更新、删除、查询数据
-- =============================================

-- 创建时间：2025-01-XX
-- 版本：1.0.0
-- 描述：项目数据初始化和数据操作脚本

-- =============================================
-- 基础数据插入
-- =============================================

-- 示例：系统配置数据
-- INSERT INTO `sys_config` (`config_key`, `config_value`, `config_desc`, `created_by`, `created_time`) VALUES
-- ('system.name', '系统名称', '系统名称配置', 'system', NOW()),
-- ('system.version', '1.0.0', '系统版本配置', 'system', NOW());

-- =============================================
-- 字典数据插入
-- =============================================

-- 示例：数据字典
-- INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `created_by`, `created_time`) VALUES
-- ('用户状态', 'user_status', 1, 'system', NOW()),
-- ('性别', 'gender', 1, 'system', NOW());

-- INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `status`, `created_by`, `created_time`) VALUES
-- (1, '启用', '1', 'user_status', 1, 'system', NOW()),
-- (2, '禁用', '0', 'user_status', 1, 'system', NOW()),
-- (1, '男', '1', 'gender', 1, 'system', NOW()),
-- (2, '女', '2', 'gender', 1, 'system', NOW());

-- =============================================
-- 权限数据插入
-- =============================================

-- 示例：菜单权限数据
-- INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `path`, `component`, `menu_type`, `visible`, `status`, `perms`, `icon`, `created_by`, `created_time`) VALUES
-- ('系统管理', 0, 1, 'system', NULL, 'M', 1, 1, NULL, 'system', 'admin', NOW()),
-- ('用户管理', 1, 1, 'user', 'system/user/index', 'C', 1, 1, 'system:user:list', 'user', 'admin', NOW()),
-- ('角色管理', 1, 2, 'role', 'system/role/index', 'C', 1, 1, 'system:role:list', 'peoples', 'admin', NOW());

-- =============================================
-- 测试数据插入
-- =============================================

-- 示例：测试用户数据
-- INSERT INTO `sys_user` (`user_name`, `nick_name`, `email`, `phone_number`, `sex`, `password`, `status`, `created_by`, `created_time`) VALUES
-- ('admin', '管理员', '<EMAIL>', '13800138000', '1', '$2a$10$7JB720yubVSOfvam/l0.OuSXsQDdnOjkHZjmvOYiyt.eKSVzF4021', 1, 'system', NOW()),
-- ('test', '测试用户', '<EMAIL>', '13900139000', '2', '$2a$10$7JB720yubVSOfvam/l0.OuSXsQDdnOjkHZjmvOYiyt.eKSVzF4021', 1, 'admin', NOW());

-- =============================================
-- 数据更新脚本
-- =============================================

-- 示例：批量更新数据
-- UPDATE `example_table` 
-- SET `status` = 1, `last_modified_by` = 'system', `last_modified_time` = NOW()
-- WHERE `status` = 0 AND `created_time` < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- =============================================
-- 数据清理脚本
-- =============================================

-- 示例：清理过期数据
-- DELETE FROM `sys_log` 
-- WHERE `created_time` < DATE_SUB(NOW(), INTERVAL 90 DAY);

-- 示例：软删除数据
-- UPDATE `example_table` 
-- SET `valid` = 0, `last_modified_by` = 'system', `last_modified_time` = NOW()
-- WHERE `status` = 0 AND `created_time` < DATE_SUB(NOW(), INTERVAL 365 DAY);

-- =============================================
-- 数据查询脚本
-- =============================================

-- 示例：统计查询
-- SELECT 
--     DATE_FORMAT(created_time, '%Y-%m') AS month,
--     COUNT(*) AS total_count,
--     SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) AS active_count
-- FROM example_table 
-- WHERE valid = 1 
-- GROUP BY DATE_FORMAT(created_time, '%Y-%m')
-- ORDER BY month DESC;

-- =============================================
-- 数据修复脚本
-- =============================================

-- 示例：数据修复
-- UPDATE `example_table` 
-- SET `name` = TRIM(`name`)
-- WHERE `name` LIKE ' %' OR `name` LIKE '% ';

-- =============================================
-- 数据迁移脚本
-- =============================================

-- 示例：数据迁移
-- INSERT INTO `new_table` (`name`, `status`, `created_time`)
-- SELECT `name`, `status`, `created_time`
-- FROM `old_table`
-- WHERE `valid` = 1;
