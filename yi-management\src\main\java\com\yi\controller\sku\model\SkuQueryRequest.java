package com.yi.controller.sku.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * SKU查询请求
 */
@Data
@ApiModel(value = "SkuQueryRequest", description = "SKU查询请求")
public class SkuQueryRequest {

    @ApiModelProperty(value = "当前页码", example = "1")
    private String current = "1";

    @ApiModelProperty(value = "每页大小", example = "10")
    private String size = "10";

    @ApiModelProperty(value = "启用状态：1-启用，0-禁用（下拉框搜索）")
    private String enabled;

    @ApiModelProperty(value = "一级类目：1-循环托盘（下拉框搜索）")
    private String firstCategory;

    @ApiModelProperty(value = "二级类目（模糊查询）")
    private String secondCategory;
}
