﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-38px;
  width:1103px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u115_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u115 {
  border-width:0px;
  position:absolute;
  left:38px;
  top:28px;
  width:140px;
  height:40px;
  display:flex;
}
#u115 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u115_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u116_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u116 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:159px;
  width:100px;
  height:60px;
  display:flex;
}
#u116 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u116_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u117_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1001px;
  height:2px;
}
#u117 {
  border-width:0px;
  position:absolute;
  left:141px;
  top:123px;
  width:1000px;
  height:1px;
  display:flex;
}
#u117 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u117_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u118_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u118 {
  border-width:0px;
  position:absolute;
  left:587px;
  top:82px;
  width:108px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u118 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u118_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u119_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u119 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:279px;
  width:100px;
  height:60px;
  display:flex;
}
#u119 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u119_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u120_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u120 {
  border-width:0px;
  position:absolute;
  left:492px;
  top:279px;
  width:100px;
  height:60px;
  display:flex;
}
#u120 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u120_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u121_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u121 {
  border-width:0px;
  position:absolute;
  left:672px;
  top:279px;
  width:100px;
  height:60px;
  display:flex;
}
#u121 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u121_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u122_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u122 {
  border-width:0px;
  position:absolute;
  left:852px;
  top:279px;
  width:100px;
  height:60px;
  display:flex;
}
#u122 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u122_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u123_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u123 {
  border-width:0px;
  position:absolute;
  left:852px;
  top:425px;
  width:100px;
  height:60px;
  display:flex;
}
#u123 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u123_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u124_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u124 {
  border-width:0px;
  position:absolute;
  left:672px;
  top:425px;
  width:100px;
  height:60px;
  display:flex;
}
#u124 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u124_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u125_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u125 {
  border-width:0px;
  position:absolute;
  left:492px;
  top:425px;
  width:100px;
  height:60px;
  display:flex;
}
#u125 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u125_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u126_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u126 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:425px;
  width:100px;
  height:60px;
  display:flex;
}
#u126 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u126_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u127 {
  border-width:0px;
  position:absolute;
  left:362px;
  top:219px;
  width:0px;
  height:0px;
}
#u127_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:65px;
}
#u127_seg1 {
  border-width:0px;
  position:absolute;
  left:-9px;
  top:47px;
  width:18px;
  height:19px;
}
#u127_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:22px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u128 {
  border-width:0px;
  position:absolute;
  left:412px;
  top:309px;
  width:0px;
  height:0px;
}
#u128_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:85px;
  height:10px;
}
#u128_seg1 {
  border-width:0px;
  position:absolute;
  left:67px;
  top:-9px;
  width:19px;
  height:18px;
}
#u128_text {
  border-width:0px;
  position:absolute;
  left:-10px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u129 {
  border-width:0px;
  position:absolute;
  left:592px;
  top:309px;
  width:0px;
  height:0px;
}
#u129_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:85px;
  height:10px;
}
#u129_seg1 {
  border-width:0px;
  position:absolute;
  left:67px;
  top:-9px;
  width:19px;
  height:18px;
}
#u129_text {
  border-width:0px;
  position:absolute;
  left:-10px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u130 {
  border-width:0px;
  position:absolute;
  left:772px;
  top:309px;
  width:0px;
  height:0px;
}
#u130_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:85px;
  height:10px;
}
#u130_seg1 {
  border-width:0px;
  position:absolute;
  left:67px;
  top:-9px;
  width:19px;
  height:18px;
}
#u130_text {
  border-width:0px;
  position:absolute;
  left:-10px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u131 {
  border-width:0px;
  position:absolute;
  left:902px;
  top:339px;
  width:0px;
  height:0px;
}
#u131_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:91px;
}
#u131_seg1 {
  border-width:0px;
  position:absolute;
  left:-9px;
  top:73px;
  width:18px;
  height:19px;
}
#u131_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:35px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u132 {
  border-width:0px;
  position:absolute;
  left:852px;
  top:455px;
  width:0px;
  height:0px;
}
#u132_seg0 {
  border-width:0px;
  position:absolute;
  left:-80px;
  top:-5px;
  width:85px;
  height:10px;
}
#u132_seg1 {
  border-width:0px;
  position:absolute;
  left:-86px;
  top:-9px;
  width:19px;
  height:18px;
}
#u132_text {
  border-width:0px;
  position:absolute;
  left:-90px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u133 {
  border-width:0px;
  position:absolute;
  left:672px;
  top:455px;
  width:0px;
  height:0px;
}
#u133_seg0 {
  border-width:0px;
  position:absolute;
  left:-80px;
  top:-5px;
  width:85px;
  height:10px;
}
#u133_seg1 {
  border-width:0px;
  position:absolute;
  left:-86px;
  top:-9px;
  width:19px;
  height:18px;
}
#u133_text {
  border-width:0px;
  position:absolute;
  left:-90px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u134 {
  border-width:0px;
  position:absolute;
  left:492px;
  top:455px;
  width:0px;
  height:0px;
}
#u134_seg0 {
  border-width:0px;
  position:absolute;
  left:-80px;
  top:-5px;
  width:85px;
  height:10px;
}
#u134_seg1 {
  border-width:0px;
  position:absolute;
  left:-86px;
  top:-9px;
  width:19px;
  height:18px;
}
#u134_text {
  border-width:0px;
  position:absolute;
  left:-90px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u135_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:85px;
  background:inherit;
  background-color:rgba(54, 169, 206, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
}
#u135 {
  border-width:0px;
  position:absolute;
  left:672px;
  top:485px;
  width:150px;
  height:85px;
  display:flex;
}
#u135 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u135_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
