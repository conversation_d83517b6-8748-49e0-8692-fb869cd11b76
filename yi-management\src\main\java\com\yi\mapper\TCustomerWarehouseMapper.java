package com.yi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yi.controller.customerwarehouse.model.CustomerWarehouseQueryRequest;
import com.yi.entity.TCustomerWarehouse;
import com.yi.mapper.vo.WarehousePageVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 仓库表 Mapper 接口
 */
@Mapper
public interface TCustomerWarehouseMapper extends BaseMapper<TCustomerWarehouse> {

    /**
     * 分页查询仓库列表
     *
     * @param page 分页参数
     * @param companyId 公司ID
     * @param warehouseName 仓库名称
     * @param enabled 启用状态
     * @return 分页结果
     */
    IPage<TCustomerWarehouse> selectWarehousePage(Page<TCustomerWarehouse> page,
                                                  @Param("companyId") Long companyId,
                                                  @Param("warehouseName") String warehouseName,
                                                  @Param("enabled") Integer enabled);

    /**
     * 根据公司ID查询仓库列表
     *
     * @param companyId 公司ID
     * @return 仓库列表
     */
    List<TCustomerWarehouse> selectByCompanyId(@Param("companyId") Long companyId);

    /**
     * 根据仓库名称查询仓库
     *
     * @param warehouseName 仓库名称
     * @return 仓库列表
     */
    List<TCustomerWarehouse> selectByWarehouseName(@Param("warehouseName") String warehouseName);

    /**
     * 查询仓库列表（左关联SKU类型）
     * 支持分页和不分页两种模式
     *
     * @param page 分页参数（为null时不分页）
     * @param request 查询条件
     * @return 查询结果
     */
    IPage<WarehousePageVO> selectWarehouseWithSku(Page<WarehousePageVO> page, @Param("request") CustomerWarehouseQueryRequest request);

    /**
     * 查询仓库列表（左关联SKU类型，不分页）
     *
     * @param request 查询条件
     * @return 查询结果
     */
    List<WarehousePageVO> selectWarehouseWithSku(@Param("request") CustomerWarehouseQueryRequest request);

    /**
     * 检查同一客户下仓库名称是否重复
     *
     * @param companyId 公司ID
     * @param warehouseName 仓库名称
     * @param excludeId 排除的仓库ID（编辑时使用）
     * @return 重复的仓库数量
     */
    int countByCompanyIdAndWarehouseName(@Param("companyId") Long companyId,
                                        @Param("warehouseName") String warehouseName,
                                        @Param("excludeId") Long excludeId);
}
