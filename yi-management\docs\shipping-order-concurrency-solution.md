# 发运订单号并发问题解决方案

## 🚨 **问题分析**

### **并发问题场景**
```java
// 线程A和线程B同时执行generateOrderNo()

时刻1: 线程A查询最大序号 → 返回5
时刻2: 线程B查询最大序号 → 也返回5（线程A还未保存）
时刻3: 线程A生成订单号 → XSD202412230006
时刻4: 线程B生成订单号 → XSD202412230006（重复！）
```

### **问题根源**
1. **查询和插入之间的时间窗口**：在查询最大序号和保存新订单之间存在时间间隔
2. **非原子操作**：序号生成和订单保存不是原子操作
3. **缺乏唯一性约束**：数据库层面没有订单号唯一性保证

## 💡 **解决方案**

### **方案1：数据库唯一约束 + 重试机制（推荐）**

#### **1. 添加数据库唯一约束**
```sql
-- 为订单号添加唯一约束
ALTER TABLE `t_shipping_order` 
ADD UNIQUE KEY `uk_order_no` (`order_no`);
```

#### **2. 实现重试机制**
```java
private String generateOrderNo() {
    int maxRetries = 5; // 最大重试次数
    
    for (int attempt = 0; attempt < maxRetries; attempt++) {
        try {
            // 生成订单号
            LocalDate now = LocalDate.now();
            String dateStr = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String prefix = "XSD" + dateStr;

            int maxSeq = getMaxSequenceOfDay(dateStr);
            String sequence = String.format("%04d", maxSeq + 1);
            String orderNo = prefix + sequence;

            // 验证订单号唯一性
            if (isOrderNoUnique(orderNo)) {
                return orderNo;
            }
            
            // 随机等待后重试
            Thread.sleep(10 + (long)(Math.random() * 20)); // 10-30ms
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("订单号生成被中断", e);
        } catch (Exception e) {
            log.warn("订单号生成失败，尝试次数: " + (attempt + 1) + ", 错误: " + e.getMessage());
        }
    }
    
    throw new RuntimeException("订单号生成失败，已达到最大重试次数");
}

private boolean isOrderNoUnique(String orderNo) {
    LambdaQueryWrapper<TShippingOrder> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(TShippingOrder::getOrderNo, orderNo)
            .eq(TShippingOrder::getValid, 1);
    
    return this.count(wrapper) == 0;
}
```

### **方案2：分布式锁（高并发场景）**

#### **使用Redis分布式锁**
```java
@Autowired
private RedisTemplate<String, String> redisTemplate;

private String generateOrderNoWithLock() {
    String lockKey = "order_no_lock:" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    String lockValue = UUID.randomUUID().toString();
    
    try {
        // 获取分布式锁，超时时间5秒
        Boolean lockAcquired = redisTemplate.opsForValue()
            .setIfAbsent(lockKey, lockValue, Duration.ofSeconds(5));
        
        if (Boolean.TRUE.equals(lockAcquired)) {
            // 获取锁成功，生成订单号
            return generateOrderNoSafely();
        } else {
            // 获取锁失败，等待后重试
            Thread.sleep(50);
            return generateOrderNoWithLock();
        }
    } catch (InterruptedException e) {
        Thread.currentThread().interrupt();
        throw new RuntimeException("订单号生成被中断", e);
    } finally {
        // 释放锁
        String script = "if redis.call('get', KEYS[1]) == ARGV[1] then " +
                       "return redis.call('del', KEYS[1]) else return 0 end";
        redisTemplate.execute(new DefaultRedisScript<>(script, Long.class), 
                            Collections.singletonList(lockKey), lockValue);
    }
}
```

### **方案3：数据库序列（MySQL 8.0+）**

#### **创建序列**
```sql
-- 创建每日重置的序列（需要定时任务重置）
CREATE TABLE `t_order_sequence` (
    `date_str` varchar(8) NOT NULL COMMENT '日期字符串',
    `current_seq` int(11) NOT NULL DEFAULT '0' COMMENT '当前序号',
    PRIMARY KEY (`date_str`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单序号表';
```

#### **使用序列生成订单号**
```java
@Transactional
private String generateOrderNoWithSequence() {
    String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    
    // 原子性更新序号
    int newSeq = orderSequenceMapper.getAndIncrementSequence(dateStr);
    
    String sequence = String.format("%04d", newSeq);
    return "XSD" + dateStr + sequence;
}
```

```xml
<!-- OrderSequenceMapper.xml -->
<update id="getAndIncrementSequence" parameterType="string">
    INSERT INTO t_order_sequence (date_str, current_seq) 
    VALUES (#{dateStr}, 1)
    ON DUPLICATE KEY UPDATE current_seq = current_seq + 1
</update>

<select id="getCurrentSequence" parameterType="string" resultType="int">
    SELECT current_seq FROM t_order_sequence WHERE date_str = #{dateStr}
</select>
```

## 🧪 **测试验证**

### **并发测试代码**
```java
@Test
void testConcurrentOrderNoGeneration() throws InterruptedException {
    int threadCount = 10;
    int ordersPerThread = 100;
    CountDownLatch latch = new CountDownLatch(threadCount);
    Set<String> orderNos = Collections.synchronizedSet(new HashSet<>());
    List<Exception> exceptions = Collections.synchronizedList(new ArrayList<>());

    for (int i = 0; i < threadCount; i++) {
        new Thread(() -> {
            try {
                for (int j = 0; j < ordersPerThread; j++) {
                    String orderNo = shippingOrderService.generateOrderNo();
                    boolean isUnique = orderNos.add(orderNo);
                    if (!isUnique) {
                        exceptions.add(new RuntimeException("重复订单号: " + orderNo));
                    }
                }
            } catch (Exception e) {
                exceptions.add(e);
            } finally {
                latch.countDown();
            }
        }).start();
    }

    latch.await(30, TimeUnit.SECONDS);

    // 验证结果
    assertEquals(0, exceptions.size(), "不应该有异常: " + exceptions);
    assertEquals(threadCount * ordersPerThread, orderNos.size(), "所有订单号应该唯一");
}
```

## 📊 **方案对比**

| 方案 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| **唯一约束+重试** | 实现简单，数据一致性好 | 重试可能影响性能 | 中低并发场景 |
| **分布式锁** | 性能好，避免重试 | 依赖Redis，复杂度高 | 高并发场景 |
| **数据库序列** | 性能最好，原子操作 | 需要额外表，跨日重置复杂 | 超高并发场景 |

## 🎯 **推荐实现**

### **生产环境推荐配置**
```java
@Service
public class TShippingOrderService extends ServiceImpl<TShippingOrderMapper, TShippingOrder> {
    
    private static final int MAX_RETRIES = 3;
    private static final int BASE_WAIT_MS = 10;
    private static final int MAX_WAIT_MS = 50;
    
    /**
     * 生成订单号（并发安全）
     */
    private String generateOrderNo() {
        for (int attempt = 0; attempt < MAX_RETRIES; attempt++) {
            try {
                String orderNo = doGenerateOrderNo();
                if (isOrderNoUnique(orderNo)) {
                    return orderNo;
                }
                
                // 指数退避等待
                long waitTime = BASE_WAIT_MS * (1L << attempt) + 
                               (long)(Math.random() * MAX_WAIT_MS);
                Thread.sleep(Math.min(waitTime, MAX_WAIT_MS));
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("订单号生成被中断", e);
            }
        }
        
        throw new RuntimeException("订单号生成失败，请稍后重试");
    }
    
    private String doGenerateOrderNo() {
        LocalDate now = LocalDate.now();
        String dateStr = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String prefix = "XSD" + dateStr;
        
        int maxSeq = getMaxSequenceOfDay(dateStr);
        String sequence = String.format("%04d", maxSeq + 1);
        
        return prefix + sequence;
    }
}
```

### **数据库约束DDL**
```sql
-- 添加订单号唯一约束
ALTER TABLE `t_shipping_order` 
ADD UNIQUE KEY `uk_order_no` (`order_no`);

-- 添加索引优化查询性能
ALTER TABLE `t_shipping_order` 
ADD INDEX `idx_order_no_date` (`order_no`, `created_time`);
```

## ⚠️ **注意事项**

### **1. 性能考虑**
- 重试次数不宜过多（建议3-5次）
- 等待时间使用指数退避算法
- 添加适当的数据库索引

### **2. 监控告警**
- 监控订单号生成失败率
- 监控重试次数分布
- 设置异常告警机制

### **3. 降级策略**
- 当重试失败时，可考虑使用UUID作为临时订单号
- 后台异步任务重新生成规范订单号

### **4. 测试验证**
- 压力测试验证并发安全性
- 监控数据库死锁情况
- 验证异常场景处理

通过以上方案，可以有效解决订单号生成的并发问题，确保订单号的唯一性和系统的稳定性！🎉
