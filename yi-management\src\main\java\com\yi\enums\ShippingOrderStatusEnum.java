package com.yi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 发运订单状态枚举
 * 
 * <AUTHOR>
 * @date 2024-12-23
 */
@Getter
@AllArgsConstructor
public enum ShippingOrderStatusEnum {

    /**
     * 待发货
     */
    PENDING(1000, "待发货"),

    /**
     * 发货中
     */
    SHIPPING(2000, "发货中"),

    /**
     * 已完结
     */
    COMPLETED(3000, "已完结"),

    /**
     * 已取消
     */
    CANCELLED(4000, "已取消"),

    /**
     * 默认值
     */
    DEFAULT(-999, "");

    private final Integer code;
    private final String description;

    /**
     * 根据状态码获取描述
     * 
     * @param code 状态码
     * @return 状态描述
     */
    public static String getDescriptionByCode(Integer code) {
        if (code == null) {
            return "";
        }
        
        for (ShippingOrderStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status.getDescription();
            }
        }
        
        return "";
    }

    /**
     * 根据状态码获取枚举
     * 
     * @param code 状态码
     * @return 状态枚举
     */
    public static ShippingOrderStatusEnum getByCode(Integer code) {
        if (code == null) {
            return DEFAULT;
        }
        
        for (ShippingOrderStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        
        return DEFAULT;
    }

    /**
     * 判断状态是否有效
     * 
     * @param code 状态码
     * @return 是否有效
     */
    public static boolean isValidStatus(Integer code) {
        if (code == null) {
            return false;
        }
        
        for (ShippingOrderStatusEnum status : values()) {
            if (status.getCode().equals(code) && status != DEFAULT) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 判断订单是否可以取消
     * 
     * @param code 状态码
     * @return 是否可以取消
     */
    public static boolean canCancel(Integer code) {
        return PENDING.getCode().equals(code);
    }

    /**
     * 判断订单是否可以修改
     * 
     * @param code 状态码
     * @return 是否可以修改
     */
    public static boolean canModify(Integer code) {
        return PENDING.getCode().equals(code);
    }

    /**
     * 判断订单是否已完成
     * 
     * @param code 状态码
     * @return 是否已完成
     */
    public static boolean isCompleted(Integer code) {
        return COMPLETED.getCode().equals(code) || CANCELLED.getCode().equals(code);
    }
}
