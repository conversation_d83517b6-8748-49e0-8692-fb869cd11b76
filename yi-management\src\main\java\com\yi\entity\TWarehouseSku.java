package com.yi.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 仓库SKU关联表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_warehouse_sku")
@ApiModel(value = "TWarehouseSku对象", description = "仓库SKU关联表")
public class TWarehouseSku extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库ID
     */
    @ApiModelProperty(value = "仓库ID")
    private Long warehouseId;

    /**
     * 一级类目 1:循环托盘
     */
    @ApiModelProperty(value = "一级类目 1:循环托盘")
    private Integer firstCategory;

    /**
     * 二级类目
     */
    @ApiModelProperty(value = "二级类目")
    private String secondCategory;

    /**
     * 启用状态：1-启用，0-禁用
     */
    @ApiModelProperty(value = "启用状态：1-启用，0-禁用")
    private Integer enabled;
}
