package com.yi.service.impl;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yi.controller.user.model.*;
import com.yi.entity.SysDept;
import com.yi.entity.SysRole;
import com.yi.entity.SysUser;
import com.yi.entity.SysUserRole;
import com.yi.mapper.SysUserMapper;
import com.yi.service.SysDeptService;
import com.yi.service.SysRoleService;
import com.yi.service.SysUserRoleService;
import com.yi.service.SysUserService;
import com.yi.utils.FormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 系统用户表 服务实现类
 */
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {

    @Autowired
    private SysUserRoleService sysUserRoleService;

    @Autowired
    private SysRoleService sysRoleService;

    @Autowired
    private SysDeptService sysDeptService;

    @Override
    public IPage<SysUser> selectUserPage(Page<SysUser> page, String username, String realName, 
                                         String email, String phone, Long deptId, Integer status) {
        return baseMapper.selectUserPage(page, username, realName, email, phone, deptId, status);
    }

    @Override
    public SysUser selectByUsername(String username) {
        return baseMapper.selectByUsername(username);
    }

    @Override
    public SysUser selectByEmail(String email) {
        return baseMapper.selectByEmail(email);
    }

    @Override
    public SysUser selectByPhone(String phone) {
        return baseMapper.selectByPhone(phone);
    }

    @Override
    public List<SysUser> selectByDeptId(Long deptId) {
        return baseMapper.selectByDeptId(deptId);
    }

    @Override
    public List<SysUser> selectByRoleId(Long roleId) {
        return baseMapper.selectByRoleId(roleId);
    }

    @Override
    public List<Long> selectRoleIdsByUserId(Long userId) {
        return baseMapper.selectRoleIdsByUserId(userId);
    }

    @Override
    public List<String> selectPermissionsByUserId(Long userId) {
        return baseMapper.selectPermissionsByUserId(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createUser(SysUser user) {
        // 检查用户名是否存在
        if (checkUsernameExists(user.getUsername(), null)) {
            throw new RuntimeException("用户名已存在");
        }
        
        // 检查邮箱是否存在
        if (StringUtils.hasText(user.getEmail()) && checkEmailExists(user.getEmail(), null)) {
            throw new RuntimeException("邮箱已存在");
        }
        
        // 检查手机号是否存在
        if (StringUtils.hasText(user.getPhone()) && checkPhoneExists(user.getPhone(), null)) {
            throw new RuntimeException("手机号已存在");
        }
        
        // 加密密码
        if (StringUtils.hasText(user.getPassword())) {
            user.setPassword(DigestUtils.md5DigestAsHex(user.getPassword().getBytes()));
        }
        
        // 设置默认值
        user.setStatus(1);
        user.setValid(1);
        user.setCreatedTime(LocalDateTime.now());
        user.setLastModifiedTime(LocalDateTime.now());
        
        return save(user);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUser(SysUser user) {
        // 检查用户名是否存在
        if (checkUsernameExists(user.getUsername(), user.getId())) {
            throw new RuntimeException("用户名已存在");
        }
        
        // 检查邮箱是否存在
        if (StringUtils.hasText(user.getEmail()) && checkEmailExists(user.getEmail(), user.getId())) {
            throw new RuntimeException("邮箱已存在");
        }
        
        // 检查手机号是否存在
        if (StringUtils.hasText(user.getPhone()) && checkPhoneExists(user.getPhone(), user.getId())) {
            throw new RuntimeException("手机号已存在");
        }
        
        user.setLastModifiedTime(LocalDateTime.now());
        return updateById(user);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteUser(Long userId) {
        // 删除用户角色关联
        sysUserRoleService.deleteByUserId(userId);
        
        // 逻辑删除用户
        SysUser user = new SysUser();
        user.setId(userId);
        user.setValid(0);
        user.setLastModifiedTime(LocalDateTime.now());
        return updateById(user);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteUsers(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return true;
        }
        
        for (Long userId : userIds) {
            deleteUser(userId);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetPassword(Long userId, String newPassword) {
        SysUser user = new SysUser();
        user.setId(userId);
        user.setPassword(DigestUtils.md5DigestAsHex(newPassword.getBytes()));
        user.setLastModifiedTime(LocalDateTime.now());
        return updateById(user);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean changePassword(Long userId, String oldPassword, String newPassword) {
        SysUser user = getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        String encryptedOldPassword = DigestUtils.md5DigestAsHex(oldPassword.getBytes());
        if (!encryptedOldPassword.equals(user.getPassword())) {
            throw new RuntimeException("原密码错误");
        }
        
        return resetPassword(userId, newPassword);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignRoles(Long userId, List<Long> roleIds) {
        // 先删除原有角色关联
        sysUserRoleService.deleteByUserId(userId);
        
        // 添加新的角色关联
        if (!CollectionUtils.isEmpty(roleIds)) {
            List<SysUserRole> userRoles = roleIds.stream().map(roleId -> {
                SysUserRole userRole = new SysUserRole();
                userRole.setUserId(userId);
                userRole.setRoleId(roleId);
                userRole.setValid(1);
                userRole.setCreatedTime(LocalDateTime.now());
                userRole.setLastModifiedTime(LocalDateTime.now());
                return userRole;
            }).collect(Collectors.toList());
            
            return sysUserRoleService.saveBatch(userRoles);
        }
        
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(Long userId, Integer status) {
        SysUser user = new SysUser();
        user.setId(userId);
        user.setStatus(status);
        user.setLastModifiedTime(LocalDateTime.now());
        return updateById(user);
    }

    @Override
    public boolean checkUsernameExists(String username, Long excludeId) {
        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUser::getUsername, username)
               .eq(SysUser::getValid, 1);
        if (excludeId != null) {
            wrapper.ne(SysUser::getId, excludeId);
        }
        return count(wrapper) > 0;
    }

    @Override
    public boolean checkEmailExists(String email, Long excludeId) {
        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUser::getEmail, email)
               .eq(SysUser::getValid, 1);
        if (excludeId != null) {
            wrapper.ne(SysUser::getId, excludeId);
        }
        return count(wrapper) > 0;
    }

    @Override
    public boolean checkPhoneExists(String phone, Long excludeId) {
        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUser::getPhone, phone)
               .eq(SysUser::getValid, 1);
        if (excludeId != null) {
            wrapper.ne(SysUser::getId, excludeId);
        }
        return count(wrapper) > 0;
    }

    // ==================== 新增的业务方法实现 ====================

    @Override
    public IPage<UserPageResponse> getUserPageResponse(UserQueryRequest request) {
        // 转换分页参数
        Integer current = FormatUtils.safeToInteger(request.getCurrent(), 1);
        Integer size = FormatUtils.safeToInteger(request.getSize(), 10);
        Page<SysUser> page = new Page<>(current, size);

        // 分页查询用户
        IPage<SysUser> originalPage = selectUserPage(page, request.getUsername(), request.getRealName(),
                request.getEmail(), request.getPhone(), request.getDeptId(), request.getStatus());

        // 转换为Response对象
        List<UserPageResponse> responseList = originalPage.getRecords().stream()
                .map(this::convertToPageResponse)
                .collect(Collectors.toList());

        // 构建返回的分页对象
        Page<UserPageResponse> responsePage = new Page<>(originalPage.getCurrent(), originalPage.getSize(), originalPage.getTotal());
        responsePage.setRecords(responseList);

        return responsePage;
    }

    @Override
    public UserDetailResponse getUserDetailById(Long userId) {
        SysUser user = getById(userId);
        if (user == null || user.getValid() == 0) {
            return null;
        }

        UserDetailResponse response = new UserDetailResponse();
        BeanUtils.copyProperties(user, response);

        // 设置性别描述
        if (user.getGender() != null) {
            response.setGenderDesc(user.getGender() == 1 ? "男" : "女");
        }

        // 设置状态描述
        response.setStatusDesc(user.getStatus() == 1 ? "启用" : "禁用");

        // 设置部门信息
        if (user.getDeptId() != null) {
            SysDept dept = sysDeptService.getById(user.getDeptId());
            if (dept != null) {
                response.setDeptName(dept.getDeptName());
            }
        }

        // 设置角色信息
        List<SysRole> roles = sysRoleService.selectByUserId(userId);
        if (!CollectionUtils.isEmpty(roles)) {
            response.setRoleIds(roles.stream().map(SysRole::getId).collect(Collectors.toList()));
            List<UserDetailResponse.UserRoleInfo> roleInfos = roles.stream().map(role -> {
                UserDetailResponse.UserRoleInfo roleInfo = new UserDetailResponse.UserRoleInfo();
                roleInfo.setRoleId(role.getId());
                roleInfo.setRoleCode(role.getRoleCode());
                roleInfo.setRoleName(role.getRoleName());
                roleInfo.setRoleDesc(role.getRoleDesc());
                return roleInfo;
            }).collect(Collectors.toList());
            response.setRoles(roleInfos);
        }

        // 设置权限信息
        List<String> permissions = selectPermissionsByUserId(userId);
        response.setPermissions(permissions);

        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addUser(UserRequest request) {
        SysUser user = new SysUser();
        BeanUtils.copyProperties(request, user);

        // 创建用户
        boolean success = createUser(user);
        if (success && !CollectionUtils.isEmpty(request.getRoleIds())) {
            // 分配角色
            assignRoles(user.getId(), request.getRoleIds());
        }

        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUser(UserRequest request) {
        SysUser user = new SysUser();
        BeanUtils.copyProperties(request, user);

        // 更新用户基本信息
        boolean success = updateUser(user);
        if (success && request.getRoleIds() != null) {
            // 重新分配角色
            assignRoles(user.getId(), request.getRoleIds());
        }

        return success;
    }

    @Override
    public void exportUserList(UserQueryRequest request, HttpServletResponse response) throws IOException {
        // 查询所有符合条件的数据（不分页）
        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUser::getValid, 1)
                .like(StringUtils.hasText(request.getUsername()), SysUser::getUsername, request.getUsername())
                .like(StringUtils.hasText(request.getRealName()), SysUser::getRealName, request.getRealName())
                .like(StringUtils.hasText(request.getEmail()), SysUser::getEmail, request.getEmail())
                .like(StringUtils.hasText(request.getPhone()), SysUser::getPhone, request.getPhone())
                .eq(request.getDeptId() != null, SysUser::getDeptId, request.getDeptId())
                .eq(request.getStatus() != null, SysUser::getStatus, request.getStatus())
                .orderByDesc(SysUser::getCreatedTime);

        List<SysUser> userList = list(wrapper);

        // 查询部门信息
        Map<Long, String> deptMap = sysDeptService.list().stream()
                .collect(Collectors.toMap(SysDept::getId, SysDept::getDeptName, (k1, k2) -> k1));

        // 查询用户角色信息
        Map<Long, List<SysRole>> userRoleMap = userList.stream()
                .collect(Collectors.toMap(SysUser::getId, user -> sysRoleService.selectByUserId(user.getId())));

        // 转换为导出VO
        List<UserExportVO> exportList = userList.stream().map(user -> {
            UserExportVO exportVO = new UserExportVO();
            exportVO.setStatus(user.getStatus() == 1 ? "启用" : "禁用");
            exportVO.setUsername(user.getUsername());
            exportVO.setRealName(user.getRealName());
            exportVO.setEmail(user.getEmail());
            exportVO.setPhone(user.getPhone());
            exportVO.setGender(user.getGender() != null ? (user.getGender() == 1 ? "男" : "女") : "");
            exportVO.setBirthday(user.getBirthday() != null ? user.getBirthday().toString() : "");
            exportVO.setDeptName(deptMap.get(user.getDeptId()));

            // 角色信息
            List<SysRole> roles = userRoleMap.get(user.getId());
            if (!CollectionUtils.isEmpty(roles)) {
                String roleNames = roles.stream().map(SysRole::getRoleName).collect(Collectors.joining(","));
                exportVO.setRoles(roleNames);
            }

            exportVO.setLoginIp(user.getLoginIp());
            exportVO.setLoginTime(user.getLoginTime() != null ?
                user.getLoginTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "");
            exportVO.setCreatedBy(user.getCreatedBy());
            exportVO.setCreatedTime(user.getCreatedTime() != null ?
                user.getCreatedTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "");

            return exportVO;
        }).collect(Collectors.toList());

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("用户列表_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")), "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        // 导出Excel
        EasyExcel.write(response.getOutputStream(), UserExportVO.class)
                .sheet("用户列表")
                .doWrite(exportList);
    }

    /**
     * 转换为分页响应对象
     */
    private UserPageResponse convertToPageResponse(SysUser user) {
        UserPageResponse response = new UserPageResponse();
        BeanUtils.copyProperties(user, response);

        // 设置性别描述
        if (user.getGender() != null) {
            response.setGenderDesc(user.getGender() == 1 ? "男" : "女");
        }

        // 设置状态描述
        response.setStatusDesc(user.getStatus() == 1 ? "启用" : "禁用");

        // 设置部门信息
        if (user.getDeptId() != null) {
            SysDept dept = sysDeptService.getById(user.getDeptId());
            if (dept != null) {
                response.setDeptName(dept.getDeptName());
            }
        }

        // 设置角色信息
        List<SysRole> roles = sysRoleService.selectByUserId(user.getId());
        if (!CollectionUtils.isEmpty(roles)) {
            List<UserPageResponse.UserRoleInfo> roleInfos = roles.stream().map(role -> {
                UserPageResponse.UserRoleInfo roleInfo = new UserPageResponse.UserRoleInfo();
                roleInfo.setRoleId(role.getId());
                roleInfo.setRoleCode(role.getRoleCode());
                roleInfo.setRoleName(role.getRoleName());
                return roleInfo;
            }).collect(Collectors.toList());
            response.setRoles(roleInfos);
        }

        return response;
    }
}
