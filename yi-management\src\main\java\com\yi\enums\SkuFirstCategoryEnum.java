package com.yi.enums;

/**
 * SKU一级类目枚举
 */
public enum SkuFirstCategoryEnum {

    /**
     * 循环托盘
     */
    CIRCULATION_PALLET(1, "循环托盘");

    private final Integer code;
    private final String description;

    SkuFirstCategoryEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据code获取描述
     *
     * @param code 编码
     * @return 描述
     */
    public static String getDescriptionByCode(Integer code) {
        if (code == null) {
            return "";
        }
        for (SkuFirstCategoryEnum category : values()) {
            if (category.getCode().equals(code)) {
                return category.getDescription();
            }
        }
        return "";
    }

    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static SkuFirstCategoryEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SkuFirstCategoryEnum category : values()) {
            if (category.getCode().equals(code)) {
                return category;
            }
        }
        return null;
    }
}
