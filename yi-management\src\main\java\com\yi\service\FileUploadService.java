package com.yi.service;

import com.yi.config.FileUploadConfig;
import com.yi.controller.common.model.FileUploadResponse;
import com.yi.utils.FileUploadUtils;
import com.yi.utils.FormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 文件上传服务
 */
@Service
public class FileUploadService {

    @Autowired
    private FileUploadConfig fileUploadConfig;

    /**
     * 上传单个文件到临时目录
     *
     * @param file 文件
     * @param module 模块名称（用于分类存储）
     * @return 上传结果
     * @throws IOException IO异常
     */
    public FileUploadResponse uploadTempFile(MultipartFile file, String module) throws IOException {
        return uploadFile(file, module, true);
    }

    /**
     * 上传单个文件到正式目录
     *
     * @param file 文件
     * @param module 模块名称（用于分类存储）
     * @return 上传结果
     * @throws IOException IO异常
     */
    public FileUploadResponse uploadFile(MultipartFile file, String module) throws IOException {
        return uploadFile(file, module, false);
    }

    /**
     * 上传单个文件
     *
     * @param file 文件
     * @param module 模块名称（用于分类存储）
     * @param isTemp 是否为临时文件
     * @return 上传结果
     * @throws IOException IO异常
     */
    private FileUploadResponse uploadFile(MultipartFile file, String module, boolean isTemp) throws IOException {
        // 1. 基础校验
        if (file == null || file.isEmpty()) {
            throw new RuntimeException("文件不能为空");
        }

        String originalFileName = file.getOriginalFilename();
        if (originalFileName == null || originalFileName.isEmpty()) {
            throw new RuntimeException("文件名不能为空");
        }

        // 2. 文件类型校验
        if (!FileUploadUtils.isAllowedFileType(originalFileName, fileUploadConfig.getAllowedTypes())) {
            throw new RuntimeException("不支持的文件类型，支持的类型：" + String.join(", ", fileUploadConfig.getAllowedTypes()));
        }

        // 3. 文件大小校验
        if (FileUploadUtils.isFileSizeExceeded(file, fileUploadConfig.getMaxFileSize())) {
            throw new RuntimeException("文件大小超过限制，最大允许：" + FileUploadUtils.formatFileSize(fileUploadConfig.getMaxFileSize()));
        }

        // 4. 生成文件路径
        String datePath = FileUploadUtils.generateDatePath();
        String uniqueFileName = FileUploadUtils.generateUniqueFileName(originalFileName);

        // 模块路径：basePath/module/yyyy/MM/dd/uniqueFileName
        String moduleDir = module != null && !module.isEmpty() ? module : "common";
        String relativePath = moduleDir + "/" + datePath + "/" + uniqueFileName;

        // 根据是否临时文件选择不同的存储路径
        String basePath = isTemp ? fileUploadConfig.getTempPath() : fileUploadConfig.getBasePath();
        String fullPath = basePath + "/" + relativePath;

        // 5. 保存文件
        FileUploadUtils.saveFile(file, fullPath);

        // 6. 构建返回结果
        FileUploadResponse response = new FileUploadResponse();
        response.setFileName(uniqueFileName);
        response.setOriginalFileName(originalFileName);
        response.setFileSize(file.getSize());
        response.setFileType(FileUploadUtils.getFileExtension(originalFileName));
        response.setFilePath(relativePath);
        response.setIsTemp(isTemp);
        response.setUploadTime(FormatUtils.formatDateTime(LocalDateTime.now()));

        // 根据是否临时文件设置不同的访问URL
        String urlPrefix = isTemp ? fileUploadConfig.getTempUrlPrefix() : fileUploadConfig.getUrlPrefix();
        response.setFileUrl(urlPrefix + "/" + relativePath);

        return response;
    }

    /**
     * 删除文件
     *
     * @param filePath 文件相对路径
     * @return 是否删除成功
     */
    public boolean deleteFile(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return false;
        }
        
        String fullPath = fileUploadConfig.getBasePath() + "/" + filePath;
        return FileUploadUtils.deleteFile(fullPath);
    }

    /**
     * 获取文件完整路径
     *
     * @param filePath 文件相对路径
     * @return 完整路径
     */
    public String getFullPath(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return null;
        }
        return fileUploadConfig.getBasePath() + "/" + filePath;
    }

    /**
     * 获取文件访问URL
     *
     * @param filePath 文件相对路径
     * @param isTemp 是否为临时文件
     * @return 访问URL
     */
    public String getFileUrl(String filePath, boolean isTemp) {
        if (filePath == null || filePath.isEmpty()) {
            return null;
        }
        String urlPrefix = isTemp ? fileUploadConfig.getTempUrlPrefix() : fileUploadConfig.getUrlPrefix();
        return urlPrefix + "/" + filePath;
    }

    /**
     * 获取正式文件访问URL（兼容旧方法）
     *
     * @param filePath 文件相对路径
     * @return 访问URL
     */
    public String getFileUrl(String filePath) {
        return getFileUrl(filePath, false);
    }

    /**
     * 将临时文件移动到正式目录
     *
     * @param tempFilePath 临时文件相对路径
     * @param module 目标模块名称
     * @return 移动后的文件信息
     * @throws IOException IO异常
     */
    public FileUploadResponse moveTempFileToFormal(String tempFilePath, String module) throws IOException {
        if (tempFilePath == null || tempFilePath.isEmpty()) {
            throw new RuntimeException("临时文件路径不能为空");
        }

        // 1. 构建临时文件完整路径
        String tempFullPath = fileUploadConfig.getTempPath() + "/" + tempFilePath;
        File tempFile = new File(tempFullPath);

        if (!tempFile.exists()) {
            throw new RuntimeException("临时文件不存在：" + tempFilePath);
        }

        // 2. 生成正式文件路径
        String originalFileName = extractOriginalFileName(tempFilePath);
        String datePath = FileUploadUtils.generateDatePath();
        String uniqueFileName = FileUploadUtils.generateUniqueFileName(originalFileName);

        String moduleDir = module != null && !module.isEmpty() ? module : "common";
        String formalRelativePath = moduleDir + "/" + datePath + "/" + uniqueFileName;
        String formalFullPath = fileUploadConfig.getBasePath() + "/" + formalRelativePath;

        // 3. 创建目标目录
        File formalFile = new File(formalFullPath);
        File parentDir = formalFile.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            parentDir.mkdirs();
        }

        // 4. 移动文件
        if (!tempFile.renameTo(formalFile)) {
            throw new IOException("文件移动失败：从 " + tempFullPath + " 到 " + formalFullPath);
        }

        // 5. 构建返回结果
        FileUploadResponse response = new FileUploadResponse();
        response.setFileName(uniqueFileName);
        response.setOriginalFileName(originalFileName);
        response.setFileSize(formalFile.length());
        response.setFileType(FileUploadUtils.getFileExtension(originalFileName));
        response.setFilePath(formalRelativePath);
        response.setFileUrl(fileUploadConfig.getUrlPrefix() + "/" + formalRelativePath);
        response.setIsTemp(false);
        response.setUploadTime(FormatUtils.formatDateTime(LocalDateTime.now()));

        return response;
    }

    /**
     * 批量将临时文件移动到正式目录
     *
     * @param tempFilePaths 临时文件路径列表
     * @param module 目标模块名称
     * @return 移动结果列表
     */
    public List<FileUploadResponse> moveTempFilesToFormal(List<String> tempFilePaths, String module) {
        List<FileUploadResponse> results = new ArrayList<>();

        if (tempFilePaths != null) {
            for (String tempFilePath : tempFilePaths) {
                try {
                    FileUploadResponse response = moveTempFileToFormal(tempFilePath, module);
                    results.add(response);
                } catch (Exception e) {
                    // 记录错误但继续处理其他文件
                    System.err.println("移动临时文件失败：" + tempFilePath + ", 错误：" + e.getMessage());
                }
            }
        }

        return results;
    }

    /**
     * 从文件路径中提取原始文件名（用于移动时保持扩展名）
     *
     * @param filePath 文件路径
     * @return 原始文件名
     */
    private String extractOriginalFileName(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return "unknown";
        }

        String fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
        String extension = FileUploadUtils.getFileExtension(fileName);

        return "file" + (extension.isEmpty() ? "" : "." + extension);
    }
}
