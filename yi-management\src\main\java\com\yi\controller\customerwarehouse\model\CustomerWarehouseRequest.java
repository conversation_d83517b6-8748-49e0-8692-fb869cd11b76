package com.yi.controller.customerwarehouse.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 仓库请求
 */
@Data
@ApiModel(value = "CustomerWarehouseRequest", description = "仓库请求")
public class CustomerWarehouseRequest {

    @ApiModelProperty(value = "主键ID")
    private String id;

    @NotBlank(message = "公司ID不能为空")
    @ApiModelProperty(value = "公司ID", required = true)
    private String companyId;

    @NotBlank(message = "仓库名称不能为空")
    @ApiModelProperty(value = "仓库名称", required = true)
    private String warehouseName;

    @NotNull(message = "省份ID不能为空")
    @ApiModelProperty(value = "省份ID", required = true)
    private String provinceId;

    @NotBlank(message = "省份名称不能为空")
    @ApiModelProperty(value = "省份名称", required = true)
    private String provinceName;

    @NotNull(message = "城市ID不能为空")
    @ApiModelProperty(value = "城市ID", required = true)
    private String cityId;

    @NotBlank(message = "城市名称不能为空")
    @ApiModelProperty(value = "城市名称", required = true)
    private String cityName;

    @ApiModelProperty(value = "区县ID")
    private String areaId;

    @ApiModelProperty(value = "区县名称")
    private String areaName;

    @NotBlank(message = "详细地址不能为空")
    @ApiModelProperty(value = "详细地址", required = true)
    private String detailedAddress;

    @NotBlank(message = "收货人不能为空")
    @ApiModelProperty(value = "收货人", required = true)
    private String contactPerson;

    @NotBlank(message = "手机号不能为空")
    @ApiModelProperty(value = "手机号", required = true)
    private String mobilePhone;

    @ApiModelProperty(value = "座机号")
    private String landlinePhone;

    @ApiModelProperty(value = "SKU类型列表（合约客户必填，至少1个，最多9个）")
    private List<CustomerWarehouseSkuTypeRequest> skuTypes;

    @NotNull(message = "启用状态不能为空")
    @ApiModelProperty(value = "启用状态：1-启用，0-禁用", required = true)
    private String enabled;

    @ApiModelProperty(value = "备注")
    private String remark;
}
