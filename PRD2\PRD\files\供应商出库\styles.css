﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-50px;
  width:2355px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u2736_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1350px;
  height:154px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2736 {
  border-width:0px;
  position:absolute;
  left:50px;
  top:38px;
  width:1350px;
  height:154px;
  display:flex;
}
#u2736 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2736_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2737_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1351px;
  height:2px;
}
#u2737 {
  border-width:0px;
  position:absolute;
  left:50px;
  top:37px;
  width:1350px;
  height:1px;
  display:flex;
}
#u2737 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2737_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2738_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2738 {
  border-width:0px;
  position:absolute;
  left:50px;
  top:8px;
  width:120px;
  height:30px;
  display:flex;
}
#u2738 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2738_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2739_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2739 {
  border-width:0px;
  position:absolute;
  left:1344px;
  top:17px;
  width:56px;
  height:20px;
  display:flex;
}
#u2739 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2739_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2740_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2740 {
  border-width:0px;
  position:absolute;
  left:70px;
  top:62px;
  width:28px;
  height:16px;
  display:flex;
}
#u2740 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2740_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2741_input {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2741_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2741_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2741 {
  border-width:0px;
  position:absolute;
  left:108px;
  top:58px;
  width:120px;
  height:24px;
  display:flex;
}
#u2741 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2741_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2741.disabled {
}
.u2741_input_option {
}
#u2742_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2742 {
  border-width:0px;
  position:absolute;
  left:957px;
  top:62px;
  width:28px;
  height:16px;
  display:flex;
}
#u2742 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2742_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2743_input {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2743_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2743_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2743 {
  border-width:0px;
  position:absolute;
  left:990px;
  top:58px;
  width:120px;
  height:24px;
  display:flex;
}
#u2743 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2743_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2743.disabled {
}
#u2744_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2744 {
  border-width:0px;
  position:absolute;
  left:735px;
  top:62px;
  width:42px;
  height:16px;
  display:flex;
}
#u2744 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2744_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2745_input {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2745_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2745_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2745 {
  border-width:0px;
  position:absolute;
  left:787px;
  top:58px;
  width:120px;
  height:24px;
  display:flex;
}
#u2745 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2745_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2745.disabled {
}
#u2746_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2746 {
  border-width:0px;
  position:absolute;
  left:499px;
  top:62px;
  width:56px;
  height:16px;
  display:flex;
}
#u2746 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2746_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2747_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:25px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2747 {
  border-width:0px;
  position:absolute;
  left:1205px;
  top:102px;
  width:80px;
  height:25px;
  display:flex;
}
#u2747 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2747_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2748_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2748 {
  border-width:0px;
  position:absolute;
  left:1300px;
  top:102px;
  width:80px;
  height:25px;
  display:flex;
}
#u2748 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2748_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2749_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:25px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2749 {
  border-width:0px;
  position:absolute;
  left:50px;
  top:226px;
  width:80px;
  height:25px;
  display:flex;
}
#u2749 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2749_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2750 {
  border-width:0px;
  position:absolute;
  left:50px;
  top:261px;
  width:2355px;
  height:337px;
}
#u2751_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:30px;
}
#u2751 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:30px;
  display:flex;
}
#u2751 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2751_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2752_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2752 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:0px;
  width:112px;
  height:30px;
  display:flex;
}
#u2752 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2752_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2753_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2753 {
  border-width:0px;
  position:absolute;
  left:148px;
  top:0px;
  width:112px;
  height:30px;
  display:flex;
}
#u2753 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2753_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2754_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:30px;
}
#u2754 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:0px;
  width:122px;
  height:30px;
  display:flex;
}
#u2754 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2754_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2755_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:30px;
}
#u2755 {
  border-width:0px;
  position:absolute;
  left:382px;
  top:0px;
  width:143px;
  height:30px;
  display:flex;
}
#u2755 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2755_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2756_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  height:30px;
}
#u2756 {
  border-width:0px;
  position:absolute;
  left:525px;
  top:0px;
  width:133px;
  height:30px;
  display:flex;
}
#u2756 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2756_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2757_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:30px;
}
#u2757 {
  border-width:0px;
  position:absolute;
  left:658px;
  top:0px;
  width:90px;
  height:30px;
  display:flex;
}
#u2757 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2757_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2758_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
}
#u2758 {
  border-width:0px;
  position:absolute;
  left:748px;
  top:0px;
  width:119px;
  height:30px;
  display:flex;
}
#u2758 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2758_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2759_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2759 {
  border-width:0px;
  position:absolute;
  left:867px;
  top:0px;
  width:112px;
  height:30px;
  display:flex;
}
#u2759 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2759_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2760_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u2760 {
  border-width:0px;
  position:absolute;
  left:979px;
  top:0px;
  width:116px;
  height:30px;
  display:flex;
}
#u2760 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2760_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2761_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:146px;
  height:30px;
}
#u2761 {
  border-width:0px;
  position:absolute;
  left:1095px;
  top:0px;
  width:146px;
  height:30px;
  display:flex;
}
#u2761 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2761_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2762_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:30px;
}
#u2762 {
  border-width:0px;
  position:absolute;
  left:1241px;
  top:0px;
  width:97px;
  height:30px;
  display:flex;
}
#u2762 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2762_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2763_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2763 {
  border-width:0px;
  position:absolute;
  left:1338px;
  top:0px;
  width:112px;
  height:30px;
  display:flex;
}
#u2763 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2763_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2764_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2764 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:0px;
  width:112px;
  height:30px;
  display:flex;
}
#u2764 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2764_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2765_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u2765 {
  border-width:0px;
  position:absolute;
  left:1562px;
  top:0px;
  width:139px;
  height:30px;
  display:flex;
}
#u2765 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2765_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2766_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:30px;
}
#u2766 {
  border-width:0px;
  position:absolute;
  left:1701px;
  top:0px;
  width:96px;
  height:30px;
  display:flex;
}
#u2766 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2766_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2767_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:121px;
  height:30px;
}
#u2767 {
  border-width:0px;
  position:absolute;
  left:1797px;
  top:0px;
  width:121px;
  height:30px;
  display:flex;
}
#u2767 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2767_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2768_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u2768 {
  border-width:0px;
  position:absolute;
  left:1918px;
  top:0px;
  width:118px;
  height:30px;
  display:flex;
}
#u2768 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2768_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2769_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:30px;
}
#u2769 {
  border-width:0px;
  position:absolute;
  left:2036px;
  top:0px;
  width:98px;
  height:30px;
  display:flex;
}
#u2769 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2769_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2770_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:109px;
  height:30px;
}
#u2770 {
  border-width:0px;
  position:absolute;
  left:2134px;
  top:0px;
  width:109px;
  height:30px;
  display:flex;
}
#u2770 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2770_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2771_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2771 {
  border-width:0px;
  position:absolute;
  left:2243px;
  top:0px;
  width:112px;
  height:30px;
  display:flex;
}
#u2771 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2771_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2772_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:34px;
}
#u2772 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:30px;
  width:36px;
  height:34px;
  display:flex;
}
#u2772 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2772_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2773_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:34px;
}
#u2773 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:30px;
  width:112px;
  height:34px;
  display:flex;
}
#u2773 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2773_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2774_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:34px;
}
#u2774 {
  border-width:0px;
  position:absolute;
  left:148px;
  top:30px;
  width:112px;
  height:34px;
  display:flex;
}
#u2774 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2774_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2775_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:34px;
}
#u2775 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:30px;
  width:122px;
  height:34px;
  display:flex;
}
#u2775 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2775_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2776_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:34px;
}
#u2776 {
  border-width:0px;
  position:absolute;
  left:382px;
  top:30px;
  width:143px;
  height:34px;
  display:flex;
}
#u2776 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2776_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2777_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  height:34px;
}
#u2777 {
  border-width:0px;
  position:absolute;
  left:525px;
  top:30px;
  width:133px;
  height:34px;
  display:flex;
}
#u2777 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2777_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2778_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:34px;
}
#u2778 {
  border-width:0px;
  position:absolute;
  left:658px;
  top:30px;
  width:90px;
  height:34px;
  display:flex;
}
#u2778 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2778_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2779_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:34px;
}
#u2779 {
  border-width:0px;
  position:absolute;
  left:748px;
  top:30px;
  width:119px;
  height:34px;
  display:flex;
}
#u2779 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2779_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2780_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:34px;
}
#u2780 {
  border-width:0px;
  position:absolute;
  left:867px;
  top:30px;
  width:112px;
  height:34px;
  display:flex;
}
#u2780 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2780_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2781_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:34px;
}
#u2781 {
  border-width:0px;
  position:absolute;
  left:979px;
  top:30px;
  width:116px;
  height:34px;
  display:flex;
}
#u2781 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2781_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2782_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:146px;
  height:34px;
}
#u2782 {
  border-width:0px;
  position:absolute;
  left:1095px;
  top:30px;
  width:146px;
  height:34px;
  display:flex;
}
#u2782 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2782_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2783_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:34px;
}
#u2783 {
  border-width:0px;
  position:absolute;
  left:1241px;
  top:30px;
  width:97px;
  height:34px;
  display:flex;
}
#u2783 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2783_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2784_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:34px;
}
#u2784 {
  border-width:0px;
  position:absolute;
  left:1338px;
  top:30px;
  width:112px;
  height:34px;
  display:flex;
}
#u2784 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2784_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2785_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:34px;
}
#u2785 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:30px;
  width:112px;
  height:34px;
  display:flex;
}
#u2785 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2785_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2786_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:34px;
}
#u2786 {
  border-width:0px;
  position:absolute;
  left:1562px;
  top:30px;
  width:139px;
  height:34px;
  display:flex;
}
#u2786 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2786_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2787_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:34px;
}
#u2787 {
  border-width:0px;
  position:absolute;
  left:1701px;
  top:30px;
  width:96px;
  height:34px;
  display:flex;
}
#u2787 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2787_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2788_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:121px;
  height:34px;
}
#u2788 {
  border-width:0px;
  position:absolute;
  left:1797px;
  top:30px;
  width:121px;
  height:34px;
  display:flex;
}
#u2788 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2788_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2789_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:34px;
}
#u2789 {
  border-width:0px;
  position:absolute;
  left:1918px;
  top:30px;
  width:118px;
  height:34px;
  display:flex;
}
#u2789 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2789_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2790_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:34px;
}
#u2790 {
  border-width:0px;
  position:absolute;
  left:2036px;
  top:30px;
  width:98px;
  height:34px;
  display:flex;
}
#u2790 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2790_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2791_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:109px;
  height:34px;
}
#u2791 {
  border-width:0px;
  position:absolute;
  left:2134px;
  top:30px;
  width:109px;
  height:34px;
  display:flex;
}
#u2791 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2791_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2792_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:34px;
}
#u2792 {
  border-width:0px;
  position:absolute;
  left:2243px;
  top:30px;
  width:112px;
  height:34px;
  display:flex;
}
#u2792 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2792_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2793_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:31px;
}
#u2793 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:64px;
  width:36px;
  height:31px;
  display:flex;
}
#u2793 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2793_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2794_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:31px;
}
#u2794 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:64px;
  width:112px;
  height:31px;
  display:flex;
}
#u2794 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2794_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2795_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:31px;
}
#u2795 {
  border-width:0px;
  position:absolute;
  left:148px;
  top:64px;
  width:112px;
  height:31px;
  display:flex;
}
#u2795 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2795_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2796_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:31px;
}
#u2796 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:64px;
  width:122px;
  height:31px;
  display:flex;
}
#u2796 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2796_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2797_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:31px;
}
#u2797 {
  border-width:0px;
  position:absolute;
  left:382px;
  top:64px;
  width:143px;
  height:31px;
  display:flex;
}
#u2797 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2797_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2798_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  height:31px;
}
#u2798 {
  border-width:0px;
  position:absolute;
  left:525px;
  top:64px;
  width:133px;
  height:31px;
  display:flex;
}
#u2798 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2798_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2799_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:31px;
}
#u2799 {
  border-width:0px;
  position:absolute;
  left:658px;
  top:64px;
  width:90px;
  height:31px;
  display:flex;
}
#u2799 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2799_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2800_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:31px;
}
#u2800 {
  border-width:0px;
  position:absolute;
  left:748px;
  top:64px;
  width:119px;
  height:31px;
  display:flex;
}
#u2800 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2800_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2801_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:31px;
}
#u2801 {
  border-width:0px;
  position:absolute;
  left:867px;
  top:64px;
  width:112px;
  height:31px;
  display:flex;
}
#u2801 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2801_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2802_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:31px;
}
#u2802 {
  border-width:0px;
  position:absolute;
  left:979px;
  top:64px;
  width:116px;
  height:31px;
  display:flex;
}
#u2802 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2802_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2803_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:146px;
  height:31px;
}
#u2803 {
  border-width:0px;
  position:absolute;
  left:1095px;
  top:64px;
  width:146px;
  height:31px;
  display:flex;
}
#u2803 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2803_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2804_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:31px;
}
#u2804 {
  border-width:0px;
  position:absolute;
  left:1241px;
  top:64px;
  width:97px;
  height:31px;
  display:flex;
}
#u2804 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2804_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2805_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:31px;
}
#u2805 {
  border-width:0px;
  position:absolute;
  left:1338px;
  top:64px;
  width:112px;
  height:31px;
  display:flex;
}
#u2805 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2805_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2806_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:31px;
}
#u2806 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:64px;
  width:112px;
  height:31px;
  display:flex;
}
#u2806 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2806_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2807_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:31px;
}
#u2807 {
  border-width:0px;
  position:absolute;
  left:1562px;
  top:64px;
  width:139px;
  height:31px;
  display:flex;
}
#u2807 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2807_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2808_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:31px;
}
#u2808 {
  border-width:0px;
  position:absolute;
  left:1701px;
  top:64px;
  width:96px;
  height:31px;
  display:flex;
}
#u2808 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2808_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2809_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:121px;
  height:31px;
}
#u2809 {
  border-width:0px;
  position:absolute;
  left:1797px;
  top:64px;
  width:121px;
  height:31px;
  display:flex;
}
#u2809 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2809_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2810_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:31px;
}
#u2810 {
  border-width:0px;
  position:absolute;
  left:1918px;
  top:64px;
  width:118px;
  height:31px;
  display:flex;
}
#u2810 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2810_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2811_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:31px;
}
#u2811 {
  border-width:0px;
  position:absolute;
  left:2036px;
  top:64px;
  width:98px;
  height:31px;
  display:flex;
}
#u2811 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2811_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2812_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:109px;
  height:31px;
}
#u2812 {
  border-width:0px;
  position:absolute;
  left:2134px;
  top:64px;
  width:109px;
  height:31px;
  display:flex;
}
#u2812 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2812_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2813_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:31px;
}
#u2813 {
  border-width:0px;
  position:absolute;
  left:2243px;
  top:64px;
  width:112px;
  height:31px;
  display:flex;
}
#u2813 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2813_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2814_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:30px;
}
#u2814 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:95px;
  width:36px;
  height:30px;
  display:flex;
}
#u2814 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2814_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2815_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2815 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:95px;
  width:112px;
  height:30px;
  display:flex;
}
#u2815 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2815_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2816_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2816 {
  border-width:0px;
  position:absolute;
  left:148px;
  top:95px;
  width:112px;
  height:30px;
  display:flex;
}
#u2816 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2816_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2817_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:30px;
}
#u2817 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:95px;
  width:122px;
  height:30px;
  display:flex;
}
#u2817 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2817_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2818_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:30px;
}
#u2818 {
  border-width:0px;
  position:absolute;
  left:382px;
  top:95px;
  width:143px;
  height:30px;
  display:flex;
}
#u2818 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2818_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2819_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  height:30px;
}
#u2819 {
  border-width:0px;
  position:absolute;
  left:525px;
  top:95px;
  width:133px;
  height:30px;
  display:flex;
}
#u2819 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2819_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2820_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:30px;
}
#u2820 {
  border-width:0px;
  position:absolute;
  left:658px;
  top:95px;
  width:90px;
  height:30px;
  display:flex;
}
#u2820 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2820_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2821_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
}
#u2821 {
  border-width:0px;
  position:absolute;
  left:748px;
  top:95px;
  width:119px;
  height:30px;
  display:flex;
}
#u2821 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2821_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2822_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2822 {
  border-width:0px;
  position:absolute;
  left:867px;
  top:95px;
  width:112px;
  height:30px;
  display:flex;
}
#u2822 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2822_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2823_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u2823 {
  border-width:0px;
  position:absolute;
  left:979px;
  top:95px;
  width:116px;
  height:30px;
  display:flex;
}
#u2823 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2823_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2824_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:146px;
  height:30px;
}
#u2824 {
  border-width:0px;
  position:absolute;
  left:1095px;
  top:95px;
  width:146px;
  height:30px;
  display:flex;
}
#u2824 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2824_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2825_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:30px;
}
#u2825 {
  border-width:0px;
  position:absolute;
  left:1241px;
  top:95px;
  width:97px;
  height:30px;
  display:flex;
}
#u2825 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2825_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2826_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2826 {
  border-width:0px;
  position:absolute;
  left:1338px;
  top:95px;
  width:112px;
  height:30px;
  display:flex;
}
#u2826 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2826_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2827_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2827 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:95px;
  width:112px;
  height:30px;
  display:flex;
}
#u2827 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2827_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2828_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u2828 {
  border-width:0px;
  position:absolute;
  left:1562px;
  top:95px;
  width:139px;
  height:30px;
  display:flex;
}
#u2828 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2828_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2829_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:30px;
}
#u2829 {
  border-width:0px;
  position:absolute;
  left:1701px;
  top:95px;
  width:96px;
  height:30px;
  display:flex;
}
#u2829 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2829_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2830_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:121px;
  height:30px;
}
#u2830 {
  border-width:0px;
  position:absolute;
  left:1797px;
  top:95px;
  width:121px;
  height:30px;
  display:flex;
}
#u2830 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2830_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2831_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u2831 {
  border-width:0px;
  position:absolute;
  left:1918px;
  top:95px;
  width:118px;
  height:30px;
  display:flex;
}
#u2831 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2831_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2832_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:30px;
}
#u2832 {
  border-width:0px;
  position:absolute;
  left:2036px;
  top:95px;
  width:98px;
  height:30px;
  display:flex;
}
#u2832 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2832_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2833_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:109px;
  height:30px;
}
#u2833 {
  border-width:0px;
  position:absolute;
  left:2134px;
  top:95px;
  width:109px;
  height:30px;
  display:flex;
}
#u2833 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2833_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2834_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2834 {
  border-width:0px;
  position:absolute;
  left:2243px;
  top:95px;
  width:112px;
  height:30px;
  display:flex;
}
#u2834 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2834_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2835_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:32px;
}
#u2835 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:125px;
  width:36px;
  height:32px;
  display:flex;
}
#u2835 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2835_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2836_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:32px;
}
#u2836 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:125px;
  width:112px;
  height:32px;
  display:flex;
}
#u2836 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2836_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2837_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:32px;
}
#u2837 {
  border-width:0px;
  position:absolute;
  left:148px;
  top:125px;
  width:112px;
  height:32px;
  display:flex;
}
#u2837 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2837_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2838_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:32px;
}
#u2838 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:125px;
  width:122px;
  height:32px;
  display:flex;
}
#u2838 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2838_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2839_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:32px;
}
#u2839 {
  border-width:0px;
  position:absolute;
  left:382px;
  top:125px;
  width:143px;
  height:32px;
  display:flex;
}
#u2839 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2839_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2840_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  height:32px;
}
#u2840 {
  border-width:0px;
  position:absolute;
  left:525px;
  top:125px;
  width:133px;
  height:32px;
  display:flex;
}
#u2840 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2840_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2841_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:32px;
}
#u2841 {
  border-width:0px;
  position:absolute;
  left:658px;
  top:125px;
  width:90px;
  height:32px;
  display:flex;
}
#u2841 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2841_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2842_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:32px;
}
#u2842 {
  border-width:0px;
  position:absolute;
  left:748px;
  top:125px;
  width:119px;
  height:32px;
  display:flex;
}
#u2842 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2842_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2843_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:32px;
}
#u2843 {
  border-width:0px;
  position:absolute;
  left:867px;
  top:125px;
  width:112px;
  height:32px;
  display:flex;
}
#u2843 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2843_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2844_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:32px;
}
#u2844 {
  border-width:0px;
  position:absolute;
  left:979px;
  top:125px;
  width:116px;
  height:32px;
  display:flex;
}
#u2844 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2844_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2845_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:146px;
  height:32px;
}
#u2845 {
  border-width:0px;
  position:absolute;
  left:1095px;
  top:125px;
  width:146px;
  height:32px;
  display:flex;
}
#u2845 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2845_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2846_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:32px;
}
#u2846 {
  border-width:0px;
  position:absolute;
  left:1241px;
  top:125px;
  width:97px;
  height:32px;
  display:flex;
}
#u2846 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2846_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2847_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:32px;
}
#u2847 {
  border-width:0px;
  position:absolute;
  left:1338px;
  top:125px;
  width:112px;
  height:32px;
  display:flex;
}
#u2847 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2847_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2848_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:32px;
}
#u2848 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:125px;
  width:112px;
  height:32px;
  display:flex;
}
#u2848 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2848_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2849_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:32px;
}
#u2849 {
  border-width:0px;
  position:absolute;
  left:1562px;
  top:125px;
  width:139px;
  height:32px;
  display:flex;
}
#u2849 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2849_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2850_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:32px;
}
#u2850 {
  border-width:0px;
  position:absolute;
  left:1701px;
  top:125px;
  width:96px;
  height:32px;
  display:flex;
}
#u2850 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2850_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2851_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:121px;
  height:32px;
}
#u2851 {
  border-width:0px;
  position:absolute;
  left:1797px;
  top:125px;
  width:121px;
  height:32px;
  display:flex;
}
#u2851 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2851_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2852_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:32px;
}
#u2852 {
  border-width:0px;
  position:absolute;
  left:1918px;
  top:125px;
  width:118px;
  height:32px;
  display:flex;
}
#u2852 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2852_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2853_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:32px;
}
#u2853 {
  border-width:0px;
  position:absolute;
  left:2036px;
  top:125px;
  width:98px;
  height:32px;
  display:flex;
}
#u2853 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2853_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2854_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:109px;
  height:32px;
}
#u2854 {
  border-width:0px;
  position:absolute;
  left:2134px;
  top:125px;
  width:109px;
  height:32px;
  display:flex;
}
#u2854 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2854_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2855_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:32px;
}
#u2855 {
  border-width:0px;
  position:absolute;
  left:2243px;
  top:125px;
  width:112px;
  height:32px;
  display:flex;
}
#u2855 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2855_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2856_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:30px;
}
#u2856 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:157px;
  width:36px;
  height:30px;
  display:flex;
}
#u2856 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2856_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2857_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2857 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:157px;
  width:112px;
  height:30px;
  display:flex;
}
#u2857 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2857_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2858_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2858 {
  border-width:0px;
  position:absolute;
  left:148px;
  top:157px;
  width:112px;
  height:30px;
  display:flex;
}
#u2858 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2858_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2859_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:30px;
}
#u2859 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:157px;
  width:122px;
  height:30px;
  display:flex;
}
#u2859 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2859_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2860_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:30px;
}
#u2860 {
  border-width:0px;
  position:absolute;
  left:382px;
  top:157px;
  width:143px;
  height:30px;
  display:flex;
}
#u2860 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2860_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2861_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  height:30px;
}
#u2861 {
  border-width:0px;
  position:absolute;
  left:525px;
  top:157px;
  width:133px;
  height:30px;
  display:flex;
}
#u2861 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2861_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2862_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:30px;
}
#u2862 {
  border-width:0px;
  position:absolute;
  left:658px;
  top:157px;
  width:90px;
  height:30px;
  display:flex;
}
#u2862 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2862_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2863_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
}
#u2863 {
  border-width:0px;
  position:absolute;
  left:748px;
  top:157px;
  width:119px;
  height:30px;
  display:flex;
}
#u2863 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2863_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2864_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2864 {
  border-width:0px;
  position:absolute;
  left:867px;
  top:157px;
  width:112px;
  height:30px;
  display:flex;
}
#u2864 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2864_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2865_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u2865 {
  border-width:0px;
  position:absolute;
  left:979px;
  top:157px;
  width:116px;
  height:30px;
  display:flex;
}
#u2865 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2865_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2866_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:146px;
  height:30px;
}
#u2866 {
  border-width:0px;
  position:absolute;
  left:1095px;
  top:157px;
  width:146px;
  height:30px;
  display:flex;
}
#u2866 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2866_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2867_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:30px;
}
#u2867 {
  border-width:0px;
  position:absolute;
  left:1241px;
  top:157px;
  width:97px;
  height:30px;
  display:flex;
}
#u2867 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2867_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2868_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2868 {
  border-width:0px;
  position:absolute;
  left:1338px;
  top:157px;
  width:112px;
  height:30px;
  display:flex;
}
#u2868 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2868_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2869_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2869 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:157px;
  width:112px;
  height:30px;
  display:flex;
}
#u2869 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2869_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2870_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u2870 {
  border-width:0px;
  position:absolute;
  left:1562px;
  top:157px;
  width:139px;
  height:30px;
  display:flex;
}
#u2870 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2870_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2871_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:30px;
}
#u2871 {
  border-width:0px;
  position:absolute;
  left:1701px;
  top:157px;
  width:96px;
  height:30px;
  display:flex;
}
#u2871 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2871_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2872_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:121px;
  height:30px;
}
#u2872 {
  border-width:0px;
  position:absolute;
  left:1797px;
  top:157px;
  width:121px;
  height:30px;
  display:flex;
}
#u2872 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2872_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2873_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u2873 {
  border-width:0px;
  position:absolute;
  left:1918px;
  top:157px;
  width:118px;
  height:30px;
  display:flex;
}
#u2873 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2873_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2874_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:30px;
}
#u2874 {
  border-width:0px;
  position:absolute;
  left:2036px;
  top:157px;
  width:98px;
  height:30px;
  display:flex;
}
#u2874 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2874_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2875_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:109px;
  height:30px;
}
#u2875 {
  border-width:0px;
  position:absolute;
  left:2134px;
  top:157px;
  width:109px;
  height:30px;
  display:flex;
}
#u2875 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2875_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2876_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2876 {
  border-width:0px;
  position:absolute;
  left:2243px;
  top:157px;
  width:112px;
  height:30px;
  display:flex;
}
#u2876 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2876_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2877_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:30px;
}
#u2877 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:187px;
  width:36px;
  height:30px;
  display:flex;
}
#u2877 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2877_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2878_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2878 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:187px;
  width:112px;
  height:30px;
  display:flex;
}
#u2878 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2878_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2879_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2879 {
  border-width:0px;
  position:absolute;
  left:148px;
  top:187px;
  width:112px;
  height:30px;
  display:flex;
}
#u2879 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2879_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2880_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:30px;
}
#u2880 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:187px;
  width:122px;
  height:30px;
  display:flex;
}
#u2880 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2880_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2881_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:30px;
}
#u2881 {
  border-width:0px;
  position:absolute;
  left:382px;
  top:187px;
  width:143px;
  height:30px;
  display:flex;
}
#u2881 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2881_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2882_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  height:30px;
}
#u2882 {
  border-width:0px;
  position:absolute;
  left:525px;
  top:187px;
  width:133px;
  height:30px;
  display:flex;
}
#u2882 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2882_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2883_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:30px;
}
#u2883 {
  border-width:0px;
  position:absolute;
  left:658px;
  top:187px;
  width:90px;
  height:30px;
  display:flex;
}
#u2883 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2883_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2884_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
}
#u2884 {
  border-width:0px;
  position:absolute;
  left:748px;
  top:187px;
  width:119px;
  height:30px;
  display:flex;
}
#u2884 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2884_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2885_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2885 {
  border-width:0px;
  position:absolute;
  left:867px;
  top:187px;
  width:112px;
  height:30px;
  display:flex;
}
#u2885 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2885_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2886_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u2886 {
  border-width:0px;
  position:absolute;
  left:979px;
  top:187px;
  width:116px;
  height:30px;
  display:flex;
}
#u2886 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2886_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2887_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:146px;
  height:30px;
}
#u2887 {
  border-width:0px;
  position:absolute;
  left:1095px;
  top:187px;
  width:146px;
  height:30px;
  display:flex;
}
#u2887 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2887_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2888_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:30px;
}
#u2888 {
  border-width:0px;
  position:absolute;
  left:1241px;
  top:187px;
  width:97px;
  height:30px;
  display:flex;
}
#u2888 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2888_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2889_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2889 {
  border-width:0px;
  position:absolute;
  left:1338px;
  top:187px;
  width:112px;
  height:30px;
  display:flex;
}
#u2889 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2889_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2890_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2890 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:187px;
  width:112px;
  height:30px;
  display:flex;
}
#u2890 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2890_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2891_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u2891 {
  border-width:0px;
  position:absolute;
  left:1562px;
  top:187px;
  width:139px;
  height:30px;
  display:flex;
}
#u2891 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2891_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2892_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:30px;
}
#u2892 {
  border-width:0px;
  position:absolute;
  left:1701px;
  top:187px;
  width:96px;
  height:30px;
  display:flex;
}
#u2892 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2892_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2893_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:121px;
  height:30px;
}
#u2893 {
  border-width:0px;
  position:absolute;
  left:1797px;
  top:187px;
  width:121px;
  height:30px;
  display:flex;
}
#u2893 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2893_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2894_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u2894 {
  border-width:0px;
  position:absolute;
  left:1918px;
  top:187px;
  width:118px;
  height:30px;
  display:flex;
}
#u2894 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2894_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2895_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:30px;
}
#u2895 {
  border-width:0px;
  position:absolute;
  left:2036px;
  top:187px;
  width:98px;
  height:30px;
  display:flex;
}
#u2895 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2895_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2896_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:109px;
  height:30px;
}
#u2896 {
  border-width:0px;
  position:absolute;
  left:2134px;
  top:187px;
  width:109px;
  height:30px;
  display:flex;
}
#u2896 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2896_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2897_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2897 {
  border-width:0px;
  position:absolute;
  left:2243px;
  top:187px;
  width:112px;
  height:30px;
  display:flex;
}
#u2897 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2897_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2898_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:30px;
}
#u2898 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:217px;
  width:36px;
  height:30px;
  display:flex;
}
#u2898 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2898_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2899_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2899 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:217px;
  width:112px;
  height:30px;
  display:flex;
}
#u2899 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2899_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2900_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2900 {
  border-width:0px;
  position:absolute;
  left:148px;
  top:217px;
  width:112px;
  height:30px;
  display:flex;
}
#u2900 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2900_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2901_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:30px;
}
#u2901 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:217px;
  width:122px;
  height:30px;
  display:flex;
}
#u2901 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2901_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2902_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:30px;
}
#u2902 {
  border-width:0px;
  position:absolute;
  left:382px;
  top:217px;
  width:143px;
  height:30px;
  display:flex;
}
#u2902 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2902_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2903_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  height:30px;
}
#u2903 {
  border-width:0px;
  position:absolute;
  left:525px;
  top:217px;
  width:133px;
  height:30px;
  display:flex;
}
#u2903 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2903_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2904_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:30px;
}
#u2904 {
  border-width:0px;
  position:absolute;
  left:658px;
  top:217px;
  width:90px;
  height:30px;
  display:flex;
}
#u2904 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2904_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2905_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
}
#u2905 {
  border-width:0px;
  position:absolute;
  left:748px;
  top:217px;
  width:119px;
  height:30px;
  display:flex;
}
#u2905 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2905_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2906_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2906 {
  border-width:0px;
  position:absolute;
  left:867px;
  top:217px;
  width:112px;
  height:30px;
  display:flex;
}
#u2906 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2906_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2907_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u2907 {
  border-width:0px;
  position:absolute;
  left:979px;
  top:217px;
  width:116px;
  height:30px;
  display:flex;
}
#u2907 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2907_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2908_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:146px;
  height:30px;
}
#u2908 {
  border-width:0px;
  position:absolute;
  left:1095px;
  top:217px;
  width:146px;
  height:30px;
  display:flex;
}
#u2908 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2908_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2909_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:30px;
}
#u2909 {
  border-width:0px;
  position:absolute;
  left:1241px;
  top:217px;
  width:97px;
  height:30px;
  display:flex;
}
#u2909 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2909_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2910_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2910 {
  border-width:0px;
  position:absolute;
  left:1338px;
  top:217px;
  width:112px;
  height:30px;
  display:flex;
}
#u2910 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2910_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2911_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2911 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:217px;
  width:112px;
  height:30px;
  display:flex;
}
#u2911 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2911_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2912_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u2912 {
  border-width:0px;
  position:absolute;
  left:1562px;
  top:217px;
  width:139px;
  height:30px;
  display:flex;
}
#u2912 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2912_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2913_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:30px;
}
#u2913 {
  border-width:0px;
  position:absolute;
  left:1701px;
  top:217px;
  width:96px;
  height:30px;
  display:flex;
}
#u2913 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2913_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2914_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:121px;
  height:30px;
}
#u2914 {
  border-width:0px;
  position:absolute;
  left:1797px;
  top:217px;
  width:121px;
  height:30px;
  display:flex;
}
#u2914 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2914_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2915_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u2915 {
  border-width:0px;
  position:absolute;
  left:1918px;
  top:217px;
  width:118px;
  height:30px;
  display:flex;
}
#u2915 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2915_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2916_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:30px;
}
#u2916 {
  border-width:0px;
  position:absolute;
  left:2036px;
  top:217px;
  width:98px;
  height:30px;
  display:flex;
}
#u2916 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2916_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2917_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:109px;
  height:30px;
}
#u2917 {
  border-width:0px;
  position:absolute;
  left:2134px;
  top:217px;
  width:109px;
  height:30px;
  display:flex;
}
#u2917 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2917_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2918_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2918 {
  border-width:0px;
  position:absolute;
  left:2243px;
  top:217px;
  width:112px;
  height:30px;
  display:flex;
}
#u2918 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2918_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2919_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:30px;
}
#u2919 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:247px;
  width:36px;
  height:30px;
  display:flex;
}
#u2919 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2919_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2920_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2920 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:247px;
  width:112px;
  height:30px;
  display:flex;
}
#u2920 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2920_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2921_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2921 {
  border-width:0px;
  position:absolute;
  left:148px;
  top:247px;
  width:112px;
  height:30px;
  display:flex;
}
#u2921 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2921_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2922_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:30px;
}
#u2922 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:247px;
  width:122px;
  height:30px;
  display:flex;
}
#u2922 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2922_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2923_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:30px;
}
#u2923 {
  border-width:0px;
  position:absolute;
  left:382px;
  top:247px;
  width:143px;
  height:30px;
  display:flex;
}
#u2923 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2923_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2924_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  height:30px;
}
#u2924 {
  border-width:0px;
  position:absolute;
  left:525px;
  top:247px;
  width:133px;
  height:30px;
  display:flex;
}
#u2924 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2924_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2925_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:30px;
}
#u2925 {
  border-width:0px;
  position:absolute;
  left:658px;
  top:247px;
  width:90px;
  height:30px;
  display:flex;
}
#u2925 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2925_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2926_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
}
#u2926 {
  border-width:0px;
  position:absolute;
  left:748px;
  top:247px;
  width:119px;
  height:30px;
  display:flex;
}
#u2926 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2926_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2927_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2927 {
  border-width:0px;
  position:absolute;
  left:867px;
  top:247px;
  width:112px;
  height:30px;
  display:flex;
}
#u2927 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2927_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2928_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u2928 {
  border-width:0px;
  position:absolute;
  left:979px;
  top:247px;
  width:116px;
  height:30px;
  display:flex;
}
#u2928 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2928_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2929_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:146px;
  height:30px;
}
#u2929 {
  border-width:0px;
  position:absolute;
  left:1095px;
  top:247px;
  width:146px;
  height:30px;
  display:flex;
}
#u2929 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2929_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2930_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:30px;
}
#u2930 {
  border-width:0px;
  position:absolute;
  left:1241px;
  top:247px;
  width:97px;
  height:30px;
  display:flex;
}
#u2930 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2930_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2931_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2931 {
  border-width:0px;
  position:absolute;
  left:1338px;
  top:247px;
  width:112px;
  height:30px;
  display:flex;
}
#u2931 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2931_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2932_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2932 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:247px;
  width:112px;
  height:30px;
  display:flex;
}
#u2932 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2932_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2933_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u2933 {
  border-width:0px;
  position:absolute;
  left:1562px;
  top:247px;
  width:139px;
  height:30px;
  display:flex;
}
#u2933 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2933_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2934_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:30px;
}
#u2934 {
  border-width:0px;
  position:absolute;
  left:1701px;
  top:247px;
  width:96px;
  height:30px;
  display:flex;
}
#u2934 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2934_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2935_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:121px;
  height:30px;
}
#u2935 {
  border-width:0px;
  position:absolute;
  left:1797px;
  top:247px;
  width:121px;
  height:30px;
  display:flex;
}
#u2935 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2935_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2936_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u2936 {
  border-width:0px;
  position:absolute;
  left:1918px;
  top:247px;
  width:118px;
  height:30px;
  display:flex;
}
#u2936 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2936_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2937_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:30px;
}
#u2937 {
  border-width:0px;
  position:absolute;
  left:2036px;
  top:247px;
  width:98px;
  height:30px;
  display:flex;
}
#u2937 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2937_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2938_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:109px;
  height:30px;
}
#u2938 {
  border-width:0px;
  position:absolute;
  left:2134px;
  top:247px;
  width:109px;
  height:30px;
  display:flex;
}
#u2938 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2938_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2939_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2939 {
  border-width:0px;
  position:absolute;
  left:2243px;
  top:247px;
  width:112px;
  height:30px;
  display:flex;
}
#u2939 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2939_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2940_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:30px;
}
#u2940 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:277px;
  width:36px;
  height:30px;
  display:flex;
}
#u2940 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2940_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2941_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2941 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:277px;
  width:112px;
  height:30px;
  display:flex;
}
#u2941 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2941_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2942_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2942 {
  border-width:0px;
  position:absolute;
  left:148px;
  top:277px;
  width:112px;
  height:30px;
  display:flex;
}
#u2942 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2942_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2943_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:30px;
}
#u2943 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:277px;
  width:122px;
  height:30px;
  display:flex;
}
#u2943 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2943_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2944_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:30px;
}
#u2944 {
  border-width:0px;
  position:absolute;
  left:382px;
  top:277px;
  width:143px;
  height:30px;
  display:flex;
}
#u2944 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2944_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2945_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  height:30px;
}
#u2945 {
  border-width:0px;
  position:absolute;
  left:525px;
  top:277px;
  width:133px;
  height:30px;
  display:flex;
}
#u2945 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2945_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2946_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:30px;
}
#u2946 {
  border-width:0px;
  position:absolute;
  left:658px;
  top:277px;
  width:90px;
  height:30px;
  display:flex;
}
#u2946 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2946_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2947_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
}
#u2947 {
  border-width:0px;
  position:absolute;
  left:748px;
  top:277px;
  width:119px;
  height:30px;
  display:flex;
}
#u2947 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2947_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2948_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2948 {
  border-width:0px;
  position:absolute;
  left:867px;
  top:277px;
  width:112px;
  height:30px;
  display:flex;
}
#u2948 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2948_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2949_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u2949 {
  border-width:0px;
  position:absolute;
  left:979px;
  top:277px;
  width:116px;
  height:30px;
  display:flex;
}
#u2949 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2949_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2950_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:146px;
  height:30px;
}
#u2950 {
  border-width:0px;
  position:absolute;
  left:1095px;
  top:277px;
  width:146px;
  height:30px;
  display:flex;
}
#u2950 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2950_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2951_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:30px;
}
#u2951 {
  border-width:0px;
  position:absolute;
  left:1241px;
  top:277px;
  width:97px;
  height:30px;
  display:flex;
}
#u2951 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2951_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2952_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2952 {
  border-width:0px;
  position:absolute;
  left:1338px;
  top:277px;
  width:112px;
  height:30px;
  display:flex;
}
#u2952 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2952_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2953_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2953 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:277px;
  width:112px;
  height:30px;
  display:flex;
}
#u2953 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2953_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2954_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u2954 {
  border-width:0px;
  position:absolute;
  left:1562px;
  top:277px;
  width:139px;
  height:30px;
  display:flex;
}
#u2954 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2954_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2955_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:30px;
}
#u2955 {
  border-width:0px;
  position:absolute;
  left:1701px;
  top:277px;
  width:96px;
  height:30px;
  display:flex;
}
#u2955 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2955_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2956_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:121px;
  height:30px;
}
#u2956 {
  border-width:0px;
  position:absolute;
  left:1797px;
  top:277px;
  width:121px;
  height:30px;
  display:flex;
}
#u2956 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2956_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2957_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u2957 {
  border-width:0px;
  position:absolute;
  left:1918px;
  top:277px;
  width:118px;
  height:30px;
  display:flex;
}
#u2957 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2957_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2958_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:30px;
}
#u2958 {
  border-width:0px;
  position:absolute;
  left:2036px;
  top:277px;
  width:98px;
  height:30px;
  display:flex;
}
#u2958 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2958_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2959_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:109px;
  height:30px;
}
#u2959 {
  border-width:0px;
  position:absolute;
  left:2134px;
  top:277px;
  width:109px;
  height:30px;
  display:flex;
}
#u2959 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2959_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2960_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2960 {
  border-width:0px;
  position:absolute;
  left:2243px;
  top:277px;
  width:112px;
  height:30px;
  display:flex;
}
#u2960 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2960_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2961_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:30px;
}
#u2961 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:307px;
  width:36px;
  height:30px;
  display:flex;
}
#u2961 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2961_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2962_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2962 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:307px;
  width:112px;
  height:30px;
  display:flex;
}
#u2962 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2962_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2963_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2963 {
  border-width:0px;
  position:absolute;
  left:148px;
  top:307px;
  width:112px;
  height:30px;
  display:flex;
}
#u2963 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2963_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2964_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:30px;
}
#u2964 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:307px;
  width:122px;
  height:30px;
  display:flex;
}
#u2964 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2964_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2965_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:30px;
}
#u2965 {
  border-width:0px;
  position:absolute;
  left:382px;
  top:307px;
  width:143px;
  height:30px;
  display:flex;
}
#u2965 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2965_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2966_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  height:30px;
}
#u2966 {
  border-width:0px;
  position:absolute;
  left:525px;
  top:307px;
  width:133px;
  height:30px;
  display:flex;
}
#u2966 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2966_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2967_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:30px;
}
#u2967 {
  border-width:0px;
  position:absolute;
  left:658px;
  top:307px;
  width:90px;
  height:30px;
  display:flex;
}
#u2967 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2967_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2968_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
}
#u2968 {
  border-width:0px;
  position:absolute;
  left:748px;
  top:307px;
  width:119px;
  height:30px;
  display:flex;
}
#u2968 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2968_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2969_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2969 {
  border-width:0px;
  position:absolute;
  left:867px;
  top:307px;
  width:112px;
  height:30px;
  display:flex;
}
#u2969 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2969_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2970_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u2970 {
  border-width:0px;
  position:absolute;
  left:979px;
  top:307px;
  width:116px;
  height:30px;
  display:flex;
}
#u2970 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2970_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2971_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:146px;
  height:30px;
}
#u2971 {
  border-width:0px;
  position:absolute;
  left:1095px;
  top:307px;
  width:146px;
  height:30px;
  display:flex;
}
#u2971 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2971_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2972_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:30px;
}
#u2972 {
  border-width:0px;
  position:absolute;
  left:1241px;
  top:307px;
  width:97px;
  height:30px;
  display:flex;
}
#u2972 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2972_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2973_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2973 {
  border-width:0px;
  position:absolute;
  left:1338px;
  top:307px;
  width:112px;
  height:30px;
  display:flex;
}
#u2973 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2973_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2974_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2974 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:307px;
  width:112px;
  height:30px;
  display:flex;
}
#u2974 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2974_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2975_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u2975 {
  border-width:0px;
  position:absolute;
  left:1562px;
  top:307px;
  width:139px;
  height:30px;
  display:flex;
}
#u2975 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2975_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2976_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:30px;
}
#u2976 {
  border-width:0px;
  position:absolute;
  left:1701px;
  top:307px;
  width:96px;
  height:30px;
  display:flex;
}
#u2976 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2976_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2977_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:121px;
  height:30px;
}
#u2977 {
  border-width:0px;
  position:absolute;
  left:1797px;
  top:307px;
  width:121px;
  height:30px;
  display:flex;
}
#u2977 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2977_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2978_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u2978 {
  border-width:0px;
  position:absolute;
  left:1918px;
  top:307px;
  width:118px;
  height:30px;
  display:flex;
}
#u2978 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2978_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2979_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:30px;
}
#u2979 {
  border-width:0px;
  position:absolute;
  left:2036px;
  top:307px;
  width:98px;
  height:30px;
  display:flex;
}
#u2979 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2979_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2980_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:109px;
  height:30px;
}
#u2980 {
  border-width:0px;
  position:absolute;
  left:2134px;
  top:307px;
  width:109px;
  height:30px;
  display:flex;
}
#u2980 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2980_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2981_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:30px;
}
#u2981 {
  border-width:0px;
  position:absolute;
  left:2243px;
  top:307px;
  width:112px;
  height:30px;
  display:flex;
}
#u2981 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2981_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2982_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2982 {
  border-width:0px;
  position:absolute;
  left:50px;
  top:619px;
  width:57px;
  height:16px;
  display:flex;
}
#u2982 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2982_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2983_input {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:22px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2983_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:22px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2983_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2983 {
  border-width:0px;
  position:absolute;
  left:117px;
  top:613px;
  width:80px;
  height:22px;
  display:flex;
}
#u2983 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2983_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:22px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2983.disabled {
}
.u2983_input_option {
}
#u2984_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2984 {
  border-width:0px;
  position:absolute;
  left:207px;
  top:619px;
  width:168px;
  height:16px;
  display:flex;
}
#u2984 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2984_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2985_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2985 {
  border-width:0px;
  position:absolute;
  left:385px;
  top:619px;
  width:28px;
  height:16px;
  display:flex;
}
#u2985 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2985_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2986_input {
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:22px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2986_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:22px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2986_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u2986 {
  border-width:0px;
  position:absolute;
  left:418px;
  top:613px;
  width:30px;
  height:22px;
  display:flex;
  text-align:center;
}
#u2986 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2986_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:22px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u2986.disabled {
}
#u2987_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2987 {
  border-width:0px;
  position:absolute;
  left:453px;
  top:619px;
  width:14px;
  height:16px;
  display:flex;
}
#u2987 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2987_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2988_input {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2988_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2988_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2988 {
  border-width:0px;
  position:absolute;
  left:565px;
  top:58px;
  width:120px;
  height:24px;
  display:flex;
}
#u2988 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2988_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2988.disabled {
}
#u2989_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2989 {
  border-width:0px;
  position:absolute;
  left:274px;
  top:62px;
  width:56px;
  height:16px;
  display:flex;
}
#u2989 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2989_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2990_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2990 {
  border-width:0px;
  position:absolute;
  left:70px;
  top:119px;
  width:56px;
  height:16px;
  display:flex;
}
#u2990 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2990_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2991_input {
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2991_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2991_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2991 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:115px;
  width:100px;
  height:24px;
  display:flex;
}
#u2991 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2991_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2991.disabled {
}
#u2992_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2992 {
  border-width:0px;
  position:absolute;
  left:239px;
  top:119px;
  width:14px;
  height:16px;
  display:flex;
}
#u2992 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2992_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2993_input {
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2993_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2993_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2993 {
  border-width:0px;
  position:absolute;
  left:256px;
  top:115px;
  width:100px;
  height:24px;
  display:flex;
}
#u2993 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2993_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2993.disabled {
}
#u2994_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2994 {
  border-width:0px;
  position:absolute;
  left:1160px;
  top:62px;
  width:56px;
  height:16px;
  display:flex;
}
#u2994 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2994_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2995_input {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2995_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2995_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2995 {
  border-width:0px;
  position:absolute;
  left:1221px;
  top:58px;
  width:120px;
  height:24px;
  display:flex;
}
#u2995 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2995_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2995.disabled {
}
#u2996_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#02A7F0;
}
#u2996 {
  border-width:0px;
  position:absolute;
  left:2312px;
  top:303px;
  width:28px;
  height:16px;
  display:flex;
  color:#02A7F0;
}
#u2996 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2996_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2997_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#02A7F0;
}
#u2997 {
  border-width:0px;
  position:absolute;
  left:2360px;
  top:303px;
  width:28px;
  height:16px;
  display:flex;
  color:#02A7F0;
}
#u2997 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2997_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2998_input {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2998_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2998_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2998 {
  border-width:0px;
  position:absolute;
  left:340px;
  top:58px;
  width:120px;
  height:24px;
  display:flex;
}
#u2998 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2998_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2998.disabled {
}
#u2999_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2999 {
  border-width:0px;
  position:absolute;
  left:434px;
  top:119px;
  width:70px;
  height:16px;
  display:flex;
}
#u2999 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2999_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3000_input {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3000_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3000_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3000 {
  border-width:0px;
  position:absolute;
  left:509px;
  top:115px;
  width:120px;
  height:24px;
  display:flex;
}
#u3000 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3000_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3000.disabled {
}
#u3001_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1300px;
  height:189px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  font-size:14px;
  line-height:20px;
}
#u3001 {
  border-width:0px;
  position:absolute;
  left:50px;
  top:662px;
  width:1300px;
  height:189px;
  display:flex;
  font-size:14px;
  line-height:20px;
}
#u3001 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u3001_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3002_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1174px;
  height:76px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#000000;
  line-height:19px;
}
#u3002 {
  border-width:0px;
  position:absolute;
  left:69px;
  top:670px;
  width:1174px;
  height:76px;
  display:flex;
  font-size:15px;
  color:#000000;
  line-height:19px;
}
#u3002 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3002_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3003_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3003 {
  border-width:0px;
  position:absolute;
  left:679px;
  top:119px;
  width:112px;
  height:16px;
  display:flex;
}
#u3003 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3003_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3004_input {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3004_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3004_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3004 {
  border-width:0px;
  position:absolute;
  left:801px;
  top:115px;
  width:120px;
  height:24px;
  display:flex;
}
#u3004 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3004_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3004.disabled {
}
