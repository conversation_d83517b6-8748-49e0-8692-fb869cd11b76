-- t_contract表结构修改：添加cancel_reason字段

-- 1. 添加cancel_reason字段
ALTER TABLE `t_contract` ADD COLUMN `cancel_reason` varchar(500) DEFAULT NULL COMMENT '作废原因' AFTER `expiry_date`;

-- 2. 修改后的完整表结构（供参考）
/*
CREATE TABLE `t_contract` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `contract_no` varchar(100) NOT NULL COMMENT '合同编号（唯一）',
  `out_customer_company_id` int(4) NOT NULL COMMENT '我司主体',
  `customer_company_id` bigint(20) NOT NULL COMMENT '客户主体ID',
  `contract_status` int(4) NOT NULL DEFAULT '1' COMMENT '合同状态：1-待生效，2-生效中，3-已到期，4-已作废',
  `archive_status` int(4) NOT NULL DEFAULT '1' COMMENT '归档状态：1-待归档，2-已归档',
  `effective_date` date NOT NULL COMMENT '合同生效日期',
  `expiry_date` date NOT NULL COMMENT '合同失效日期',
  `cancel_reason` varchar(500) DEFAULT NULL COMMENT '作废原因',
  `remark` varchar(1000) DEFAULT NULL COMMENT '备注信息',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_modified_by` varchar(50) DEFAULT NULL COMMENT '最后修改人',
  `last_modified_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `valid` int(4) NOT NULL DEFAULT '1' COMMENT '有效性：1-有效，0-无效',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合同管理表';
*/

-- 字段说明：
-- cancel_reason: 作废原因，varchar(500)，允许为空
-- 位置：在expiry_date字段之后，remark字段之前
