package com.yi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 启用状态枚举
 */
@Getter
@AllArgsConstructor
public enum EnabledStatusEnum {

    /**
     * 禁用
     */
    DISABLED(0, "禁用"),

    /**
     * 启用
     */
    ENABLED(1, "启用");

    /**
     * 状态键
     */
    private final Integer key;

    /**
     * 状态值
     */
    private final String value;

    /**
     * 根据键获取枚举
     *
     * @param key 键
     * @return 枚举
     */
    public static EnabledStatusEnum getByKey(Integer key) {
        if (key == null) {
            return null;
        }
        for (EnabledStatusEnum status : EnabledStatusEnum.values()) {
            if (status.getKey().equals(key)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据键获取值
     *
     * @param key 键
     * @return 值
     */
    public static String getValueByKey(Integer key) {
        EnabledStatusEnum status = getByKey(key);
        return status != null ? status.getValue() : null;
    }

    /**
     * 判断是否启用
     *
     * @param key 键
     * @return 是否启用
     */
    public static boolean isEnabled(Integer key) {
        return ENABLED.getKey().equals(key);
    }

    /**
     * 判断是否禁用
     *
     * @param key 键
     * @return 是否禁用
     */
    public static boolean isDisabled(Integer key) {
        return DISABLED.getKey().equals(key);
    }
}
