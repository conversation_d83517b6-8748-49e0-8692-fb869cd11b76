package com.yi.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yi.entity.SysUserRole;
import com.yi.mapper.SysUserRoleMapper;
import com.yi.service.SysUserRoleService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户角色关联表 服务实现类
 */
@Service
public class SysUserRoleServiceImpl extends ServiceImpl<SysUserRoleMapper, SysUserRole> implements SysUserRoleService {

    @Override
    public int deleteByUserId(Long userId) {
        return baseMapper.deleteByUserId(userId);
    }

    @Override
    public int deleteByRoleId(Long roleId) {
        return baseMapper.deleteByRoleId(roleId);
    }

    @Override
    public int batchInsert(List<SysUserRole> userRoles) {
        return baseMapper.batchInsert(userRoles);
    }

    @Override
    public List<Long> selectRoleIdsByUserId(Long userId) {
        return baseMapper.selectRoleIdsByUserId(userId);
    }

    @Override
    public List<Long> selectUserIdsByRoleId(Long roleId) {
        return baseMapper.selectUserIdsByRoleId(roleId);
    }

    @Override
    public boolean existsByUserIdAndRoleId(Long userId, Long roleId) {
        return baseMapper.existsByUserIdAndRoleId(userId, roleId);
    }
}
