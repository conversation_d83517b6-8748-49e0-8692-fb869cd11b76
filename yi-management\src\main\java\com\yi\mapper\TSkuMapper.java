package com.yi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yi.entity.TSku;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * SKU表 Mapper 接口
 */
@Mapper
public interface TSkuMapper extends BaseMapper<TSku> {

    /**
     * 分页查询SKU列表
     *
     * @param page 分页参数
     * @param firstCategory 一级类目
     * @param secondCategory 二级类目
     * @param thirdCategory 三级类目
     * @param enabled 启用状态
     * @return 分页结果
     */
    IPage<TSku> selectSkuPage(Page<TSku> page,
                              @Param("firstCategory") Integer firstCategory,
                              @Param("secondCategory") Integer secondCategory,
                              @Param("thirdCategory") Integer thirdCategory,
                              @Param("enabled") Integer enabled);

    /**
     * 根据类目查询SKU
     *
     * @param firstCategory 一级类目
     * @param secondCategory 二级类目
     * @param thirdCategory 三级类目
     * @return SKU列表
     */
    List<TSku> selectByCategory(@Param("firstCategory") Integer firstCategory,
                                @Param("secondCategory") Integer secondCategory,
                                @Param("thirdCategory") Integer thirdCategory);

    /**
     * 根据规格查询SKU
     *
     * @param length 长度
     * @param width 宽度
     * @param height 高度
     * @return SKU列表
     */
    List<TSku> selectBySize(@Param("length") Integer length,
                            @Param("width") Integer width,
                            @Param("height") Integer height);
}
