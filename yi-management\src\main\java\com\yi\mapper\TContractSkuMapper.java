package com.yi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yi.entity.TContractSku;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 合同SKU明细表 Mapper 接口
 */
@Mapper
public interface TContractSkuMapper extends BaseMapper<TContractSku> {

    /**
     * 根据合同ID查询SKU明细列表
     *
     * @param contractId 合同ID
     * @return SKU明细列表
     */
    List<TContractSku> selectByContractId(@Param("contractId") Long contractId);

    /**
     * 根据合同ID和一级类目查询SKU明细
     *
     * @param contractId 合同ID
     * @param firstCategory 一级类目
     * @return SKU明细
     */
    TContractSku selectByContractIdAndFirstCategory(@Param("contractId") Long contractId, 
                                                   @Param("firstCategory") Integer firstCategory);

    /**
     * 根据合同ID删除SKU明细
     *
     * @param contractId 合同ID
     * @return 影响行数
     */
    int deleteByContractId(@Param("contractId") Long contractId);
}
