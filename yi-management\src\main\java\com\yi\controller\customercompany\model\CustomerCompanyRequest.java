package com.yi.controller.customercompany.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 客户公司请求
 */
@Data
@ApiModel(value = "CustomerCompanyRequest", description = "客户公司请求")
public class CustomerCompanyRequest {

    @ApiModelProperty(value = "主键ID")
    private String id;

    @NotBlank(message = "客户类型不能为空")
    @ApiModelProperty(value = "客户类型：1-合约客户，2-非合约客户", required = true)
    private String customerCompanyType;

    @NotBlank(message = "公司名称不能为空")
    @ApiModelProperty(value = "公司名称", required = true)
    private String companyName;

    @ApiModelProperty(value = "省份ID")
    private String provinceId;

    @ApiModelProperty(value = "省份名称")
    private String provinceName;

    @ApiModelProperty(value = "城市ID")
    private String cityId;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "区县ID")
    private String areaId;

    @ApiModelProperty(value = "区县名称")
    private String areaName;

    @ApiModelProperty(value = "详细地址")
    private String detailedAddress;

    @ApiModelProperty(value = "联系人")
    private String contactPerson;

    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    @ApiModelProperty(value = "开票类型：1-后补，2-与客户公司一致")
    private String invoiceType;

    @ApiModelProperty(value = "开票公司名称")
    private String invoiceCompanyName;

    @ApiModelProperty(value = "税号")
    private String taxNumber;

    @ApiModelProperty(value = "开票联系人")
    private String invoiceContactPerson;

    @ApiModelProperty(value = "开票手机号")
    private String invoiceMobile;

    @ApiModelProperty(value = "开票邮箱")
    private String invoiceEmail;

    @ApiModelProperty(value = "银行名称")
    private String bankName;

    @ApiModelProperty(value = "银行账号")
    private String bankAccount;

    @ApiModelProperty(value = "备注")
    private String remark;
}
