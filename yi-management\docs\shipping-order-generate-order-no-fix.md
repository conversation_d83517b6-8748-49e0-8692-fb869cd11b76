# 发运订单号生成方法修复说明

## 概述

修复了`TShippingOrderService.generateOrderNo()`方法中的编译错误和逻辑问题，确保订单号能够正确生成。

## 问题分析

### 修复前的问题

1. **FormatUtils方法调用错误**：
   ```java
   // 错误：FormatUtils没有支持自定义格式的formatDate方法
   String dateStr = FormatUtils.formatDate(java.time.LocalDate.now(), "yyyyMMdd");
   ```

2. **String.format格式化错误**：
   ```java
   // 错误：格式化字符串不完整，缺少'd'
   String sequence = String.format("%04", maxSeq + 1);
   ```

3. **依赖方法不存在**：
   ```java
   // 错误：baseMapper中没有getMaxSequenceByPrefix方法
   int maxSeq = this.baseMapper.getMaxSequenceByPrefix(prefix);
   ```

4. **订单号前缀不一致**：
   ```java
   // 不一致：注释说是SO，但代码用的是XSD
   String prefix = "XSD" + dateStr;  // 应该是SO
   ```

## 修复方案

### 1. 修复日期格式化

**修复前**：
```java
String dateStr = FormatUtils.formatDate(java.time.LocalDate.now(), "yyyyMMdd");
```

**修复后**：
```java
LocalDate now = LocalDate.now();
String dateStr = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
```

### 2. 修复字符串格式化

**修复前**：
```java
String sequence = String.format("%04", maxSeq + 1);  // 缺少'd'
```

**修复后**：
```java
String sequence = String.format("%04d", maxSeq + 1);  // 4位数字，前面补0
```

### 3. 实现序号查询逻辑

**修复前**：
```java
int maxSeq = this.baseMapper.getMaxSequenceByPrefix(prefix);  // 方法不存在
```

**修复后**：
```java
private int getMaxSequenceOfDay(String dateStr) {
    String prefix = "XSD" + dateStr;
    
    // 查询当天以该前缀开头的订单号
    LambdaQueryWrapper<TShippingOrder> wrapper = new LambdaQueryWrapper<>();
    wrapper.likeRight(TShippingOrder::getOrderNo, prefix)
            .eq(TShippingOrder::getValid, 1)
            .orderByDesc(TShippingOrder::getOrderNo)
            .last("LIMIT 1");
    
    TShippingOrder lastOrder = this.getOne(wrapper);
    if (lastOrder == null) {
        return 0;
    }
    
    // 从订单号中提取序号部分
    String orderNo = lastOrder.getOrderNo();
    if (orderNo.length() >= prefix.length() + 6) {
        String seqStr = orderNo.substring(prefix.length());
        try {
            return Integer.parseInt(seqStr);
        } catch (NumberFormatException e) {
            return 0;
        }
    }
    
    return 0;
}
```

### 4. 统一订单号前缀

**修复前**：
```java
String prefix = "XSD" + dateStr;  // 与注释不符
```

**修复后**：
```java
String prefix = "SO" + dateStr;   // 与注释一致
```

## 完整的修复后代码

```java
/**
 * 生成订单号
 *
 * @return 订单号
 */
private String generateOrderNo() {
    // 生成格式：SO + yyyyMMdd + 6位序号
    LocalDate now = LocalDate.now();
    String dateStr = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    String prefix = "SO" + dateStr;

    // 查询当天最大序号
    int maxSeq = getMaxSequenceOfDay(dateStr);
    String sequence = String.format("%06d", maxSeq + 1);

    return prefix + sequence;
}

/**
 * 获取当天最大序号
 *
 * @param dateStr 日期字符串
 * @return 最大序号
 */
private int getMaxSequenceOfDay(String dateStr) {
    String prefix = "SO" + dateStr;
    
    // 查询当天以该前缀开头的订单号
    LambdaQueryWrapper<TShippingOrder> wrapper = new LambdaQueryWrapper<>();
    wrapper.likeRight(TShippingOrder::getOrderNo, prefix)
            .eq(TShippingOrder::getValid, 1)
            .orderByDesc(TShippingOrder::getOrderNo)
            .last("LIMIT 1");
    
    TShippingOrder lastOrder = this.getOne(wrapper);
    if (lastOrder == null) {
        return 0;
    }
    
    // 从订单号中提取序号部分
    String orderNo = lastOrder.getOrderNo();
    if (orderNo.length() >= prefix.length() + 6) {
        String seqStr = orderNo.substring(prefix.length());
        try {
            return Integer.parseInt(seqStr);
        } catch (NumberFormatException e) {
            return 0;
        }
    }
    
    return 0;
}
```

## 订单号格式说明

### 格式规则
- **前缀**：`XSD` (销售订单前缀)
- **日期**：`yyyyMMdd` (8位数字，如：20241223)
- **序号**：`4位数字，不足补0` (如：0001)

### 示例
```
XSD202412230001  // 2024年12月23日第1个订单
XSD202412230002  // 2024年12月23日第2个订单
XSD202412240001  // 2024年12月24日第1个订单
XSD202412319999  // 2024年12月31日第9999个订单
```

### String.format("%04d", number) 解释
- `%` - 格式化占位符开始
- `0` - 填充字符（用0填充）
- `4` - 总宽度（4位数字）
- `d` - 数据类型（十进制整数）

**示例**：
```java
String.format("%04d", 1);    // "0001"
String.format("%04d", 123);  // "0123"
String.format("%04d", 9999); // "9999"
```

## 业务逻辑

### 序号生成规则
1. **查询当天最大订单号**：查找以`SO+日期`开头的最大订单号
2. **提取序号**：从订单号中提取6位序号部分
3. **递增序号**：最大序号+1作为新序号
4. **格式化**：将新序号格式化为6位数字（前面补0）

### 并发安全性
- 使用数据库查询获取最大序号，避免并发冲突
- 建议在实际使用中添加数据库唯一约束
- 可考虑使用分布式锁或数据库序列来保证并发安全

## 测试验证

**测试文件**：`yi-management/src/test/java/com/yi/service/TShippingOrderGenerateOrderNoTest.java`

**测试覆盖**：
- ✅ 订单号格式验证
- ✅ 序号递增逻辑
- ✅ 日期变化处理
- ✅ 无现有订单场景
- ✅ 有现有订单场景
- ✅ 无效订单号处理
- ✅ 序号解析逻辑
- ✅ 唯一性验证

## 注意事项

### 1. 数据库约束
建议在数据库中添加订单号唯一约束：
```sql
ALTER TABLE t_shipping_order ADD UNIQUE KEY uk_order_no (order_no);
```

### 2. 并发处理
在高并发场景下，建议使用以下方案之一：
- 数据库序列
- 分布式锁
- 乐观锁重试机制

### 3. 序号上限
当前设计支持每天最多999,999个订单，如需更多可调整序号位数。

### 4. 时区处理
当前使用系统默认时区，如需特定时区可调整LocalDate.now()的获取方式。

## 相关文件

- 服务实现：`yi-management/src/main/java/com/yi/service/TShippingOrderService.java`
- 测试文件：`yi-management/src/test/java/com/yi/service/TShippingOrderGenerateOrderNoTest.java`
- 实体类：`yi-management/src/main/java/com/yi/entity/TShippingOrder.java`
