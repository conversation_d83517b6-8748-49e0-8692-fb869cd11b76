package com.yi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yi.entity.TShippingOrderLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 发运订单日志表 Mapper 接口
 */
@Mapper
public interface TShippingOrderLogMapper extends BaseMapper<TShippingOrderLog> {

    /**
     * 根据订单ID查询日志列表
     *
     * @param orderId 订单ID
     * @return 日志列表
     */
    List<TShippingOrderLog> selectByOrderId(@Param("orderId") Long orderId);
}
