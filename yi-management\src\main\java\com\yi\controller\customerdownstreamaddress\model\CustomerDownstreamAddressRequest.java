package com.yi.controller.customerdownstreamaddress.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 客户下游地址关联请求
 */
@Data
@ApiModel(value = "CustomerDownstreamAddressRequest", description = "客户下游地址关联请求")
public class CustomerDownstreamAddressRequest {


    @NotBlank(message = "公司ID不能为空")
    @ApiModelProperty(value = "公司ID", required = true)
    private String companyId;

    @NotBlank(message = "下游客户ID不能为空")
    @ApiModelProperty(value = "下游客户ID", required = true)
    private String downstreamCustomerCompanyId;

    @NotBlank(message = "仓库ID不能为空")
    @ApiModelProperty(value = "下游客户仓库ID", required = true)
    private String warehouseId;

}
