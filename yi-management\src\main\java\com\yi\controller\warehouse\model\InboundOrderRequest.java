package com.yi.controller.warehouse.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 入库单请求
 */
@Data
@ApiModel(value = "InboundOrderRequest", description = "入库单请求")
public class InboundOrderRequest {

    @ApiModelProperty(value = "入库单ID（编辑时必填）")
    private Long id;

    @ApiModelProperty(value = "入库单号（与出库单号一致，可不填由系统生成）")
    private String orderNo;

    @NotNull(message = "入库类型不能为空")
    @ApiModelProperty(value = "入库类型：1-采购入库，2-回收入库，3-调拨入库，4-销售入库", required = true)
    private Integer inboundType;

    @NotNull(message = "入库仓库ID不能为空")
    @ApiModelProperty(value = "入库仓库ID", required = true)
    private Long inboundWarehouseId;

    @NotBlank(message = "入库仓库名称不能为空")
    @ApiModelProperty(value = "入库仓库名称", required = true)
    private String inboundWarehouseName;

    @ApiModelProperty(value = "配送方式")
    private String deliveryMethod;

    @ApiModelProperty(value = "车牌号")
    private String vehicleNumber;

    @ApiModelProperty(value = "司机姓名")
    private String driverName;

    @ApiModelProperty(value = "司机联系方式")
    private String driverPhone;

    @ApiModelProperty(value = "发货仓库ID")
    private Long senderWarehouseId;

    @ApiModelProperty(value = "发货仓库名称")
    private String senderWarehouseName;

    @ApiModelProperty(value = "发货地址")
    private String senderAddress;

    @NotNull(message = "一级类目不能为空")
    @ApiModelProperty(value = "一级类目：1-共享托盘", required = true)
    private Integer firstCategory;

    @ApiModelProperty(value = "二级类目")
    private String secondCategory;

    @NotNull(message = "计划入库数不能为空")
    @Min(value = 1, message = "计划入库数必须大于0")
    @ApiModelProperty(value = "计划入库数", required = true)
    private Integer plannedQuantity;

    @ApiModelProperty(value = "关联出库单ID")
    private Long outboundOrderId;

    @ApiModelProperty(value = "备注")
    private String remark;
}
