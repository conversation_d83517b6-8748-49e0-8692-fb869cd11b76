package com.yi.controller.shippingorder.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 发运订单查询请求
 */
@Data
@ApiModel(value = "ShippingOrderQueryRequest", description = "发运订单查询请求")
public class ShippingOrderQueryRequest {

    @ApiModelProperty(value = "当前页码", example = "1")
    private String current = "1";

    @ApiModelProperty(value = "每页大小", example = "10")
    private String size = "10";

    @ApiModelProperty(value = "订单号（模糊查询）")
    private String orderNo;

    @ApiModelProperty(value = "合同编号（模糊查询）")
    private String contractCode;

    @ApiModelProperty(value = "客户公司名称（模糊查询）")
    private String customerCompanyName;

    @ApiModelProperty(value = "订单状态：PENDING-待发货，SHIPPING-发货中，COMPLETED-已完结，CANCELLED-已取消")
    private String status;

}
