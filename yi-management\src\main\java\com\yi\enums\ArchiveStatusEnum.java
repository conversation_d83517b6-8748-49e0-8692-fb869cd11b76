package com.yi.enums;

/**
 * 归档状态枚举
 */
public enum ArchiveStatusEnum {
    
    PENDING(1, "待归档"),
    ARCHIVED(2, "已归档");
    
    private final Integer code;
    private final String name;
    
    ArchiveStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    /**
     * 根据code获取枚举
     */
    public static ArchiveStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ArchiveStatusEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
    
    /**
     * 根据code获取名称
     */
    public static String getNameByCode(Integer code) {
        ArchiveStatusEnum item = getByCode(code);
        return item != null ? item.getName() : "";
    }
}
