package com.yi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yi.entity.InboundOrder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 入库单表 服务类
 */
public interface InboundOrderService extends IService<InboundOrder> {

    /**
     * 分页查询入库单列表
     *
     * @param page 分页参数
     * @param orderNo 入库单号（模糊查询）
     * @param status 入库状态
     * @param inboundType 入库类型
     * @param inboundWarehouseId 入库仓库ID
     * @param senderWarehouseId 发货仓库ID
     * @param firstCategory 一级类目
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分页结果
     */
    IPage<InboundOrder> selectInboundOrderPage(Page<InboundOrder> page, String orderNo, Integer status,
                                               Integer inboundType, Long inboundWarehouseId, Long senderWarehouseId,
                                               Integer firstCategory, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据入库单号查询入库单
     *
     * @param orderNo 入库单号
     * @return 入库单信息
     */
    InboundOrder selectByOrderNo(String orderNo);

    /**
     * 根据状态查询入库单列表
     *
     * @param status 入库状态
     * @return 入库单列表
     */
    List<InboundOrder> selectByStatus(Integer status);

    /**
     * 根据关联出库单ID查询入库单
     *
     * @param outboundOrderId 出库单ID
     * @return 入库单信息
     */
    InboundOrder selectByOutboundOrderId(Long outboundOrderId);

    /**
     * 创建入库单
     *
     * @param inboundOrder 入库单信息
     * @return 是否成功
     */
    boolean createInboundOrder(InboundOrder inboundOrder);

    /**
     * 更新入库单
     *
     * @param inboundOrder 入库单信息
     * @return 是否成功
     */
    boolean updateInboundOrder(InboundOrder inboundOrder);

    /**
     * 删除入库单
     *
     * @param id 入库单ID
     * @return 是否成功
     */
    boolean deleteInboundOrder(Long id);

    /**
     * 批量删除入库单
     *
     * @param ids 入库单ID列表
     * @return 是否成功
     */
    boolean deleteInboundOrders(List<Long> ids);

    /**
     * 部分入库（更新状态为部分入库）
     *
     * @param id 入库单ID
     * @param actualQuantity 实际入库数
     * @param lastModifiedBy 最后修改人
     * @return 是否成功
     */
    boolean partialInbound(Long id, Integer actualQuantity, String lastModifiedBy);

    /**
     * 完成入库（更新状态为已入库）
     *
     * @param id 入库单ID
     * @param actualQuantity 实际入库数
     * @param lastModifiedBy 最后修改人
     * @return 是否成功
     */
    boolean completeInbound(Long id, Integer actualQuantity, String lastModifiedBy);

    /**
     * 取消入库（回退到待入库状态）
     *
     * @param id 入库单ID
     * @param lastModifiedBy 最后修改人
     * @return 是否成功
     */
    boolean cancelInbound(Long id, String lastModifiedBy);

    /**
     * 统计各状态的入库单数量
     *
     * @return 状态统计结果
     */
    List<Map<String, Object>> getStatusStatistics();

    /**
     * 统计各类型的入库单数量
     *
     * @return 类型统计结果
     */
    List<Map<String, Object>> getTypeStatistics();

    /**
     * 查询待入库的订单
     *
     * @return 待入库订单列表
     */
    List<InboundOrder> getPendingOrders();

    /**
     * 查询部分入库的订单
     *
     * @return 部分入库订单列表
     */
    List<InboundOrder> getPartialOrders();

    /**
     * 检查入库单号是否存在
     *
     * @param orderNo 入库单号
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean checkOrderNoExists(String orderNo, Long excludeId);
}
