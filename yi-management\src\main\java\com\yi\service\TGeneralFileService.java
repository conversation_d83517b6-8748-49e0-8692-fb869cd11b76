package com.yi.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yi.entity.TGeneralFile;
import com.yi.enums.GeneralFileTypeEnum;
import com.yi.mapper.TGeneralFileMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 通用文件表 服务类
 */
@Service
public class TGeneralFileService extends ServiceImpl<TGeneralFileMapper, TGeneralFile> {

    /**
     * 根据关联ID和类型查询文件列表
     *
     * @param relatedId 关联ID
     * @param type 文件类型
     * @return 文件列表
     */
    public List<TGeneralFile> getByRelatedIdAndType(Long relatedId, Integer type) {
        LambdaQueryWrapper<TGeneralFile> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TGeneralFile::getRelatedId, relatedId)
                .eq(TGeneralFile::getType, type)
                .eq(TGeneralFile::getValid, 1)
                .orderByDesc(TGeneralFile::getCreatedTime);
        return this.list(wrapper);
    }

    /**
     * 保存合同附件
     *
     * @param contractId 合同ID
     * @param filePaths 文件路径列表
     * @return 是否成功
     */
    public boolean saveContractAttachments(Long contractId, List<String> filePaths) {
        // 先删除原有的合同附件
        deleteByRelatedIdAndType(contractId, GeneralFileTypeEnum.SALES_CONTRACT.getCode());

        if (filePaths == null || filePaths.isEmpty()) {
            return true;
        }
        
        // 保存新的附件
        for (String filePath : filePaths) {
            if (StringUtils.hasText(filePath)) {
                TGeneralFile file = new TGeneralFile();
                file.setType(GeneralFileTypeEnum.SALES_CONTRACT.getCode());
                file.setRelatedId(contractId);
                file.setFilePath(filePath.trim());
                file.setFileType(getFileTypeFromPath(filePath));
                file.setValid(1);
                
                this.save(file);
            }
        }
        
        return true;
    }

    /**
     * 根据关联ID和类型删除文件
     *
     * @param relatedId 关联ID
     * @param type 文件类型
     * @return 是否成功
     */
    public boolean deleteByRelatedIdAndType(Long relatedId, Integer type) {
        LambdaQueryWrapper<TGeneralFile> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TGeneralFile::getRelatedId, relatedId)
                .eq(TGeneralFile::getType, type);
        
        List<TGeneralFile> files = this.list(wrapper);
        for (TGeneralFile file : files) {
            file.setValid(0);
        }
        
        return this.updateBatchById(files);
    }

    /**
     * 检查是否有合同附件
     *
     * @param contractId 合同ID
     * @return 是否有附件
     */
    public boolean hasContractAttachments(Long contractId) {
        LambdaQueryWrapper<TGeneralFile> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TGeneralFile::getRelatedId, contractId)
                .eq(TGeneralFile::getType, GeneralFileTypeEnum.SALES_CONTRACT.getCode())
                .eq(TGeneralFile::getValid, 1);
        
        return this.count(wrapper) > 0;
    }

    /**
     * 获取合同附件列表
     *
     * @param contractId 合同ID
     * @return 附件列表
     */
    public List<TGeneralFile> getContractAttachments(Long contractId) {
        return getByRelatedIdAndType(contractId, GeneralFileTypeEnum.SALES_CONTRACT.getCode());
    }

    /**
     * 保存发运订单附件
     *
     * @param orderId 订单ID
     * @param filePaths 文件路径列表
     * @return 是否成功
     */
    public boolean saveShippingOrderAttachments(Long orderId, List<String> filePaths) {
        // 先删除原有的订单附件
        deleteByRelatedIdAndType(orderId, GeneralFileTypeEnum.SHIPPING_ORDER.getCode());

        if (filePaths == null || filePaths.isEmpty()) {
            return true;
        }

        // 保存新的附件
        for (String filePath : filePaths) {
            if (StringUtils.hasText(filePath)) {
                TGeneralFile file = new TGeneralFile();
                file.setType(GeneralFileTypeEnum.SHIPPING_ORDER.getCode());
                file.setRelatedId(orderId);
                file.setFilePath(filePath.trim());
                file.setFileType(getFileTypeFromPath(filePath));
                file.setValid(1);

                this.save(file);
            }
        }

        return true;
    }

    /**
     * 获取发运订单附件列表
     *
     * @param orderId 订单ID
     * @return 附件列表
     */
    public List<TGeneralFile> getShippingOrderAttachments(Long orderId) {
        return getByRelatedIdAndType(orderId, GeneralFileTypeEnum.SHIPPING_ORDER.getCode());
    }

    /**
     * 保存供应商营业执照
     *
     * @param supplierId 供应商ID
     * @param filePaths 文件路径列表
     * @return 是否成功
     */
    public boolean saveSupplierBusinessLicense(Long supplierId, List<String> filePaths) {
        // 先删除原有的营业执照
        deleteByRelatedIdAndType(supplierId, GeneralFileTypeEnum.SUPPLIER_BUSINESS_LICENSE.getCode());

        if (filePaths == null || filePaths.isEmpty()) {
            return true;
        }

        // 保存新的营业执照
        for (String filePath : filePaths) {
            if (StringUtils.hasText(filePath)) {
                TGeneralFile file = new TGeneralFile();
                file.setType(GeneralFileTypeEnum.SUPPLIER_BUSINESS_LICENSE.getCode());
                file.setRelatedId(supplierId);
                file.setFilePath(filePath.trim());
                file.setFileType(getFileTypeFromPath(filePath));
                file.setValid(1);

                this.save(file);
            }
        }

        return true;
    }

    /**
     * 获取供应商营业执照列表
     *
     * @param supplierId 供应商ID
     * @return 营业执照列表
     */
    public List<TGeneralFile> getSupplierBusinessLicense(Long supplierId) {
        return getByRelatedIdAndType(supplierId, GeneralFileTypeEnum.SUPPLIER_BUSINESS_LICENSE.getCode());
    }

    /**
     * 删除供应商营业执照
     *
     * @param supplierId 供应商ID
     * @return 是否成功
     */
    public boolean deleteSupplierBusinessLicense(Long supplierId) {
        return deleteByRelatedIdAndType(supplierId, GeneralFileTypeEnum.SUPPLIER_BUSINESS_LICENSE.getCode());
    }

    /**
     * 从文件路径中提取文件类型
     *
     * @param filePath 文件路径
     * @return 文件类型
     */
    private String getFileTypeFromPath(String filePath) {
        if (!StringUtils.hasText(filePath)) {
            return "";
        }
        
        int lastDotIndex = filePath.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < filePath.length() - 1) {
            return filePath.substring(lastDotIndex + 1).toLowerCase();
        }
        
        return "";
    }
}
