# 订单号格式示例说明

## 📋 **订单号格式规范**

### **格式定义**
```
XSD + yyyyMMdd + 4位序号
```

### **组成部分**
- **XSD** - 固定前缀（销售订单标识）
- **yyyyMMdd** - 8位日期（年月日）
- **4位序号** - 当天的订单序号，不足4位前面补0

## 🎯 **实际示例**

### **2024年12月23日的订单**
```
XSD202412230001  // 当天第1个订单
XSD202412230002  // 当天第2个订单
XSD202412230003  // 当天第3个订单
...
XSD202412230099  // 当天第99个订单
XSD202412230100  // 当天第100个订单
...
XSD202412239999  // 当天第9999个订单（最大值）
```

### **不同日期的订单**
```
XSD202412230001  // 2024年12月23日第1个订单
XSD202412240001  // 2024年12月24日第1个订单（序号重新从1开始）
XSD202412250001  // 2024年12月25日第1个订单
XSD202501010001  // 2025年1月1日第1个订单
```

## 💻 **代码实现**

### **String.format("%04d", number) 详解**
```java
// 格式化为4位数字，不足4位前面补0
String.format("%04d", 1);     // "0001"
String.format("%04d", 23);    // "0023"
String.format("%04d", 456);   // "0456"
String.format("%04d", 7890);  // "7890"
```

### **完整生成逻辑**
```java
private String generateOrderNo() {
    // 1. 获取当前日期并格式化
    LocalDate now = LocalDate.now();
    String dateStr = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    // 例如：now = 2024-12-23 → dateStr = "20241223"
    
    // 2. 构建前缀
    String prefix = "XSD" + dateStr;
    // 例如：prefix = "XSD20241223"
    
    // 3. 查询当天最大序号
    int maxSeq = getMaxSequenceOfDay(dateStr);
    // 例如：如果当天已有2个订单，maxSeq = 2
    
    // 4. 生成新序号（最大序号+1）
    String sequence = String.format("%04d", maxSeq + 1);
    // 例如：sequence = String.format("%04d", 3) = "0003"
    
    // 5. 组合完整订单号
    return prefix + sequence;
    // 例如：return "XSD20241223" + "0003" = "XSD202412230003"
}
```

## 🔍 **序号查询逻辑**

### **查询当天最大序号**
```java
private int getMaxSequenceOfDay(String dateStr) {
    // 1. 构建查询前缀
    String prefix = "XSD" + dateStr;
    // 例如：prefix = "XSD20241223"
    
    // 2. 查询当天最大订单号
    LambdaQueryWrapper<TShippingOrder> wrapper = new LambdaQueryWrapper<>();
    wrapper.likeRight(TShippingOrder::getOrderNo, prefix)  // 订单号以前缀开头
            .eq(TShippingOrder::getValid, 1)               // 有效订单
            .orderByDesc(TShippingOrder::getOrderNo)       // 按订单号降序
            .last("LIMIT 1");                              // 只取第一条
    
    TShippingOrder lastOrder = this.getOne(wrapper);
    if (lastOrder == null) {
        return 0;  // 当天没有订单，返回0
    }
    
    // 3. 从订单号中提取序号部分
    String orderNo = lastOrder.getOrderNo();
    // 例如：orderNo = "XSD202412230005"
    
    if (orderNo.length() >= prefix.length() + 4) {
        String seqStr = orderNo.substring(prefix.length());
        // 例如：seqStr = "0005"
        
        try {
            return Integer.parseInt(seqStr);
            // 例如：return 5
        } catch (NumberFormatException e) {
            return 0;  // 解析失败，返回0
        }
    }
    
    return 0;
}
```

## 📊 **业务场景示例**

### **场景1：新的一天，第一个订单**
```
当前日期：2024-12-23
查询结果：当天没有订单
最大序号：0
新序号：0 + 1 = 1
格式化：String.format("%04d", 1) = "0001"
订单号：XSD20241223 + 0001 = XSD202412230001
```

### **场景2：当天已有订单**
```
当前日期：2024-12-23
查询结果：XSD202412230005（当天最大订单号）
最大序号：5
新序号：5 + 1 = 6
格式化：String.format("%04d", 6) = "0006"
订单号：XSD20241223 + 0006 = XSD202412230006
```

### **场景3：跨日期**
```
昨天最后订单：XSD202412220999
今天第一订单：XSD202412230001（序号重新从1开始）
```

## ⚠️ **注意事项**

### **1. 序号上限**
- 当前设计支持每天最多9999个订单
- 如需更多订单，可调整序号位数（如改为5位：%05d）

### **2. 并发安全**
- 在高并发场景下可能出现序号重复
- 建议添加数据库唯一约束：`UNIQUE KEY uk_order_no (order_no)`
- 或使用分布式锁、数据库序列等方案

### **3. 时区处理**
- 当前使用系统默认时区
- 如需特定时区，可使用：`LocalDate.now(ZoneId.of("Asia/Shanghai"))`

### **4. 数据一致性**
- 删除订单时建议使用软删除（valid=0）
- 避免物理删除导致序号计算错误

## 🧪 **测试验证**

### **单元测试覆盖**
- ✅ 订单号格式验证（XSD + 8位日期 + 4位序号）
- ✅ 序号递增逻辑（1→2→3...）
- ✅ 日期变化处理（跨日序号重置）
- ✅ 无现有订单场景（序号从0001开始）
- ✅ 有现有订单场景（序号正确递增）
- ✅ 异常处理（无效订单号、解析错误等）

### **集成测试建议**
- 测试高并发订单创建
- 测试跨日期边界情况
- 测试数据库约束生效
- 测试序号上限处理

这个订单号格式设计简洁明了，便于识别和管理，同时具备良好的扩展性和维护性！🎉
