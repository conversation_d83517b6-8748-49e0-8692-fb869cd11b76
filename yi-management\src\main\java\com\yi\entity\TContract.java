package com.yi.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 合同管理表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_contract")
@ApiModel(value = "TContract对象", description = "合同管理表")
public class TContract extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "合同编号（唯一）")
    private String contractNo;

    @ApiModelProperty(value = "我司主体")
    private Integer outCustomerCompanyId;

    @ApiModelProperty(value = "客户主体ID")
    private Long customerCompanyId;

    @ApiModelProperty(value = "合同状态：1-待生效，2-生效中，3-已到期，4-已作废")
    private Integer contractStatus;

    @ApiModelProperty(value = "归档状态：1-待归档，2-已归档")
    private Integer archiveStatus;

    @ApiModelProperty(value = "合同生效日期")
    private LocalDate effectiveDate;

    @ApiModelProperty(value = "合同失效日期")
    private LocalDate expiryDate;

    @ApiModelProperty(value = "作废原因")
    private String cancelReason;
}
