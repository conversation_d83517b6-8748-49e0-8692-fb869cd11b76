package com.yi.controller.supplier.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

/**
 * 供应商导出VO
 */
@Data
@HeadRowHeight(20)
@ContentRowHeight(18)
public class SupplierExportVO {

    @ExcelProperty(value = "供应商ID")
    @ColumnWidth(15)
    private String id;

    @ExcelProperty(value = "供应商名称")
    @ColumnWidth(25)
    private String supplierName;

    @ExcelProperty(value = "联系人")
    @ColumnWidth(12)
    private String contactPerson;

    @ExcelProperty(value = "联系方式")
    @ColumnWidth(15)
    private String contactPhone;

    @ExcelProperty(value = "创建人")
    @ColumnWidth(12)
    private String createdBy;

    @ExcelProperty(value = "创建时间")
    @ColumnWidth(20)
    private String createdTime;
}
