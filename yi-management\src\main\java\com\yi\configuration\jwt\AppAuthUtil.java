package com.yi.configuration.jwt;

import com.yi.configuration.config.AppAuthConfig;
import com.yi.configuration.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * 第三方
 */
@Configuration
public class AppAuthUtil {
    @Resource
    private AppAuthConfig appAuthConfig;

    /**
     * 根据token， 公钥 解析 JWT 信息
     *
     * @param token
     * @return
     * @throws Exception
     */
    public IJWTInfo getInfoFromToken(String token) throws BizException {
        return JWTHelper.getInfoFromToken(token, appAuthConfig.getPubKeyPath());
    }

}
