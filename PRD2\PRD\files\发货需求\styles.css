﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-30px;
  width:1914px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u2035_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1301px;
  height:2px;
}
#u2035 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:37px;
  width:1300px;
  height:1px;
  display:flex;
}
#u2035 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2035_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2036_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:180px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2036 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:7px;
  width:180px;
  height:30px;
  display:flex;
}
#u2036 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2036_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2037_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2037 {
  border-width:0px;
  position:absolute;
  left:1274px;
  top:18px;
  width:56px;
  height:19px;
  display:flex;
}
#u2037 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2037_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2038_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1300px;
  height:100px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2038 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:43px;
  width:1300px;
  height:100px;
  display:flex;
}
#u2038 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2038_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2039_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2039 {
  border-width:0px;
  position:absolute;
  left:531px;
  top:59px;
  width:56px;
  height:16px;
  display:flex;
}
#u2039 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2039_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2040_input {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2040_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2040_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u2040 {
  border-width:0px;
  position:absolute;
  left:597px;
  top:55px;
  width:120px;
  height:24px;
  display:flex;
  text-align:center;
}
#u2040 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2040_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u2040.disabled {
}
#u2041_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2041 {
  border-width:0px;
  position:absolute;
  left:48px;
  top:59px;
  width:56px;
  height:16px;
  display:flex;
}
#u2041 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2041_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2042_input {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2042_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2042_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2042 {
  border-width:0px;
  position:absolute;
  left:109px;
  top:55px;
  width:120px;
  height:24px;
  display:flex;
}
#u2042 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2042_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2042.disabled {
}
.u2042_input_option {
}
#u2043_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2043 {
  border-width:0px;
  position:absolute;
  left:1059px;
  top:101px;
  width:80px;
  height:30px;
  display:flex;
}
#u2043 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2043_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2044_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2044 {
  border-width:0px;
  position:absolute;
  left:1149px;
  top:101px;
  width:80px;
  height:30px;
  display:flex;
}
#u2044 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2044_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2045_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2045 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:173px;
  width:80px;
  height:30px;
  display:flex;
}
#u2045 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2045_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2046 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:213px;
  width:1914px;
  height:334px;
}
#u2047_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:30px;
}
#u2047 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:30px;
  display:flex;
}
#u2047 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2047_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2048_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u2048 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:0px;
  width:100px;
  height:30px;
  display:flex;
}
#u2048 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2048_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2049_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:187px;
  height:30px;
}
#u2049 {
  border-width:0px;
  position:absolute;
  left:141px;
  top:0px;
  width:187px;
  height:30px;
  display:flex;
}
#u2049 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2049_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2050_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u2050 {
  border-width:0px;
  position:absolute;
  left:328px;
  top:0px;
  width:117px;
  height:30px;
  display:flex;
}
#u2050 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2050_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2051_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:30px;
}
#u2051 {
  border-width:0px;
  position:absolute;
  left:445px;
  top:0px;
  width:97px;
  height:30px;
  display:flex;
}
#u2051 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2051_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2052_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u2052 {
  border-width:0px;
  position:absolute;
  left:542px;
  top:0px;
  width:131px;
  height:30px;
  display:flex;
}
#u2052 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2052_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2053_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:189px;
  height:30px;
}
#u2053 {
  border-width:0px;
  position:absolute;
  left:673px;
  top:0px;
  width:189px;
  height:30px;
  display:flex;
}
#u2053 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2053_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2054_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:134px;
  height:30px;
}
#u2054 {
  border-width:0px;
  position:absolute;
  left:862px;
  top:0px;
  width:134px;
  height:30px;
  display:flex;
}
#u2054 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2054_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2055_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:30px;
}
#u2055 {
  border-width:0px;
  position:absolute;
  left:996px;
  top:0px;
  width:108px;
  height:30px;
  display:flex;
}
#u2055 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2055_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2056_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u2056 {
  border-width:0px;
  position:absolute;
  left:1104px;
  top:0px;
  width:120px;
  height:30px;
  display:flex;
}
#u2056 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2056_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2057_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u2057 {
  border-width:0px;
  position:absolute;
  left:1224px;
  top:0px;
  width:114px;
  height:30px;
  display:flex;
}
#u2057 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2057_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2058_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:30px;
}
#u2058 {
  border-width:0px;
  position:absolute;
  left:1338px;
  top:0px;
  width:105px;
  height:30px;
  display:flex;
}
#u2058 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2058_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2059_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
}
#u2059 {
  border-width:0px;
  position:absolute;
  left:1443px;
  top:0px;
  width:119px;
  height:30px;
  display:flex;
}
#u2059 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2059_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2060_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u2060 {
  border-width:0px;
  position:absolute;
  left:1562px;
  top:0px;
  width:120px;
  height:30px;
  display:flex;
}
#u2060 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2060_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2061_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u2061 {
  border-width:0px;
  position:absolute;
  left:1682px;
  top:0px;
  width:114px;
  height:30px;
  display:flex;
}
#u2061 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2061_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2062_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u2062 {
  border-width:0px;
  position:absolute;
  left:1796px;
  top:0px;
  width:118px;
  height:30px;
  display:flex;
}
#u2062 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2062_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2063_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:34px;
}
#u2063 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:30px;
  width:41px;
  height:34px;
  display:flex;
}
#u2063 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2063_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2064_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:34px;
}
#u2064 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:30px;
  width:100px;
  height:34px;
  display:flex;
}
#u2064 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2064_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2065_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:187px;
  height:34px;
}
#u2065 {
  border-width:0px;
  position:absolute;
  left:141px;
  top:30px;
  width:187px;
  height:34px;
  display:flex;
}
#u2065 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2065_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2066_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:34px;
}
#u2066 {
  border-width:0px;
  position:absolute;
  left:328px;
  top:30px;
  width:117px;
  height:34px;
  display:flex;
}
#u2066 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2066_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2067_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:34px;
}
#u2067 {
  border-width:0px;
  position:absolute;
  left:445px;
  top:30px;
  width:97px;
  height:34px;
  display:flex;
}
#u2067 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2067_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2068_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:34px;
}
#u2068 {
  border-width:0px;
  position:absolute;
  left:542px;
  top:30px;
  width:131px;
  height:34px;
  display:flex;
}
#u2068 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2068_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2069_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:189px;
  height:34px;
}
#u2069 {
  border-width:0px;
  position:absolute;
  left:673px;
  top:30px;
  width:189px;
  height:34px;
  display:flex;
}
#u2069 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2069_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2070_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:134px;
  height:34px;
}
#u2070 {
  border-width:0px;
  position:absolute;
  left:862px;
  top:30px;
  width:134px;
  height:34px;
  display:flex;
}
#u2070 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2070_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2071_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:34px;
}
#u2071 {
  border-width:0px;
  position:absolute;
  left:996px;
  top:30px;
  width:108px;
  height:34px;
  display:flex;
}
#u2071 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2071_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2072_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:34px;
}
#u2072 {
  border-width:0px;
  position:absolute;
  left:1104px;
  top:30px;
  width:120px;
  height:34px;
  display:flex;
}
#u2072 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2072_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2073_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:34px;
}
#u2073 {
  border-width:0px;
  position:absolute;
  left:1224px;
  top:30px;
  width:114px;
  height:34px;
  display:flex;
}
#u2073 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2073_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2074_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:34px;
}
#u2074 {
  border-width:0px;
  position:absolute;
  left:1338px;
  top:30px;
  width:105px;
  height:34px;
  display:flex;
}
#u2074 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2074_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2075_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:34px;
}
#u2075 {
  border-width:0px;
  position:absolute;
  left:1443px;
  top:30px;
  width:119px;
  height:34px;
  display:flex;
}
#u2075 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2075_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2076_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:34px;
}
#u2076 {
  border-width:0px;
  position:absolute;
  left:1562px;
  top:30px;
  width:120px;
  height:34px;
  display:flex;
}
#u2076 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2076_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2077_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:34px;
}
#u2077 {
  border-width:0px;
  position:absolute;
  left:1682px;
  top:30px;
  width:114px;
  height:34px;
  display:flex;
}
#u2077 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2077_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2078_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:34px;
}
#u2078 {
  border-width:0px;
  position:absolute;
  left:1796px;
  top:30px;
  width:118px;
  height:34px;
  display:flex;
}
#u2078 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2078_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2079_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:30px;
}
#u2079 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:64px;
  width:41px;
  height:30px;
  display:flex;
}
#u2079 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2079_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2080_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u2080 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:64px;
  width:100px;
  height:30px;
  display:flex;
}
#u2080 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2080_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2081_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:187px;
  height:30px;
}
#u2081 {
  border-width:0px;
  position:absolute;
  left:141px;
  top:64px;
  width:187px;
  height:30px;
  display:flex;
}
#u2081 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2081_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2082_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u2082 {
  border-width:0px;
  position:absolute;
  left:328px;
  top:64px;
  width:117px;
  height:30px;
  display:flex;
}
#u2082 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2082_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2083_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:30px;
}
#u2083 {
  border-width:0px;
  position:absolute;
  left:445px;
  top:64px;
  width:97px;
  height:30px;
  display:flex;
}
#u2083 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2083_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2084_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u2084 {
  border-width:0px;
  position:absolute;
  left:542px;
  top:64px;
  width:131px;
  height:30px;
  display:flex;
}
#u2084 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2084_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2085_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:189px;
  height:30px;
}
#u2085 {
  border-width:0px;
  position:absolute;
  left:673px;
  top:64px;
  width:189px;
  height:30px;
  display:flex;
}
#u2085 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2085_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2086_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:134px;
  height:30px;
}
#u2086 {
  border-width:0px;
  position:absolute;
  left:862px;
  top:64px;
  width:134px;
  height:30px;
  display:flex;
}
#u2086 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2086_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2087_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:30px;
}
#u2087 {
  border-width:0px;
  position:absolute;
  left:996px;
  top:64px;
  width:108px;
  height:30px;
  display:flex;
}
#u2087 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2087_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2088_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u2088 {
  border-width:0px;
  position:absolute;
  left:1104px;
  top:64px;
  width:120px;
  height:30px;
  display:flex;
}
#u2088 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2088_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2089_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u2089 {
  border-width:0px;
  position:absolute;
  left:1224px;
  top:64px;
  width:114px;
  height:30px;
  display:flex;
}
#u2089 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2089_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2090_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:30px;
}
#u2090 {
  border-width:0px;
  position:absolute;
  left:1338px;
  top:64px;
  width:105px;
  height:30px;
  display:flex;
}
#u2090 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2090_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2091_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
}
#u2091 {
  border-width:0px;
  position:absolute;
  left:1443px;
  top:64px;
  width:119px;
  height:30px;
  display:flex;
}
#u2091 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2091_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2092_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u2092 {
  border-width:0px;
  position:absolute;
  left:1562px;
  top:64px;
  width:120px;
  height:30px;
  display:flex;
}
#u2092 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2092_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2093_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u2093 {
  border-width:0px;
  position:absolute;
  left:1682px;
  top:64px;
  width:114px;
  height:30px;
  display:flex;
}
#u2093 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2093_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2094_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u2094 {
  border-width:0px;
  position:absolute;
  left:1796px;
  top:64px;
  width:118px;
  height:30px;
  display:flex;
}
#u2094 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2094_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2095_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:30px;
}
#u2095 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:94px;
  width:41px;
  height:30px;
  display:flex;
}
#u2095 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2095_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2096_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u2096 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:94px;
  width:100px;
  height:30px;
  display:flex;
}
#u2096 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2096_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2097_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:187px;
  height:30px;
}
#u2097 {
  border-width:0px;
  position:absolute;
  left:141px;
  top:94px;
  width:187px;
  height:30px;
  display:flex;
}
#u2097 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2097_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2098_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u2098 {
  border-width:0px;
  position:absolute;
  left:328px;
  top:94px;
  width:117px;
  height:30px;
  display:flex;
}
#u2098 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2098_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2099_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:30px;
}
#u2099 {
  border-width:0px;
  position:absolute;
  left:445px;
  top:94px;
  width:97px;
  height:30px;
  display:flex;
}
#u2099 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2099_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2100_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u2100 {
  border-width:0px;
  position:absolute;
  left:542px;
  top:94px;
  width:131px;
  height:30px;
  display:flex;
}
#u2100 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2100_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2101_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:189px;
  height:30px;
}
#u2101 {
  border-width:0px;
  position:absolute;
  left:673px;
  top:94px;
  width:189px;
  height:30px;
  display:flex;
}
#u2101 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2101_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2102_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:134px;
  height:30px;
}
#u2102 {
  border-width:0px;
  position:absolute;
  left:862px;
  top:94px;
  width:134px;
  height:30px;
  display:flex;
}
#u2102 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2102_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2103_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:30px;
}
#u2103 {
  border-width:0px;
  position:absolute;
  left:996px;
  top:94px;
  width:108px;
  height:30px;
  display:flex;
}
#u2103 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2103_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2104_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u2104 {
  border-width:0px;
  position:absolute;
  left:1104px;
  top:94px;
  width:120px;
  height:30px;
  display:flex;
}
#u2104 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2104_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2105_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u2105 {
  border-width:0px;
  position:absolute;
  left:1224px;
  top:94px;
  width:114px;
  height:30px;
  display:flex;
}
#u2105 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2105_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2106_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:30px;
}
#u2106 {
  border-width:0px;
  position:absolute;
  left:1338px;
  top:94px;
  width:105px;
  height:30px;
  display:flex;
}
#u2106 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2106_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2107_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
}
#u2107 {
  border-width:0px;
  position:absolute;
  left:1443px;
  top:94px;
  width:119px;
  height:30px;
  display:flex;
}
#u2107 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2107_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2108_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u2108 {
  border-width:0px;
  position:absolute;
  left:1562px;
  top:94px;
  width:120px;
  height:30px;
  display:flex;
}
#u2108 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2108_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2109_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u2109 {
  border-width:0px;
  position:absolute;
  left:1682px;
  top:94px;
  width:114px;
  height:30px;
  display:flex;
}
#u2109 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2109_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2110_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u2110 {
  border-width:0px;
  position:absolute;
  left:1796px;
  top:94px;
  width:118px;
  height:30px;
  display:flex;
}
#u2110 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2110_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2111_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:30px;
}
#u2111 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:124px;
  width:41px;
  height:30px;
  display:flex;
}
#u2111 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2111_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2112_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u2112 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:124px;
  width:100px;
  height:30px;
  display:flex;
}
#u2112 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2112_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2113_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:187px;
  height:30px;
}
#u2113 {
  border-width:0px;
  position:absolute;
  left:141px;
  top:124px;
  width:187px;
  height:30px;
  display:flex;
}
#u2113 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2113_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2114_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u2114 {
  border-width:0px;
  position:absolute;
  left:328px;
  top:124px;
  width:117px;
  height:30px;
  display:flex;
}
#u2114 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2114_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2115_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:30px;
}
#u2115 {
  border-width:0px;
  position:absolute;
  left:445px;
  top:124px;
  width:97px;
  height:30px;
  display:flex;
}
#u2115 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2115_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2116_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u2116 {
  border-width:0px;
  position:absolute;
  left:542px;
  top:124px;
  width:131px;
  height:30px;
  display:flex;
}
#u2116 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2116_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2117_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:189px;
  height:30px;
}
#u2117 {
  border-width:0px;
  position:absolute;
  left:673px;
  top:124px;
  width:189px;
  height:30px;
  display:flex;
}
#u2117 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2117_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2118_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:134px;
  height:30px;
}
#u2118 {
  border-width:0px;
  position:absolute;
  left:862px;
  top:124px;
  width:134px;
  height:30px;
  display:flex;
}
#u2118 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2118_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2119_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:30px;
}
#u2119 {
  border-width:0px;
  position:absolute;
  left:996px;
  top:124px;
  width:108px;
  height:30px;
  display:flex;
}
#u2119 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2119_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2120_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u2120 {
  border-width:0px;
  position:absolute;
  left:1104px;
  top:124px;
  width:120px;
  height:30px;
  display:flex;
}
#u2120 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2120_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2121_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u2121 {
  border-width:0px;
  position:absolute;
  left:1224px;
  top:124px;
  width:114px;
  height:30px;
  display:flex;
}
#u2121 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2121_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2122_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:30px;
}
#u2122 {
  border-width:0px;
  position:absolute;
  left:1338px;
  top:124px;
  width:105px;
  height:30px;
  display:flex;
}
#u2122 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2122_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2123_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
}
#u2123 {
  border-width:0px;
  position:absolute;
  left:1443px;
  top:124px;
  width:119px;
  height:30px;
  display:flex;
}
#u2123 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2123_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2124_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u2124 {
  border-width:0px;
  position:absolute;
  left:1562px;
  top:124px;
  width:120px;
  height:30px;
  display:flex;
}
#u2124 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2124_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2125_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u2125 {
  border-width:0px;
  position:absolute;
  left:1682px;
  top:124px;
  width:114px;
  height:30px;
  display:flex;
}
#u2125 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2125_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2126_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u2126 {
  border-width:0px;
  position:absolute;
  left:1796px;
  top:124px;
  width:118px;
  height:30px;
  display:flex;
}
#u2126 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2126_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2127_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:30px;
}
#u2127 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:154px;
  width:41px;
  height:30px;
  display:flex;
}
#u2127 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2127_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2128_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u2128 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:154px;
  width:100px;
  height:30px;
  display:flex;
}
#u2128 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2128_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2129_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:187px;
  height:30px;
}
#u2129 {
  border-width:0px;
  position:absolute;
  left:141px;
  top:154px;
  width:187px;
  height:30px;
  display:flex;
}
#u2129 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2129_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2130_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u2130 {
  border-width:0px;
  position:absolute;
  left:328px;
  top:154px;
  width:117px;
  height:30px;
  display:flex;
}
#u2130 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2130_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2131_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:30px;
}
#u2131 {
  border-width:0px;
  position:absolute;
  left:445px;
  top:154px;
  width:97px;
  height:30px;
  display:flex;
}
#u2131 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2131_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2132_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u2132 {
  border-width:0px;
  position:absolute;
  left:542px;
  top:154px;
  width:131px;
  height:30px;
  display:flex;
}
#u2132 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2132_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2133_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:189px;
  height:30px;
}
#u2133 {
  border-width:0px;
  position:absolute;
  left:673px;
  top:154px;
  width:189px;
  height:30px;
  display:flex;
}
#u2133 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2133_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2134_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:134px;
  height:30px;
}
#u2134 {
  border-width:0px;
  position:absolute;
  left:862px;
  top:154px;
  width:134px;
  height:30px;
  display:flex;
}
#u2134 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2134_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2135_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:30px;
}
#u2135 {
  border-width:0px;
  position:absolute;
  left:996px;
  top:154px;
  width:108px;
  height:30px;
  display:flex;
}
#u2135 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2135_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2136_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u2136 {
  border-width:0px;
  position:absolute;
  left:1104px;
  top:154px;
  width:120px;
  height:30px;
  display:flex;
}
#u2136 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2136_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2137_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u2137 {
  border-width:0px;
  position:absolute;
  left:1224px;
  top:154px;
  width:114px;
  height:30px;
  display:flex;
}
#u2137 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2137_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2138_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:30px;
}
#u2138 {
  border-width:0px;
  position:absolute;
  left:1338px;
  top:154px;
  width:105px;
  height:30px;
  display:flex;
}
#u2138 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2138_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2139_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
}
#u2139 {
  border-width:0px;
  position:absolute;
  left:1443px;
  top:154px;
  width:119px;
  height:30px;
  display:flex;
}
#u2139 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2139_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2140_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u2140 {
  border-width:0px;
  position:absolute;
  left:1562px;
  top:154px;
  width:120px;
  height:30px;
  display:flex;
}
#u2140 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2140_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2141_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u2141 {
  border-width:0px;
  position:absolute;
  left:1682px;
  top:154px;
  width:114px;
  height:30px;
  display:flex;
}
#u2141 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2141_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2142_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u2142 {
  border-width:0px;
  position:absolute;
  left:1796px;
  top:154px;
  width:118px;
  height:30px;
  display:flex;
}
#u2142 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2142_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2143_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:30px;
}
#u2143 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:184px;
  width:41px;
  height:30px;
  display:flex;
}
#u2143 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2143_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2144_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u2144 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:184px;
  width:100px;
  height:30px;
  display:flex;
}
#u2144 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2144_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2145_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:187px;
  height:30px;
}
#u2145 {
  border-width:0px;
  position:absolute;
  left:141px;
  top:184px;
  width:187px;
  height:30px;
  display:flex;
}
#u2145 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2145_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2146_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u2146 {
  border-width:0px;
  position:absolute;
  left:328px;
  top:184px;
  width:117px;
  height:30px;
  display:flex;
}
#u2146 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2146_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2147_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:30px;
}
#u2147 {
  border-width:0px;
  position:absolute;
  left:445px;
  top:184px;
  width:97px;
  height:30px;
  display:flex;
}
#u2147 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2147_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2148_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u2148 {
  border-width:0px;
  position:absolute;
  left:542px;
  top:184px;
  width:131px;
  height:30px;
  display:flex;
}
#u2148 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2148_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2149_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:189px;
  height:30px;
}
#u2149 {
  border-width:0px;
  position:absolute;
  left:673px;
  top:184px;
  width:189px;
  height:30px;
  display:flex;
}
#u2149 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2149_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2150_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:134px;
  height:30px;
}
#u2150 {
  border-width:0px;
  position:absolute;
  left:862px;
  top:184px;
  width:134px;
  height:30px;
  display:flex;
}
#u2150 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2150_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2151_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:30px;
}
#u2151 {
  border-width:0px;
  position:absolute;
  left:996px;
  top:184px;
  width:108px;
  height:30px;
  display:flex;
}
#u2151 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2151_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2152_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u2152 {
  border-width:0px;
  position:absolute;
  left:1104px;
  top:184px;
  width:120px;
  height:30px;
  display:flex;
}
#u2152 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2152_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2153_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u2153 {
  border-width:0px;
  position:absolute;
  left:1224px;
  top:184px;
  width:114px;
  height:30px;
  display:flex;
}
#u2153 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2153_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2154_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:30px;
}
#u2154 {
  border-width:0px;
  position:absolute;
  left:1338px;
  top:184px;
  width:105px;
  height:30px;
  display:flex;
}
#u2154 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2154_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2155_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
}
#u2155 {
  border-width:0px;
  position:absolute;
  left:1443px;
  top:184px;
  width:119px;
  height:30px;
  display:flex;
}
#u2155 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2155_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2156_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u2156 {
  border-width:0px;
  position:absolute;
  left:1562px;
  top:184px;
  width:120px;
  height:30px;
  display:flex;
}
#u2156 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2156_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2157_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u2157 {
  border-width:0px;
  position:absolute;
  left:1682px;
  top:184px;
  width:114px;
  height:30px;
  display:flex;
}
#u2157 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2157_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2158_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u2158 {
  border-width:0px;
  position:absolute;
  left:1796px;
  top:184px;
  width:118px;
  height:30px;
  display:flex;
}
#u2158 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2158_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2159_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:30px;
}
#u2159 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:214px;
  width:41px;
  height:30px;
  display:flex;
}
#u2159 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2159_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2160_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u2160 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:214px;
  width:100px;
  height:30px;
  display:flex;
}
#u2160 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2160_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2161_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:187px;
  height:30px;
}
#u2161 {
  border-width:0px;
  position:absolute;
  left:141px;
  top:214px;
  width:187px;
  height:30px;
  display:flex;
}
#u2161 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2161_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2162_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u2162 {
  border-width:0px;
  position:absolute;
  left:328px;
  top:214px;
  width:117px;
  height:30px;
  display:flex;
}
#u2162 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2162_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2163_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:30px;
}
#u2163 {
  border-width:0px;
  position:absolute;
  left:445px;
  top:214px;
  width:97px;
  height:30px;
  display:flex;
}
#u2163 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2163_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2164_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u2164 {
  border-width:0px;
  position:absolute;
  left:542px;
  top:214px;
  width:131px;
  height:30px;
  display:flex;
}
#u2164 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2164_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2165_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:189px;
  height:30px;
}
#u2165 {
  border-width:0px;
  position:absolute;
  left:673px;
  top:214px;
  width:189px;
  height:30px;
  display:flex;
}
#u2165 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2165_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2166_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:134px;
  height:30px;
}
#u2166 {
  border-width:0px;
  position:absolute;
  left:862px;
  top:214px;
  width:134px;
  height:30px;
  display:flex;
}
#u2166 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2166_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2167_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:30px;
}
#u2167 {
  border-width:0px;
  position:absolute;
  left:996px;
  top:214px;
  width:108px;
  height:30px;
  display:flex;
}
#u2167 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2167_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2168_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u2168 {
  border-width:0px;
  position:absolute;
  left:1104px;
  top:214px;
  width:120px;
  height:30px;
  display:flex;
}
#u2168 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2168_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2169_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u2169 {
  border-width:0px;
  position:absolute;
  left:1224px;
  top:214px;
  width:114px;
  height:30px;
  display:flex;
}
#u2169 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2169_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2170_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:30px;
}
#u2170 {
  border-width:0px;
  position:absolute;
  left:1338px;
  top:214px;
  width:105px;
  height:30px;
  display:flex;
}
#u2170 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2170_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2171_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
}
#u2171 {
  border-width:0px;
  position:absolute;
  left:1443px;
  top:214px;
  width:119px;
  height:30px;
  display:flex;
}
#u2171 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2171_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2172_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u2172 {
  border-width:0px;
  position:absolute;
  left:1562px;
  top:214px;
  width:120px;
  height:30px;
  display:flex;
}
#u2172 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2172_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2173_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u2173 {
  border-width:0px;
  position:absolute;
  left:1682px;
  top:214px;
  width:114px;
  height:30px;
  display:flex;
}
#u2173 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2173_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2174_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u2174 {
  border-width:0px;
  position:absolute;
  left:1796px;
  top:214px;
  width:118px;
  height:30px;
  display:flex;
}
#u2174 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2174_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2175_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:30px;
}
#u2175 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:244px;
  width:41px;
  height:30px;
  display:flex;
}
#u2175 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2175_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2176_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u2176 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:244px;
  width:100px;
  height:30px;
  display:flex;
}
#u2176 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2176_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2177_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:187px;
  height:30px;
}
#u2177 {
  border-width:0px;
  position:absolute;
  left:141px;
  top:244px;
  width:187px;
  height:30px;
  display:flex;
}
#u2177 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2177_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2178_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u2178 {
  border-width:0px;
  position:absolute;
  left:328px;
  top:244px;
  width:117px;
  height:30px;
  display:flex;
}
#u2178 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2178_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2179_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:30px;
}
#u2179 {
  border-width:0px;
  position:absolute;
  left:445px;
  top:244px;
  width:97px;
  height:30px;
  display:flex;
}
#u2179 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2179_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2180_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u2180 {
  border-width:0px;
  position:absolute;
  left:542px;
  top:244px;
  width:131px;
  height:30px;
  display:flex;
}
#u2180 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2180_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2181_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:189px;
  height:30px;
}
#u2181 {
  border-width:0px;
  position:absolute;
  left:673px;
  top:244px;
  width:189px;
  height:30px;
  display:flex;
}
#u2181 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2181_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2182_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:134px;
  height:30px;
}
#u2182 {
  border-width:0px;
  position:absolute;
  left:862px;
  top:244px;
  width:134px;
  height:30px;
  display:flex;
}
#u2182 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2182_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2183_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:30px;
}
#u2183 {
  border-width:0px;
  position:absolute;
  left:996px;
  top:244px;
  width:108px;
  height:30px;
  display:flex;
}
#u2183 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2183_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2184_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u2184 {
  border-width:0px;
  position:absolute;
  left:1104px;
  top:244px;
  width:120px;
  height:30px;
  display:flex;
}
#u2184 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2184_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2185_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u2185 {
  border-width:0px;
  position:absolute;
  left:1224px;
  top:244px;
  width:114px;
  height:30px;
  display:flex;
}
#u2185 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2185_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2186_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:30px;
}
#u2186 {
  border-width:0px;
  position:absolute;
  left:1338px;
  top:244px;
  width:105px;
  height:30px;
  display:flex;
}
#u2186 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2186_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2187_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
}
#u2187 {
  border-width:0px;
  position:absolute;
  left:1443px;
  top:244px;
  width:119px;
  height:30px;
  display:flex;
}
#u2187 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2187_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2188_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u2188 {
  border-width:0px;
  position:absolute;
  left:1562px;
  top:244px;
  width:120px;
  height:30px;
  display:flex;
}
#u2188 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2188_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2189_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u2189 {
  border-width:0px;
  position:absolute;
  left:1682px;
  top:244px;
  width:114px;
  height:30px;
  display:flex;
}
#u2189 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2189_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2190_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u2190 {
  border-width:0px;
  position:absolute;
  left:1796px;
  top:244px;
  width:118px;
  height:30px;
  display:flex;
}
#u2190 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2190_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2191_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:30px;
}
#u2191 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:274px;
  width:41px;
  height:30px;
  display:flex;
}
#u2191 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2191_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2192_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u2192 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:274px;
  width:100px;
  height:30px;
  display:flex;
}
#u2192 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2192_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2193_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:187px;
  height:30px;
}
#u2193 {
  border-width:0px;
  position:absolute;
  left:141px;
  top:274px;
  width:187px;
  height:30px;
  display:flex;
}
#u2193 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2193_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2194_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u2194 {
  border-width:0px;
  position:absolute;
  left:328px;
  top:274px;
  width:117px;
  height:30px;
  display:flex;
}
#u2194 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2194_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2195_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:30px;
}
#u2195 {
  border-width:0px;
  position:absolute;
  left:445px;
  top:274px;
  width:97px;
  height:30px;
  display:flex;
}
#u2195 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2195_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2196_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u2196 {
  border-width:0px;
  position:absolute;
  left:542px;
  top:274px;
  width:131px;
  height:30px;
  display:flex;
}
#u2196 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2196_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2197_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:189px;
  height:30px;
}
#u2197 {
  border-width:0px;
  position:absolute;
  left:673px;
  top:274px;
  width:189px;
  height:30px;
  display:flex;
}
#u2197 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2197_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2198_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:134px;
  height:30px;
}
#u2198 {
  border-width:0px;
  position:absolute;
  left:862px;
  top:274px;
  width:134px;
  height:30px;
  display:flex;
}
#u2198 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2198_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2199_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:30px;
}
#u2199 {
  border-width:0px;
  position:absolute;
  left:996px;
  top:274px;
  width:108px;
  height:30px;
  display:flex;
}
#u2199 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2199_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2200_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u2200 {
  border-width:0px;
  position:absolute;
  left:1104px;
  top:274px;
  width:120px;
  height:30px;
  display:flex;
}
#u2200 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2200_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2201_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u2201 {
  border-width:0px;
  position:absolute;
  left:1224px;
  top:274px;
  width:114px;
  height:30px;
  display:flex;
}
#u2201 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2201_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2202_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:30px;
}
#u2202 {
  border-width:0px;
  position:absolute;
  left:1338px;
  top:274px;
  width:105px;
  height:30px;
  display:flex;
}
#u2202 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2202_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2203_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
}
#u2203 {
  border-width:0px;
  position:absolute;
  left:1443px;
  top:274px;
  width:119px;
  height:30px;
  display:flex;
}
#u2203 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2203_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2204_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u2204 {
  border-width:0px;
  position:absolute;
  left:1562px;
  top:274px;
  width:120px;
  height:30px;
  display:flex;
}
#u2204 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2204_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2205_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u2205 {
  border-width:0px;
  position:absolute;
  left:1682px;
  top:274px;
  width:114px;
  height:30px;
  display:flex;
}
#u2205 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2205_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2206_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u2206 {
  border-width:0px;
  position:absolute;
  left:1796px;
  top:274px;
  width:118px;
  height:30px;
  display:flex;
}
#u2206 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2206_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2207_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:30px;
}
#u2207 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:304px;
  width:41px;
  height:30px;
  display:flex;
}
#u2207 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2207_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2208_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u2208 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:304px;
  width:100px;
  height:30px;
  display:flex;
}
#u2208 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2208_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2209_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:187px;
  height:30px;
}
#u2209 {
  border-width:0px;
  position:absolute;
  left:141px;
  top:304px;
  width:187px;
  height:30px;
  display:flex;
}
#u2209 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2209_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2210_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u2210 {
  border-width:0px;
  position:absolute;
  left:328px;
  top:304px;
  width:117px;
  height:30px;
  display:flex;
}
#u2210 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2210_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2211_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:30px;
}
#u2211 {
  border-width:0px;
  position:absolute;
  left:445px;
  top:304px;
  width:97px;
  height:30px;
  display:flex;
}
#u2211 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2211_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2212_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u2212 {
  border-width:0px;
  position:absolute;
  left:542px;
  top:304px;
  width:131px;
  height:30px;
  display:flex;
}
#u2212 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2212_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2213_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:189px;
  height:30px;
}
#u2213 {
  border-width:0px;
  position:absolute;
  left:673px;
  top:304px;
  width:189px;
  height:30px;
  display:flex;
}
#u2213 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2213_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2214_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:134px;
  height:30px;
}
#u2214 {
  border-width:0px;
  position:absolute;
  left:862px;
  top:304px;
  width:134px;
  height:30px;
  display:flex;
}
#u2214 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2214_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2215_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:30px;
}
#u2215 {
  border-width:0px;
  position:absolute;
  left:996px;
  top:304px;
  width:108px;
  height:30px;
  display:flex;
}
#u2215 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2215_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2216_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u2216 {
  border-width:0px;
  position:absolute;
  left:1104px;
  top:304px;
  width:120px;
  height:30px;
  display:flex;
}
#u2216 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2216_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2217_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u2217 {
  border-width:0px;
  position:absolute;
  left:1224px;
  top:304px;
  width:114px;
  height:30px;
  display:flex;
}
#u2217 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2217_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2218_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:30px;
}
#u2218 {
  border-width:0px;
  position:absolute;
  left:1338px;
  top:304px;
  width:105px;
  height:30px;
  display:flex;
}
#u2218 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2218_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2219_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
}
#u2219 {
  border-width:0px;
  position:absolute;
  left:1443px;
  top:304px;
  width:119px;
  height:30px;
  display:flex;
}
#u2219 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2219_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2220_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u2220 {
  border-width:0px;
  position:absolute;
  left:1562px;
  top:304px;
  width:120px;
  height:30px;
  display:flex;
}
#u2220 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2220_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2221_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u2221 {
  border-width:0px;
  position:absolute;
  left:1682px;
  top:304px;
  width:114px;
  height:30px;
  display:flex;
}
#u2221 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2221_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2222_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u2222 {
  border-width:0px;
  position:absolute;
  left:1796px;
  top:304px;
  width:118px;
  height:30px;
  display:flex;
}
#u2222 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2222_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2223_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2223 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:569px;
  width:57px;
  height:16px;
  display:flex;
}
#u2223 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2223_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2224_input {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:22px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2224_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:22px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2224_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2224 {
  border-width:0px;
  position:absolute;
  left:97px;
  top:563px;
  width:80px;
  height:22px;
  display:flex;
}
#u2224 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2224_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:22px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2224.disabled {
}
.u2224_input_option {
}
#u2225_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2225 {
  border-width:0px;
  position:absolute;
  left:187px;
  top:569px;
  width:168px;
  height:16px;
  display:flex;
}
#u2225 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2225_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2226_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2226 {
  border-width:0px;
  position:absolute;
  left:365px;
  top:569px;
  width:28px;
  height:16px;
  display:flex;
}
#u2226 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2226_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2227_input {
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:22px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2227_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:22px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2227_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u2227 {
  border-width:0px;
  position:absolute;
  left:398px;
  top:563px;
  width:30px;
  height:22px;
  display:flex;
  text-align:center;
}
#u2227 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2227_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:22px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u2227.disabled {
}
#u2228_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2228 {
  border-width:0px;
  position:absolute;
  left:433px;
  top:569px;
  width:14px;
  height:16px;
  display:flex;
}
#u2228 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2228_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2229_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2229 {
  border-width:0px;
  position:absolute;
  left:1900px;
  top:254px;
  width:28px;
  height:16px;
  display:flex;
}
#u2229 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2229_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2230_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2230 {
  border-width:0px;
  position:absolute;
  left:1852px;
  top:254px;
  width:28px;
  height:16px;
  display:flex;
}
#u2230 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2230_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2231_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2231 {
  border-width:0px;
  position:absolute;
  left:289px;
  top:59px;
  width:42px;
  height:16px;
  display:flex;
}
#u2231 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2231_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2232_input {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2232_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2232_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u2232 {
  border-width:0px;
  position:absolute;
  left:341px;
  top:55px;
  width:120px;
  height:24px;
  display:flex;
  text-align:center;
}
#u2232 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2232_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u2232.disabled {
}
#u2233_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2233 {
  border-width:0px;
  position:absolute;
  left:1023px;
  top:59px;
  width:56px;
  height:16px;
  display:flex;
}
#u2233 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2233_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2234_input {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2234_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2234_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2234 {
  border-width:0px;
  position:absolute;
  left:1084px;
  top:55px;
  width:120px;
  height:24px;
  display:flex;
}
#u2234 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2234_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2234.disabled {
}
.u2234_input_option {
}
#u2235_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2235 {
  border-width:0px;
  position:absolute;
  left:777px;
  top:59px;
  width:56px;
  height:16px;
  display:flex;
}
#u2235 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2235_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2236_input {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2236_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2236_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u2236 {
  border-width:0px;
  position:absolute;
  left:843px;
  top:55px;
  width:120px;
  height:24px;
  display:flex;
  text-align:center;
}
#u2236 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2236_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u2236.disabled {
}
#u2237_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1300px;
  height:189px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  font-size:14px;
  line-height:20px;
}
#u2237 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:638px;
  width:1300px;
  height:189px;
  display:flex;
  font-size:14px;
  line-height:20px;
}
#u2237 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2237_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2238_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1066px;
  height:171px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#000000;
  line-height:19px;
}
#u2238 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:646px;
  width:1066px;
  height:171px;
  display:flex;
  font-size:15px;
  color:#000000;
  line-height:19px;
}
#u2238 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2238_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
