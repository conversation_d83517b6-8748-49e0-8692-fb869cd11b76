<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yi.mapper.OutboundOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yi.entity.OutboundOrder">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="status" property="status" />
        <result column="outbound_type" property="outboundType" />
        <result column="outbound_company_id" property="outboundCompanyId" />
        <result column="outbound_company_name" property="outboundCompanyName" />
        <result column="outbound_address" property="outboundAddress" />
        <result column="delivery_method" property="deliveryMethod" />
        <result column="vehicle_number" property="vehicleNumber" />
        <result column="driver_name" property="driverName" />
        <result column="driver_phone" property="driverPhone" />
        <result column="receive_company_id" property="receiveCompanyId" />
        <result column="receive_company_name" property="receiveCompanyName" />
        <result column="receive_address" property="receiveAddress" />
        <result column="first_category" property="firstCategory" />
        <result column="second_category" property="secondCategory" />
        <result column="planned_quantity" property="plannedQuantity" />
        <result column="actual_quantity" property="actualQuantity" />
        <result column="outbound_time" property="outboundTime" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="last_modified_by" property="lastModifiedBy" />
        <result column="last_modified_time" property="lastModifiedTime" />
        <result column="valid" property="valid" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_no, status, outbound_type, outbound_company_id, outbound_company_name, 
        outbound_address, delivery_method, vehicle_number, driver_name, driver_phone,
        receive_company_id, receive_company_name, receive_address, first_category, 
        second_category, planned_quantity, actual_quantity, outbound_time, 
        created_by, created_time, last_modified_by, last_modified_time, valid, remark
    </sql>

    <!-- 分页查询出库单列表 -->
    <select id="selectOutboundOrderPage" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM t_outbound_order
        WHERE valid = 1
        <if test="orderNo != null and orderNo != ''">
            AND order_no LIKE CONCAT('%', #{orderNo}, '%')
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="outboundType != null">
            AND outbound_type = #{outboundType}
        </if>
        <if test="outboundCompanyId != null">
            AND outbound_company_id = #{outboundCompanyId}
        </if>
        <if test="receiveCompanyId != null">
            AND receive_company_id = #{receiveCompanyId}
        </if>
        <if test="firstCategory != null">
            AND first_category = #{firstCategory}
        </if>
        <if test="startTime != null">
            AND created_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND created_time &lt;= #{endTime}
        </if>
        ORDER BY created_time DESC
    </select>

    <!-- 根据出库单号查询出库单 -->
    <select id="selectByOrderNo" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM t_outbound_order
        WHERE order_no = #{orderNo} AND valid = 1
    </select>

    <!-- 根据状态查询出库单列表 -->
    <select id="selectByStatus" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM t_outbound_order
        WHERE status = #{status} AND valid = 1
        ORDER BY created_time DESC
    </select>

    <!-- 根据出库公司ID查询出库单列表 -->
    <select id="selectByOutboundCompanyId" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM t_outbound_order
        WHERE outbound_company_id = #{outboundCompanyId} AND valid = 1
        ORDER BY created_time DESC
    </select>

    <!-- 根据收货公司ID查询出库单列表 -->
    <select id="selectByReceiveCompanyId" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM t_outbound_order
        WHERE receive_company_id = #{receiveCompanyId} AND valid = 1
        ORDER BY created_time DESC
    </select>

    <!-- 统计各状态的出库单数量 -->
    <select id="selectStatusStatistics" resultType="java.util.Map">
        SELECT 
            status,
            COUNT(*) as count
        FROM t_outbound_order
        WHERE valid = 1
        GROUP BY status
    </select>

    <!-- 统计各类型的出库单数量 -->
    <select id="selectTypeStatistics" resultType="java.util.Map">
        SELECT 
            outbound_type,
            COUNT(*) as count
        FROM t_outbound_order
        WHERE valid = 1
        GROUP BY outbound_type
    </select>

    <!-- 查询指定时间范围内的出库单 -->
    <select id="selectByTimeRange" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM t_outbound_order
        WHERE valid = 1
        <if test="startTime != null">
            AND created_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND created_time &lt;= #{endTime}
        </if>
        ORDER BY created_time DESC
    </select>

    <!-- 查询待出库的订单 -->
    <select id="selectPendingOrders" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM t_outbound_order
        WHERE status = 1 AND valid = 1
        ORDER BY created_time ASC
    </select>

    <!-- 查询运输中的订单 -->
    <select id="selectInTransitOrders" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM t_outbound_order
        WHERE status = 2 AND valid = 1
        ORDER BY outbound_time ASC
    </select>

    <!-- 更新出库单状态 -->
    <update id="updateStatus">
        UPDATE t_outbound_order 
        SET status = #{status},
            <if test="actualQuantity != null">
                actual_quantity = #{actualQuantity},
            </if>
            <if test="outboundTime != null">
                outbound_time = #{outboundTime},
            </if>
            last_modified_by = #{lastModifiedBy},
            last_modified_time = NOW()
        WHERE id = #{id} AND valid = 1
    </update>

</mapper>
