package com.yi.controller.user.io;

import com.yi.configuration.jwt.TokenModule;
import com.yi.controller.user.model.ResourceVO;
import lombok.Data;

import java.util.List;

@Data
public class ManagementLoginResponseModel {

	private TokenModule tokenModel;

	private Long userId;

	private String userName;

	/** 是否过期  1、是  0否 */
	private Integer isOverdue;

	/** 用户拥有的菜单权限 */
	private List<ResourceVO> resourceList;
}
