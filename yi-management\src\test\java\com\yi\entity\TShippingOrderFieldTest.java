package com.yi.entity;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TShippingOrder实体类字段测试
 */
@SpringBootTest
public class TShippingOrderFieldTest {

    @Test
    public void testTShippingOrderFields() {
        // 创建TShippingOrder实例
        TShippingOrder order = new TShippingOrder();
        
        // 测试基本字段设置
        order.setId(1L);
        order.setOrderNo("SO20240101000001");
        order.setContractCode("CT001");
        order.setCustomerCompanyId(100L);
        order.setWarehouseId(200L);
        order.setFirstCategory(1);
        order.setSecondCategory("标准托盘");
        order.setCount(100);
        order.setShippedQuantity(0);
        order.setReceivedQuantity(0);
        order.setDemandTime(LocalDate.now());
        order.setStatus(1000); // PENDING
        order.setCancelReason("测试取消原因");
        order.setRemark("测试备注");
        order.setCreatedBy("testUser");
        order.setCreatedTime(LocalDateTime.now());
        order.setLastModifiedBy("testUser");
        order.setLastModifiedTime(LocalDateTime.now());
        order.setValid(1);

        // 验证字段值
        assertEquals(Long.valueOf(1L), order.getId());
        assertEquals("SO20240101000001", order.getOrderNo());
        assertEquals("CT001", order.getContractCode());
        assertEquals(Long.valueOf(100L), order.getCustomerCompanyId());
        assertEquals(Long.valueOf(200L), order.getWarehouseId());
        assertEquals(Integer.valueOf(1), order.getFirstCategory());
        assertEquals("标准托盘", order.getSecondCategory());
        assertEquals(Integer.valueOf(100), order.getCount());
        assertEquals(Integer.valueOf(0), order.getShippedQuantity());
        assertEquals(Integer.valueOf(0), order.getReceivedQuantity());
        assertEquals("PENDING", order.getStatus());
        assertEquals("测试取消原因", order.getCancelReason());
        assertEquals("测试备注", order.getRemark());
        assertEquals("testUser", order.getCreatedBy());
        assertEquals("testUser", order.getLastModifiedBy());
        assertEquals(Integer.valueOf(1), order.getValid());
    }

    @Test
    public void testCompleteReasonFieldNotExists() {
        // 验证completeReason字段不存在
        Field[] fields = TShippingOrder.class.getDeclaredFields();
        
        boolean hasCompleteReasonField = false;
        for (Field field : fields) {
            if ("completeReason".equals(field.getName())) {
                hasCompleteReasonField = true;
                break;
            }
        }
        
        // 断言completeReason字段不存在
        assertFalse(hasCompleteReasonField, "completeReason字段应该已被移除");
    }

    @Test
    public void testRequiredFieldsExist() {
        // 验证必要字段存在
        Field[] fields = TShippingOrder.class.getDeclaredFields();
        
        boolean hasOrderNo = false;
        boolean hasStatus = false;
        boolean hasCancelReason = false;
        boolean hasRemark = false;
        
        for (Field field : fields) {
            String fieldName = field.getName();
            if ("orderNo".equals(fieldName)) {
                hasOrderNo = true;
            } else if ("status".equals(fieldName)) {
                hasStatus = true;
            } else if ("cancelReason".equals(fieldName)) {
                hasCancelReason = true;
            } else if ("remark".equals(fieldName)) {
                hasRemark = true;
            }
        }
        
        // 断言必要字段存在
        assertTrue(hasOrderNo, "orderNo字段应该存在");
        assertTrue(hasStatus, "status字段应该存在");
        assertTrue(hasCancelReason, "cancelReason字段应该存在");
        assertTrue(hasRemark, "remark字段应该存在");
    }

    @Test
    public void testFieldCount() {
        // 验证字段数量（确保移除completeReason后字段数量正确）
        Field[] fields = TShippingOrder.class.getDeclaredFields();
        
        // 预期字段数量（不包括completeReason）
        // id, orderNo, contractCode, customerCompanyId, warehouseId, firstCategory, 
        // secondCategory, count, shippedQuantity, receivedQuantity, demandTime, 
        // status, cancelReason, remark, createdBy, createdTime, lastModifiedBy, 
        // lastModifiedTime, valid = 19个字段
        assertEquals(19, fields.length, "TShippingOrder应该有19个字段");
    }
}
