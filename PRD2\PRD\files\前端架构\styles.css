﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:1390px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u152_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1360px;
  height:60px;
}
#u152 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1360px;
  height:60px;
  display:flex;
}
#u152 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u152_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u153_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  text-align:center;
}
#u153 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:17px;
  width:72px;
  height:21px;
  display:flex;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
  text-align:center;
}
#u153 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u153_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u154_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
  text-align:center;
}
#u154 {
  border-width:0px;
  position:absolute;
  left:382px;
  top:20px;
  width:72px;
  height:18px;
  display:flex;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
  text-align:center;
}
#u154 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u154_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u155_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:660px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u155 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:60px;
  width:120px;
  height:660px;
  display:flex;
}
#u155 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u155_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u156_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1235px;
  height:655px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u156 {
  border-width:0px;
  position:absolute;
  left:125px;
  top:65px;
  width:1235px;
  height:655px;
  display:flex;
}
#u156 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u156_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u157_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
  text-align:center;
}
#u157 {
  border-width:0px;
  position:absolute;
  left:504px;
  top:20px;
  width:66px;
  height:18px;
  display:flex;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
  text-align:center;
}
#u157 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u157_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u158_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
  text-align:center;
}
#u158 {
  border-width:0px;
  position:absolute;
  left:620px;
  top:20px;
  width:96px;
  height:18px;
  display:flex;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
  text-align:center;
}
#u158 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u158_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u159_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:42px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u159 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:65px;
  width:120px;
  height:42px;
  display:flex;
}
#u159 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u159_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u160_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:42px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u160 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:107px;
  width:120px;
  height:42px;
  display:flex;
}
#u160 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u160_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u161_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:42px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u161 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:149px;
  width:120px;
  height:42px;
  display:flex;
}
#u161 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u161_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u162_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:42px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u162 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:191px;
  width:120px;
  height:42px;
  display:flex;
}
#u162 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u162_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u163_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:42px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u163 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:233px;
  width:120px;
  height:42px;
  display:flex;
}
#u163 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u163_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u164_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:42px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u164 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:275px;
  width:120px;
  height:42px;
  display:flex;
}
#u164 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u164_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u165_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
}
#u165 {
  border-width:0px;
  position:absolute;
  left:1153px;
  top:28px;
  width:16px;
  height:16px;
  display:flex;
  opacity:0.6;
}
#u165 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u165_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u166_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#F2F2F2;
}
#u166 {
  border-width:0px;
  position:absolute;
  left:1174px;
  top:28px;
  width:43px;
  height:16px;
  display:flex;
  color:#F2F2F2;
}
#u166 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u166_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u167_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
}
#u167 {
  border-width:0px;
  position:absolute;
  left:1260px;
  top:28px;
  width:16px;
  height:16px;
  display:flex;
}
#u167 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u167_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u168 {
  border-width:0px;
  position:absolute;
  left:464px;
  top:65px;
  width:625px;
  height:608px;
  visibility:hidden;
}
#u168_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:625px;
  height:608px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u168_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u169_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:348px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u169 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:348px;
  display:flex;
}
#u169 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u169_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u170_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u170 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:50px;
  display:flex;
}
#u170 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u170_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u171_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u171 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:14px;
  width:72px;
  height:22px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u171 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u171_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u172_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:13px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u172 {
  border-width:0px;
  position:absolute;
  left:463px;
  top:14px;
  width:13px;
  height:22px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u172 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u172_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u173_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u173 {
  border-width:0px;
  position:absolute;
  left:196px;
  top:292px;
  width:140px;
  height:40px;
  display:flex;
}
#u173 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u173_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u174_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u174 {
  border-width:0px;
  position:absolute;
  left:99px;
  top:139px;
  width:48px;
  height:16px;
  display:flex;
}
#u174 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u174_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u175_input {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u175_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u175_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u175 {
  border-width:0px;
  position:absolute;
  left:157px;
  top:135px;
  width:300px;
  height:24px;
  display:flex;
}
#u175 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u175_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u175.disabled {
}
#u176_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u176 {
  border-width:0px;
  position:absolute;
  left:43px;
  top:193px;
  width:104px;
  height:16px;
  display:flex;
}
#u176 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u176_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u177_input {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u177_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u177_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u177 {
  border-width:0px;
  position:absolute;
  left:157px;
  top:189px;
  width:300px;
  height:24px;
  display:flex;
}
#u177 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u177_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u177.disabled {
}
#u178_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u178 {
  border-width:0px;
  position:absolute;
  left:99px;
  top:85px;
  width:48px;
  height:16px;
  display:flex;
}
#u178 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u178_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u179_input {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u179_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u179_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u179 {
  border-width:0px;
  position:absolute;
  left:157px;
  top:81px;
  width:300px;
  height:24px;
  display:flex;
}
#u179 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u179_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u179.disabled {
}
#u180_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:420px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:13px;
  color:#D9001B;
}
#u180 {
  border-width:0px;
  position:absolute;
  left:56px;
  top:242px;
  width:420px;
  height:15px;
  display:flex;
  font-size:13px;
  color:#D9001B;
}
#u180 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u180_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u181_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u181 {
  border-width:0px;
  position:absolute;
  left:1281px;
  top:28px;
  width:56px;
  height:16px;
  display:flex;
  color:#FFFFFF;
}
#u181 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u181_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u182_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:128px;
  height:37px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta cursiva', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:italic;
  color:#02A7F0;
}
#u182 {
  border-width:0px;
  position:absolute;
  left:12px;
  top:11px;
  width:128px;
  height:37px;
  display:flex;
  font-family:'Arial Negreta cursiva', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:italic;
  color:#02A7F0;
}
#u182 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u182_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u183_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1300px;
  height:171px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  font-size:14px;
  line-height:20px;
}
#u183 {
  border-width:0px;
  position:absolute;
  left:23px;
  top:768px;
  width:1300px;
  height:171px;
  display:flex;
  font-size:14px;
  line-height:20px;
}
#u183 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u183_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u184_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1196px;
  height:114px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#000000;
  line-height:19px;
}
#u184 {
  border-width:0px;
  position:absolute;
  left:42px;
  top:776px;
  width:1196px;
  height:114px;
  display:flex;
  font-size:15px;
  color:#000000;
  line-height:19px;
}
#u184 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u184_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u185_img {
  border-width:0px;
  position:absolute;
  left:-4px;
  top:-4px;
  width:40px;
  height:50px;
}
#u185 {
  border-width:0px;
  position:absolute;
  left:1360px;
  top:20px;
  width:30px;
  height:40px;
  display:flex;
}
#u185 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 14px 2px;
  box-sizing:border-box;
  width:100%;
}
#u185_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
