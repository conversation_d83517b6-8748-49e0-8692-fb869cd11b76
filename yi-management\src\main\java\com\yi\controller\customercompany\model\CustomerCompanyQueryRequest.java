package com.yi.controller.customercompany.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 客户公司查询请求
 */
@Data
@ApiModel(value = "CustomerCompanyQueryRequest", description = "客户公司查询请求")
public class CustomerCompanyQueryRequest {

    @ApiModelProperty(value = "当前页码", example = "1")
    private String current = "1";

    @ApiModelProperty(value = "每页大小", example = "10")
    private String size = "10";

    @ApiModelProperty(value = "公司名称（模糊查询）")
    private String companyName;

    @ApiModelProperty(value = "客户类型：1-合约客户，2-非合约客户")
    private String customerCompanyType;

    @ApiModelProperty(value = "省份ID")
    private String provinceId;

    @ApiModelProperty(value = "城市ID")
    private String cityId;

    @ApiModelProperty(value = "区县ID")
    private String areaId;

    @ApiModelProperty(value = "启用状态：1-启用，0-禁用")
    private String enabled;


}
