﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-28px;
  width:1314px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u1858_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1301px;
  height:2px;
}
#u1858 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:56px;
  width:1300px;
  height:1px;
  display:flex;
}
#u1858 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1858_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1859_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:167px;
  height:32px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1859 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:24px;
  width:167px;
  height:32px;
  display:flex;
}
#u1859 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1859_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1860_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1860 {
  border-width:0px;
  position:absolute;
  left:42px;
  top:32px;
  width:70px;
  height:16px;
  display:flex;
}
#u1860 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1860_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1861_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:16px;
  background:inherit;
  background-color:rgba(217, 0, 27, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#D9001B;
}
#u1861 {
  border-width:0px;
  position:absolute;
  left:165px;
  top:32px;
  width:15px;
  height:16px;
  display:flex;
  color:#D9001B;
}
#u1861 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1861_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1862_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1862 {
  border-width:0px;
  position:absolute;
  left:1272px;
  top:36px;
  width:56px;
  height:20px;
  display:flex;
}
#u1862 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1862_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1863 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:186px;
  width:1245px;
  height:120px;
}
#u1864_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:169px;
  height:30px;
}
#u1864 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:169px;
  height:30px;
  display:flex;
}
#u1864 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1864_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1865_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:246px;
  height:30px;
}
#u1865 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:0px;
  width:246px;
  height:30px;
  display:flex;
}
#u1865 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1865_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1866_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:30px;
}
#u1866 {
  border-width:0px;
  position:absolute;
  left:415px;
  top:0px;
  width:144px;
  height:30px;
  display:flex;
}
#u1866 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1866_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1867_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:271px;
  height:30px;
}
#u1867 {
  border-width:0px;
  position:absolute;
  left:559px;
  top:0px;
  width:271px;
  height:30px;
  display:flex;
}
#u1867 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1867_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1868_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:30px;
}
#u1868 {
  border-width:0px;
  position:absolute;
  left:830px;
  top:0px;
  width:162px;
  height:30px;
  display:flex;
}
#u1868 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1868_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1869_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:253px;
  height:30px;
}
#u1869 {
  border-width:0px;
  position:absolute;
  left:992px;
  top:0px;
  width:253px;
  height:30px;
  display:flex;
}
#u1869 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1869_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1870_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:169px;
  height:30px;
}
#u1870 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:30px;
  width:169px;
  height:30px;
  display:flex;
}
#u1870 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1870_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1871_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:246px;
  height:30px;
}
#u1871 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:30px;
  width:246px;
  height:30px;
  display:flex;
}
#u1871 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1871_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1872_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:30px;
}
#u1872 {
  border-width:0px;
  position:absolute;
  left:415px;
  top:30px;
  width:144px;
  height:30px;
  display:flex;
}
#u1872 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1872_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1873_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:271px;
  height:30px;
}
#u1873 {
  border-width:0px;
  position:absolute;
  left:559px;
  top:30px;
  width:271px;
  height:30px;
  display:flex;
}
#u1873 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1873_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1874_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:30px;
}
#u1874 {
  border-width:0px;
  position:absolute;
  left:830px;
  top:30px;
  width:162px;
  height:30px;
  display:flex;
}
#u1874 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1874_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1875_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:253px;
  height:30px;
}
#u1875 {
  border-width:0px;
  position:absolute;
  left:992px;
  top:30px;
  width:253px;
  height:30px;
  display:flex;
}
#u1875 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1875_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1876_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:169px;
  height:30px;
}
#u1876 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:60px;
  width:169px;
  height:30px;
  display:flex;
}
#u1876 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1876_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1877_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:246px;
  height:30px;
}
#u1877 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:60px;
  width:246px;
  height:30px;
  display:flex;
}
#u1877 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1877_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1878_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:30px;
}
#u1878 {
  border-width:0px;
  position:absolute;
  left:415px;
  top:60px;
  width:144px;
  height:30px;
  display:flex;
}
#u1878 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1878_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1879_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:271px;
  height:30px;
}
#u1879 {
  border-width:0px;
  position:absolute;
  left:559px;
  top:60px;
  width:271px;
  height:30px;
  display:flex;
}
#u1879 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1879_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1880_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:30px;
}
#u1880 {
  border-width:0px;
  position:absolute;
  left:830px;
  top:60px;
  width:162px;
  height:30px;
  display:flex;
}
#u1880 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1880_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1881_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:253px;
  height:30px;
}
#u1881 {
  border-width:0px;
  position:absolute;
  left:992px;
  top:60px;
  width:253px;
  height:30px;
  display:flex;
}
#u1881 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1881_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1882_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:169px;
  height:30px;
}
#u1882 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:90px;
  width:169px;
  height:30px;
  display:flex;
}
#u1882 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1882_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1883_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:246px;
  height:30px;
}
#u1883 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:90px;
  width:246px;
  height:30px;
  display:flex;
}
#u1883 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1883_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1884_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:30px;
}
#u1884 {
  border-width:0px;
  position:absolute;
  left:415px;
  top:90px;
  width:144px;
  height:30px;
  display:flex;
}
#u1884 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1884_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1885_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:271px;
  height:30px;
}
#u1885 {
  border-width:0px;
  position:absolute;
  left:559px;
  top:90px;
  width:271px;
  height:30px;
  display:flex;
}
#u1885 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1885_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1886_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:30px;
}
#u1886 {
  border-width:0px;
  position:absolute;
  left:830px;
  top:90px;
  width:162px;
  height:30px;
  display:flex;
}
#u1886 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1886_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1887_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:253px;
  height:30px;
}
#u1887 {
  border-width:0px;
  position:absolute;
  left:992px;
  top:90px;
  width:253px;
  height:30px;
  display:flex;
}
#u1887 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1887_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1888_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1888 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:327px;
  width:84px;
  height:16px;
  display:flex;
}
#u1888 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1888_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1889_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1004px;
  height:75px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1889 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:348px;
  width:1004px;
  height:75px;
  display:flex;
}
#u1889 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1889_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1890_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1890 {
  border-width:0px;
  position:absolute;
  left:53px;
  top:807px;
  width:28px;
  height:16px;
  display:flex;
}
#u1890 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1890_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1891_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:120px;
}
#u1891 {
  border-width:0px;
  position:absolute;
  left:53px;
  top:828px;
  width:104px;
  height:120px;
  display:flex;
}
#u1891 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1891_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1892_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:120px;
}
#u1892 {
  border-width:0px;
  position:absolute;
  left:167px;
  top:828px;
  width:104px;
  height:120px;
  display:flex;
}
#u1892 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1892_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1893_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:120px;
}
#u1893 {
  border-width:0px;
  position:absolute;
  left:281px;
  top:828px;
  width:104px;
  height:120px;
  display:flex;
}
#u1893 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1893_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1894_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:120px;
}
#u1894 {
  border-width:0px;
  position:absolute;
  left:395px;
  top:828px;
  width:104px;
  height:120px;
  display:flex;
}
#u1894 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1894_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1895_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:120px;
}
#u1895 {
  border-width:0px;
  position:absolute;
  left:509px;
  top:828px;
  width:104px;
  height:120px;
  display:flex;
}
#u1895 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1895_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1896_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:120px;
}
#u1896 {
  border-width:0px;
  position:absolute;
  left:623px;
  top:828px;
  width:104px;
  height:120px;
  display:flex;
}
#u1896 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1896_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1897_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1897 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:67px;
  width:140px;
  height:40px;
  display:flex;
}
#u1897 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1897_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1898_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1898 {
  border-width:0px;
  position:absolute;
  left:215px;
  top:67px;
  width:140px;
  height:40px;
  display:flex;
}
#u1898 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1898_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1899_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:120px;
}
#u1899 {
  border-width:0px;
  position:absolute;
  left:737px;
  top:828px;
  width:104px;
  height:120px;
  display:flex;
}
#u1899 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1899_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1900_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1300px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1900 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:1143px;
  width:1300px;
  height:50px;
  display:flex;
}
#u1900 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1900_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1901_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1901 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:1156px;
  width:72px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1901 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1901_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1902 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:1207px;
  width:1227px;
  height:171px;
}
#u1903_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:30px;
}
#u1903 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:30px;
  display:flex;
}
#u1903 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1903_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1904_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:229px;
  height:30px;
}
#u1904 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:0px;
  width:229px;
  height:30px;
  display:flex;
}
#u1904 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1904_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1905_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:134px;
  height:30px;
}
#u1905 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:0px;
  width:134px;
  height:30px;
  display:flex;
}
#u1905 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1905_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1906_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:211px;
  height:30px;
}
#u1906 {
  border-width:0px;
  position:absolute;
  left:404px;
  top:0px;
  width:211px;
  height:30px;
  display:flex;
}
#u1906 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1906_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1907_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:612px;
  height:30px;
}
#u1907 {
  border-width:0px;
  position:absolute;
  left:615px;
  top:0px;
  width:612px;
  height:30px;
  display:flex;
}
#u1907 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1907_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1908_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:30px;
}
#u1908 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:30px;
  width:41px;
  height:30px;
  display:flex;
}
#u1908 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1908_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1909_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:229px;
  height:30px;
}
#u1909 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:30px;
  width:229px;
  height:30px;
  display:flex;
}
#u1909 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1909_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1910_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:134px;
  height:30px;
}
#u1910 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:30px;
  width:134px;
  height:30px;
  display:flex;
}
#u1910 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1910_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1911_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:211px;
  height:30px;
}
#u1911 {
  border-width:0px;
  position:absolute;
  left:404px;
  top:30px;
  width:211px;
  height:30px;
  display:flex;
}
#u1911 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1911_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1912_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:612px;
  height:30px;
}
#u1912 {
  border-width:0px;
  position:absolute;
  left:615px;
  top:30px;
  width:612px;
  height:30px;
  display:flex;
}
#u1912 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1912_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1913_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:37px;
}
#u1913 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:60px;
  width:41px;
  height:37px;
  display:flex;
}
#u1913 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1913_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1914_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:229px;
  height:37px;
}
#u1914 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:60px;
  width:229px;
  height:37px;
  display:flex;
}
#u1914 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1914_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1915_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:134px;
  height:37px;
}
#u1915 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:60px;
  width:134px;
  height:37px;
  display:flex;
}
#u1915 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1915_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1916_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:211px;
  height:37px;
}
#u1916 {
  border-width:0px;
  position:absolute;
  left:404px;
  top:60px;
  width:211px;
  height:37px;
  display:flex;
}
#u1916 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1916_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1917_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:612px;
  height:37px;
}
#u1917 {
  border-width:0px;
  position:absolute;
  left:615px;
  top:60px;
  width:612px;
  height:37px;
  display:flex;
}
#u1917 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1917_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1918_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:37px;
}
#u1918 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:97px;
  width:41px;
  height:37px;
  display:flex;
}
#u1918 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1918_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1919_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:229px;
  height:37px;
}
#u1919 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:97px;
  width:229px;
  height:37px;
  display:flex;
}
#u1919 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1919_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1920_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:134px;
  height:37px;
}
#u1920 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:97px;
  width:134px;
  height:37px;
  display:flex;
}
#u1920 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1920_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1921_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:211px;
  height:37px;
}
#u1921 {
  border-width:0px;
  position:absolute;
  left:404px;
  top:97px;
  width:211px;
  height:37px;
  display:flex;
}
#u1921 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1921_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1922_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:612px;
  height:37px;
}
#u1922 {
  border-width:0px;
  position:absolute;
  left:615px;
  top:97px;
  width:612px;
  height:37px;
  display:flex;
}
#u1922 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1922_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1923_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:37px;
}
#u1923 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:134px;
  width:41px;
  height:37px;
  display:flex;
}
#u1923 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1923_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1924_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:229px;
  height:37px;
}
#u1924 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:134px;
  width:229px;
  height:37px;
  display:flex;
}
#u1924 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1924_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1925_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:134px;
  height:37px;
}
#u1925 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:134px;
  width:134px;
  height:37px;
  display:flex;
}
#u1925 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1925_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1926_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:211px;
  height:37px;
}
#u1926 {
  border-width:0px;
  position:absolute;
  left:404px;
  top:134px;
  width:211px;
  height:37px;
  display:flex;
}
#u1926 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1926_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1927_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:612px;
  height:37px;
}
#u1927 {
  border-width:0px;
  position:absolute;
  left:615px;
  top:134px;
  width:612px;
  height:37px;
  display:flex;
}
#u1927 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1927_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1928_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1300px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1928 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:117px;
  width:1300px;
  height:50px;
  display:flex;
}
#u1928 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1928_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1929_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1929 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:132px;
  width:72px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1929 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1929_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1930_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1300px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1930 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:462px;
  width:1300px;
  height:50px;
  display:flex;
}
#u1930 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1930_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1931_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1931 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:475px;
  width:72px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1931 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1931_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1932 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:552px;
  width:1227px;
  height:91px;
}
#u1933_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:30px;
}
#u1933 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:30px;
  display:flex;
}
#u1933 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1933_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1934_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:290px;
  height:30px;
}
#u1934 {
  border-width:0px;
  position:absolute;
  left:51px;
  top:0px;
  width:290px;
  height:30px;
  display:flex;
}
#u1934 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1934_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1935_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:214px;
  height:30px;
}
#u1935 {
  border-width:0px;
  position:absolute;
  left:341px;
  top:0px;
  width:214px;
  height:30px;
  display:flex;
}
#u1935 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1935_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1936_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:223px;
  height:30px;
}
#u1936 {
  border-width:0px;
  position:absolute;
  left:555px;
  top:0px;
  width:223px;
  height:30px;
  display:flex;
}
#u1936 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1936_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1937_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:245px;
  height:30px;
}
#u1937 {
  border-width:0px;
  position:absolute;
  left:778px;
  top:0px;
  width:245px;
  height:30px;
  display:flex;
}
#u1937 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1937_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1938_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:204px;
  height:30px;
}
#u1938 {
  border-width:0px;
  position:absolute;
  left:1023px;
  top:0px;
  width:204px;
  height:30px;
  display:flex;
}
#u1938 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1938_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1939_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:31px;
}
#u1939 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:30px;
  width:51px;
  height:31px;
  display:flex;
}
#u1939 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1939_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1940_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:290px;
  height:31px;
}
#u1940 {
  border-width:0px;
  position:absolute;
  left:51px;
  top:30px;
  width:290px;
  height:31px;
  display:flex;
}
#u1940 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1940_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1941_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:214px;
  height:31px;
}
#u1941 {
  border-width:0px;
  position:absolute;
  left:341px;
  top:30px;
  width:214px;
  height:31px;
  display:flex;
}
#u1941 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1941_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1942_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:223px;
  height:31px;
}
#u1942 {
  border-width:0px;
  position:absolute;
  left:555px;
  top:30px;
  width:223px;
  height:31px;
  display:flex;
}
#u1942 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1942_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1943_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:245px;
  height:31px;
}
#u1943 {
  border-width:0px;
  position:absolute;
  left:778px;
  top:30px;
  width:245px;
  height:31px;
  display:flex;
}
#u1943 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1943_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1944_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:204px;
  height:31px;
}
#u1944 {
  border-width:0px;
  position:absolute;
  left:1023px;
  top:30px;
  width:204px;
  height:31px;
  display:flex;
}
#u1944 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1944_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1945_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:30px;
}
#u1945 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:61px;
  width:51px;
  height:30px;
  display:flex;
}
#u1945 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1945_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1946_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:290px;
  height:30px;
}
#u1946 {
  border-width:0px;
  position:absolute;
  left:51px;
  top:61px;
  width:290px;
  height:30px;
  display:flex;
}
#u1946 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1946_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1947_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:214px;
  height:30px;
}
#u1947 {
  border-width:0px;
  position:absolute;
  left:341px;
  top:61px;
  width:214px;
  height:30px;
  display:flex;
}
#u1947 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1947_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1948_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:223px;
  height:30px;
}
#u1948 {
  border-width:0px;
  position:absolute;
  left:555px;
  top:61px;
  width:223px;
  height:30px;
  display:flex;
}
#u1948 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1948_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1949_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:245px;
  height:30px;
}
#u1949 {
  border-width:0px;
  position:absolute;
  left:778px;
  top:61px;
  width:245px;
  height:30px;
  display:flex;
}
#u1949 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1949_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1950_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:204px;
  height:30px;
}
#u1950 {
  border-width:0px;
  position:absolute;
  left:1023px;
  top:61px;
  width:204px;
  height:30px;
  display:flex;
}
#u1950 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1950_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1951_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1951 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:526px;
  width:56px;
  height:16px;
  display:flex;
}
#u1951 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1951_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1952_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:204px;
  height:63px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1952 {
  border-width:0px;
  position:absolute;
  left:1078px;
  top:580px;
  width:204px;
  height:63px;
  display:flex;
}
#u1952 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1952_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1953 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:689px;
  width:1227px;
  height:91px;
}
#u1954_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:30px;
}
#u1954 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:30px;
  display:flex;
}
#u1954 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1954_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1955_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:290px;
  height:30px;
}
#u1955 {
  border-width:0px;
  position:absolute;
  left:51px;
  top:0px;
  width:290px;
  height:30px;
  display:flex;
}
#u1955 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1955_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1956_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:214px;
  height:30px;
}
#u1956 {
  border-width:0px;
  position:absolute;
  left:341px;
  top:0px;
  width:214px;
  height:30px;
  display:flex;
}
#u1956 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1956_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1957_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:223px;
  height:30px;
}
#u1957 {
  border-width:0px;
  position:absolute;
  left:555px;
  top:0px;
  width:223px;
  height:30px;
  display:flex;
}
#u1957 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1957_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1958_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:245px;
  height:30px;
}
#u1958 {
  border-width:0px;
  position:absolute;
  left:778px;
  top:0px;
  width:245px;
  height:30px;
  display:flex;
}
#u1958 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1958_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1959_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:204px;
  height:30px;
}
#u1959 {
  border-width:0px;
  position:absolute;
  left:1023px;
  top:0px;
  width:204px;
  height:30px;
  display:flex;
}
#u1959 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1959_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1960_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:31px;
}
#u1960 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:30px;
  width:51px;
  height:31px;
  display:flex;
}
#u1960 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1960_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1961_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:290px;
  height:31px;
}
#u1961 {
  border-width:0px;
  position:absolute;
  left:51px;
  top:30px;
  width:290px;
  height:31px;
  display:flex;
}
#u1961 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1961_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1962_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:214px;
  height:31px;
}
#u1962 {
  border-width:0px;
  position:absolute;
  left:341px;
  top:30px;
  width:214px;
  height:31px;
  display:flex;
}
#u1962 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1962_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1963_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:223px;
  height:31px;
}
#u1963 {
  border-width:0px;
  position:absolute;
  left:555px;
  top:30px;
  width:223px;
  height:31px;
  display:flex;
}
#u1963 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1963_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1964_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:245px;
  height:31px;
}
#u1964 {
  border-width:0px;
  position:absolute;
  left:778px;
  top:30px;
  width:245px;
  height:31px;
  display:flex;
}
#u1964 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1964_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1965_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:204px;
  height:31px;
}
#u1965 {
  border-width:0px;
  position:absolute;
  left:1023px;
  top:30px;
  width:204px;
  height:31px;
  display:flex;
}
#u1965 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1965_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1966_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:30px;
}
#u1966 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:61px;
  width:51px;
  height:30px;
  display:flex;
}
#u1966 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1966_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1967_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:290px;
  height:30px;
}
#u1967 {
  border-width:0px;
  position:absolute;
  left:51px;
  top:61px;
  width:290px;
  height:30px;
  display:flex;
}
#u1967 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1967_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1968_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:214px;
  height:30px;
}
#u1968 {
  border-width:0px;
  position:absolute;
  left:341px;
  top:61px;
  width:214px;
  height:30px;
  display:flex;
}
#u1968 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1968_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1969_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:223px;
  height:30px;
}
#u1969 {
  border-width:0px;
  position:absolute;
  left:555px;
  top:61px;
  width:223px;
  height:30px;
  display:flex;
}
#u1969 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1969_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1970_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:245px;
  height:30px;
}
#u1970 {
  border-width:0px;
  position:absolute;
  left:778px;
  top:61px;
  width:245px;
  height:30px;
  display:flex;
}
#u1970 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1970_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1971_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:204px;
  height:30px;
}
#u1971 {
  border-width:0px;
  position:absolute;
  left:1023px;
  top:61px;
  width:204px;
  height:30px;
  display:flex;
}
#u1971 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1971_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1972_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1972 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:663px;
  width:56px;
  height:16px;
  display:flex;
}
#u1972 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1972_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1973_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:204px;
  height:63px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1973 {
  border-width:0px;
  position:absolute;
  left:1078px;
  top:717px;
  width:204px;
  height:63px;
  display:flex;
}
#u1973 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1973_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1974 {
  border-width:0px;
  position:absolute;
  left:378px;
  top:108px;
  width:500px;
  height:555px;
  visibility:hidden;
}
#u1974_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:555px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1974_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1975_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:433px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1975 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:433px;
  display:flex;
}
#u1975 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1975_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1976_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1976 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:50px;
  display:flex;
}
#u1976 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1976_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1977_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:center;
}
#u1977 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:16px;
  width:64px;
  height:18px;
  display:flex;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:center;
}
#u1977 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1977_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1978_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:center;
}
#u1978 {
  border-width:0px;
  position:absolute;
  left:459px;
  top:16px;
  width:11px;
  height:18px;
  display:flex;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:center;
}
#u1978 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1978_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1979_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1979 {
  border-width:0px;
  position:absolute;
  left:64px;
  top:85px;
  width:62px;
  height:16px;
  display:flex;
}
#u1979 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1979_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1980_input {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1980_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1980_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1980 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:80px;
  width:300px;
  height:26px;
  display:flex;
}
#u1980 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1980_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:26px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1980.disabled {
}
.u1980_input_option {
}
#u1981_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1981 {
  border-width:0px;
  position:absolute;
  left:64px;
  top:131px;
  width:62px;
  height:16px;
  display:flex;
}
#u1981 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1981_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1982 label {
  left:0px;
  width:100%;
}
#u1982_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:2px;
  width:12px;
  height:12px;
}
#u1982 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:132px;
  width:100px;
  height:15px;
  display:flex;
}
#u1982 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u1982_img.selected {
}
#u1982.selected {
}
#u1982_img.disabled {
}
#u1982.disabled {
}
#u1982_img.selectedDisabled {
}
#u1982.selectedDisabled {
}
#u1982_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u1982_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u1983_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1983 {
  border-width:0px;
  position:absolute;
  left:64px;
  top:173px;
  width:62px;
  height:16px;
  display:flex;
}
#u1983 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1983_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1984_input {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1984_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1984_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1984 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:169px;
  width:300px;
  height:24px;
  display:flex;
}
#u1984 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1984_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1984.disabled {
}
#u1985_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1985 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:261px;
  width:90px;
  height:16px;
  display:flex;
}
#u1985 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1985_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1986_input {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1986_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1986_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1986 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:257px;
  width:300px;
  height:24px;
  display:flex;
}
#u1986 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1986_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1986.disabled {
}
#u1987_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:76px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1987 {
  border-width:0px;
  position:absolute;
  left:50px;
  top:217px;
  width:76px;
  height:16px;
  display:flex;
}
#u1987 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1987_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1988_input {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1988_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1988_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1988 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:213px;
  width:300px;
  height:24px;
  display:flex;
}
#u1988 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1988_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1988.disabled {
}
#u1989_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1989 {
  border-width:0px;
  position:absolute;
  left:78px;
  top:305px;
  width:48px;
  height:16px;
  display:flex;
}
#u1989 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1989_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1990_input {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1990_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1990_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1990 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:301px;
  width:300px;
  height:24px;
  display:flex;
}
#u1990 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1990_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1990.disabled {
}
#u1991_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1991 {
  border-width:0px;
  position:absolute;
  left:180px;
  top:369px;
  width:140px;
  height:40px;
  display:flex;
}
#u1991 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1991_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1974_state1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:555px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u1974_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1992_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:297px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1992 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:297px;
  display:flex;
}
#u1992 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1992_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1993_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1993 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:50px;
  display:flex;
}
#u1993 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1993_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1994_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:center;
}
#u1994 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:16px;
  width:32px;
  height:18px;
  display:flex;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:center;
}
#u1994 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1994_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1995_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:center;
}
#u1995 {
  border-width:0px;
  position:absolute;
  left:459px;
  top:16px;
  width:11px;
  height:18px;
  display:flex;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:center;
}
#u1995 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1995_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1996_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1996 {
  border-width:0px;
  position:absolute;
  left:38px;
  top:87px;
  width:62px;
  height:16px;
  display:flex;
}
#u1996 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1996_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1997_input {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:67px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1997_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:67px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1997_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:67px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1997 {
  border-width:0px;
  position:absolute;
  left:130px;
  top:87px;
  width:300px;
  height:67px;
  display:flex;
}
#u1997 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1997_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:67px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1997.disabled {
}
#u1998_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1998 {
  border-width:0px;
  position:absolute;
  left:290px;
  top:202px;
  width:140px;
  height:30px;
  display:flex;
}
#u1998 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1998_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1999_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1999 {
  border-width:0px;
  position:absolute;
  left:120px;
  top:202px;
  width:140px;
  height:30px;
  display:flex;
}
#u1999 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1999_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1974_state2 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:555px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u1974_state2_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u2000_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:297px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2000 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:297px;
  display:flex;
}
#u2000 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2000_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2001_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2001 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:50px;
  display:flex;
}
#u2001 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2001_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2002_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:center;
}
#u2002 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:16px;
  width:32px;
  height:18px;
  display:flex;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:center;
}
#u2002 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2002_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2003_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:center;
}
#u2003 {
  border-width:0px;
  position:absolute;
  left:459px;
  top:16px;
  width:11px;
  height:18px;
  display:flex;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:center;
}
#u2003 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2003_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2004_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2004 {
  border-width:0px;
  position:absolute;
  left:38px;
  top:87px;
  width:62px;
  height:16px;
  display:flex;
}
#u2004 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2004_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2005_input {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:67px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2005_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:67px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2005_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:67px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2005 {
  border-width:0px;
  position:absolute;
  left:130px;
  top:87px;
  width:300px;
  height:67px;
  display:flex;
}
#u2005 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2005_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:67px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2005.disabled {
}
#u2006_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2006 {
  border-width:0px;
  position:absolute;
  left:290px;
  top:202px;
  width:140px;
  height:30px;
  display:flex;
}
#u2006 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2006_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2007_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2007 {
  border-width:0px;
  position:absolute;
  left:120px;
  top:202px;
  width:140px;
  height:30px;
  display:flex;
}
#u2007 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2007_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1974_state3 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:555px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u1974_state3_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u2008_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:523px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2008 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:523px;
  display:flex;
}
#u2008 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2008_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2009_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2009 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:50px;
  display:flex;
}
#u2009 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2009_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2010_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:center;
}
#u2010 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:16px;
  width:80px;
  height:18px;
  display:flex;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:center;
}
#u2010 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2010_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2011_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:center;
}
#u2011 {
  border-width:0px;
  position:absolute;
  left:459px;
  top:16px;
  width:11px;
  height:18px;
  display:flex;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:center;
}
#u2011 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2011_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2012_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2012 {
  border-width:0px;
  position:absolute;
  left:64px;
  top:85px;
  width:62px;
  height:16px;
  display:flex;
}
#u2012 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2012_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2013_input {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2013_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2013_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2013 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:80px;
  width:300px;
  height:26px;
  display:flex;
}
#u2013 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2013_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:26px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2013.disabled {
}
.u2013_input_option {
}
#u2014_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2014 {
  border-width:0px;
  position:absolute;
  left:180px;
  top:466px;
  width:140px;
  height:40px;
  display:flex;
}
#u2014 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2014_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2015_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:137px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u2015 {
  border-width:0px;
  position:absolute;
  left:64px;
  top:159px;
  width:137px;
  height:16px;
  display:flex;
  font-size:14px;
}
#u2015 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2015_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2016_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u2016 {
  border-width:0px;
  position:absolute;
  left:221px;
  top:128px;
  width:70px;
  height:16px;
  display:flex;
  font-size:14px;
}
#u2016 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2016_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2017_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u2017 {
  border-width:0px;
  position:absolute;
  left:316px;
  top:128px;
  width:70px;
  height:16px;
  display:flex;
  font-size:14px;
}
#u2017 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2017_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2018_input {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2018_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2018_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2018 {
  border-width:0px;
  position:absolute;
  left:211px;
  top:154px;
  width:80px;
  height:26px;
  display:flex;
}
#u2018 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2018_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:26px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2018.disabled {
}
#u2019_input {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2019_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2019_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2019 {
  border-width:0px;
  position:absolute;
  left:311px;
  top:154px;
  width:80px;
  height:26px;
  display:flex;
}
#u2019 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2019_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:26px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2019.disabled {
}
#u2020_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u2020 {
  border-width:0px;
  position:absolute;
  left:64px;
  top:195px;
  width:138px;
  height:16px;
  display:flex;
  font-size:14px;
}
#u2020 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2020_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2021_input {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2021_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2021_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2021 {
  border-width:0px;
  position:absolute;
  left:211px;
  top:190px;
  width:80px;
  height:26px;
  display:flex;
}
#u2021 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2021_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:26px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2021.disabled {
}
#u2022_input {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2022_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2022_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2022 {
  border-width:0px;
  position:absolute;
  left:311px;
  top:190px;
  width:80px;
  height:26px;
  display:flex;
}
#u2022 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2022_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:26px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2022.disabled {
}
#u2023_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u2023 {
  border-width:0px;
  position:absolute;
  left:64px;
  top:231px;
  width:139px;
  height:16px;
  display:flex;
  font-size:14px;
}
#u2023 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2023_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2024_input {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2024_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2024_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2024 {
  border-width:0px;
  position:absolute;
  left:211px;
  top:226px;
  width:80px;
  height:26px;
  display:flex;
}
#u2024 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2024_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:26px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2024.disabled {
}
#u2025_input {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2025_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2025_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2025 {
  border-width:0px;
  position:absolute;
  left:311px;
  top:226px;
  width:80px;
  height:26px;
  display:flex;
}
#u2025 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2025_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:26px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2025.disabled {
}
#u2026_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#D9001B;
}
#u2026 {
  border-width:0px;
  position:absolute;
  left:72px;
  top:267px;
  width:131px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#D9001B;
}
#u2026 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2026_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2027_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u2027 {
  border-width:0px;
  position:absolute;
  left:64px;
  top:298px;
  width:62px;
  height:32px;
  display:flex;
  font-size:14px;
}
#u2027 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2027_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2028_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:222px;
  height:117px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:26px;
}
#u2028 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:296px;
  width:222px;
  height:117px;
  display:flex;
  font-size:26px;
}
#u2028 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2028_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2029_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2029 {
  border-width:0px;
  position:absolute;
  left:378px;
  top:68px;
  width:140px;
  height:40px;
  display:flex;
}
#u2029 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2029_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2030_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2030 {
  border-width:0px;
  position:absolute;
  left:538px;
  top:68px;
  width:140px;
  height:40px;
  display:flex;
}
#u2030 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2030_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2031_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2031 {
  border-width:0px;
  position:absolute;
  left:53px;
  top:978px;
  width:98px;
  height:16px;
  display:flex;
}
#u2031 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2031_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2032_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:120px;
}
#u2032 {
  border-width:0px;
  position:absolute;
  left:53px;
  top:999px;
  width:104px;
  height:120px;
  display:flex;
}
#u2032 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2032_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2033_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1300px;
  height:113px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  font-size:14px;
  line-height:20px;
}
#u2033 {
  border-width:0px;
  position:absolute;
  left:42px;
  top:1412px;
  width:1300px;
  height:113px;
  display:flex;
  font-size:14px;
  line-height:20px;
}
#u2033 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2033_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2034_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:461px;
  height:76px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#000000;
  line-height:19px;
}
#u2034 {
  border-width:0px;
  position:absolute;
  left:61px;
  top:1420px;
  width:461px;
  height:76px;
  display:flex;
  font-size:15px;
  color:#000000;
  line-height:19px;
}
#u2034 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2034_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
