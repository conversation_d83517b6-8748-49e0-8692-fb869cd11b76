﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-30px;
  width:1782px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u1130_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1301px;
  height:2px;
}
#u1130 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:37px;
  width:1300px;
  height:1px;
  display:flex;
}
#u1130 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1130_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1131_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:180px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1131 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:7px;
  width:180px;
  height:30px;
  display:flex;
}
#u1131 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1131_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1132_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1132 {
  border-width:0px;
  position:absolute;
  left:1274px;
  top:18px;
  width:56px;
  height:19px;
  display:flex;
}
#u1132 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1132_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1133_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1300px;
  height:100px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1133 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:43px;
  width:1300px;
  height:100px;
  display:flex;
}
#u1133 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1133_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1134_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1134 {
  border-width:0px;
  position:absolute;
  left:792px;
  top:59px;
  width:56px;
  height:16px;
  display:flex;
}
#u1134 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1134_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1135_input {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1135_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1135_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u1135 {
  border-width:0px;
  position:absolute;
  left:858px;
  top:55px;
  width:120px;
  height:24px;
  display:flex;
  text-align:center;
}
#u1135 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1135_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u1135.disabled {
}
#u1136_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1136 {
  border-width:0px;
  position:absolute;
  left:299px;
  top:59px;
  width:56px;
  height:16px;
  display:flex;
}
#u1136 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1136_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1137_input {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1137_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1137_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u1137 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:55px;
  width:120px;
  height:24px;
  display:flex;
  text-align:center;
}
#u1137 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1137_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u1137.disabled {
}
#u1138_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1138 {
  border-width:0px;
  position:absolute;
  left:48px;
  top:59px;
  width:56px;
  height:16px;
  display:flex;
}
#u1138 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1138_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1139_input {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1139_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1139_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1139 {
  border-width:0px;
  position:absolute;
  left:109px;
  top:55px;
  width:120px;
  height:24px;
  display:flex;
}
#u1139 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1139_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1139.disabled {
}
.u1139_input_option {
}
#u1140_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1140 {
  border-width:0px;
  position:absolute;
  left:1059px;
  top:101px;
  width:80px;
  height:30px;
  display:flex;
}
#u1140 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1140_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1141_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1141 {
  border-width:0px;
  position:absolute;
  left:1149px;
  top:101px;
  width:80px;
  height:30px;
  display:flex;
}
#u1141 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1141_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1142_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1142 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:173px;
  width:140px;
  height:30px;
  display:flex;
}
#u1142 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1142_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1143_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1143 {
  border-width:0px;
  position:absolute;
  left:190px;
  top:173px;
  width:80px;
  height:30px;
  display:flex;
}
#u1143 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1143_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1144 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:213px;
  width:1782px;
  height:336px;
}
#u1145_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:32px;
}
#u1145 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:32px;
  display:flex;
}
#u1145 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1145_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1146_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:32px;
}
#u1146 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:0px;
  width:129px;
  height:32px;
  display:flex;
}
#u1146 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1146_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1147_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:32px;
}
#u1147 {
  border-width:0px;
  position:absolute;
  left:170px;
  top:0px;
  width:186px;
  height:32px;
  display:flex;
}
#u1147 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1147_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1148_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:111px;
  height:32px;
}
#u1148 {
  border-width:0px;
  position:absolute;
  left:356px;
  top:0px;
  width:111px;
  height:32px;
  display:flex;
}
#u1148 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1148_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1149_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:113px;
  height:32px;
}
#u1149 {
  border-width:0px;
  position:absolute;
  left:467px;
  top:0px;
  width:113px;
  height:32px;
  display:flex;
}
#u1149 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1149_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1150_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:32px;
}
#u1150 {
  border-width:0px;
  position:absolute;
  left:580px;
  top:0px;
  width:100px;
  height:32px;
  display:flex;
}
#u1150 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1150_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1151_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:32px;
}
#u1151 {
  border-width:0px;
  position:absolute;
  left:680px;
  top:0px;
  width:117px;
  height:32px;
  display:flex;
}
#u1151 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1151_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1152_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:175px;
  height:32px;
}
#u1152 {
  border-width:0px;
  position:absolute;
  left:797px;
  top:0px;
  width:175px;
  height:32px;
  display:flex;
}
#u1152 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1152_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1153_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:127px;
  height:32px;
}
#u1153 {
  border-width:0px;
  position:absolute;
  left:972px;
  top:0px;
  width:127px;
  height:32px;
  display:flex;
}
#u1153 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1153_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1154_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:32px;
}
#u1154 {
  border-width:0px;
  position:absolute;
  left:1099px;
  top:0px;
  width:100px;
  height:32px;
  display:flex;
}
#u1154 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1154_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1155_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:32px;
}
#u1155 {
  border-width:0px;
  position:absolute;
  left:1199px;
  top:0px;
  width:130px;
  height:32px;
  display:flex;
}
#u1155 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1155_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1156_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:32px;
}
#u1156 {
  border-width:0px;
  position:absolute;
  left:1329px;
  top:0px;
  width:114px;
  height:32px;
  display:flex;
}
#u1156 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1156_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1157_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:32px;
}
#u1157 {
  border-width:0px;
  position:absolute;
  left:1443px;
  top:0px;
  width:106px;
  height:32px;
  display:flex;
}
#u1157 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1157_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1158_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:32px;
}
#u1158 {
  border-width:0px;
  position:absolute;
  left:1549px;
  top:0px;
  width:114px;
  height:32px;
  display:flex;
}
#u1158 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1158_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1159_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:32px;
}
#u1159 {
  border-width:0px;
  position:absolute;
  left:1663px;
  top:0px;
  width:119px;
  height:32px;
  display:flex;
}
#u1159 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1159_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1160_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:34px;
}
#u1160 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:32px;
  width:41px;
  height:34px;
  display:flex;
}
#u1160 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1160_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1161_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:34px;
}
#u1161 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:32px;
  width:129px;
  height:34px;
  display:flex;
}
#u1161 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1161_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1162_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:34px;
}
#u1162 {
  border-width:0px;
  position:absolute;
  left:170px;
  top:32px;
  width:186px;
  height:34px;
  display:flex;
}
#u1162 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1162_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1163_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:111px;
  height:34px;
}
#u1163 {
  border-width:0px;
  position:absolute;
  left:356px;
  top:32px;
  width:111px;
  height:34px;
  display:flex;
}
#u1163 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1163_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1164_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:113px;
  height:34px;
}
#u1164 {
  border-width:0px;
  position:absolute;
  left:467px;
  top:32px;
  width:113px;
  height:34px;
  display:flex;
}
#u1164 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1164_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1165_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:34px;
}
#u1165 {
  border-width:0px;
  position:absolute;
  left:580px;
  top:32px;
  width:100px;
  height:34px;
  display:flex;
}
#u1165 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1165_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1166_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:34px;
}
#u1166 {
  border-width:0px;
  position:absolute;
  left:680px;
  top:32px;
  width:117px;
  height:34px;
  display:flex;
}
#u1166 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1166_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1167_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:175px;
  height:34px;
}
#u1167 {
  border-width:0px;
  position:absolute;
  left:797px;
  top:32px;
  width:175px;
  height:34px;
  display:flex;
}
#u1167 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1167_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1168_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:127px;
  height:34px;
}
#u1168 {
  border-width:0px;
  position:absolute;
  left:972px;
  top:32px;
  width:127px;
  height:34px;
  display:flex;
}
#u1168 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1168_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1169_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:34px;
}
#u1169 {
  border-width:0px;
  position:absolute;
  left:1099px;
  top:32px;
  width:100px;
  height:34px;
  display:flex;
}
#u1169 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1169_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1170_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:34px;
}
#u1170 {
  border-width:0px;
  position:absolute;
  left:1199px;
  top:32px;
  width:130px;
  height:34px;
  display:flex;
}
#u1170 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1170_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1171_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:34px;
}
#u1171 {
  border-width:0px;
  position:absolute;
  left:1329px;
  top:32px;
  width:114px;
  height:34px;
  display:flex;
}
#u1171 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1171_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1172_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:34px;
}
#u1172 {
  border-width:0px;
  position:absolute;
  left:1443px;
  top:32px;
  width:106px;
  height:34px;
  display:flex;
}
#u1172 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1172_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1173_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:34px;
}
#u1173 {
  border-width:0px;
  position:absolute;
  left:1549px;
  top:32px;
  width:114px;
  height:34px;
  display:flex;
}
#u1173 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1173_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1174_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:34px;
}
#u1174 {
  border-width:0px;
  position:absolute;
  left:1663px;
  top:32px;
  width:119px;
  height:34px;
  display:flex;
}
#u1174 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1174_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1175_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:30px;
}
#u1175 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:66px;
  width:41px;
  height:30px;
  display:flex;
}
#u1175 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1175_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1176_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:30px;
}
#u1176 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:66px;
  width:129px;
  height:30px;
  display:flex;
}
#u1176 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1176_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1177_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:30px;
}
#u1177 {
  border-width:0px;
  position:absolute;
  left:170px;
  top:66px;
  width:186px;
  height:30px;
  display:flex;
}
#u1177 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1177_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1178_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:111px;
  height:30px;
}
#u1178 {
  border-width:0px;
  position:absolute;
  left:356px;
  top:66px;
  width:111px;
  height:30px;
  display:flex;
}
#u1178 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1178_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1179_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:113px;
  height:30px;
}
#u1179 {
  border-width:0px;
  position:absolute;
  left:467px;
  top:66px;
  width:113px;
  height:30px;
  display:flex;
}
#u1179 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1179_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1180_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u1180 {
  border-width:0px;
  position:absolute;
  left:580px;
  top:66px;
  width:100px;
  height:30px;
  display:flex;
}
#u1180 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1180_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1181_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u1181 {
  border-width:0px;
  position:absolute;
  left:680px;
  top:66px;
  width:117px;
  height:30px;
  display:flex;
}
#u1181 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1181_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1182_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:175px;
  height:30px;
}
#u1182 {
  border-width:0px;
  position:absolute;
  left:797px;
  top:66px;
  width:175px;
  height:30px;
  display:flex;
}
#u1182 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1182_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1183_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:127px;
  height:30px;
}
#u1183 {
  border-width:0px;
  position:absolute;
  left:972px;
  top:66px;
  width:127px;
  height:30px;
  display:flex;
}
#u1183 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1183_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1184_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u1184 {
  border-width:0px;
  position:absolute;
  left:1099px;
  top:66px;
  width:100px;
  height:30px;
  display:flex;
}
#u1184 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1184_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1185_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:30px;
}
#u1185 {
  border-width:0px;
  position:absolute;
  left:1199px;
  top:66px;
  width:130px;
  height:30px;
  display:flex;
}
#u1185 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1185_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1186_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u1186 {
  border-width:0px;
  position:absolute;
  left:1329px;
  top:66px;
  width:114px;
  height:30px;
  display:flex;
}
#u1186 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1186_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1187_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:30px;
}
#u1187 {
  border-width:0px;
  position:absolute;
  left:1443px;
  top:66px;
  width:106px;
  height:30px;
  display:flex;
}
#u1187 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1187_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1188_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u1188 {
  border-width:0px;
  position:absolute;
  left:1549px;
  top:66px;
  width:114px;
  height:30px;
  display:flex;
}
#u1188 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1188_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1189_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
}
#u1189 {
  border-width:0px;
  position:absolute;
  left:1663px;
  top:66px;
  width:119px;
  height:30px;
  display:flex;
}
#u1189 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1189_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1190_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:30px;
}
#u1190 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:96px;
  width:41px;
  height:30px;
  display:flex;
}
#u1190 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1190_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1191_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:30px;
}
#u1191 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:96px;
  width:129px;
  height:30px;
  display:flex;
}
#u1191 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1191_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1192_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:30px;
}
#u1192 {
  border-width:0px;
  position:absolute;
  left:170px;
  top:96px;
  width:186px;
  height:30px;
  display:flex;
}
#u1192 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1192_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1193_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:111px;
  height:30px;
}
#u1193 {
  border-width:0px;
  position:absolute;
  left:356px;
  top:96px;
  width:111px;
  height:30px;
  display:flex;
}
#u1193 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1193_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1194_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:113px;
  height:30px;
}
#u1194 {
  border-width:0px;
  position:absolute;
  left:467px;
  top:96px;
  width:113px;
  height:30px;
  display:flex;
}
#u1194 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1194_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1195_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u1195 {
  border-width:0px;
  position:absolute;
  left:580px;
  top:96px;
  width:100px;
  height:30px;
  display:flex;
}
#u1195 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1195_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1196_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u1196 {
  border-width:0px;
  position:absolute;
  left:680px;
  top:96px;
  width:117px;
  height:30px;
  display:flex;
}
#u1196 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1196_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1197_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:175px;
  height:30px;
}
#u1197 {
  border-width:0px;
  position:absolute;
  left:797px;
  top:96px;
  width:175px;
  height:30px;
  display:flex;
}
#u1197 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1197_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1198_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:127px;
  height:30px;
}
#u1198 {
  border-width:0px;
  position:absolute;
  left:972px;
  top:96px;
  width:127px;
  height:30px;
  display:flex;
}
#u1198 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1198_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1199_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u1199 {
  border-width:0px;
  position:absolute;
  left:1099px;
  top:96px;
  width:100px;
  height:30px;
  display:flex;
}
#u1199 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1199_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1200_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:30px;
}
#u1200 {
  border-width:0px;
  position:absolute;
  left:1199px;
  top:96px;
  width:130px;
  height:30px;
  display:flex;
}
#u1200 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1200_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1201_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u1201 {
  border-width:0px;
  position:absolute;
  left:1329px;
  top:96px;
  width:114px;
  height:30px;
  display:flex;
}
#u1201 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1201_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1202_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:30px;
}
#u1202 {
  border-width:0px;
  position:absolute;
  left:1443px;
  top:96px;
  width:106px;
  height:30px;
  display:flex;
}
#u1202 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1202_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1203_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u1203 {
  border-width:0px;
  position:absolute;
  left:1549px;
  top:96px;
  width:114px;
  height:30px;
  display:flex;
}
#u1203 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1203_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1204_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
}
#u1204 {
  border-width:0px;
  position:absolute;
  left:1663px;
  top:96px;
  width:119px;
  height:30px;
  display:flex;
}
#u1204 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1204_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1205_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:30px;
}
#u1205 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:126px;
  width:41px;
  height:30px;
  display:flex;
}
#u1205 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1205_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1206_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:30px;
}
#u1206 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:126px;
  width:129px;
  height:30px;
  display:flex;
}
#u1206 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1206_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1207_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:30px;
}
#u1207 {
  border-width:0px;
  position:absolute;
  left:170px;
  top:126px;
  width:186px;
  height:30px;
  display:flex;
}
#u1207 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1207_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1208_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:111px;
  height:30px;
}
#u1208 {
  border-width:0px;
  position:absolute;
  left:356px;
  top:126px;
  width:111px;
  height:30px;
  display:flex;
}
#u1208 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1208_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1209_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:113px;
  height:30px;
}
#u1209 {
  border-width:0px;
  position:absolute;
  left:467px;
  top:126px;
  width:113px;
  height:30px;
  display:flex;
}
#u1209 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1209_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1210_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u1210 {
  border-width:0px;
  position:absolute;
  left:580px;
  top:126px;
  width:100px;
  height:30px;
  display:flex;
}
#u1210 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1210_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1211_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u1211 {
  border-width:0px;
  position:absolute;
  left:680px;
  top:126px;
  width:117px;
  height:30px;
  display:flex;
}
#u1211 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1211_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1212_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:175px;
  height:30px;
}
#u1212 {
  border-width:0px;
  position:absolute;
  left:797px;
  top:126px;
  width:175px;
  height:30px;
  display:flex;
}
#u1212 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1212_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1213_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:127px;
  height:30px;
}
#u1213 {
  border-width:0px;
  position:absolute;
  left:972px;
  top:126px;
  width:127px;
  height:30px;
  display:flex;
}
#u1213 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1213_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1214_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u1214 {
  border-width:0px;
  position:absolute;
  left:1099px;
  top:126px;
  width:100px;
  height:30px;
  display:flex;
}
#u1214 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1214_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1215_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:30px;
}
#u1215 {
  border-width:0px;
  position:absolute;
  left:1199px;
  top:126px;
  width:130px;
  height:30px;
  display:flex;
}
#u1215 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1215_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1216_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u1216 {
  border-width:0px;
  position:absolute;
  left:1329px;
  top:126px;
  width:114px;
  height:30px;
  display:flex;
}
#u1216 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1216_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1217_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:30px;
}
#u1217 {
  border-width:0px;
  position:absolute;
  left:1443px;
  top:126px;
  width:106px;
  height:30px;
  display:flex;
}
#u1217 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1217_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1218_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u1218 {
  border-width:0px;
  position:absolute;
  left:1549px;
  top:126px;
  width:114px;
  height:30px;
  display:flex;
}
#u1218 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1218_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1219_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
}
#u1219 {
  border-width:0px;
  position:absolute;
  left:1663px;
  top:126px;
  width:119px;
  height:30px;
  display:flex;
}
#u1219 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1219_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1220_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:30px;
}
#u1220 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:156px;
  width:41px;
  height:30px;
  display:flex;
}
#u1220 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1220_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1221_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:30px;
}
#u1221 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:156px;
  width:129px;
  height:30px;
  display:flex;
}
#u1221 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1221_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1222_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:30px;
}
#u1222 {
  border-width:0px;
  position:absolute;
  left:170px;
  top:156px;
  width:186px;
  height:30px;
  display:flex;
}
#u1222 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1222_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1223_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:111px;
  height:30px;
}
#u1223 {
  border-width:0px;
  position:absolute;
  left:356px;
  top:156px;
  width:111px;
  height:30px;
  display:flex;
}
#u1223 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1223_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1224_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:113px;
  height:30px;
}
#u1224 {
  border-width:0px;
  position:absolute;
  left:467px;
  top:156px;
  width:113px;
  height:30px;
  display:flex;
}
#u1224 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1224_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1225_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u1225 {
  border-width:0px;
  position:absolute;
  left:580px;
  top:156px;
  width:100px;
  height:30px;
  display:flex;
}
#u1225 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1225_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1226_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u1226 {
  border-width:0px;
  position:absolute;
  left:680px;
  top:156px;
  width:117px;
  height:30px;
  display:flex;
}
#u1226 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1226_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1227_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:175px;
  height:30px;
}
#u1227 {
  border-width:0px;
  position:absolute;
  left:797px;
  top:156px;
  width:175px;
  height:30px;
  display:flex;
}
#u1227 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1227_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1228_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:127px;
  height:30px;
}
#u1228 {
  border-width:0px;
  position:absolute;
  left:972px;
  top:156px;
  width:127px;
  height:30px;
  display:flex;
}
#u1228 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1228_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1229_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u1229 {
  border-width:0px;
  position:absolute;
  left:1099px;
  top:156px;
  width:100px;
  height:30px;
  display:flex;
}
#u1229 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1229_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1230_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:30px;
}
#u1230 {
  border-width:0px;
  position:absolute;
  left:1199px;
  top:156px;
  width:130px;
  height:30px;
  display:flex;
}
#u1230 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1230_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1231_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u1231 {
  border-width:0px;
  position:absolute;
  left:1329px;
  top:156px;
  width:114px;
  height:30px;
  display:flex;
}
#u1231 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1231_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1232_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:30px;
}
#u1232 {
  border-width:0px;
  position:absolute;
  left:1443px;
  top:156px;
  width:106px;
  height:30px;
  display:flex;
}
#u1232 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1232_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1233_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u1233 {
  border-width:0px;
  position:absolute;
  left:1549px;
  top:156px;
  width:114px;
  height:30px;
  display:flex;
}
#u1233 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1233_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1234_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
}
#u1234 {
  border-width:0px;
  position:absolute;
  left:1663px;
  top:156px;
  width:119px;
  height:30px;
  display:flex;
}
#u1234 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1234_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1235_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:30px;
}
#u1235 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:186px;
  width:41px;
  height:30px;
  display:flex;
}
#u1235 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1235_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1236_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:30px;
}
#u1236 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:186px;
  width:129px;
  height:30px;
  display:flex;
}
#u1236 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1236_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1237_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:30px;
}
#u1237 {
  border-width:0px;
  position:absolute;
  left:170px;
  top:186px;
  width:186px;
  height:30px;
  display:flex;
}
#u1237 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1237_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1238_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:111px;
  height:30px;
}
#u1238 {
  border-width:0px;
  position:absolute;
  left:356px;
  top:186px;
  width:111px;
  height:30px;
  display:flex;
}
#u1238 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1238_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1239_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:113px;
  height:30px;
}
#u1239 {
  border-width:0px;
  position:absolute;
  left:467px;
  top:186px;
  width:113px;
  height:30px;
  display:flex;
}
#u1239 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1239_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1240_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u1240 {
  border-width:0px;
  position:absolute;
  left:580px;
  top:186px;
  width:100px;
  height:30px;
  display:flex;
}
#u1240 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1240_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1241_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u1241 {
  border-width:0px;
  position:absolute;
  left:680px;
  top:186px;
  width:117px;
  height:30px;
  display:flex;
}
#u1241 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1241_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1242_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:175px;
  height:30px;
}
#u1242 {
  border-width:0px;
  position:absolute;
  left:797px;
  top:186px;
  width:175px;
  height:30px;
  display:flex;
}
#u1242 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1242_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1243_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:127px;
  height:30px;
}
#u1243 {
  border-width:0px;
  position:absolute;
  left:972px;
  top:186px;
  width:127px;
  height:30px;
  display:flex;
}
#u1243 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1243_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1244_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u1244 {
  border-width:0px;
  position:absolute;
  left:1099px;
  top:186px;
  width:100px;
  height:30px;
  display:flex;
}
#u1244 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1244_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1245_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:30px;
}
#u1245 {
  border-width:0px;
  position:absolute;
  left:1199px;
  top:186px;
  width:130px;
  height:30px;
  display:flex;
}
#u1245 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1245_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1246_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u1246 {
  border-width:0px;
  position:absolute;
  left:1329px;
  top:186px;
  width:114px;
  height:30px;
  display:flex;
}
#u1246 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1246_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1247_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:30px;
}
#u1247 {
  border-width:0px;
  position:absolute;
  left:1443px;
  top:186px;
  width:106px;
  height:30px;
  display:flex;
}
#u1247 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1247_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1248_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u1248 {
  border-width:0px;
  position:absolute;
  left:1549px;
  top:186px;
  width:114px;
  height:30px;
  display:flex;
}
#u1248 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1248_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1249_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
}
#u1249 {
  border-width:0px;
  position:absolute;
  left:1663px;
  top:186px;
  width:119px;
  height:30px;
  display:flex;
}
#u1249 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1249_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1250_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:30px;
}
#u1250 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:216px;
  width:41px;
  height:30px;
  display:flex;
}
#u1250 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1250_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1251_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:30px;
}
#u1251 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:216px;
  width:129px;
  height:30px;
  display:flex;
}
#u1251 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1251_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1252_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:30px;
}
#u1252 {
  border-width:0px;
  position:absolute;
  left:170px;
  top:216px;
  width:186px;
  height:30px;
  display:flex;
}
#u1252 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1252_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1253_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:111px;
  height:30px;
}
#u1253 {
  border-width:0px;
  position:absolute;
  left:356px;
  top:216px;
  width:111px;
  height:30px;
  display:flex;
}
#u1253 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1253_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1254_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:113px;
  height:30px;
}
#u1254 {
  border-width:0px;
  position:absolute;
  left:467px;
  top:216px;
  width:113px;
  height:30px;
  display:flex;
}
#u1254 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1254_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1255_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u1255 {
  border-width:0px;
  position:absolute;
  left:580px;
  top:216px;
  width:100px;
  height:30px;
  display:flex;
}
#u1255 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1255_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1256_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u1256 {
  border-width:0px;
  position:absolute;
  left:680px;
  top:216px;
  width:117px;
  height:30px;
  display:flex;
}
#u1256 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1256_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1257_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:175px;
  height:30px;
}
#u1257 {
  border-width:0px;
  position:absolute;
  left:797px;
  top:216px;
  width:175px;
  height:30px;
  display:flex;
}
#u1257 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1257_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1258_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:127px;
  height:30px;
}
#u1258 {
  border-width:0px;
  position:absolute;
  left:972px;
  top:216px;
  width:127px;
  height:30px;
  display:flex;
}
#u1258 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1258_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1259_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u1259 {
  border-width:0px;
  position:absolute;
  left:1099px;
  top:216px;
  width:100px;
  height:30px;
  display:flex;
}
#u1259 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1259_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1260_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:30px;
}
#u1260 {
  border-width:0px;
  position:absolute;
  left:1199px;
  top:216px;
  width:130px;
  height:30px;
  display:flex;
}
#u1260 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1260_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1261_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u1261 {
  border-width:0px;
  position:absolute;
  left:1329px;
  top:216px;
  width:114px;
  height:30px;
  display:flex;
}
#u1261 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1261_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1262_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:30px;
}
#u1262 {
  border-width:0px;
  position:absolute;
  left:1443px;
  top:216px;
  width:106px;
  height:30px;
  display:flex;
}
#u1262 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1262_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1263_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u1263 {
  border-width:0px;
  position:absolute;
  left:1549px;
  top:216px;
  width:114px;
  height:30px;
  display:flex;
}
#u1263 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1263_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1264_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
}
#u1264 {
  border-width:0px;
  position:absolute;
  left:1663px;
  top:216px;
  width:119px;
  height:30px;
  display:flex;
}
#u1264 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1264_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1265_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:30px;
}
#u1265 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:246px;
  width:41px;
  height:30px;
  display:flex;
}
#u1265 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1265_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1266_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:30px;
}
#u1266 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:246px;
  width:129px;
  height:30px;
  display:flex;
}
#u1266 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1266_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1267_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:30px;
}
#u1267 {
  border-width:0px;
  position:absolute;
  left:170px;
  top:246px;
  width:186px;
  height:30px;
  display:flex;
}
#u1267 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1267_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1268_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:111px;
  height:30px;
}
#u1268 {
  border-width:0px;
  position:absolute;
  left:356px;
  top:246px;
  width:111px;
  height:30px;
  display:flex;
}
#u1268 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1268_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1269_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:113px;
  height:30px;
}
#u1269 {
  border-width:0px;
  position:absolute;
  left:467px;
  top:246px;
  width:113px;
  height:30px;
  display:flex;
}
#u1269 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1269_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1270_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u1270 {
  border-width:0px;
  position:absolute;
  left:580px;
  top:246px;
  width:100px;
  height:30px;
  display:flex;
}
#u1270 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1270_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1271_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u1271 {
  border-width:0px;
  position:absolute;
  left:680px;
  top:246px;
  width:117px;
  height:30px;
  display:flex;
}
#u1271 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1271_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1272_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:175px;
  height:30px;
}
#u1272 {
  border-width:0px;
  position:absolute;
  left:797px;
  top:246px;
  width:175px;
  height:30px;
  display:flex;
}
#u1272 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1272_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1273_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:127px;
  height:30px;
}
#u1273 {
  border-width:0px;
  position:absolute;
  left:972px;
  top:246px;
  width:127px;
  height:30px;
  display:flex;
}
#u1273 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1273_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1274_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u1274 {
  border-width:0px;
  position:absolute;
  left:1099px;
  top:246px;
  width:100px;
  height:30px;
  display:flex;
}
#u1274 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1274_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1275_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:30px;
}
#u1275 {
  border-width:0px;
  position:absolute;
  left:1199px;
  top:246px;
  width:130px;
  height:30px;
  display:flex;
}
#u1275 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1275_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1276_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u1276 {
  border-width:0px;
  position:absolute;
  left:1329px;
  top:246px;
  width:114px;
  height:30px;
  display:flex;
}
#u1276 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1276_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1277_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:30px;
}
#u1277 {
  border-width:0px;
  position:absolute;
  left:1443px;
  top:246px;
  width:106px;
  height:30px;
  display:flex;
}
#u1277 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1277_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1278_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u1278 {
  border-width:0px;
  position:absolute;
  left:1549px;
  top:246px;
  width:114px;
  height:30px;
  display:flex;
}
#u1278 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1278_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1279_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
}
#u1279 {
  border-width:0px;
  position:absolute;
  left:1663px;
  top:246px;
  width:119px;
  height:30px;
  display:flex;
}
#u1279 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1279_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1280_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:30px;
}
#u1280 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:276px;
  width:41px;
  height:30px;
  display:flex;
}
#u1280 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1280_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1281_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:30px;
}
#u1281 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:276px;
  width:129px;
  height:30px;
  display:flex;
}
#u1281 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1281_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1282_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:30px;
}
#u1282 {
  border-width:0px;
  position:absolute;
  left:170px;
  top:276px;
  width:186px;
  height:30px;
  display:flex;
}
#u1282 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1282_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1283_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:111px;
  height:30px;
}
#u1283 {
  border-width:0px;
  position:absolute;
  left:356px;
  top:276px;
  width:111px;
  height:30px;
  display:flex;
}
#u1283 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1283_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1284_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:113px;
  height:30px;
}
#u1284 {
  border-width:0px;
  position:absolute;
  left:467px;
  top:276px;
  width:113px;
  height:30px;
  display:flex;
}
#u1284 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1284_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1285_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u1285 {
  border-width:0px;
  position:absolute;
  left:580px;
  top:276px;
  width:100px;
  height:30px;
  display:flex;
}
#u1285 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1285_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1286_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u1286 {
  border-width:0px;
  position:absolute;
  left:680px;
  top:276px;
  width:117px;
  height:30px;
  display:flex;
}
#u1286 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1286_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1287_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:175px;
  height:30px;
}
#u1287 {
  border-width:0px;
  position:absolute;
  left:797px;
  top:276px;
  width:175px;
  height:30px;
  display:flex;
}
#u1287 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1287_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1288_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:127px;
  height:30px;
}
#u1288 {
  border-width:0px;
  position:absolute;
  left:972px;
  top:276px;
  width:127px;
  height:30px;
  display:flex;
}
#u1288 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1288_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1289_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u1289 {
  border-width:0px;
  position:absolute;
  left:1099px;
  top:276px;
  width:100px;
  height:30px;
  display:flex;
}
#u1289 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1289_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1290_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:30px;
}
#u1290 {
  border-width:0px;
  position:absolute;
  left:1199px;
  top:276px;
  width:130px;
  height:30px;
  display:flex;
}
#u1290 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1290_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1291_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u1291 {
  border-width:0px;
  position:absolute;
  left:1329px;
  top:276px;
  width:114px;
  height:30px;
  display:flex;
}
#u1291 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1291_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1292_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:30px;
}
#u1292 {
  border-width:0px;
  position:absolute;
  left:1443px;
  top:276px;
  width:106px;
  height:30px;
  display:flex;
}
#u1292 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1292_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1293_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u1293 {
  border-width:0px;
  position:absolute;
  left:1549px;
  top:276px;
  width:114px;
  height:30px;
  display:flex;
}
#u1293 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1293_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1294_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
}
#u1294 {
  border-width:0px;
  position:absolute;
  left:1663px;
  top:276px;
  width:119px;
  height:30px;
  display:flex;
}
#u1294 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1294_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1295_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:30px;
}
#u1295 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:306px;
  width:41px;
  height:30px;
  display:flex;
}
#u1295 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1295_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1296_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:30px;
}
#u1296 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:306px;
  width:129px;
  height:30px;
  display:flex;
}
#u1296 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1296_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1297_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:30px;
}
#u1297 {
  border-width:0px;
  position:absolute;
  left:170px;
  top:306px;
  width:186px;
  height:30px;
  display:flex;
}
#u1297 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1297_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1298_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:111px;
  height:30px;
}
#u1298 {
  border-width:0px;
  position:absolute;
  left:356px;
  top:306px;
  width:111px;
  height:30px;
  display:flex;
}
#u1298 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1298_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1299_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:113px;
  height:30px;
}
#u1299 {
  border-width:0px;
  position:absolute;
  left:467px;
  top:306px;
  width:113px;
  height:30px;
  display:flex;
}
#u1299 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1299_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1300_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u1300 {
  border-width:0px;
  position:absolute;
  left:580px;
  top:306px;
  width:100px;
  height:30px;
  display:flex;
}
#u1300 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1300_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1301_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u1301 {
  border-width:0px;
  position:absolute;
  left:680px;
  top:306px;
  width:117px;
  height:30px;
  display:flex;
}
#u1301 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1301_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1302_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:175px;
  height:30px;
}
#u1302 {
  border-width:0px;
  position:absolute;
  left:797px;
  top:306px;
  width:175px;
  height:30px;
  display:flex;
}
#u1302 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1302_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1303_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:127px;
  height:30px;
}
#u1303 {
  border-width:0px;
  position:absolute;
  left:972px;
  top:306px;
  width:127px;
  height:30px;
  display:flex;
}
#u1303 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1303_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1304_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u1304 {
  border-width:0px;
  position:absolute;
  left:1099px;
  top:306px;
  width:100px;
  height:30px;
  display:flex;
}
#u1304 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1304_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1305_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:30px;
}
#u1305 {
  border-width:0px;
  position:absolute;
  left:1199px;
  top:306px;
  width:130px;
  height:30px;
  display:flex;
}
#u1305 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1305_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1306_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u1306 {
  border-width:0px;
  position:absolute;
  left:1329px;
  top:306px;
  width:114px;
  height:30px;
  display:flex;
}
#u1306 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1306_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1307_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:30px;
}
#u1307 {
  border-width:0px;
  position:absolute;
  left:1443px;
  top:306px;
  width:106px;
  height:30px;
  display:flex;
}
#u1307 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1307_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1308_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:30px;
}
#u1308 {
  border-width:0px;
  position:absolute;
  left:1549px;
  top:306px;
  width:114px;
  height:30px;
  display:flex;
}
#u1308 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1308_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1309_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
}
#u1309 {
  border-width:0px;
  position:absolute;
  left:1663px;
  top:306px;
  width:119px;
  height:30px;
  display:flex;
}
#u1309 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1309_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1310_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1310 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:569px;
  width:57px;
  height:16px;
  display:flex;
}
#u1310 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1310_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1311_input {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:22px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1311_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:22px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1311_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1311 {
  border-width:0px;
  position:absolute;
  left:97px;
  top:563px;
  width:80px;
  height:22px;
  display:flex;
}
#u1311 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1311_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:22px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1311.disabled {
}
.u1311_input_option {
}
#u1312_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1312 {
  border-width:0px;
  position:absolute;
  left:187px;
  top:569px;
  width:168px;
  height:16px;
  display:flex;
}
#u1312 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1312_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1313_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1313 {
  border-width:0px;
  position:absolute;
  left:365px;
  top:569px;
  width:28px;
  height:16px;
  display:flex;
}
#u1313 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1313_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1314_input {
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:22px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1314_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:22px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1314_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u1314 {
  border-width:0px;
  position:absolute;
  left:398px;
  top:563px;
  width:30px;
  height:22px;
  display:flex;
  text-align:center;
}
#u1314 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1314_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:22px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u1314.disabled {
}
#u1315_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1315 {
  border-width:0px;
  position:absolute;
  left:433px;
  top:569px;
  width:14px;
  height:16px;
  display:flex;
}
#u1315 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1315_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1316_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1316 {
  border-width:0px;
  position:absolute;
  left:1763px;
  top:254px;
  width:28px;
  height:16px;
  display:flex;
}
#u1316 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1316_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1317_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1317 {
  border-width:0px;
  position:absolute;
  left:1715px;
  top:254px;
  width:28px;
  height:16px;
  display:flex;
}
#u1317 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1317_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1318_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1318 {
  border-width:0px;
  position:absolute;
  left:550px;
  top:59px;
  width:42px;
  height:16px;
  display:flex;
}
#u1318 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1318_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1319_input {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1319_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1319_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u1319 {
  border-width:0px;
  position:absolute;
  left:602px;
  top:55px;
  width:120px;
  height:24px;
  display:flex;
  text-align:center;
}
#u1319 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1319_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u1319.disabled {
}
#u1320_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1300px;
  height:189px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  font-size:14px;
  line-height:20px;
}
#u1320 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:638px;
  width:1300px;
  height:189px;
  display:flex;
  font-size:14px;
  line-height:20px;
}
#u1320 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u1320_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1321_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:858px;
  height:114px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#000000;
  line-height:19px;
}
#u1321 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:646px;
  width:858px;
  height:114px;
  display:flex;
  font-size:15px;
  color:#000000;
  line-height:19px;
}
#u1321 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1321_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
