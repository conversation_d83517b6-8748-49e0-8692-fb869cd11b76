# 出入库管理系统API使用示例

## 概述

本文档提供了出入库管理系统的API使用示例和测试用例。

## 单号生成服务示例

### 1. 生成出库单号

```java
@Autowired
private OrderNumberService orderNumberService;

// 生成出库单号
String orderNo = orderNumberService.generateOutboundOrderNo();
// 结果示例：*********0001

// 获取当日已生成的单号数量
Integer count = orderNumberService.getTodayOrderCount();
```

## 出库单管理示例

### 1. 创建出库单

```java
@Autowired
private OutboundOrderService outboundOrderService;

// 创建出库单
OutboundOrder order = new OutboundOrder();
order.setOutboundType(OutboundTypeEnum.SALES.getCode()); // 销售出库
order.setOutboundCompanyId(1L);
order.setOutboundCompanyName("易托盘科技有限公司");
order.setOutboundAddress("上海市浦东新区张江高科技园区");
order.setDeliveryMethod("物流配送");
order.setVehicleNumber("沪A12345");
order.setDriverName("张师傅");
order.setDriverPhone("13800138001");
order.setReceiveCompanyId(2L);
order.setReceiveCompanyName("客户公司A");
order.setReceiveAddress("北京市朝阳区建国门外大街1号");
order.setFirstCategory(FirstCategoryEnum.SHARED_PALLET.getCode());
order.setSecondCategory("标准托盘");
order.setPlannedQuantity(100);
order.setCreatedBy("admin");

boolean success = outboundOrderService.createOutboundOrder(order);
```

### 2. 分页查询出库单

```java
// 分页查询
Page<OutboundOrder> page = new Page<>(1, 10);
IPage<OutboundOrder> result = outboundOrderService.selectOutboundOrderPage(
    page, 
    "*********", // 单号模糊查询
    OutboundStatusEnum.PENDING.getCode(), // 待出库状态
    OutboundTypeEnum.SALES.getCode(), // 销售出库
    1L, // 出库公司ID
    null, // 收货公司ID
    FirstCategoryEnum.SHARED_PALLET.getCode(), // 一级类目
    LocalDateTime.now().minusDays(7), // 开始时间
    LocalDateTime.now() // 结束时间
);
```

### 3. 出库状态流转

```java
// 确认出库（待出库 → 运输中）
boolean confirmed = outboundOrderService.confirmOutbound(
    orderId, 
    95, // 实际出库数
    "admin" // 操作人
);

// 完成出库（运输中 → 已出库）
boolean completed = outboundOrderService.completeOutbound(orderId, "admin");

// 取消出库（运输中 → 待出库）
boolean cancelled = outboundOrderService.cancelOutbound(orderId, "admin");
```

### 4. 查询统计信息

```java
// 查询各状态的出库单数量
List<Map<String, Object>> statusStats = outboundOrderService.getStatusStatistics();
// 结果示例：[{status=1, count=10}, {status=2, count=5}, {status=3, count=20}]

// 查询各类型的出库单数量
List<Map<String, Object>> typeStats = outboundOrderService.getTypeStatistics();
// 结果示例：[{outbound_type=1, count=25}, {outbound_type=2, count=10}]

// 查询待出库的订单
List<OutboundOrder> pendingOrders = outboundOrderService.getPendingOrders();

// 查询运输中的订单
List<OutboundOrder> inTransitOrders = outboundOrderService.getInTransitOrders();
```

## 入库单管理示例

### 1. 创建入库单

```java
@Autowired
private InboundOrderService inboundOrderService;

// 创建入库单
InboundOrder order = new InboundOrder();
order.setOrderNo("*********0001"); // 与出库单号一致
order.setInboundType(InboundTypeEnum.SALES_RETURN.getCode()); // 销售入库
order.setInboundWarehouseId(2L);
order.setInboundWarehouseName("北京仓库");
order.setDeliveryMethod("物流配送");
order.setVehicleNumber("沪A12345");
order.setDriverName("张师傅");
order.setDriverPhone("13800138001");
order.setSenderWarehouseId(1L);
order.setSenderWarehouseName("上海总仓");
order.setSenderAddress("上海市浦东新区张江高科技园区");
order.setFirstCategory(FirstCategoryEnum.SHARED_PALLET.getCode());
order.setSecondCategory("标准托盘");
order.setPlannedQuantity(100);
order.setOutboundOrderId(outboundOrderId); // 关联出库单ID
order.setCreatedBy("admin");

boolean success = inboundOrderService.createInboundOrder(order);
```

### 2. 入库状态流转

```java
// 部分入库（待入库 → 部分入库）
boolean partialInbound = inboundOrderService.partialInbound(
    orderId, 
    50, // 实际入库数
    "admin" // 操作人
);

// 完成入库（部分入库 → 已入库）
boolean completed = inboundOrderService.completeInbound(
    orderId, 
    100, // 最终入库总数
    "admin" // 操作人
);
```

## 业务场景示例

### 1. 销售出库完整流程

```java
// 1. 创建销售出库单
OutboundOrder outboundOrder = new OutboundOrder();
// ... 设置出库单信息
outboundOrderService.createOutboundOrder(outboundOrder);

// 2. 创建对应的入库单（客户仓库入库）
InboundOrder inboundOrder = new InboundOrder();
inboundOrder.setOrderNo(outboundOrder.getOrderNo()); // 使用相同单号
inboundOrder.setInboundType(InboundTypeEnum.SALES_RETURN.getCode());
// ... 设置入库单信息
inboundOrderService.createInboundOrder(inboundOrder);

// 3. 确认出库
outboundOrderService.confirmOutbound(outboundOrder.getId(), 95, "admin");

// 4. 完成出库
outboundOrderService.completeOutbound(outboundOrder.getId(), "admin");

// 5. 客户确认入库
inboundOrderService.completeInbound(inboundOrder.getId(), 95, "admin");
```

### 2. 仓库调拨流程

```java
// 1. 创建调拨出库单
OutboundOrder transferOut = new OutboundOrder();
transferOut.setOutboundType(OutboundTypeEnum.TRANSFER.getCode());
// ... 设置出库信息
outboundOrderService.createOutboundOrder(transferOut);

// 2. 创建调拨入库单
InboundOrder transferIn = new InboundOrder();
transferIn.setOrderNo(transferOut.getOrderNo());
transferIn.setInboundType(InboundTypeEnum.TRANSFER.getCode());
// ... 设置入库信息
inboundOrderService.createInboundOrder(transferIn);

// 3. 执行调拨流程
outboundOrderService.confirmOutbound(transferOut.getId(), 100, "admin");
outboundOrderService.completeOutbound(transferOut.getId(), "admin");
inboundOrderService.completeInbound(transferIn.getId(), 100, "admin");
```

## 错误处理示例

### 1. 状态流转错误

```java
try {
    // 尝试对已完成的订单再次确认出库
    outboundOrderService.confirmOutbound(orderId, 100, "admin");
} catch (RuntimeException e) {
    // 捕获异常：只有待出库状态的订单才能确认出库
    log.error("出库确认失败：{}", e.getMessage());
}
```

### 2. 单号重复错误

```java
try {
    OutboundOrder order = new OutboundOrder();
    order.setOrderNo("*********0001"); // 已存在的单号
    outboundOrderService.createOutboundOrder(order);
} catch (RuntimeException e) {
    // 捕获异常：出库单号已存在
    log.error("创建出库单失败：{}", e.getMessage());
}
```

## 数据验证示例

### 1. 检查单号是否存在

```java
// 检查出库单号是否存在
boolean exists = outboundOrderService.checkOrderNoExists("*********0001", null);

// 检查时排除指定ID
boolean exists2 = outboundOrderService.checkOrderNoExists("*********0001", 1L);
```

### 2. 状态枚举验证

```java
// 验证出库状态是否有效
boolean validStatus = OutboundStatusEnum.isValid(1); // true
boolean invalidStatus = OutboundStatusEnum.isValid(99); // false

// 获取状态描述
String statusDesc = OutboundStatusEnum.getDescByCode(1); // "待出库"
```

## 性能优化建议

### 1. 分页查询优化

```java
// 使用索引字段进行查询
Page<OutboundOrder> page = new Page<>(1, 20); // 适当的分页大小
IPage<OutboundOrder> result = outboundOrderService.selectOutboundOrderPage(
    page, 
    null, // 避免模糊查询
    OutboundStatusEnum.PENDING.getCode(), // 使用索引字段
    null, 
    1L, // 使用索引字段
    null, 
    null, 
    startTime, // 使用索引字段
    endTime
);
```

### 2. 批量操作

```java
// 批量删除
List<Long> ids = Arrays.asList(1L, 2L, 3L);
outboundOrderService.deleteOutboundOrders(ids);
```

## 注意事项

1. **事务管理**：状态流转操作都使用了事务，确保数据一致性
2. **并发安全**：单号生成使用数据库锁，保证并发安全
3. **状态验证**：严格按照业务流程进行状态流转验证
4. **数据完整性**：创建订单时会自动设置默认值和必要字段

这些示例展示了出入库管理系统的主要功能和使用方法，可以根据实际业务需求进行调整和扩展。
