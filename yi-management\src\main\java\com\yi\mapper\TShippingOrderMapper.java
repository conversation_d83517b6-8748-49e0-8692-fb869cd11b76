package com.yi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yi.entity.TShippingOrder;
import com.yi.mapper.vo.ShippingOrderPageVO;
import com.yi.controller.shippingorder.model.ShippingOrderQueryRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 发运订单表 Mapper 接口
 */
@Mapper
public interface TShippingOrderMapper extends BaseMapper<TShippingOrder> {

    /**
     * 分页查询发运订单列表
     *
     * @param page 分页参数
     * @param request 查询条件
     * @return 分页结果
     */
    IPage<ShippingOrderPageVO> selectShippingOrderPage(Page<ShippingOrderPageVO> page, 
                                                       @Param("request") ShippingOrderQueryRequest request);

    /**
     * 查询发运订单列表（不分页）
     *
     * @param request 查询条件
     * @return 订单列表
     */
    List<ShippingOrderPageVO> selectShippingOrderList(@Param("request") ShippingOrderQueryRequest request);

    /**
     * 根据订单号查询发运订单
     *
     * @param orderNo 订单号
     * @return 发运订单
     */
    TShippingOrder selectByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 根据客户公司ID查询发运订单数量
     *
     * @param customerCompanyId 客户公司ID
     * @return 订单数量
     */
    int countByCustomerCompanyId(@Param("customerCompanyId") Long customerCompanyId);

    /**
     * 根据仓库ID查询发运订单数量
     *
     * @param warehouseId 仓库ID
     * @return 订单数量
     */
    int countByWarehouseId(@Param("warehouseId") Long warehouseId);

    /**
     * 根据状态查询发运订单列表
     *
     * @param status 订单状态
     * @return 订单列表
     */
    List<TShippingOrder> selectByStatus(@Param("status") String status);

    /**
     * 更新发运订单状态
     *
     * @param id 订单ID
     * @param status 新状态
     * @param lastModifiedBy 修改人
     * @return 更新行数
     */
    int updateStatus(@Param("id") Long id, 
                    @Param("status") String status, 
                    @Param("lastModifiedBy") String lastModifiedBy);

    /**
     * 更新发货数量
     *
     * @param id 订单ID
     * @param shippedQuantity 发货数量
     * @param lastModifiedBy 修改人
     * @return 更新行数
     */
    int updateShippedQuantity(@Param("id") Long id, 
                             @Param("shippedQuantity") Integer shippedQuantity, 
                             @Param("lastModifiedBy") String lastModifiedBy);

    /**
     * 更新签收数量
     *
     * @param id 订单ID
     * @param receivedQuantity 签收数量
     * @param lastModifiedBy 修改人
     * @return 更新行数
     */
    int updateReceivedQuantity(@Param("id") Long id,
                              @Param("receivedQuantity") Integer receivedQuantity,
                              @Param("lastModifiedBy") String lastModifiedBy);

    /**
     * 根据前缀获取最大序号
     *
     * @param prefix 订单号前缀
     * @return 最大序号
     */
    int getMaxSequenceByPrefix(@Param("prefix") String prefix);
}
