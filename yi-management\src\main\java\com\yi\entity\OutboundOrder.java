package com.yi.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 出库单表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_outbound_order")
@ApiModel(value = "OutboundOrder对象", description = "出库单表")
public class OutboundOrder extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 出库单号（F+年月日+递增序列号）
     */
    @ApiModelProperty(value = "出库单号（F+年月日+递增序列号）")
    @TableField("order_no")
    private String orderNo;

    /**
     * 出库状态：1-待出库，2-运输中，3-已出库
     */
    @ApiModelProperty(value = "出库状态：1-待出库，2-运输中，3-已出库")
    @TableField("status")
    private Integer status;

    /**
     * 出库类型：1-销售出库，2-调拨出库
     */
    @ApiModelProperty(value = "出库类型：1-销售出库，2-调拨出库")
    @TableField("outbound_type")
    private Integer outboundType;

    /**
     * 出库公司ID
     */
    @ApiModelProperty(value = "出库公司ID")
    @TableField("outbound_company_id")
    private Long outboundCompanyId;

    /**
     * 出库公司名称
     */
    @ApiModelProperty(value = "出库公司名称")
    @TableField("outbound_company_name")
    private String outboundCompanyName;

    /**
     * 出库地址
     */
    @ApiModelProperty(value = "出库地址")
    @TableField("outbound_address")
    private String outboundAddress;

    /**
     * 配送方式
     */
    @ApiModelProperty(value = "配送方式")
    @TableField("delivery_method")
    private String deliveryMethod;

    /**
     * 车牌号
     */
    @ApiModelProperty(value = "车牌号")
    @TableField("vehicle_number")
    private String vehicleNumber;

    /**
     * 司机姓名
     */
    @ApiModelProperty(value = "司机姓名")
    @TableField("driver_name")
    private String driverName;

    /**
     * 司机联系方式
     */
    @ApiModelProperty(value = "司机联系方式")
    @TableField("driver_phone")
    private String driverPhone;

    /**
     * 收货公司ID
     */
    @ApiModelProperty(value = "收货公司ID")
    @TableField("receive_company_id")
    private Long receiveCompanyId;

    /**
     * 收货公司名称
     */
    @ApiModelProperty(value = "收货公司名称")
    @TableField("receive_company_name")
    private String receiveCompanyName;

    /**
     * 收货地址
     */
    @ApiModelProperty(value = "收货地址")
    @TableField("receive_address")
    private String receiveAddress;

    /**
     * 一级类目：1-共享托盘
     */
    @ApiModelProperty(value = "一级类目：1-共享托盘")
    @TableField("first_category")
    private Integer firstCategory;

    /**
     * 二级类目
     */
    @ApiModelProperty(value = "二级类目")
    @TableField("second_category")
    private String secondCategory;

    /**
     * 计划出库数
     */
    @ApiModelProperty(value = "计划出库数")
    @TableField("planned_quantity")
    private Integer plannedQuantity;

    /**
     * 实际出库数
     */
    @ApiModelProperty(value = "实际出库数")
    @TableField("actual_quantity")
    private Integer actualQuantity;

    /**
     * 出库时间
     */
    @ApiModelProperty(value = "出库时间")
    @TableField("outbound_time")
    private LocalDateTime outboundTime;
}
