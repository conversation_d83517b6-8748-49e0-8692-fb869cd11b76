package com.yi.constant;


import java.math.BigDecimal;

public class CommonConstant {


    public static final String RETURN_SUCCESS = "1";
    public static final String RETURN_FAILURE = "-1";
    public static final Integer NEGATIVE_INTEGER_ONE = -1;
    public static final String ONE = "1";
    public static final String TWO = "2";
    public static final String THREE = "3";
    public static final String ZERO = "0";
    public static final String FOUR = "4";
    public static final String FIVE = "5";
    public static final String SIX = "6";
    public static final String SEVEN = "7";
    public static final String EIGHT = "8";
    public static final String NINE = "9";
    public static final String ONE_ZERO = "10";
    public static final Integer NEGATIVE_ONE = -1;
    public static final Integer INTEGER_ONE = 1;
    public static final Integer INTEGER_ZERO = 0;
    public static final Integer INTEGER_TWO = 2;
    public static final Integer INTEGER_THREE = 3;
    public static final Integer INTEGER_FOUR = 4;
    public static final Integer INT_FIFTY = 50;
    public static final Integer INT_THREE = 3;
    public static final Integer INTEGER_FIVE = 5;
    public static final Integer INTEGER_SIX = 6;
    public static final Integer INTEGER_SEVEN = 7;
    public static final Integer INTEGER_EIGHT = 8;
    public static final Integer INTEGER_NINE = 9;
    public static final String INTEGER_TWO_HUNDRED = "200";
    public static final Integer INTEGER_TEN = 10;
    public static final Integer INTEGER_TWENTY = 20;
    public static final Integer ONE_HUNDRED = 100;
    public static final Integer ONE_THOUSAND = 1000;
    public static final Long LONG_ZERO = 0L;
    public static final Long LONG_ONE = 1L;
    public static final Long LONG_TWO = 2L;
    public static final Long LONG_THREE = 3L;
    public static final Long LONG_FOUR = 4L;
    public static final Long LONG_THIRTY = 30L;
    public static final Long LONG_THREE_HUNDRED = 300L;
    public static final Long YELO_ERROR_LOGIN_CHECK_PERIOD = 600L;
    public static final String LOGIN_ERROR_COUNT = "yelo_login_error_count_";
    public static final String YELO_SUPP_LOGIN_ERROR_COUNT = "yelo_supp_login_error_count_";
    public static final Long TEN_DAY_SECONDS = 864000L;//10天
    public static final Long TWO_DAY_SECONDS = 216000L;//2天

    public static final String WATER_MARK_PREFIX = "wm";
    public static final Integer WATER_MARK_FONT_SIZE = 10;
    public static final String WATER_MARK_FONT_TYPE = "微软雅黑";
    public static final String WATER_MARK_FONT_COLOR = "99FF0000";
    public static final Long LONG_ELEVEN = 11L;
    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    public static final String YYYYMMDD = "yyyyMMdd";
    public static final String YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";
    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static final String CRM_ID_REGEXP = "^[a-zA-Z0-9]{1,10}$";
    public static final String ELLIPSIS = "...";
    public static final String STRING_EMPTY = "";
    public static final String UNDERSCORE = "_";
    public static final String SEPARATOR_COMMA = ",";
    public static final String COMPANY = "company";
    public static final Integer WEBSOCKET_MESSAGE_LENGTH = 25;

    public static final String JPUSH_EXTRA_MODULE = "extraModule";
    public static final String JPUSH_EXTRA_MSG_TYPE = "extraMsgType";

    public static final String VERIFY_CODE_USER = "验证码已使用";
    public static final String VERIFY_CODE_USER_DELETE = "重复获取以删除";
    public static final String BAI_DU_TOKEN = "bai_du_token";
    public static final String IMAGE_OCR_KEY = "image_ocr_key";
    public static final String CHECH_SENSITIVE_WORD_KEY = "check_sensitive_word_key";
    public static final String SYSTEM_ADMIN = "systemAdmin";
    public static final String OSS_FILE_KEY = "oss_file_src_";
    public static final String OSS_STS_CONFIGURATION_KEY = "OSS_STS_CONFIGURATION";
    public static final String LIMITED_IDEMPOTENT_MESSAGE_FOR_JOB_IS_RUNNING = "服务正在运行";
    public static final String WAREHOUSE_SCAN_LOGIN_PERMISSION="warehouse_scan_login_permission";
    public static final String WAREHOUSE_FIX_READER_LOGIN_PERMISSION="warehouse_fix_reader_login_permission";

    public static final String  ALIYUN_REPORT_URL_PARMS = "aliyun_report_url_params";
    public static final String  ALIYUN_REPORT_TOKEN_KEY_CACHE = "aliyun_report_token_cache";
    public static final String  BI_REPORT_URL = "bi_report_url";
    public static final String BI_SCHEDULE = "bi_schedule";


    public static final String MES_MANAGEMENT_WEBAPI_PERMISSIONS_KEY = "MES_MANAGEMENT_WEBAPI_PERMISSIONS_KEY";

    public static final String MES_ROLE_CODE_GENERATE_CODE_KEY = "MES_ROLE_CODE_GENERATE_CODE_";

    public static final String MES_USER_CODE_GENERATE_CODE_KEY = "MES_USER_CODE_GENERATE_CODE_";

    public static final String EXPORT_EXCEL_SUFFIX = ".xls";


    public static final String LIMITED_IDEMPOTENT_MESSAGE = "请勿重复请求，稍后再试";



    public static final String YELO_LIFE_ORG_NAME = "company_xs_bzjszx";

    public static final String YELO_LIFE_ORG2_NAME = "company_xs_sczx";

    public static final String CHARSET_UTF_8 = "UTF-8";

    public static final String ROLE_NAME_FORBIDDEN = "异常客户禁用";

    public static final String CUSTOMER_ID = "客户id";

    public static final String CUSTOMER_NAME = "客户名称";

    public static final String LOG_DEFAULT_NAME = "系统";

    public static final String SUPPLIER_NAME = "供应商";

    public static final String USER_CODE_START = "U";

    public static final String ROLE_CODE_START = "R";

    public static final String PREVIOUS_ACTION_CONTACTS = "PREVIOUS_ACTION_CONTACTS";
    public static final String COLON = ":";

    //正在称重中的单号
    public static final String WEIGHTING_ORDER_CODE = "WEIGHTING_ORDER_CODE";

    public static final BigDecimal SKU_WEIGHT = new BigDecimal("2");


    private CommonConstant() {

    }

}
