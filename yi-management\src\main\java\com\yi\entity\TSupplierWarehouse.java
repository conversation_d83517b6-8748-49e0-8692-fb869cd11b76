package com.yi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 供应商仓库表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_supplier_warehouse")
public class TSupplierWarehouse extends BaseEntity {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 省份ID
     */
    private Long provinceId;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市ID
     */
    private Long cityId;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 区县ID
     */
    private Long areaId;

    /**
     * 区县名称
     */
    private String areaName;

    /**
     * 地址详情
     */
    private String detailedAddress;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 联系方式
     */
    private String contactPhone;

    /**
     * 启用状态：1-启用，0-禁用
     */
    private Integer enabled;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 最后修改人
     */
    private String lastModifiedBy;

    /**
     * 最后修改时间
     */
    private LocalDateTime lastModifiedTime;

    /**
     * 有效性：1-有效，0-无效
     */
    private Integer valid;
}
