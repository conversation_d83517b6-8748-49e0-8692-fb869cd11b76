<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yi.mapper.TCustomerCompanyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yi.entity.TCustomerCompany">
        <id column="id" property="id" />
        <result column="customer_company_type" property="customerCompanyType" />
        <result column="company_name" property="companyName" />
        <result column="province_id" property="provinceId" />
        <result column="province_name" property="provinceName" />
        <result column="city_id" property="cityId" />
        <result column="city_name" property="cityName" />
        <result column="area_id" property="areaId" />
        <result column="area_name" property="areaName" />
        <result column="detailed_address" property="detailedAddress" />
        <result column="contact_person" property="contactPerson" />
        <result column="contact_phone" property="contactPhone" />
        <result column="invoice_type" property="invoiceType" />
        <result column="invoice_company_name" property="invoiceCompanyName" />
        <result column="tax_number" property="taxNumber" />
        <result column="invoice_contact_person" property="invoiceContactPerson" />
        <result column="invoice_mobile" property="invoiceMobile" />
        <result column="invoice_email" property="invoiceEmail" />
        <result column="bank_name" property="bankName" />
        <result column="bank_account" property="bankAccount" />
        <result column="enabled" property="enabled" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="last_modified_by" property="lastModifiedBy" />
        <result column="last_modified_time" property="lastModifiedTime" />
        <result column="valid" property="valid" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, customer_company_type, company_name, province_id, province_name,
        city_id, city_name, area_id, area_name, detailed_address, contact_person, contact_phone,
        invoice_type, invoice_company_name, tax_number, invoice_contact_person, invoice_mobile,
        invoice_email, bank_name, bank_account, enabled, created_by, created_time,
        last_modified_by, last_modified_time, valid, remark
    </sql>

    <!-- 分页查询客户公司列表 -->
    <select id="selectCustomerCompanyPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_customer_company
        WHERE valid = 1
        <if test="companyName != null and companyName != ''">
            AND company_name LIKE CONCAT('%', #{companyName}, '%')
        </if>
        <if test="customerCompanyType != null and customerCompanyType != ''">
            AND customer_company_type = #{customerCompanyType}
        </if>
        <if test="enabled != null">
            AND enabled = #{enabled}
        </if>
        ORDER BY created_time DESC
    </select>

    <!-- 根据公司名称查询客户公司 -->
    <select id="selectByCompanyName" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_customer_company
        WHERE valid = 1 AND company_name = #{companyName}
        ORDER BY created_time DESC
    </select>

    <!-- 根据公司类型查询客户公司 -->
    <select id="selectByCompanyType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_customer_company
        WHERE valid = 1
        <if test="customerCompanyType != null and customerCompanyType != ''">
            AND customer_company_type = #{customerCompanyType}
        </if>
        AND enabled = 1
        ORDER BY created_time DESC
    </select>

</mapper>
