package com.yi.controller.shippingdemand.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;

/**
 * 发货需求请求
 */
@Data
@ApiModel(value = "ShippingDemandRequest", description = "发货需求请求")
public class ShippingDemandRequest {

    @ApiModelProperty(value = "主键ID（编辑时必填）")
    private String id;

    @ApiModelProperty(value = "发运订单号", required = true)
    @NotBlank(message = "发运订单号不能为空")
    private String orderNo;

    @ApiModelProperty(value = "发货状态：PENDING-待发货，SHIPPED-已发货，COMPLETED-已完结，CANCELLED-已取消")
    private String status;

    @ApiModelProperty(value = "客户公司ID", required = true)
    @NotBlank(message = "客户公司不能为空")
    private String customerCompanyId;

    @ApiModelProperty(value = "收货仓库ID", required = true)
    @NotBlank(message = "收货仓库不能为空")
    private String warehouseId;

    @ApiModelProperty(value = "一级类目：1-共享托盘", required = true)
    @NotBlank(message = "一级类目不能为空")
    private String firstCategory;

    @ApiModelProperty(value = "二级类目")
    private String secondCategory;

    @ApiModelProperty(value = "需求数量", required = true)
    @NotBlank(message = "需求数量不能为空")
    private String demandQuantity;

    @ApiModelProperty(value = "待确认数量")
    private String pendingQuantity;

    @ApiModelProperty(value = "发货数量")
    private String shippedQuantity;

    @ApiModelProperty(value = "未执行数量")
    private String unexecutedQuantity;

    @ApiModelProperty(value = "需求时间（格式：yyyy-MM-dd）", required = true)
    @NotBlank(message = "需求时间不能为空")
    private String demandTime;
}
