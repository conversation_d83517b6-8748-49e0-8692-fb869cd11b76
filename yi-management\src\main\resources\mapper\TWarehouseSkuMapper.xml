<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yi.mapper.TWarehouseSkuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yi.entity.TWarehouseSku">
        <id column="id" property="id" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="sku_id" property="skuId" />
        <result column="enabled" property="enabled" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="last_modified_by" property="lastModifiedBy" />
        <result column="last_modified_time" property="lastModifiedTime" />
        <result column="valid" property="valid" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, warehouse_id, sku_id, enabled, created_by, created_time, 
        last_modified_by, last_modified_time, valid, remark
    </sql>

    <!-- 分页查询仓库SKU关联列表 -->
    <select id="selectWarehouseSkuPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_warehouse_sku
        WHERE valid = 1
        <if test="warehouseId != null">
            AND warehouse_id = #{warehouseId}
        </if>
        <if test="skuId != null">
            AND sku_id = #{skuId}
        </if>
        <if test="enabled != null">
            AND enabled = #{enabled}
        </if>
        ORDER BY created_time DESC
    </select>

    <!-- 根据仓库ID查询SKU关联列表 -->
    <select id="selectByWarehouseId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_warehouse_sku
        WHERE valid = 1 AND warehouse_id = #{warehouseId}
        ORDER BY created_time DESC
    </select>

    <!-- 根据SKU ID查询仓库关联列表 -->
    <select id="selectBySkuId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_warehouse_sku
        WHERE valid = 1 AND sku_id = #{skuId}
        ORDER BY created_time DESC
    </select>

    <!-- 根据仓库ID和SKU ID查询关联关系 -->
    <select id="selectByWarehouseIdAndSkuId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_warehouse_sku
        WHERE valid = 1 AND warehouse_id = #{warehouseId} AND sku_id = #{skuId}
        LIMIT 1
    </select>

</mapper>
