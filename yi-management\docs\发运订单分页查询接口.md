# 发运订单分页查询接口

## 接口信息

### 分页查询接口

**接口地址**: `POST /api/shipping-order/page`

**接口描述**: 分页查询发运订单列表

### 导出接口

**接口地址**: `POST /api/shipping-order/export`

**接口描述**: 导出发运订单列表到Excel文件

## 请求参数

```json
{
    "current": "1",           // 当前页码，默认1
    "size": "10",            // 每页大小，默认10
    "orderNo": "",           // 订单号（模糊查询）
    "contractCode": "",      // 合同编号（模糊查询）
    "customerCompanyId": "", // 客户公司ID
    "customerCompanyName": "", // 客户公司名称（模糊查询）
    "warehouseId": "",       // 收货仓库ID
    "warehouseName": "",     // 收货仓库名称（模糊查询）
    "firstCategory": "",     // 一级类目：1-共享托盘
    "secondCategory": "",    // 二级类目（模糊查询）
    "status": "",           // 订单状态：PENDING-待发货，SHIPPING-发货中，COMPLETED-已完结，CANCELLED-已取消
    "demandTimeStart": "",   // 需求时间开始（格式：yyyy-MM-dd）
    "demandTimeEnd": "",     // 需求时间结束（格式：yyyy-MM-dd）
    "createdTimeStart": "",  // 创建时间开始（格式：yyyy-MM-dd）
    "createdTimeEnd": "",    // 创建时间结束（格式：yyyy-MM-dd）
    "createdBy": ""         // 创建人（模糊查询）
}
```

## 返回参数

### 成功响应

```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "current": 1,        // 当前页码
        "size": 10,          // 每页大小
        "total": 100,        // 总记录数
        "pages": 10,         // 总页数
        "records": [         // 数据列表
            {
                "id": "1",                           // 主键ID
                "orderNo": "ORDER20240101001",       // 订单号
                "contractCode": "CONTRACT001",        // 合同编号
                "customerCompanyName": "北京测试公司", // 客户名称
                "warehouseName": "北京仓库",          // 收货仓库
                "warehouseAddress": "北京市朝阳区建国路1号", // 收货地址
                "receiverName": "张三 138****5678",    // 收货人（姓名+脱敏手机号）
                "product": "共享托盘-标准托盘",        // 产品
                "count": "100",                      // 需求数量
                "shippedQuantity": "80",             // 发货数量
                "receivedQuantity": "75",            // 签收数量
                "status": "SHIPPING",                // 订单状态
                "createdBy": "admin",                // 创建人
                "createdTime": "2024-01-01 10:00:00" // 创建时间
            }
        ]
    }
}
```

## 字段说明

### 返回字段详细说明

| 字段名 | 类型 | 说明 | 数据来源 |
|--------|------|------|----------|
| id | String | 主键ID | t_shipping_order.id |
| orderNo | String | 订单号 | t_shipping_order.order_no |
| contractCode | String | 合同编号 | t_shipping_order.contract_code |
| customerCompanyName | String | 客户名称 | t_customer_company.company_name（联查） |
| warehouseName | String | 收货仓库 | t_customer_warehouse.warehouse_name（联查） |
| warehouseAddress | String | 收货地址 | t_customer_warehouse拼接地址（联查） |
| receiverName | String | 收货人 | t_customer_warehouse.receiver_name + receiver_phone（联查+脱敏） |
| product | String | 产品 | 根据一级类目和二级类目组合生成 |
| count | String | 需求数量 | t_shipping_order.count |
| shippedQuantity | String | 发货数量 | t_shipping_order.shipped_quantity |
| receivedQuantity | String | 签收数量 | t_shipping_order.received_quantity |
| status | String | 订单状态 | t_shipping_order.status |
| createdBy | String | 创建人 | t_shipping_order.created_by |
| createdTime | String | 创建时间 | t_shipping_order.created_time |

### 订单状态枚举

| 状态值 | 状态名称 |
|--------|----------|
| PENDING | 待发货 |
| SHIPPING | 发货中 |
| COMPLETED | 已完结 |
| CANCELLED | 已取消 |

### 产品字段生成规则

产品字段由一级类目和二级类目组合生成：

- 一级类目：1 → "共享托盘"
- 二级类目：直接显示二级类目名称
- 组合规则："{一级类目名称}-{二级类目名称}"

示例：
- 一级类目=1，二级类目="标准托盘" → "共享托盘-标准托盘"
- 一级类目=1，二级类目为空 → "共享托盘"

### 收货地址字段生成规则

收货地址由仓库表的地址字段拼接生成：
```sql
CONCAT(w.province_name, w.city_name, w.area_name, w.detailed_address) as warehouse_address
```

### 收货人字段生成规则

收货人字段由联系人姓名和脱敏后的联系方式组合生成：

**组合规则**: `{联系人姓名} {脱敏手机号}`

**脱敏规则**: 手机号第4-7位用*代替

**示例**:
- 原始数据：姓名="张三"，手机号="13812345678"
- 显示结果：`"张三 138****5678"`

**特殊情况处理**:
- 只有姓名无手机号：显示姓名
- 只有手机号无姓名：显示脱敏手机号
- 手机号长度不足7位：不进行脱敏
- 都为空：显示空字符串

## 数据库查询

### 主要关联表

1. **t_shipping_order** - 发运订单主表
2. **t_customer_company** - 客户公司表（联查客户名称）
3. **t_customer_warehouse** - 客户仓库表（联查仓库信息、收货地址、收货人）

### 查询SQL示例

```sql
SELECT 
    so.id,
    so.order_no,
    so.contract_code,
    cc.company_name as customer_company_name,
    w.warehouse_name,
    CONCAT(w.province_name, w.city_name, w.area_name, w.detailed_address) as warehouse_address,
    w.receiver_name,
    w.receiver_phone,
    so.first_category,
    so.second_category,
    so.count,
    so.shipped_quantity,
    so.received_quantity,
    so.status,
    so.created_by,
    so.created_time,
    so.valid
FROM t_shipping_order so
LEFT JOIN t_customer_company cc ON so.customer_company_id = cc.id AND cc.valid = 1
LEFT JOIN t_customer_warehouse w ON so.warehouse_id = w.id AND w.valid = 1
WHERE so.valid = 1
ORDER BY so.created_time DESC
```

## 使用示例

### 查询所有订单

```bash
curl -X POST "http://localhost:8080/api/shipping-order/page" \
  -H "Content-Type: application/json" \
  -d '{
    "current": "1",
    "size": "10"
  }'
```

### 按状态查询

```bash
curl -X POST "http://localhost:8080/api/shipping-order/page" \
  -H "Content-Type: application/json" \
  -d '{
    "current": "1",
    "size": "10",
    "status": "PENDING"
  }'
```

### 按客户名称查询

```bash
curl -X POST "http://localhost:8080/api/shipping-order/page" \
  -H "Content-Type: application/json" \
  -d '{
    "current": "1",
    "size": "10",
    "customerCompanyName": "测试公司"
  }'
```

## 导出接口详细说明

### 导出请求参数

导出接口的请求参数与分页查询接口完全相同，支持所有查询条件的过滤。

### 导出响应

导出接口直接返回Excel文件流，浏览器会自动下载文件。

**文件名格式**: `发运订单列表_yyyyMMdd_HHmmss.xlsx`

**示例文件名**: `发运订单列表_20240101_143022.xlsx`

### 导出字段说明

导出的Excel文件包含以下列：

| 列名 | 说明 | 数据来源 |
|------|------|----------|
| 订单号 | 发运订单号 | t_shipping_order.order_no |
| 合同编号 | 关联的合同编号 | t_shipping_order.contract_code |
| 客户名称 | 客户公司名称 | t_customer_company.company_name（联查） |
| 收货仓库 | 收货仓库名称 | t_customer_warehouse.warehouse_name（联查） |
| 收货地址 | 完整收货地址 | t_customer_warehouse拼接地址（联查） |
| 收货人 | 收货联系人+脱敏手机号 | t_customer_warehouse.receiver_name + receiver_phone（联查+脱敏） |
| 产品 | 产品名称 | 根据一级类目和二级类目组合生成 |
| 需求数量 | 订单需求数量 | t_shipping_order.count |
| 发货数量 | 已发货数量 | t_shipping_order.shipped_quantity |
| 签收数量 | 已签收数量 | t_shipping_order.received_quantity |
| 订单状态 | 订单当前状态 | t_shipping_order.status（转换为中文） |
| 创建人 | 订单创建人 | t_shipping_order.created_by |
| 创建时间 | 订单创建时间 | t_shipping_order.created_time |

### 导出使用示例

```bash
# 导出所有订单
curl -X POST "http://localhost:8080/api/shipping-order/export" \
  -H "Content-Type: application/json" \
  -d '{}' \
  --output "发运订单列表.xlsx"

# 导出指定状态的订单
curl -X POST "http://localhost:8080/api/shipping-order/export" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "PENDING"
  }' \
  --output "待发货订单列表.xlsx"

# 导出指定时间范围的订单
curl -X POST "http://localhost:8080/api/shipping-order/export" \
  -H "Content-Type: application/json" \
  -d '{
    "createdTimeStart": "2024-01-01",
    "createdTimeEnd": "2024-01-31"
  }' \
  --output "一月份订单列表.xlsx"
```

### 注意事项

1. **数据量限制**: 导出功能会查询所有符合条件的数据，建议在导出大量数据时添加适当的查询条件
2. **文件格式**: 导出文件为Excel 2007+格式(.xlsx)
3. **字符编码**: 文件使用UTF-8编码，支持中文字符
4. **状态转换**: 导出时会将状态代码转换为中文名称（如PENDING→待发货）
5. **时间格式**: 创建时间格式为"yyyy-MM-dd HH:mm:ss"
