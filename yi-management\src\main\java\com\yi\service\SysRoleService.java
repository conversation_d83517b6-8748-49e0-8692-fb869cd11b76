package com.yi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yi.entity.SysRole;

import java.util.List;

/**
 * 系统角色表 服务类
 */
public interface SysRoleService extends IService<SysRole> {

    /**
     * 分页查询角色列表
     *
     * @param page 分页参数
     * @param roleCode 角色编码（模糊查询）
     * @param roleName 角色名称（模糊查询）
     * @param status 状态
     * @return 分页结果
     */
    IPage<SysRole> selectRolePage(Page<SysRole> page, String roleCode, String roleName, Integer status);

    /**
     * 根据角色编码查询角色
     *
     * @param roleCode 角色编码
     * @return 角色信息
     */
    SysRole selectByRoleCode(String roleCode);

    /**
     * 根据用户ID查询角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<SysRole> selectByUserId(Long userId);

    /**
     * 查询角色的资源ID列表
     *
     * @param roleId 角色ID
     * @return 资源ID列表
     */
    List<Long> selectResourceIdsByRoleId(Long roleId);

    /**
     * 查询所有启用的角色
     *
     * @return 角色列表
     */
    List<SysRole> selectEnabledRoles();

    /**
     * 创建角色
     *
     * @param role 角色信息
     * @return 是否成功
     */
    boolean createRole(SysRole role);

    /**
     * 更新角色
     *
     * @param role 角色信息
     * @return 是否成功
     */
    boolean updateRole(SysRole role);

    /**
     * 删除角色
     *
     * @param roleId 角色ID
     * @return 是否成功
     */
    boolean deleteRole(Long roleId);

    /**
     * 批量删除角色
     *
     * @param roleIds 角色ID列表
     * @return 是否成功
     */
    boolean deleteRoles(List<Long> roleIds);

    /**
     * 分配角色权限
     *
     * @param roleId 角色ID
     * @param resourceIds 资源ID列表
     * @return 是否成功
     */
    boolean assignPermissions(Long roleId, List<Long> resourceIds);

    /**
     * 启用/禁用角色
     *
     * @param roleId 角色ID
     * @param status 状态
     * @return 是否成功
     */
    boolean updateStatus(Long roleId, Integer status);

    /**
     * 检查角色编码是否存在
     *
     * @param roleCode 角色编码
     * @param excludeId 排除的角色ID
     * @return 是否存在
     */
    boolean checkRoleCodeExists(String roleCode, Long excludeId);
}
