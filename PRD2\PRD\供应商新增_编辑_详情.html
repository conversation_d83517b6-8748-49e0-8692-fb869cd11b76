﻿<!DOCTYPE html>
<html>
  <head>
    <title>供应商新增/编辑/详情</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/供应商新增_编辑_详情/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/供应商新增_编辑_详情/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (线段) -->
      <div id="u2558" class="ax_default line1">
        <img id="u2558_img" class="img " src="images/客户管理/u350.svg"/>
        <div id="u2558_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2559" class="ax_default box_21">
        <div id="u2559_div" class=""></div>
        <div id="u2559_text" class="text ">
          <p><span>供应商新增/编辑/详情&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; X</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2560" class="ax_default link_button">
        <div id="u2560_div" class=""></div>
        <div id="u2560_text" class="text ">
          <p><span>刷新本页</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2561" class="ax_default label">
        <div id="u2561_div" class=""></div>
        <div id="u2561_text" class="text ">
          <p><span>*企业名称</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u2562" class="ax_default text_field">
        <div id="u2562_div" class=""></div>
        <input id="u2562_input" type="text" value="" class="u2562_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2563" class="ax_default box_21">
        <div id="u2563_div" class=""></div>
        <div id="u2563_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2564" class="ax_default heading_3">
        <div id="u2564_div" class=""></div>
        <div id="u2564_text" class="text ">
          <p><span>基本信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2565" class="ax_default label">
        <div id="u2565_div" class=""></div>
        <div id="u2565_text" class="text ">
          <p><span>*联系人</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u2566" class="ax_default text_field">
        <div id="u2566_div" class=""></div>
        <input id="u2566_input" type="text" value="" class="u2566_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2567" class="ax_default label">
        <div id="u2567_div" class=""></div>
        <div id="u2567_text" class="text ">
          <p><span>*联系方式</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u2568" class="ax_default text_field">
        <div id="u2568_div" class=""></div>
        <input id="u2568_input" type="text" value="" class="u2568_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2569" class="ax_default label">
        <div id="u2569_div" class=""></div>
        <div id="u2569_text" class="text ">
          <p><span>手机号或者座机号</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2570" class="ax_default primary_button">
        <div id="u2570_div" class=""></div>
        <div id="u2570_text" class="text ">
          <p><span>保存</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2571" class="ax_default label">
        <div id="u2571_div" class=""></div>
        <div id="u2571_text" class="text ">
          <p><span>营业执照</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2572" class="ax_default box_1">
        <div id="u2572_div" class=""></div>
        <div id="u2572_text" class="text ">
          <p><span>+</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2573" class="ax_default label">
        <div id="u2573_div" class=""></div>
        <div id="u2573_text" class="text ">
          <p><span>开户行</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u2574" class="ax_default text_field">
        <div id="u2574_div" class=""></div>
        <input id="u2574_input" type="text" value="" class="u2574_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2575" class="ax_default label">
        <div id="u2575_div" class=""></div>
        <div id="u2575_text" class="text ">
          <p><span>银行账号</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u2576" class="ax_default text_field">
        <div id="u2576_div" class=""></div>
        <input id="u2576_input" type="text" value="" class="u2576_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2577" class="ax_default label">
        <div id="u2577_div" class=""></div>
        <div id="u2577_text" class="text ">
          <p><span>备注</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u2578" class="ax_default text_field">
        <div id="u2578_div" class=""></div>
        <input id="u2578_input" type="text" value="" class="u2578_input"/>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
