package com.yi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yi.controller.cloudwarehouse.model.CloudWarehouseQueryRequest;
import com.yi.entity.TCloudWarehouse;
import com.yi.mapper.vo.CloudWarehousePageVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 云仓表 Mapper 接口
 */
@Mapper
public interface TCloudWarehouseMapper extends BaseMapper<TCloudWarehouse> {

    /**
     * 分页查询云仓列表
     *
     * @param page 分页参数
     * @param request 查询条件
     * @return 分页结果
     */
    IPage<CloudWarehousePageVO> selectCloudWarehousePage(Page<CloudWarehousePageVO> page, 
                                                         @Param("request") CloudWarehouseQueryRequest request);
}
