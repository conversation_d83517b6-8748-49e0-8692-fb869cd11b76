package com.yi.configuration.jwt;

/**
 * description:
 *
 * <AUTHOR>
 * @create 2019-03-01 14:56
 */
public enum JwtApplicationTokenPrefixEnum {
    MES_MANAGEMENT_WEB(1,"TOKEN_MES_MANAGEMENT_WEB_", "MES后台"),
    MES_CUSTOMER_WX(2,"TOKEN_MES_CUSTOMER_WX_", "MES小程序"),
    MES_CUSTOMER_APP(3,"TOKEN_MES_CUSTOMER_APP_", "MESAPP"),

    ;
    private Integer code;
    private String key;
    private String value;

    JwtApplicationTokenPrefixEnum(Integer code, String key, String value) {
        this.code = code;
        this.key = key;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static JwtApplicationTokenPrefixEnum getEnum(String key) {
        for (JwtApplicationTokenPrefixEnum t : values()) {
            if (t.getKey().equals(key)) {
                return t;
            }
        }
        return null;
    }

}
