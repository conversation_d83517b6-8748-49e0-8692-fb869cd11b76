package com.yi.controller.contract.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 合同附件响应
 */
@Data
@ApiModel(value = "ContractAttachmentResponse", description = "合同附件响应")
public class ContractAttachmentResponse {

    @ApiModelProperty(value = "文件ID")
    private String id;

    @ApiModelProperty(value = "文件类型")
    private String fileType;

    @ApiModelProperty(value = "文件路径")
    private String filePath;

    @ApiModelProperty(value = "文件访问URL")
    private String fileUrl;

    @ApiModelProperty(value = "上传时间")
    private String uploadTime;
}
