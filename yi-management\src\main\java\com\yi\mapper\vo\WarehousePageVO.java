package com.yi.mapper.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 仓库分页查询VO
 */
@Data
public class WarehousePageVO {

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 公司ID
     */
    private Long companyId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 区县名称
     */
    private String areaName;

    /**
     * 详细地址
     */
    private String detailedAddress;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 手机号
     */
    private String mobilePhone;

    /**
     * 座机号
     */
    private String landlinePhone;

    /**
     * 启用状态：1-启用，0-禁用
     */
    private Integer enabled;

    /**
     * 一级类目
     */
    private Integer firstCategory;

    /**
     * 二级类目
     */
    private String secondCategory;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
}
