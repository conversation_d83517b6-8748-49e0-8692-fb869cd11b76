package com.yi.controller.shippingorder.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 发运订单详情响应
 */
@Data
@ApiModel(value = "ShippingOrderDetailResponse", description = "发运订单详情响应")
public class ShippingOrderDetailResponse {

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "客户公司ID")
    private String customerCompanyId;

    @ApiModelProperty(value = "客户公司名称")
    private String customerCompanyName;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "收货仓库ID")
    private String warehouseId;

    @ApiModelProperty(value = "收货仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "收货人")
    private String receiverName;

    @ApiModelProperty(value = "手机号")
    private String receiverPhone;

    @ApiModelProperty(value = "省份")
    private String province;

    @ApiModelProperty(value = "城市")
    private String city;

    @ApiModelProperty(value = "区县")
    private String district;

    @ApiModelProperty(value = "详细地址")
    private String detailAddress;

    @ApiModelProperty(value = "完整地址")
    private String fullAddress;

    @ApiModelProperty(value = "一级类目")
    private String firstCategory;

    @ApiModelProperty(value = "一级类目名称")
    private String firstCategoryName;

    @ApiModelProperty(value = "二级类目")
    private String secondCategory;

    @ApiModelProperty(value = "产品")
    private String product;

    @ApiModelProperty(value = "需求数量")
    private String count;

    @ApiModelProperty(value = "发货数量")
    private String shippedQuantity;

    @ApiModelProperty(value = "签收数量")
    private String receivedQuantity;

    @ApiModelProperty(value = "需求时间")
    private String demandTime;

    @ApiModelProperty(value = "订单状态")
    private String status;

    @ApiModelProperty(value = "订单状态名称")
    private String statusName;

    @ApiModelProperty(value = "取消原因")
    private String cancelReason;

    @ApiModelProperty(value = "备注信息")
    private String remark;

    @ApiModelProperty(value = "附件列表")
    private List<String> attachmentUrls;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private String createdTime;

    @ApiModelProperty(value = "最后修改人")
    private String lastModifiedBy;

    @ApiModelProperty(value = "最后修改时间")
    private String lastModifiedTime;
}
