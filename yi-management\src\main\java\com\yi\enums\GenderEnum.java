package com.yi.enums;

/**
 * 性别枚举
 */
public enum GenderEnum {
    
    FEMALE(0, "女"),
    MALE(1, "男");

    private final Integer code;
    private final String desc;

    GenderEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByCode(Integer code) {
        if (code == null) {
            return "";
        }
        for (GenderEnum gender : values()) {
            if (gender.getCode().equals(code)) {
                return gender.getDesc();
            }
        }
        return "";
    }
}
