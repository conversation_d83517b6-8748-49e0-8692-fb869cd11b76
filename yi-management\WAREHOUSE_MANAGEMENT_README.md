# 出入库管理系统

## 概述

出入库管理系统提供了完整的仓库出入库功能，包括出库单管理、入库单管理、单号自动生成等核心功能。

## 功能特性

### 🚚 出库管理
- **出库状态**：待出库、运输中、已出库
- **出库类型**：销售出库、调拨出库
- **单号格式**：F + 年月日 + 4位递增序列号（如：F202412010001）
- **完整信息**：出库公司、收货公司、配送方式、司机信息、货物类目等

### 📦 入库管理
- **入库状态**：待入库、部分入库、已入库
- **入库类型**：采购入库、回收入库、调拨入库、销售入库
- **单号关联**：入库单号与出库单号保持一致
- **完整信息**：入库仓库、发货仓库、配送方式、司机信息、货物类目等

### 🔢 单号生成
- **自动生成**：F开头 + 年月日 + 4位递增序列号
- **唯一性保证**：数据库级别的唯一约束
- **按日重置**：每日序列号从0001开始递增

## 数据库表结构

### 核心表

1. **t_outbound_order** - 出库单表
   - 包含出库单的所有信息
   - 支持状态流转：待出库 → 运输中 → 已出库

2. **t_inbound_order** - 入库单表
   - 包含入库单的所有信息
   - 支持状态流转：待入库 → 部分入库 → 已入库

3. **t_order_sequence** - 单号序列表
   - 用于生成唯一的单号序列
   - 按日期分组管理序列号

## 文件结构

```
src/main/
├── java/com/yi/
│   ├── entity/                    # 实体类
│   │   ├── OutboundOrder.java     # 出库单实体
│   │   ├── InboundOrder.java      # 入库单实体
│   │   └── OrderSequence.java     # 单号序列实体
│   ├── enums/                     # 枚举类
│   │   ├── OutboundStatusEnum.java    # 出库状态枚举
│   │   ├── InboundStatusEnum.java     # 入库状态枚举
│   │   ├── OutboundTypeEnum.java      # 出库类型枚举
│   │   ├── InboundTypeEnum.java       # 入库类型枚举
│   │   └── FirstCategoryEnum.java     # 一级类目枚举
│   ├── mapper/                    # Mapper接口
│   │   ├── OutboundOrderMapper.java
│   │   ├── InboundOrderMapper.java
│   │   └── OrderSequenceMapper.java
│   └── service/                   # Service层
│       ├── OrderNumberService.java        # 单号生成服务
│       ├── OutboundOrderService.java      # 出库单服务
│       └── impl/
│           └── OrderNumberServiceImpl.java
└── resources/
    ├── mapper/                    # MyBatis XML映射文件
    │   ├── OutboundOrderMapper.xml
    │   ├── InboundOrderMapper.xml
    │   └── OrderSequenceMapper.xml
    └── sql/                       # SQL脚本
        ├── warehouse_ddl.sql      # 表结构创建脚本
        └── warehouse_dml.sql      # 初始化数据脚本
```

## 使用说明

### 1. 数据库初始化

```sql
-- 1. 执行表结构创建脚本
source src/main/resources/sql/warehouse_ddl.sql;

-- 2. 执行初始化数据脚本
source src/main/resources/sql/warehouse_dml.sql;
```

### 2. 状态枚举说明

#### 出库状态
- **1** - 待出库：订单已创建，等待出库操作
- **2** - 运输中：货物已出库，正在运输途中
- **3** - 已出库：货物已送达，出库完成

#### 入库状态
- **1** - 待入库：订单已创建，等待入库操作
- **2** - 部分入库：货物部分入库，还有剩余
- **3** - 已入库：货物全部入库完成

#### 出库类型
- **1** - 销售出库：销售给客户的出库
- **2** - 调拨出库：仓库间调拨的出库

#### 入库类型
- **1** - 采购入库：采购商品的入库
- **2** - 回收入库：回收商品的入库
- **3** - 调拨入库：仓库间调拨的入库
- **4** - 销售入库：销售退货的入库

### 3. 单号生成规则

- **格式**：F + YYYYMMDD + 序列号
- **示例**：F202412010001（2024年12月1日第1个单号）
- **特点**：
  - 每日序列号从0001开始
  - 自动递增，保证唯一性
  - 出库单和入库单使用相同的单号

### 4. 业务流程

#### 出库流程
1. **创建出库单**：状态为"待出库"
2. **确认出库**：更新实际出库数，状态变为"运输中"
3. **完成出库**：货物送达，状态变为"已出库"

#### 入库流程
1. **创建入库单**：状态为"待入库"
2. **部分入库**：货物部分到达，状态变为"部分入库"
3. **完成入库**：货物全部到达，状态变为"已入库"

### 5. 核心功能

#### 单号生成服务
```java
@Autowired
private OrderNumberService orderNumberService;

// 生成出库单号
String outboundOrderNo = orderNumberService.generateOutboundOrderNo();

// 生成入库单号
String inboundOrderNo = orderNumberService.generateInboundOrderNo();
```

#### 出库单管理
```java
@Autowired
private OutboundOrderService outboundOrderService;

// 创建出库单
OutboundOrder order = new OutboundOrder();
// ... 设置订单信息
outboundOrderService.createOutboundOrder(order);

// 确认出库
outboundOrderService.confirmOutbound(orderId, actualQuantity, "操作人");

// 完成出库
outboundOrderService.completeOutbound(orderId, "操作人");
```

## 扩展说明

### 1. 添加新的出库类型
在 `OutboundTypeEnum` 中添加新的枚举值，并更新相关业务逻辑。

### 2. 添加新的入库类型
在 `InboundTypeEnum` 中添加新的枚举值，并更新相关业务逻辑。

### 3. 自定义单号格式
修改 `OrderNumberServiceImpl` 中的单号生成逻辑。

### 4. 添加审批流程
可以在状态流转中添加审批节点，扩展状态枚举。

## 注意事项

1. **并发安全**：单号生成使用数据库锁保证并发安全
2. **事务管理**：关键操作使用事务保证数据一致性
3. **状态流转**：严格按照业务流程进行状态流转
4. **数据完整性**：使用外键约束保证数据完整性

这个出入库管理系统提供了完整的仓库管理功能，可以根据实际业务需求进行扩展和定制。
