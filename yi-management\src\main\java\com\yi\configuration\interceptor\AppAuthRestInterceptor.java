package com.yi.configuration.interceptor;

import com.yi.configuration.annotation.OpenInterface;
import com.yi.configuration.auth.MesAuthService;
import com.yi.configuration.config.AppAuthConfig;
import com.yi.configuration.exception.AuthExceptionCodeEnum;
import com.yi.configuration.exception.BizException;
import com.yi.configuration.jwt.AppAuthUtil;
import com.yi.configuration.jwt.IJWTInfo;
import com.yi.configuration.jwt.JwtConstants;
import com.yi.configuration.jwt.TokenModule;
import com.yi.configuration.threadlocal.ThreadContext;
import com.yi.utils.RedisUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 拦截器，未登录不能访问
 */
public class AppAuthRestInterceptor extends HandlerInterceptorAdapter {
    private static final Logger log = LoggerFactory.getLogger(AppAuthRestInterceptor.class);
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private AppAuthUtil appAuthUtil;
    @Resource
    private MesAuthService mesAuthService;
    @Resource
    private AppAuthConfig appAuthConfig;

    public AppAuthRestInterceptor() {
    }

    // token过期时间距离当前时间小于等于30分钟的话，就执行刷新替换动作
    private static final long maxMillis = 30 * 60 * 1000L;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        HandlerMethod handlerMethod;
        if (handler instanceof HandlerMethod) {
            handlerMethod = (HandlerMethod) handler;
        } else {
            return super.preHandle(request, response, handler);
        }
        // 开放接口无需登录
        OpenInterface annotation = handlerMethod.getMethodAnnotation(OpenInterface.class);
        if (annotation != null) {
            return super.preHandle(request, response, handler);
        }

        // 优先从Header中获取Token
        String tokenOfThroughSession = request.getHeader(appAuthConfig.getTokenHeader());
        if (StringUtils.isBlank(tokenOfThroughSession)) {
            // 其次从Cookies中获取Token
            Cookie[] cookies = request.getCookies();
            if (cookies != null) {
                for (Cookie cookie : cookies) {
                    if (null != cookie && cookie.getName().equals(appAuthConfig.getTokenHeader())) {
                        tokenOfThroughSession = cookie.getValue();
                        break;
                    }
                }
            }
            // 最后从URL追加的参数获取Token
            if (StringUtils.isBlank(tokenOfThroughSession) && request.getMethod().equals(RequestMethod.GET.name())) {
                tokenOfThroughSession = request.getParameter(appAuthConfig.getTokenHeader());
            }
        }

        if (StringUtils.isBlank(tokenOfThroughSession)) {
            throw new BizException(AuthExceptionCodeEnum.TOKEN_NOT_NULL);
        }

        // 可能为空/为过期时间/为新重置的token
        IJWTInfo infoOfJwtFromToken = appAuthUtil.getInfoFromToken(tokenOfThroughSession);

        String userCode = infoOfJwtFromToken.getUserCode();
        if (StringUtils.isBlank(userCode)) {
            throw new BizException(AuthExceptionCodeEnum.JWT_TOKEN_EXPIRED);
        }
        Object redisTokenRealValueString = null;
        redisUtil.get(userCode);
        // 为空的话，正常情况下为重置密码/手动退出操作导致的结果
        if (null == redisTokenRealValueString) {
            throw new BizException(AuthExceptionCodeEnum.JWT_TOKEN_USER_CODE_NOT_EXIST);
        }
        Map<String, Object> tokenInfoMap = (Map<String, Object>) redisTokenRealValueString;
        // 从redis存取的tokenMap里获取到最新的token，再进行比较
        String token = (String) tokenInfoMap.get(JwtConstants.JWT_TOKEN_MAP_KEY_TOKEN);
        if (StringUtils.isBlank(token)) {
            throw new BizException(AuthExceptionCodeEnum.JWT_TOKEN_EXPIRED);
        } else if (!token.equals(tokenOfThroughSession)) {
            // 比较redis的token是不是当前的token，如果不是说明token已过期，需要中心登录
            throw new BizException(AuthExceptionCodeEnum.JWT_TOKEN_USER_CODE_NOT_EXIST);
        }
        // 比较正确之后再计算过期时间，在最后半个小时里进行token的刷新
        Date tokenExpireTime = (Date) tokenInfoMap.get(JwtConstants.JWT_TOKEN_MAP_KEY_TOKEN_EXPIRE_TIME);
        if (tokenExpireTime != null) {
            // 当前请求的token离过期还剩余时间在30m内的话，执行刷新token动作
            long millis = tokenExpireTime.getTime() - Calendar.getInstance().getTimeInMillis();
            if (millis <= 0) {
                throw new BizException(AuthExceptionCodeEnum.JWT_TOKEN_EXPIRED);
            } else if (millis <= maxMillis) {
                TokenModule tokenDTORefreshResultData = mesAuthService.refreshToken(tokenOfThroughSession);
                // 为避免同一操作页面，多个请求都落在半小时刷新token区间段，保证只重新生成一次token
                tokenOfThroughSession = tokenDTORefreshResultData.getToken();
            }
        }
        // 返回Token：有两种情况1)原始有效期内的token2)或者刷新阶段后的token
        response.setHeader(appAuthConfig.getTokenHeader(), tokenOfThroughSession);
        // 设置用户基本信息，设置用户会话上下文用户基本信息供API层使用,基于ThreadLocal方式存储
        // 这边增加值需要在TransferUserInfoFromRequestHeaderFilter#initUserInfo 中也要增加相应的ThreadLocal写入，否则传递token值service层取不到值
        ThreadContext.setUserName(infoOfJwtFromToken.getUserName());
        ThreadContext.setUserId(infoOfJwtFromToken.getUserId());
        ThreadContext.setToken(tokenOfThroughSession);

        if (null != infoOfJwtFromToken.getUserRoles() && !infoOfJwtFromToken.getUserRoles().isEmpty()) {
            ThreadContext.setUserRoles(infoOfJwtFromToken.getUserRoles());
        }
        log.info("tokenOfThroughSession={} \\r\\n appId={}, userName={},userCode={}", tokenOfThroughSession, infoOfJwtFromToken.getAppId(), infoOfJwtFromToken.getUserName(), infoOfJwtFromToken.getUserCode());
        return super.preHandle(request, response, handler);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        ThreadContext.remove();
        super.afterCompletion(request, response, handler, ex);
    }
}
