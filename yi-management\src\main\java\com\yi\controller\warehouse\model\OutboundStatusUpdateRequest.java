package com.yi.controller.warehouse.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 出库单状态更新请求
 */
@Data
@ApiModel(value = "OutboundStatusUpdateRequest", description = "出库单状态更新请求")
public class OutboundStatusUpdateRequest {

    @NotNull(message = "出库单ID不能为空")
    @ApiModelProperty(value = "出库单ID", required = true)
    private Long id;

    @ApiModelProperty(value = "实际出库数（确认出库时必填）")
    @Min(value = 0, message = "实际出库数不能小于0")
    private Integer actualQuantity;

    @ApiModelProperty(value = "操作备注")
    private String remark;
}
