package com.yi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yi.controller.customerdownstreamaddress.model.CustomerDownstreamAddressQueryRequest;
import com.yi.entity.TCustomerDownstreamAddress;
import com.yi.mapper.vo.CustomerDownstreamAddressPageVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户下游地址关联表 Mapper 接口
 */
@Mapper
public interface TCustomerDownstreamAddressMapper extends BaseMapper<TCustomerDownstreamAddress> {

    /**
     * 分页查询客户下游地址关联列表
     *
     * @param page 分页参数
     * @param companyId 公司ID
     * @param downstreamCustomerCompanyId 下游客户ID
     * @param warehouseId 仓库ID
     * @param enabled 启用状态
     * @return 分页结果
     */
    IPage<TCustomerDownstreamAddress> selectDownstreamAddressPage(Page<TCustomerDownstreamAddress> page,
                                                                  @Param("companyId") Long companyId,
                                                                  @Param("downstreamCustomerCompanyId") Long downstreamCustomerCompanyId,
                                                                  @Param("warehouseId") Long warehouseId,
                                                                  @Param("enabled") Integer enabled);

    /**
     * 根据下游客户ID查询关联地址列表
     *
     * @param downstreamCustomerCompanyId 下游客户ID
     * @return 关联地址列表
     */
    List<TCustomerDownstreamAddress> selectByDownstreamCustomerId(@Param("downstreamCustomerCompanyId") Long downstreamCustomerCompanyId);

    /**
     * 根据仓库ID查询关联地址列表
     *
     * @param warehouseId 仓库ID
     * @return 关联地址列表
     */
    List<TCustomerDownstreamAddress> selectByWarehouseId(@Param("warehouseId") Long warehouseId);

    /**
     * 查询客户下游地址关联列表（左关联客户公司和仓库）
     * 支持分页和不分页两种模式
     *
     * @param page 分页参数（为null时不分页）
     * @param request 查询条件
     * @return 查询结果
     */
    IPage<CustomerDownstreamAddressPageVO> selectCustomerDownstreamAddressWithDetail(Page<CustomerDownstreamAddressPageVO> page, @Param("request") CustomerDownstreamAddressQueryRequest request);

    /**
     * 查询客户下游地址关联列表（左关联客户公司和仓库，不分页）
     *
     * @param request 查询条件
     * @return 查询结果
     */
    List<CustomerDownstreamAddressPageVO> selectCustomerDownstreamAddressWithDetail(@Param("request") CustomerDownstreamAddressQueryRequest request);

    /**
     * 根据ID查询客户下游地址关联详情（左关联客户公司和仓库）
     *
     * @param id 关联ID
     * @return 关联详情
     */
    CustomerDownstreamAddressPageVO selectCustomerDownstreamAddressDetailById(@Param("id") Long id);
}
