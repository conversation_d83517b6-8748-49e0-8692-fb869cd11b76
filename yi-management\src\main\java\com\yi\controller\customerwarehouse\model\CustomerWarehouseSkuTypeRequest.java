package com.yi.controller.customerwarehouse.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 仓库SKU类型请求
 */
@Data
@ApiModel(value = "WarehouseSkuTypeRequest", description = "仓库SKU类型请求")
public class CustomerWarehouseSkuTypeRequest {

    @NotBlank(message = "一级类目不能为空")
    @ApiModelProperty(value = "一级类目：1-循环托盘", required = true)
    private String firstCategory;

    @ApiModelProperty(value = "二级类目")
    private String secondCategory;
}
