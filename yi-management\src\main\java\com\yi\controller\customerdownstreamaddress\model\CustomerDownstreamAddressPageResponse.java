package com.yi.controller.customerdownstreamaddress.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 客户下游地址关联分页返回响应
 */
@Data
@ApiModel(value = "CustomerDownstreamAddressPageResponse", description = "客户下游地址关联分页返回响应")
public class CustomerDownstreamAddressPageResponse {

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "关联状态")
    private String status;

    @ApiModelProperty(value = "下游客户")
    private String downstreamCustomerName;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "详细地址")
    private String detailedAddress;

    @ApiModelProperty(value = "最近编辑人")
    private String lastModifiedBy;

    @ApiModelProperty(value = "最近编辑时间")
    private String lastModifiedTime;
}
