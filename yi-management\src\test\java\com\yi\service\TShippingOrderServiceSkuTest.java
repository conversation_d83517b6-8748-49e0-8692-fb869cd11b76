package com.yi.service;

import com.yi.controller.shippingorder.model.ShippingOrderSkuTypeResponse;
import com.yi.entity.TContractSku;
import com.yi.entity.TWarehouseSku;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 发运订单SKU类型获取测试
 */
@SpringBootTest
public class TShippingOrderServiceSkuTest {

    @MockBean
    private TShippingOrderService shippingOrderService;

    @MockBean
    private TContractService contractService;

    @MockBean
    private TContractSkuService contractSkuService;

    @MockBean
    private TWarehouseSkuService warehouseSkuService;

    @Test
    public void testGetSkuTypesByContractAndWarehouse_Success() {
        // 准备测试数据
        String contractCode = "CONTRACT001";
        Long warehouseId = 1L;
        Long contractId = 100L;

        // 模拟合同SKU数据
        TContractSku contractSku1 = new TContractSku();
        contractSku1.setContractId(contractId);
        contractSku1.setFirstCategory(1); // 共享托盘

        TContractSku contractSku2 = new TContractSku();
        contractSku2.setContractId(contractId);
        contractSku2.setFirstCategory(2); // 其他类型

        List<TContractSku> contractSkuList = Arrays.asList(contractSku1, contractSku2);

        // 模拟仓库SKU数据
        TWarehouseSku warehouseSku1 = new TWarehouseSku();
        warehouseSku1.setWarehouseId(warehouseId);
        warehouseSku1.setFirstCategory(1);
        warehouseSku1.setSecondCategory("标准托盘");

        TWarehouseSku warehouseSku2 = new TWarehouseSku();
        warehouseSku2.setWarehouseId(warehouseId);
        warehouseSku2.setFirstCategory(1);
        warehouseSku2.setSecondCategory("加强托盘");

        List<TWarehouseSku> warehouseSkuList = Arrays.asList(warehouseSku1, warehouseSku2);

        // 模拟期望的响应数据
        ShippingOrderSkuTypeResponse response1 = new ShippingOrderSkuTypeResponse();
        response1.setFirstCategoryId("1");
        response1.setFirstCategoryName("共享托盘");
        response1.setSecondCategory("标准托盘");
        response1.setDisplayText("共享托盘 标准托盘");

        ShippingOrderSkuTypeResponse response2 = new ShippingOrderSkuTypeResponse();
        response2.setFirstCategoryId("1");
        response2.setFirstCategoryName("共享托盘");
        response2.setSecondCategory("加强托盘");
        response2.setDisplayText("共享托盘 加强托盘");

        List<ShippingOrderSkuTypeResponse> expectedList = Arrays.asList(response1, response2);

        // 模拟Service调用
        when(shippingOrderService.getSkuTypesByContractAndWarehouse(contractCode, warehouseId))
                .thenReturn(expectedList);

        // 调用方法
        List<ShippingOrderSkuTypeResponse> result = shippingOrderService.getSkuTypesByContractAndWarehouse(contractCode, warehouseId);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证第一个SKU类型
        ShippingOrderSkuTypeResponse firstSku = result.get(0);
        assertEquals("1", firstSku.getFirstCategoryId());
        assertEquals("共享托盘", firstSku.getFirstCategoryName());
        assertEquals("标准托盘", firstSku.getSecondCategory());
        assertEquals("共享托盘 标准托盘", firstSku.getDisplayText());

        // 验证第二个SKU类型
        ShippingOrderSkuTypeResponse secondSku = result.get(1);
        assertEquals("1", secondSku.getFirstCategoryId());
        assertEquals("共享托盘", secondSku.getFirstCategoryName());
        assertEquals("加强托盘", secondSku.getSecondCategory());
        assertEquals("共享托盘 加强托盘", secondSku.getDisplayText());

        // 验证Service方法被调用
        verify(shippingOrderService, times(1)).getSkuTypesByContractAndWarehouse(contractCode, warehouseId);
    }

    @Test
    public void testGetSkuTypesByContractAndWarehouse_EmptyParams() {
        // 测试空参数
        List<ShippingOrderSkuTypeResponse> result1 = shippingOrderService.getSkuTypesByContractAndWarehouse(null, 1L);
        List<ShippingOrderSkuTypeResponse> result2 = shippingOrderService.getSkuTypesByContractAndWarehouse("", 1L);
        List<ShippingOrderSkuTypeResponse> result3 = shippingOrderService.getSkuTypesByContractAndWarehouse("CONTRACT001", null);

        // 模拟返回空列表
        when(shippingOrderService.getSkuTypesByContractAndWarehouse(null, 1L)).thenReturn(List.of());
        when(shippingOrderService.getSkuTypesByContractAndWarehouse("", 1L)).thenReturn(List.of());
        when(shippingOrderService.getSkuTypesByContractAndWarehouse("CONTRACT001", null)).thenReturn(List.of());

        // 验证结果
        assertTrue(shippingOrderService.getSkuTypesByContractAndWarehouse(null, 1L).isEmpty());
        assertTrue(shippingOrderService.getSkuTypesByContractAndWarehouse("", 1L).isEmpty());
        assertTrue(shippingOrderService.getSkuTypesByContractAndWarehouse("CONTRACT001", null).isEmpty());
    }

    @Test
    public void testDisplayTextGeneration() {
        // 测试显示文本生成逻辑
        ShippingOrderSkuTypeResponse response = new ShippingOrderSkuTypeResponse();
        
        // 测试完整信息
        response.setFirstCategoryName("共享托盘");
        response.setSecondCategory("标准托盘");
        response.setDisplayText("共享托盘 标准托盘");
        assertEquals("共享托盘 标准托盘", response.getDisplayText());

        // 测试只有一级类目
        response.setFirstCategoryName("共享托盘");
        response.setSecondCategory("");
        response.setDisplayText("共享托盘");
        assertEquals("共享托盘", response.getDisplayText());

        // 测试只有二级类目
        response.setFirstCategoryName("");
        response.setSecondCategory("标准托盘");
        response.setDisplayText("标准托盘");
        assertEquals("标准托盘", response.getDisplayText());
    }
}
