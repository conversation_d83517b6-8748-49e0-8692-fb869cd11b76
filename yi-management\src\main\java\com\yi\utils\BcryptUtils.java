package com.yi.utils;

import cn.hutool.crypto.digest.BCrypt;

public class BcryptUtils {


    /**
     * 给用户随机一个BCrypt的盐值
     * @return
     */
    public  static String genSaltForUser(){
        return BCrypt.gensalt();
    }


    /**
     * 给用户随机一个BCrypt的盐值
     * @return
     */
    public static String bcryptForPassword(String password,String salt){
        return  BCrypt.hashpw(password,salt);
    }
}
