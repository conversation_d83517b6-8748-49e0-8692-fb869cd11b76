# 出入库管理系统 Postman 测试集合

## 概述

本文档提供了完整的Postman测试用例，可以直接导入Postman进行API测试。

## 环境变量设置

在Postman中设置以下环境变量：

```json
{
  "baseUrl": "http://localhost:8080",
  "token": "your-auth-token-here"
}
```

## 1. 单号生成测试

### 1.1 生成出库单号

```http
POST {{baseUrl}}/api/warehouse/order-number/outbound
Authorization: Bearer {{token}}
```

### 1.2 获取当日单号数量

```http
GET {{baseUrl}}/api/warehouse/order-number/count/today
Authorization: Bearer {{token}}
```

### 1.3 预览下一个单号

```http
GET {{baseUrl}}/api/warehouse/order-number/preview
Authorization: Bearer {{token}}
```

## 2. 出库单管理测试

### 2.1 创建出库单

```http
POST {{baseUrl}}/api/warehouse/outbound
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "outboundType": 1,
  "outboundCompanyId": 1,
  "outboundCompanyName": "易托盘科技有限公司",
  "outboundAddress": "上海市浦东新区张江高科技园区",
  "deliveryMethod": "物流配送",
  "vehicleNumber": "沪A12345",
  "driverName": "张师傅",
  "driverPhone": "13800138001",
  "receiveCompanyId": 2,
  "receiveCompanyName": "客户公司A",
  "receiveAddress": "北京市朝阳区建国门外大街1号",
  "firstCategory": 1,
  "secondCategory": "标准托盘",
  "plannedQuantity": 100,
  "remark": "测试出库单"
}
```

### 2.2 分页查询出库单

```http
POST {{baseUrl}}/api/warehouse/outbound/page
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "current": "1",
  "size": "10",
  "status": 1,
  "outboundType": 1
}
```

### 2.3 查询出库单详情

```http
GET {{baseUrl}}/api/warehouse/outbound/1
Authorization: Bearer {{token}}
```

### 2.4 确认出库

```http
POST {{baseUrl}}/api/warehouse/outbound/confirm
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "id": 1,
  "actualQuantity": 95,
  "remark": "实际出库95个"
}
```

### 2.5 完成出库

```http
POST {{baseUrl}}/api/warehouse/outbound/complete
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "id": 1,
  "remark": "货物已送达"
}
```

### 2.6 查询待出库订单

```http
GET {{baseUrl}}/api/warehouse/outbound/pending
Authorization: Bearer {{token}}
```

### 2.7 查询状态统计

```http
GET {{baseUrl}}/api/warehouse/outbound/statistics/status
Authorization: Bearer {{token}}
```

## 3. 入库单管理测试

### 3.1 创建入库单

```http
POST {{baseUrl}}/api/warehouse/inbound
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "orderNo": "F202412010001",
  "inboundType": 4,
  "inboundWarehouseId": 2,
  "inboundWarehouseName": "北京仓库",
  "deliveryMethod": "物流配送",
  "vehicleNumber": "沪A12345",
  "driverName": "张师傅",
  "driverPhone": "13800138001",
  "senderWarehouseId": 1,
  "senderWarehouseName": "上海总仓",
  "senderAddress": "上海市浦东新区张江高科技园区",
  "firstCategory": 1,
  "secondCategory": "标准托盘",
  "plannedQuantity": 100,
  "outboundOrderId": 1,
  "remark": "测试入库单"
}
```

### 3.2 分页查询入库单

```http
POST {{baseUrl}}/api/warehouse/inbound/page
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "current": "1",
  "size": "10",
  "status": 1,
  "inboundType": 4
}
```

### 3.3 部分入库

```http
POST {{baseUrl}}/api/warehouse/inbound/partial
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "id": 1,
  "actualQuantity": 50,
  "remark": "部分入库50个"
}
```

### 3.4 完成入库

```http
POST {{baseUrl}}/api/warehouse/inbound/complete
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "id": 1,
  "actualQuantity": 100,
  "remark": "全部入库完成"
}
```

## 4. 完整业务流程测试

### 4.1 销售出库流程

```javascript
// Postman Pre-request Script
pm.globals.set("orderNo", "");

// Test 1: 创建出库单
pm.test("创建出库单", function () {
    pm.response.to.have.status(200);
    var jsonData = pm.response.json();
    pm.expect(jsonData.code).to.eql(200);
    pm.globals.set("orderNo", jsonData.data);
    pm.globals.set("outboundOrderId", jsonData.data);
});

// Test 2: 创建入库单
pm.test("创建入库单", function () {
    pm.response.to.have.status(200);
    var jsonData = pm.response.json();
    pm.expect(jsonData.code).to.eql(200);
});

// Test 3: 确认出库
pm.test("确认出库", function () {
    pm.response.to.have.status(200);
    var jsonData = pm.response.json();
    pm.expect(jsonData.code).to.eql(200);
});

// Test 4: 完成出库
pm.test("完成出库", function () {
    pm.response.to.have.status(200);
    var jsonData = pm.response.json();
    pm.expect(jsonData.code).to.eql(200);
});

// Test 5: 完成入库
pm.test("完成入库", function () {
    pm.response.to.have.status(200);
    var jsonData = pm.response.json();
    pm.expect(jsonData.code).to.eql(200);
});
```

## 5. 错误场景测试

### 5.1 状态流转错误

```http
# 尝试对已完成的订单再次确认出库
POST {{baseUrl}}/api/warehouse/outbound/confirm
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "id": 1,
  "actualQuantity": 100
}

# 预期响应: 
# {
#   "code": 500,
#   "message": "确认出库失败：只有待出库状态的订单才能确认出库"
# }
```

### 5.2 数据验证错误

```http
# 创建出库单时缺少必填字段
POST {{baseUrl}}/api/warehouse/outbound
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "outboundType": 1
  // 缺少其他必填字段
}

# 预期响应: 
# {
#   "code": 400,
#   "message": "参数验证失败：出库公司ID不能为空"
# }
```

## 6. 性能测试

### 6.1 并发单号生成测试

```javascript
// Postman Collection Runner 设置
// Iterations: 100
// Delay: 0ms

pm.test("并发生成单号唯一性", function () {
    pm.response.to.have.status(200);
    var jsonData = pm.response.json();
    pm.expect(jsonData.code).to.eql(200);
    
    // 检查单号格式
    var orderNo = jsonData.data;
    pm.expect(orderNo).to.match(/^F\d{8}\d{4}$/);
    
    // 存储单号用于后续唯一性检查
    var orderNos = pm.globals.get("orderNos") || [];
    pm.expect(orderNos).to.not.include(orderNo);
    orderNos.push(orderNo);
    pm.globals.set("orderNos", orderNos);
});
```

## 7. 数据清理

### 7.1 清理测试数据

```http
# 批量删除测试出库单
DELETE {{baseUrl}}/api/warehouse/outbound/batch
Authorization: Bearer {{token}}
Content-Type: application/json

[1, 2, 3, 4, 5]

# 批量删除测试入库单
DELETE {{baseUrl}}/api/warehouse/inbound/batch
Authorization: Bearer {{token}}
Content-Type: application/json

[1, 2, 3, 4, 5]
```

## 8. 导入说明

### 8.1 Postman Collection JSON

将以下内容保存为 `.json` 文件，然后在Postman中导入：

```json
{
  "info": {
    "name": "出入库管理系统API测试",
    "description": "完整的出入库管理系统API测试集合",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "variable": [
    {
      "key": "baseUrl",
      "value": "http://localhost:8080"
    }
  ],
  "auth": {
    "type": "bearer",
    "bearer": [
      {
        "key": "token",
        "value": "{{token}}",
        "type": "string"
      }
    ]
  }
}
```

### 8.2 环境配置

在Postman中创建环境，添加以下变量：

- `baseUrl`: `http://localhost:8080`
- `token`: `your-auth-token`

## 9. 自动化测试脚本

### 9.1 Newman 命令行测试

```bash
# 安装Newman
npm install -g newman

# 运行测试集合
newman run warehouse-api-tests.json \
  --environment warehouse-env.json \
  --reporters cli,html \
  --reporter-html-export warehouse-test-report.html
```

### 9.2 CI/CD 集成

```yaml
# GitHub Actions 示例
name: API Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Run API Tests
        run: |
          npm install -g newman
          newman run tests/warehouse-api-tests.json \
            --environment tests/warehouse-env.json \
            --reporters cli,junit \
            --reporter-junit-export test-results.xml
```

这个Postman测试集合提供了完整的API测试覆盖，包括正常流程、错误场景、性能测试和自动化集成。
