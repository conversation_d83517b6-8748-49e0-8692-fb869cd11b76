# 文件上传配置
file:
  upload:
    # 临时文件上传根路径（绝对路径）
    temp-path: /data/uploads/temp
    # 正式文件存储根路径（绝对路径）
    base-path: /data/uploads
    # 允许上传的文件类型
    allowed-types:
      - jpg
      - jpeg
      - png
      - gif
      - bmp
      - webp
      - pdf
      - doc
      - docx
      - xls
      - xlsx
      - ppt
      - pptx
      - txt
      - csv
      - zip
      - rar
      - 7z
    # 单个文件最大大小（10MB）
    max-file-size: 10485760
    # 总上传大小限制（50MB）
    max-request-size: 52428800
    # 临时文件访问URL前缀
    temp-url-prefix: /temp-files
    # 正式文件访问URL前缀
    url-prefix: /files
    # 临时文件保留时间（小时）
    temp-file-retention-hours: 24

# Spring Boot 文件上传配置
spring:
  servlet:
    multipart:
      # 启用文件上传
      enabled: true
      # 单个文件最大大小
      max-file-size: 10MB
      # 总上传大小限制
      max-request-size: 50MB
      # 文件写入磁盘的阈值
      file-size-threshold: 2KB
      # 临时文件存储位置
      location: /tmp
