<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yi.mapper.SysDeptMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yi.entity.SysDept">
        <id column="id" property="id" />
        <result column="parent_id" property="parentId" />
        <result column="dept_code" property="deptCode" />
        <result column="dept_name" property="deptName" />
        <result column="dept_desc" property="deptDesc" />
        <result column="order_num" property="orderNum" />
        <result column="leader" property="leader" />
        <result column="phone" property="phone" />
        <result column="email" property="email" />
        <result column="status" property="status" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="last_modified_by" property="lastModifiedBy" />
        <result column="last_modified_time" property="lastModifiedTime" />
        <result column="valid" property="valid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, parent_id, dept_code, dept_name, dept_desc, order_num, leader, phone, 
        email, status, created_by, created_time, last_modified_by, last_modified_time, valid
    </sql>

    <!-- 查询部门树形结构 -->
    <select id="selectDeptTree" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM t_sys_dept
        WHERE valid = 1
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY parent_id ASC, order_num ASC, created_time ASC
    </select>

    <!-- 根据父ID查询子部门 -->
    <select id="selectByParentId" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM t_sys_dept
        WHERE parent_id = #{parentId} AND valid = 1
        ORDER BY order_num ASC, created_time ASC
    </select>

    <!-- 根据部门编码查询部门 -->
    <select id="selectByDeptCode" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM t_sys_dept
        WHERE dept_code = #{deptCode} AND valid = 1
    </select>

    <!-- 查询部门及其所有子部门ID -->
    <select id="selectDeptAndChildrenIds" resultType="java.lang.Long">
        WITH RECURSIVE dept_tree AS (
            SELECT id, parent_id, dept_name
            FROM t_sys_dept
            WHERE id = #{deptId} AND valid = 1

            UNION ALL

            SELECT d.id, d.parent_id, d.dept_name
            FROM t_sys_dept d
            INNER JOIN dept_tree dt ON d.parent_id = dt.id
            WHERE d.valid = 1
        )
        SELECT id FROM dept_tree
    </select>

    <!-- 查询所有启用的部门 -->
    <select id="selectEnabledDepts" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM t_sys_dept
        WHERE valid = 1 AND status = 1
        ORDER BY parent_id ASC, order_num ASC, created_time ASC
    </select>

    <!-- 检查部门是否有子部门 -->
    <select id="hasChildren" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM t_sys_dept
        WHERE parent_id = #{deptId} AND valid = 1
    </select>

    <!-- 检查部门是否有用户 -->
    <select id="hasUsers" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM t_sys_user
        WHERE dept_id = #{deptId} AND valid = 1
    </select>

</mapper>
