# 发货需求分页查询接口

## 接口信息

**接口地址**: `POST /api/shipping-demand/page`

**接口描述**: 分页查询发货需求列表，支持多条件筛选

## 请求参数

### 请求体

```json
{
    "current": "1",
    "size": "10",
    "status": "1000",
    "orderNo": "SO20240101",
    "customerCompanyName": "测试客户",
    "warehouseName": "测试仓库"
}
```

### 字段说明

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | String | 否 | 当前页码，默认"1" |
| size | String | 否 | 每页大小，默认"10" |
| status | String | 否 | 发货状态：1000-待发货，2000-已发货，3000-已完结，4000-已取消 |
| orderNo | String | 否 | 发运订单号（模糊查询） |
| customerCompanyName | String | 否 | 客户主体名称（模糊查询） |
| warehouseName | String | 否 | 收货仓库名称（模糊查询） |

## 返回参数

### 成功响应

```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "current": 1,
        "size": 10,
        "total": 100,
        "pages": 10,
        "records": [
            {
                "id": "1",
                "status": "1000",
                "statusName": "待发货",
                "orderNo": "SO20240101000001",
                "customerCompanyName": "测试客户公司",
                "warehouseName": "测试收货仓库",
                "receivingAddress": "北京市朝阳区测试地址123号",
                "receiverName": "张三",
                "productName": "共享托盘-标准托盘",
                "demandQuantity": "100",
                "pendingQuantity": "80",
                "shippedQuantity": "20",
                "unexecutedQuantity": "0",
                "demandTime": "2024-01-01",
                "createdBy": "admin",
                "createdTime": "2024-01-01 10:00:00"
            }
        ]
    }
}
```

### 返回字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | String | 发货需求ID |
| status | String | 发货状态码 |
| statusName | String | 发货状态名称 |
| orderNo | String | 发运订单号 |
| customerCompanyName | String | 客户主体名称 |
| warehouseName | String | 收货仓库名称 |
| receivingAddress | String | 收货地址 |
| receiverName | String | 收货人 |
| productName | String | 产品名称（一级类目-二级类目） |
| demandQuantity | String | 需求数量 |
| pendingQuantity | String | 待确认数量 |
| shippedQuantity | String | 发货数量 |
| unexecutedQuantity | String | 未执行数量 |
| demandTime | String | 需求时间 |
| createdBy | String | 创建人 |
| createdTime | String | 创建时间 |

## 业务逻辑

### 状态管理

发货需求状态流转：
```
1000(待发货) → 2000(已发货) → 3000(已完结)
     ↓
4000(已取消)
```

### 数量关系

数量字段之间的关系：
```
需求数量 = 待确认数量 + 发货数量 + 未执行数量
```

- **需求数量**: 订单创建时的原始需求
- **待确认数量**: 等待确认发货的数量
- **发货数量**: 已确认发货的数量
- **未执行数量**: 需求时间结束后未执行的数量

### 产品名称组合

产品名称由一级类目和二级类目组合而成：
- 只有一级类目：显示"共享托盘"
- 有二级类目：显示"共享托盘-标准托盘"

### 查询条件

#### 1. 状态筛选
- 精确匹配状态码
- 支持的状态值：1000、2000、3000、4000

#### 2. 订单号筛选
- 模糊查询，支持部分订单号匹配
- 例如：输入"SO2024"可匹配"SO20240101000001"

#### 3. 客户主体筛选
- 模糊查询客户公司名称
- 例如：输入"测试"可匹配"测试客户公司"

#### 4. 收货仓库筛选
- 模糊查询仓库名称
- 例如：输入"仓库"可匹配"测试收货仓库"

## 数据库查询

### SQL逻辑

```sql
SELECT 
    sd.id,
    sd.order_no,
    sd.status,
    CASE sd.status
        WHEN 1000 THEN '待发货'
        WHEN 2000 THEN '已发货'
        WHEN 3000 THEN '已完结'
        WHEN 4000 THEN '已取消'
        ELSE '未知状态'
    END as status_name,
    cc.company_name as customer_company_name,
    w.warehouse_name,
    w.receiving_address,
    w.receiver_name,
    sd.first_category,
    CASE sd.first_category
        WHEN 1 THEN '共享托盘'
        ELSE '未知类目'
    END as first_category_name,
    sd.second_category,
    sd.demand_quantity,
    sd.pending_quantity,
    sd.shipped_quantity,
    sd.unexecuted_quantity,
    sd.demand_time,
    sd.created_by,
    sd.created_time
FROM t_shipping_demand sd
LEFT JOIN t_customer_company cc ON sd.customer_company_id = cc.id
LEFT JOIN t_customer_warehouse w ON sd.warehouse_id = w.id
WHERE sd.valid = 1
  AND (#{status} IS NULL OR sd.status = #{status})
  AND (#{orderNo} IS NULL OR sd.order_no LIKE CONCAT('%', #{orderNo}, '%'))
  AND (#{customerCompanyName} IS NULL OR cc.company_name LIKE CONCAT('%', #{customerCompanyName}, '%'))
  AND (#{warehouseName} IS NULL OR w.warehouse_name LIKE CONCAT('%', #{warehouseName}, '%'))
ORDER BY sd.created_time DESC
```

### 关联表说明

- **t_shipping_demand**: 发货需求主表
- **t_customer_company**: 客户公司表，获取客户名称
- **t_customer_warehouse**: 客户仓库表，获取仓库信息、收货地址、收货人

## 使用示例

### 基本分页查询

```bash
curl -X POST "http://localhost:8080/api/shipping-demand/page" \
  -H "Content-Type: application/json" \
  -d '{
    "current": "1",
    "size": "10"
  }'
```

### 按状态筛选

```bash
curl -X POST "http://localhost:8080/api/shipping-demand/page" \
  -H "Content-Type: application/json" \
  -d '{
    "current": "1",
    "size": "10",
    "status": "1000"
  }'
```

### 多条件组合查询

```bash
curl -X POST "http://localhost:8080/api/shipping-demand/page" \
  -H "Content-Type: application/json" \
  -d '{
    "current": "1",
    "size": "20",
    "status": "1000",
    "orderNo": "SO2024",
    "customerCompanyName": "测试客户",
    "warehouseName": "北京仓库"
  }'
```

## 导出功能

### 导出接口

**接口地址**: `POST /api/shipping-demand/export`

**请求参数**: 与分页查询相同（不需要current和size参数）

**响应**: 直接下载Excel文件

### 导出字段

导出的Excel包含以下字段：
1. 序号
2. 状态
3. 订单号
4. 客户主体
5. 收货仓库
6. 收货地址
7. 收货人
8. 产品
9. 需求数量
10. 待确认数
11. 发货数量
12. 未执行数
13. 需求时间
14. 创建人
15. 创建时间

### 导出示例

```bash
curl -X POST "http://localhost:8080/api/shipping-demand/export" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "1000",
    "customerCompanyName": "测试客户"
  }' \
  --output "发货需求列表.xlsx"
```

## 错误处理

### 常见错误

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 400 | 分页参数错误 | current或size参数格式不正确 |
| 400 | 状态参数错误 | status参数值不在有效范围内 |
| 500 | 查询失败 | 数据库查询异常 |

### 错误响应示例

```json
{
    "code": 400,
    "message": "分页参数错误：页码必须大于0",
    "data": null
}
```

## 性能优化

### 索引建议

```sql
-- 发货需求表索引
CREATE INDEX idx_shipping_demand_status ON t_shipping_demand(status);
CREATE INDEX idx_shipping_demand_order_no ON t_shipping_demand(order_no);
CREATE INDEX idx_shipping_demand_customer_id ON t_shipping_demand(customer_company_id);
CREATE INDEX idx_shipping_demand_warehouse_id ON t_shipping_demand(warehouse_id);
CREATE INDEX idx_shipping_demand_created_time ON t_shipping_demand(created_time);
CREATE INDEX idx_shipping_demand_valid ON t_shipping_demand(valid);

-- 复合索引
CREATE INDEX idx_shipping_demand_status_time ON t_shipping_demand(status, created_time);
```

### 查询优化

1. **分页优化**: 使用LIMIT和OFFSET进行分页
2. **条件优化**: 只有非空条件才参与WHERE子句
3. **排序优化**: 按创建时间倒序，利用时间索引
4. **关联优化**: 使用LEFT JOIN避免数据丢失

## 注意事项

1. **数据一致性**: 确保关联表数据完整性
2. **权限控制**: 根据用户权限过滤可见数据
3. **性能监控**: 监控大数据量查询性能
4. **缓存策略**: 考虑对热点数据进行缓存
5. **导出限制**: 导出数据量建议不超过10万条
