package com.yi.controller.warehouse;

import com.yi.common.Result;
import com.yi.service.OrderNumberService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 单号生成控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/warehouse/order-number")
@Api(tags = "单号生成管理")
public class OrderNumberController {

    @Autowired
    private OrderNumberService orderNumberService;

    @PostMapping("/outbound")
    @ApiOperation("生成出库单号")
    public Result<String> generateOutboundOrderNo() {
        try {
            String orderNo = orderNumberService.generateOutboundOrderNo();
            return Result.success("生成成功", orderNo);
        } catch (Exception e) {
            log.error("生成出库单号失败", e);
            return Result.failed("生成失败：" + e.getMessage());
        }
    }

    @PostMapping("/inbound")
    @ApiOperation("生成入库单号")
    public Result<String> generateInboundOrderNo() {
        try {
            String orderNo = orderNumberService.generateInboundOrderNo();
            return Result.success("生成成功", orderNo);
        } catch (Exception e) {
            log.error("生成入库单号失败", e);
            return Result.failed("生成失败：" + e.getMessage());
        }
    }

    @PostMapping("/generate/{dateKey}")
    @ApiOperation("根据指定日期生成单号")
    public Result<String> generateOrderNoByDate(@ApiParam("日期键（YYYYMMDD）") @PathVariable String dateKey) {
        try {
            // 验证日期格式
            if (dateKey == null || !dateKey.matches("\\d{8}")) {
                return Result.failed("日期格式错误，应为YYYYMMDD格式");
            }

            String orderNo = orderNumberService.generateOrderNoByDate(dateKey);
            return Result.success("生成成功", orderNo);
        } catch (Exception e) {
            log.error("生成单号失败", e);
            return Result.failed("生成失败：" + e.getMessage());
        }
    }

    @GetMapping("/count/today")
    @ApiOperation("获取当日已生成的单号数量")
    public Result<Integer> getTodayOrderCount() {
        try {
            Integer count = orderNumberService.getTodayOrderCount();
            return Result.success(count);
        } catch (Exception e) {
            log.error("获取当日单号数量失败", e);
            return Result.failed("获取失败：" + e.getMessage());
        }
    }

    @GetMapping("/count/{dateKey}")
    @ApiOperation("获取指定日期已生成的单号数量")
    public Result<Integer> getOrderCountByDate(@ApiParam("日期键（YYYYMMDD）") @PathVariable String dateKey) {
        try {
            // 验证日期格式
            if (dateKey == null || !dateKey.matches("\\d{8}")) {
                return Result.failed("日期格式错误，应为YYYYMMDD格式");
            }

            Integer count = orderNumberService.getOrderCountByDate(dateKey);
            return Result.success(count);
        } catch (Exception e) {
            log.error("获取指定日期单号数量失败", e);
            return Result.failed("获取失败：" + e.getMessage());
        }
    }

    @GetMapping("/info")
    @ApiOperation("获取单号生成信息")
    public Result<Map<String, Object>> getOrderNumberInfo() {
        try {
            Map<String, Object> info = new HashMap<>();

            // 当日单号数量
            Integer todayCount = orderNumberService.getTodayOrderCount();
            info.put("todayCount", todayCount);

            // 下一个单号预览
            String nextOrderNo = orderNumberService.generateOutboundOrderNo();
            info.put("nextOrderNo", nextOrderNo);

            // 单号格式说明
            info.put("format", "F + YYYYMMDD + 4位序列号");
            info.put("example", "F202412010001");
            info.put("description", "F开头，年月日，4位递增序列号");

            return Result.success(info);
        } catch (Exception e) {
            log.error("获取单号生成信息失败", e);
            return Result.failed("获取失败：" + e.getMessage());
        }
    }

    @GetMapping("/preview")
    @ApiOperation("预览下一个单号（不实际生成）")
    public Result<Map<String, Object>> previewNextOrderNo() {
        try {
            Map<String, Object> preview = new HashMap<>();

            // 当前已生成数量
            Integer currentCount = orderNumberService.getTodayOrderCount();
            preview.put("currentCount", currentCount);

            // 下一个序列号
            Integer nextSequence = currentCount + 1;
            preview.put("nextSequence", nextSequence);

            // 预览单号格式
            String dateKey = java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd"));
            String previewOrderNo = String.format("F%s%04d", dateKey, nextSequence);
            preview.put("previewOrderNo", previewOrderNo);

            return Result.success(preview);
        } catch (Exception e) {
            log.error("预览下一个单号失败", e);
            return Result.failed("预览失败：" + e.getMessage());
        }
    }
}
