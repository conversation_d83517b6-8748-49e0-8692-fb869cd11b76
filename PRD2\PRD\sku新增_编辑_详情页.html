﻿<!DOCTYPE html>
<html>
  <head>
    <title>SKU新增/编辑/详情页</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/sku新增_编辑_详情页/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/sku新增_编辑_详情页/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (线段) -->
      <div id="u323" class="ax_default line1">
        <img id="u323_img" class="img " src="images/sku管理/u187.svg"/>
        <div id="u323_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u324" class="ax_default box_3">
        <div id="u324_div" class=""></div>
        <div id="u324_text" class="text ">
          <p><span>SKU新增/编辑/详情&nbsp; &nbsp; &nbsp; &nbsp;&nbsp; </span><span style="color:#D9001B;">X</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u325" class="ax_default link_button">
        <div id="u325_div" class=""></div>
        <div id="u325_text" class="text ">
          <p><span>刷新本页</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u326" class="ax_default label">
        <div id="u326_div" class=""></div>
        <div id="u326_text" class="text ">
          <p><span>*一级类目</span></p>
        </div>
      </div>

      <!-- Unnamed (下拉列表) -->
      <div id="u327" class="ax_default droplist">
        <div id="u327_div" class=""></div>
        <select id="u327_input" class="u327_input">
          <option class="u327_input_option" value="易炬托盘">易炬托盘</option>
        </select>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u328" class="ax_default label">
        <div id="u328_div" class=""></div>
        <div id="u328_text" class="text ">
          <p><span>*二级类目</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u329" class="ax_default text_field">
        <div id="u329_div" class=""></div>
        <input id="u329_input" type="text" value="" class="u329_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u330" class="ax_default label">
        <div id="u330_div" class=""></div>
        <div id="u330_text" class="text ">
          <p><span>*规格</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u331" class="ax_default text_field">
        <div id="u331_div" class=""></div>
        <input id="u331_input" type="text" value="" class="u331_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u332" class="ax_default label">
        <div id="u332_div" class=""></div>
        <div id="u332_text" class="text ">
          <p><span>长</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u333" class="ax_default label">
        <div id="u333_div" class=""></div>
        <div id="u333_text" class="text ">
          <p><span>mm</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u334" class="ax_default text_field">
        <div id="u334_div" class=""></div>
        <input id="u334_input" type="text" value="" class="u334_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u335" class="ax_default label">
        <div id="u335_div" class=""></div>
        <div id="u335_text" class="text ">
          <p><span>宽</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u336" class="ax_default label">
        <div id="u336_div" class=""></div>
        <div id="u336_text" class="text ">
          <p><span>mm</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u337" class="ax_default text_field">
        <div id="u337_div" class=""></div>
        <input id="u337_input" type="text" value="" class="u337_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u338" class="ax_default label">
        <div id="u338_div" class=""></div>
        <div id="u338_text" class="text ">
          <p><span>高</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u339" class="ax_default label">
        <div id="u339_div" class=""></div>
        <div id="u339_text" class="text ">
          <p><span>mm</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u340" class="ax_default label">
        <div id="u340_div" class=""></div>
        <div id="u340_text" class="text ">
          <p><span>*重量</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u341" class="ax_default text_field">
        <div id="u341_div" class=""></div>
        <input id="u341_input" type="text" value="" class="u341_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u342" class="ax_default label">
        <div id="u342_div" class=""></div>
        <div id="u342_text" class="text ">
          <p><span>Kg</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u343" class="ax_default primary_button">
        <div id="u343_div" class=""></div>
        <div id="u343_text" class="text ">
          <p><span>保存</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u344" class="ax_default box_1">
        <div id="u344_div" class=""></div>
        <div id="u344_text" class="text ">
          <p><span>&nbsp;</span><span>不多于300字</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u345" class="ax_default label">
        <div id="u345_div" class=""></div>
        <div id="u345_text" class="text ">
          <p><span>备注</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u346" class="ax_default sticky_1">
        <div id="u346_div" class=""></div>
        <div id="u346_text" class="text ">
          <p><span>【一级+二级+三级】唯一不可重复；</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u347" class="ax_default label">
        <div id="u347_div" class=""></div>
        <div id="u347_text" class="text ">
          <p><span>*三级类目</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u348" class="ax_default text_field">
        <div id="u348_div" class=""></div>
        <input id="u348_input" type="text" value="" class="u348_input"/>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
