# 账号管理模块API测试示例

## 测试环境准备

1. 确保数据库已执行RBAC相关SQL脚本
2. 启动Spring Boot应用
3. 使用Postman或其他API测试工具

## API测试用例

### 1. 分页查询用户列表

```http
POST http://localhost:8080/api/sys-user/page
Content-Type: application/json

{
  "current": "1",
  "size": "10",
  "status": 1
}
```

**期望响应：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "username": "admin",
        "realName": "系统管理员",
        "email": "<EMAIL>",
        "phone": "13800138000",
        "genderDesc": "男",
        "deptName": "总公司",
        "statusDesc": "启用",
        "roles": [
          {
            "roleId": 1,
            "roleCode": "SUPER_ADMIN",
            "roleName": "超级管理员"
          }
        ],
        "createdTime": "2023-12-01T09:00:00"
      }
    ],
    "total": 4,
    "current": 1,
    "size": 10
  }
}
```

### 2. 根据条件筛选用户

```http
POST http://localhost:8080/api/sys-user/page
Content-Type: application/json

{
  "current": "1",
  "size": "10",
  "realName": "管理员",
  "status": 1
}
```

### 3. 获取用户详情

```http
GET http://localhost:8080/api/sys-user/1
```

**期望响应：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "username": "admin",
    "realName": "系统管理员",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "genderDesc": "男",
    "deptName": "总公司",
    "statusDesc": "启用",
    "roleIds": [1],
    "roles": [
      {
        "roleId": 1,
        "roleCode": "SUPER_ADMIN",
        "roleName": "超级管理员",
        "roleDesc": "拥有系统所有权限"
      }
    ],
    "permissions": [
      "system:user:list",
      "system:user:add",
      "system:user:edit"
    ]
  }
}
```

### 4. 新增用户

```http
POST http://localhost:8080/api/sys-user/add
Content-Type: application/json

{
  "username": "testuser",
  "password": "123456",
  "realName": "测试用户",
  "email": "<EMAIL>",
  "phone": "13800138001",
  "gender": 1,
  "deptId": 2,
  "status": 1,
  "roleIds": [4]
}
```

**期望响应：**
```json
{
  "code": 200,
  "message": "新增成功",
  "data": true
}
```

### 5. 编辑用户

```http
PUT http://localhost:8080/api/sys-user/edit
Content-Type: application/json

{
  "id": 5,
  "username": "testuser",
  "realName": "测试用户（已修改）",
  "email": "<EMAIL>",
  "phone": "13800138001",
  "gender": 1,
  "deptId": 2,
  "status": 1,
  "roleIds": [4, 5]
}
```

### 6. 启用/禁用用户

```http
PUT http://localhost:8080/api/sys-user/5/status?status=0
```

**期望响应：**
```json
{
  "code": 200,
  "message": "禁用成功",
  "data": true
}
```

### 7. 重置密码

```http
PUT http://localhost:8080/api/sys-user/reset-password
Content-Type: application/json

{
  "userId": 5,
  "newPassword": "newpassword123"
}
```

### 8. 修改密码

```http
PUT http://localhost:8080/api/sys-user/change-password
Content-Type: application/json

{
  "userId": 5,
  "oldPassword": "123456",
  "newPassword": "newpassword123"
}
```

### 9. 分配用户角色

```http
PUT http://localhost:8080/api/sys-user/5/roles
Content-Type: application/json

[4, 5]
```

### 10. 删除用户

```http
DELETE http://localhost:8080/api/sys-user/5
```

### 11. 批量删除用户

```http
DELETE http://localhost:8080/api/sys-user/batch
Content-Type: application/json

[5, 6, 7]
```

### 12. 检查用户名是否存在

```http
GET http://localhost:8080/api/sys-user/check-username?username=testuser&excludeId=5
```

**期望响应：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": false
}
```

### 13. 导出用户列表

```http
POST http://localhost:8080/api/sys-user/export
Content-Type: application/json

{
  "status": "1",
  "deptId": 2
}
```

**期望响应：** Excel文件下载

## 错误场景测试

### 1. 用户名重复

```http
POST http://localhost:8080/api/sys-user/add
Content-Type: application/json

{
  "username": "admin",
  "password": "123456",
  "realName": "重复用户名",
  "email": "<EMAIL>"
}
```

**期望响应：**
```json
{
  "code": 500,
  "message": "用户名已存在",
  "data": null
}
```

### 2. 邮箱格式错误

```http
POST http://localhost:8080/api/sys-user/add
Content-Type: application/json

{
  "username": "testuser2",
  "password": "123456",
  "realName": "测试用户2",
  "email": "invalid-email"
}
```

**期望响应：**
```json
{
  "code": 400,
  "message": "邮箱格式不正确",
  "data": null
}
```

### 3. 手机号格式错误

```http
POST http://localhost:8080/api/sys-user/add
Content-Type: application/json

{
  "username": "testuser3",
  "password": "123456",
  "realName": "测试用户3",
  "phone": "123456"
}
```

**期望响应：**
```json
{
  "code": 400,
  "message": "手机号格式不正确",
  "data": null
}
```

### 4. 用户不存在

```http
GET http://localhost:8080/api/sys-user/999
```

**期望响应：**
```json
{
  "code": 500,
  "message": "用户不存在",
  "data": null
}
```

### 5. 原密码错误

```http
PUT http://localhost:8080/api/sys-user/change-password
Content-Type: application/json

{
  "userId": 1,
  "oldPassword": "wrongpassword",
  "newPassword": "newpassword123"
}
```

**期望响应：**
```json
{
  "code": 500,
  "message": "原密码错误",
  "data": null
}
```

## 性能测试

### 1. 大量数据分页查询

```http
POST http://localhost:8080/api/sys-user/page
Content-Type: application/json

{
  "current": "1",
  "size": "100"
}
```

### 2. 模糊查询性能

```http
POST http://localhost:8080/api/sys-user/page
Content-Type: application/json

{
  "current": "1",
  "size": "10",
  "realName": "用户",
  "email": "@example.com"
}
```

## 注意事项

1. **数据准备**：确保数据库中有足够的测试数据
2. **权限验证**：如果集成了权限验证，需要先登录获取token
3. **数据清理**：测试完成后清理测试数据
4. **并发测试**：可以使用JMeter等工具进行并发测试
5. **边界值测试**：测试各种边界值和异常情况

## 自动化测试脚本示例

```javascript
// Postman测试脚本示例
pm.test("Status code is 200", function () {
    pm.response.to.have.status(200);
});

pm.test("Response has correct structure", function () {
    var jsonData = pm.response.json();
    pm.expect(jsonData).to.have.property('code');
    pm.expect(jsonData).to.have.property('message');
    pm.expect(jsonData).to.have.property('data');
});

pm.test("User list is not empty", function () {
    var jsonData = pm.response.json();
    pm.expect(jsonData.data.records).to.be.an('array');
    pm.expect(jsonData.data.records.length).to.be.greaterThan(0);
});
```

这些测试用例覆盖了账号管理模块的主要功能，可以帮助验证API的正确性和稳定性。
