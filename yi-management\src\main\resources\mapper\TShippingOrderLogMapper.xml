<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yi.mapper.TShippingOrderLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yi.entity.TShippingOrderLog">
        <id column="id" property="id" />
        <result column="order_id" property="orderId" />
        <result column="action" property="action" />
        <result column="operator" property="operator" />
        <result column="operate_time" property="operateTime" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_id, action, operator, operate_time, remark
    </sql>

    <!-- 根据订单ID查询日志列表 -->
    <select id="selectByOrderId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_shipping_order_log
        WHERE order_id = #{orderId}
        ORDER BY operate_time DESC
    </select>

</mapper>
