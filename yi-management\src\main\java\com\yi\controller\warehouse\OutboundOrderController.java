package com.yi.controller.warehouse;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yi.common.Result;
import com.yi.controller.warehouse.model.*;
import com.yi.entity.OutboundOrder;
import com.yi.enums.FirstCategoryEnum;
import com.yi.enums.OutboundStatusEnum;
import com.yi.enums.OutboundTypeEnum;
import com.yi.service.OutboundOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 出库单管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/warehouse/outbound")
@Api(tags = "出库单管理")
@Validated
public class OutboundOrderController {

    @Autowired
    private OutboundOrderService outboundOrderService;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @PostMapping("/page")
    @ApiOperation("分页查询出库单列表")
    public Result<IPage<OutboundOrderResponse>> page(@RequestBody OutboundOrderQueryRequest request) {
        try {
            // 构建分页参数
            Page<OutboundOrder> page = new Page<>(
                Long.parseLong(request.getCurrent()),
                Long.parseLong(request.getSize())
            );

            // 时间参数转换
            LocalDateTime startTime = null;
            LocalDateTime endTime = null;
            if (StringUtils.hasText(request.getStartTime())) {
                startTime = LocalDateTime.parse(request.getStartTime(), DATE_TIME_FORMATTER);
            }
            if (StringUtils.hasText(request.getEndTime())) {
                endTime = LocalDateTime.parse(request.getEndTime(), DATE_TIME_FORMATTER);
            }

            // 查询数据
            IPage<OutboundOrder> result = outboundOrderService.selectOutboundOrderPage(
                page, request.getOrderNo(), request.getStatus(), request.getOutboundType(),
                request.getOutboundCompanyId(), request.getReceiveCompanyId(),
                request.getFirstCategory(), startTime, endTime
            );

            // 转换为响应对象
            IPage<OutboundOrderResponse> responseResult = result.convert(this::convertToResponse);

            return Result.success(responseResult);
        } catch (Exception e) {
            log.error("分页查询出库单失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    @GetMapping("/{id}")
    @ApiOperation("根据ID查询出库单详情")
    public Result<OutboundOrderResponse> getById(@ApiParam("出库单ID") @PathVariable Long id) {
        try {
            OutboundOrder order = outboundOrderService.getById(id);
            if (order == null) {
                return Result.failed("出库单不存在");
            }
            return Result.success(convertToResponse(order));
        } catch (Exception e) {
            log.error("查询出库单详情失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    @GetMapping("/orderNo/{orderNo}")
    @ApiOperation("根据单号查询出库单详情")
    public Result<OutboundOrderResponse> getByOrderNo(@ApiParam("出库单号") @PathVariable String orderNo) {
        try {
            OutboundOrder order = outboundOrderService.selectByOrderNo(orderNo);
            if (order == null) {
                return Result.failed("出库单不存在");
            }
            return Result.success(convertToResponse(order));
        } catch (Exception e) {
            log.error("查询出库单详情失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    @PostMapping
    @ApiOperation("创建出库单")
    public Result<String> create(@Valid @RequestBody OutboundOrderRequest request) {
        try {
            OutboundOrder order = convertToEntity(request);
            order.setCreatedBy("admin"); // TODO: 从当前登录用户获取
            
            boolean success = outboundOrderService.createOutboundOrder(order);
            if (success) {
                return Result.success("创建成功", order.getOrderNo());
            } else {
                return Result.failed("创建失败");
            }
        } catch (Exception e) {
            log.error("创建出库单失败", e);
            return Result.failed("创建失败：" + e.getMessage());
        }
    }

    @PutMapping
    @ApiOperation("更新出库单")
    public Result<String> update(@Valid @RequestBody OutboundOrderRequest request) {
        try {
            if (request.getId() == null) {
                return Result.failed("出库单ID不能为空");
            }

            OutboundOrder order = convertToEntity(request);
            order.setLastModifiedBy("admin"); // TODO: 从当前登录用户获取
            
            boolean success = outboundOrderService.updateOutboundOrder(order);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.failed("更新失败");
            }
        } catch (Exception e) {
            log.error("更新出库单失败", e);
            return Result.failed("更新失败：" + e.getMessage());
        }
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除出库单")
    public Result<String> delete(@ApiParam("出库单ID") @PathVariable Long id) {
        try {
            boolean success = outboundOrderService.deleteOutboundOrder(id);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.failed("删除失败");
            }
        } catch (Exception e) {
            log.error("删除出库单失败", e);
            return Result.failed("删除失败：" + e.getMessage());
        }
    }

    @DeleteMapping("/batch")
    @ApiOperation("批量删除出库单")
    public Result<String> deleteBatch(@RequestBody List<Long> ids) {
        try {
            boolean success = outboundOrderService.deleteOutboundOrders(ids);
            if (success) {
                return Result.success("批量删除成功");
            } else {
                return Result.failed("批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除出库单失败", e);
            return Result.failed("批量删除失败：" + e.getMessage());
        }
    }

    @PostMapping("/confirm")
    @ApiOperation("确认出库（待出库 → 运输中）")
    public Result<String> confirmOutbound(@Valid @RequestBody OutboundStatusUpdateRequest request) {
        try {
            if (request.getActualQuantity() == null) {
                return Result.failed("实际出库数不能为空");
            }

            boolean success = outboundOrderService.confirmOutbound(
                request.getId(),
                request.getActualQuantity(),
                "admin" // TODO: 从当前登录用户获取
            );

            if (success) {
                return Result.success("确认出库成功");
            } else {
                return Result.failed("确认出库失败");
            }
        } catch (Exception e) {
            log.error("确认出库失败", e);
            return Result.failed("确认出库失败：" + e.getMessage());
        }
    }

    @PostMapping("/complete")
    @ApiOperation("完成出库（运输中 → 已出库）")
    public Result<String> completeOutbound(@Valid @RequestBody OutboundStatusUpdateRequest request) {
        try {
            boolean success = outboundOrderService.completeOutbound(
                request.getId(),
                "admin" // TODO: 从当前登录用户获取
            );

            if (success) {
                return Result.success("完成出库成功");
            } else {
                return Result.failed("完成出库失败");
            }
        } catch (Exception e) {
            log.error("完成出库失败", e);
            return Result.failed("完成出库失败：" + e.getMessage());
        }
    }

    @PostMapping("/cancel")
    @ApiOperation("取消出库（运输中 → 待出库）")
    public Result<String> cancelOutbound(@Valid @RequestBody OutboundStatusUpdateRequest request) {
        try {
            boolean success = outboundOrderService.cancelOutbound(
                request.getId(),
                "admin" // TODO: 从当前登录用户获取
            );

            if (success) {
                return Result.success("取消出库成功");
            } else {
                return Result.failed("取消出库失败");
            }
        } catch (Exception e) {
            log.error("取消出库失败", e);
            return Result.failed("取消出库失败：" + e.getMessage());
        }
    }

    @GetMapping("/statistics/status")
    @ApiOperation("查询各状态的出库单数量统计")
    public Result<List<Map<String, Object>>> getStatusStatistics() {
        try {
            List<Map<String, Object>> statistics = outboundOrderService.getStatusStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("查询状态统计失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    @GetMapping("/statistics/type")
    @ApiOperation("查询各类型的出库单数量统计")
    public Result<List<Map<String, Object>>> getTypeStatistics() {
        try {
            List<Map<String, Object>> statistics = outboundOrderService.getTypeStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("查询类型统计失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    @GetMapping("/pending")
    @ApiOperation("查询待出库的订单")
    public Result<List<OutboundOrderResponse>> getPendingOrders() {
        try {
            List<OutboundOrder> orders = outboundOrderService.getPendingOrders();
            List<OutboundOrderResponse> responses = orders.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
            return Result.success(responses);
        } catch (Exception e) {
            log.error("查询待出库订单失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    @GetMapping("/in-transit")
    @ApiOperation("查询运输中的订单")
    public Result<List<OutboundOrderResponse>> getInTransitOrders() {
        try {
            List<OutboundOrder> orders = outboundOrderService.getInTransitOrders();
            List<OutboundOrderResponse> responses = orders.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
            return Result.success(responses);
        } catch (Exception e) {
            log.error("查询运输中订单失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 实体转换为响应对象
     */
    private OutboundOrderResponse convertToResponse(OutboundOrder order) {
        OutboundOrderResponse response = new OutboundOrderResponse();
        BeanUtils.copyProperties(order, response);
        
        // 设置枚举描述
        response.setStatusDesc(OutboundStatusEnum.getDescByCode(order.getStatus()));
        response.setOutboundTypeDesc(OutboundTypeEnum.getDescByCode(order.getOutboundType()));
        response.setFirstCategoryDesc(FirstCategoryEnum.getDescByCode(order.getFirstCategory()));
        
        return response;
    }

    /**
     * 请求对象转换为实体
     */
    private OutboundOrder convertToEntity(OutboundOrderRequest request) {
        OutboundOrder order = new OutboundOrder();
        BeanUtils.copyProperties(request, order);
        return order;
    }
}
