package com.yi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 开票类型枚举
 */
@Getter
@AllArgsConstructor
public enum InvoiceTypeEnum {

    /**
     * 后补
     */
    SUPPLEMENT(1, "后补"),

    /**
     * 与客户公司一致
     */
    SAME_AS_CUSTOMER(2, "与客户公司一致");

    /**
     * 类型键
     */
    private final Integer key;

    /**
     * 类型值
     */
    private final String value;

    /**
     * 根据键获取枚举
     *
     * @param key 键
     * @return 枚举
     */
    public static InvoiceTypeEnum getByKey(Integer key) {
        if (key == null) {
            return null;
        }
        for (InvoiceTypeEnum invoiceType : InvoiceTypeEnum.values()) {
            if (invoiceType.getKey().equals(key)) {
                return invoiceType;
            }
        }
        return null;
    }

    /**
     * 根据键获取值
     *
     * @param key 键
     * @return 值
     */
    public static String getValueByKey(Integer key) {
        InvoiceTypeEnum invoiceType = getByKey(key);
        return invoiceType != null ? invoiceType.getValue() : null;
    }

    /**
     * 判断是否为后补
     *
     * @param key 键
     * @return 是否为后补
     */
    public static boolean isSupplement(Integer key) {
        return SUPPLEMENT.getKey().equals(key);
    }

    /**
     * 判断是否与客户公司一致
     *
     * @param key 键
     * @return 是否与客户公司一致
     */
    public static boolean isSameAsCustomer(Integer key) {
        return SAME_AS_CUSTOMER.getKey().equals(key);
    }
}
