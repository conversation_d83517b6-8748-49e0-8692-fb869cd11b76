package com.yi.controller.contract.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 合同操作日志响应
 */
@Data
@ApiModel(value = "ContractLogResponse", description = "合同操作日志响应")
public class ContractLogResponse {

    @ApiModelProperty(value = "日志ID")
    private String id;

    @ApiModelProperty(value = "合同ID")
    private String contractId;

    @ApiModelProperty(value = "操作动作")
    private String action;

    @ApiModelProperty(value = "操作动作名称")
    private String actionName;

    @ApiModelProperty(value = "操作人")
    private String operator;

    @ApiModelProperty(value = "操作时间")
    private String operationTime;

    @ApiModelProperty(value = "备注信息")
    private String remark;
}
