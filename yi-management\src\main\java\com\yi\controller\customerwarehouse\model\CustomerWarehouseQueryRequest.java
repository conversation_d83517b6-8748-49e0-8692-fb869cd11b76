package com.yi.controller.customerwarehouse.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 仓库查询请求
 */
@Data
@ApiModel(value = "CustomerWarehouseQueryRequest", description = "仓库查询请求")
public class CustomerWarehouseQueryRequest {

    @ApiModelProperty(value = "当前页码", example = "1")
    private String current = "1";

    @ApiModelProperty(value = "每页大小", example = "10")
    private String size = "10";

    @ApiModelProperty(value = "启用状态：1-启用，0-禁用（下拉框搜索）")
    private String enabled;

    @ApiModelProperty(value = "公司名称（模糊查询）")
    private String companyName;

    @ApiModelProperty(value = "仓库名称（模糊查询）")
    private String warehouseName;

    @ApiModelProperty(value = "地址（模糊查询）")
    private String address;
}
