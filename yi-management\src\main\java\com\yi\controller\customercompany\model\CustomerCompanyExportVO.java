package com.yi.controller.customercompany.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

/**
 * 客户公司导出VO
 */
@Data
@HeadRowHeight(20)
@ContentRowHeight(18)
public class CustomerCompanyExportVO {

    @ExcelProperty(value = "状态")
    @ColumnWidth(10)
    private String status;

    @ExcelProperty(value = "客户类型")
    @ColumnWidth(12)
    private String customerType;

    @ExcelProperty(value = "公司名称")
    @ColumnWidth(25)
    private String companyName;

    @ExcelProperty(value = "注册地址")
    @ColumnWidth(40)
    private String registrationAddress;

    @ExcelProperty(value = "联系人")
    @ColumnWidth(12)
    private String contactPerson;

    @ExcelProperty(value = "联系方式")
    @ColumnWidth(15)
    private String contactPhone;

    @ExcelProperty(value = "创建人")
    @ColumnWidth(12)
    private String createdBy;

    @ExcelProperty(value = "创建时间")
    @ColumnWidth(20)
    private String createdTime;
}
