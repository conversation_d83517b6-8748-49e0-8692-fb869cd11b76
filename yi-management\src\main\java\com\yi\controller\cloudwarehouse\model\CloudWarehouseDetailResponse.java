package com.yi.controller.cloudwarehouse.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 云仓详情响应
 */
@Data
@ApiModel("云仓详情响应")
public class CloudWarehouseDetailResponse {

    @ApiModelProperty("云仓ID")
    private String id;

    @ApiModelProperty("仓库类型")
    private String warehouseTypeText;

    @ApiModelProperty("仓库属性")
    private String warehouseAttributeText;

    @ApiModelProperty("仓库名称")
    private String warehouseName;

    @ApiModelProperty("省份名称")
    private String provinceName;

    @ApiModelProperty("城市名称")
    private String cityName;

    @ApiModelProperty("区县名称")
    private String areaName;

    @ApiModelProperty("详细地址")
    private String detailedAddress;

    @ApiModelProperty("联系人")
    private String contactPerson;

    @ApiModelProperty("联系方式")
    private String contactPhone;

    @ApiModelProperty("作业时间")
    private String workingHours;

    @ApiModelProperty("备注")
    private String remark;
}
