<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yi.mapper.TSupplierMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yi.entity.TSupplier">
        <id column="id" property="id" />
        <result column="supplier_name" property="supplierName" />
        <result column="contact_person" property="contactPerson" />
        <result column="contact_phone" property="contactPhone" />
        <result column="bank_name" property="bankName" />
        <result column="bank_account" property="bankAccount" />
        <result column="remark" property="remark" />
        <result column="enabled" property="enabled" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="last_modified_by" property="lastModifiedBy" />
        <result column="last_modified_time" property="lastModifiedTime" />
        <result column="valid" property="valid" />
    </resultMap>

    <!-- 分页查询结果映射 -->
    <resultMap id="PageResultMap" type="com.yi.mapper.vo.SupplierPageVO">
        <id column="id" property="id" />
        <result column="supplier_name" property="supplierName" />
        <result column="contact_person" property="contactPerson" />
        <result column="contact_phone" property="contactPhone" />
        <result column="bank_name" property="bankName" />
        <result column="bank_account" property="bankAccount" />
        <result column="remark" property="remark" />
        <result column="enabled" property="enabled" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="last_modified_by" property="lastModifiedBy" />
        <result column="last_modified_time" property="lastModifiedTime" />
        <result column="valid" property="valid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, supplier_name, contact_person, contact_phone, bank_name, bank_account, 
        remark, enabled, created_by, created_time, last_modified_by, last_modified_time, valid
    </sql>

    <!-- 分页查询供应商列表 -->
    <select id="selectSupplierPage" resultMap="PageResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_supplier
        WHERE valid = 1
        <if test="request.supplierName != null and request.supplierName != ''">
            AND supplier_name LIKE CONCAT('%', #{request.supplierName}, '%')
        </if>
        <if test="request.contactPerson != null and request.contactPerson != ''">
            AND contact_person LIKE CONCAT('%', #{request.contactPerson}, '%')
        </if>
        <if test="request.contactPhone != null and request.contactPhone != ''">
            AND contact_phone LIKE CONCAT('%', #{request.contactPhone}, '%')
        </if>
        <if test="request.enabled != null">
            AND enabled = #{request.enabled}
        </if>
        <if test="request.createdBy != null and request.createdBy != ''">
            AND created_by LIKE CONCAT('%', #{request.createdBy}, '%')
        </if>
        <if test="request.createdTimeStart != null and request.createdTimeStart != ''">
            AND created_time >= #{request.createdTimeStart}
        </if>
        <if test="request.createdTimeEnd != null and request.createdTimeEnd != ''">
            AND created_time &lt;= #{request.createdTimeEnd}
        </if>
        ORDER BY created_time DESC
    </select>

    <!-- 查询供应商列表（不分页） -->
    <select id="selectSupplierList" resultMap="PageResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_supplier
        WHERE valid = 1
        <if test="request.supplierName != null and request.supplierName != ''">
            AND supplier_name LIKE CONCAT('%', #{request.supplierName}, '%')
        </if>
        <if test="request.contactPerson != null and request.contactPerson != ''">
            AND contact_person LIKE CONCAT('%', #{request.contactPerson}, '%')
        </if>
        <if test="request.contactPhone != null and request.contactPhone != ''">
            AND contact_phone LIKE CONCAT('%', #{request.contactPhone}, '%')
        </if>
        <if test="request.enabled != null">
            AND enabled = #{request.enabled}
        </if>
        <if test="request.createdBy != null and request.createdBy != ''">
            AND created_by LIKE CONCAT('%', #{request.createdBy}, '%')
        </if>
        <if test="request.createdTimeStart != null and request.createdTimeStart != ''">
            AND created_time >= #{request.createdTimeStart}
        </if>
        <if test="request.createdTimeEnd != null and request.createdTimeEnd != ''">
            AND created_time &lt;= #{request.createdTimeEnd}
        </if>
        ORDER BY created_time DESC
    </select>

    <!-- 根据供应商名称查询供应商 -->
    <select id="selectBySupplierName" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_supplier
        WHERE supplier_name = #{supplierName} AND valid = 1
        LIMIT 1
    </select>

    <!-- 根据启用状态查询供应商列表 -->
    <select id="selectByEnabled" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_supplier
        WHERE enabled = #{enabled} AND valid = 1
        ORDER BY created_time DESC
    </select>

    <!-- 更新供应商启用状态 -->
    <update id="updateEnabled">
        UPDATE t_supplier
        SET enabled = #{enabled},
            last_modified_by = #{lastModifiedBy},
            last_modified_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 检查供应商名称是否存在 -->
    <select id="countBySupplierName" resultType="int">
        SELECT COUNT(*)
        FROM t_supplier
        WHERE supplier_name = #{supplierName} AND valid = 1
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

</mapper>
