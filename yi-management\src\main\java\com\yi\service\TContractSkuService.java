package com.yi.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yi.entity.TContractSku;
import com.yi.enums.BusinessModeEnum;
import com.yi.enums.SkuFirstCategoryEnum;
import com.yi.mapper.TContractSkuMapper;
import com.yi.utils.FormatUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 合同SKU明细表 服务类
 */
@Service
public class TContractSkuService extends ServiceImpl<TContractSkuMapper, TContractSku> {

    /**
     * 根据合同ID查询SKU明细列表
     *
     * @param contractId 合同ID
     * @return SKU明细列表
     */
    public List<TContractSku> getByContractId(Long contractId) {
        LambdaQueryWrapper<TContractSku> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TContractSku::getContractId, contractId)
                .eq(TContractSku::getValid, 1)
                .orderByDesc(TContractSku::getCreatedTime);
        return this.list(wrapper);
    }

    /**
     * 检查合同下是否已存在指定一级类目的SKU明细
     *
     * @param contractId 合同ID
     * @param firstCategory 一级类目
     * @param excludeId 排除的SKU明细ID（编辑时使用）
     * @return 是否存在
     */
    public boolean existsByContractIdAndFirstCategory(Long contractId, Integer firstCategory, Long excludeId) {
        LambdaQueryWrapper<TContractSku> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TContractSku::getContractId, contractId)
                .eq(TContractSku::getFirstCategory, firstCategory)
                .eq(TContractSku::getValid, 1);
        
        if (excludeId != null) {
            wrapper.ne(TContractSku::getId, excludeId);
        }
        
        return this.count(wrapper) > 0;
    }

    /**
     * 新增合同SKU明细
     *
     * @param contractId 合同ID
     * @param firstCategory 一级类目
     * @param businessMode 业务模式
     * @return 是否成功
     */
    public boolean addContractSku(Long contractId, Integer firstCategory, Integer businessMode) {
        // 验证一级类目是否有效
        if (SkuFirstCategoryEnum.getByCode(firstCategory) == null) {
            throw new RuntimeException("无效的一级类目");
        }
        
        // 验证业务模式是否有效
        if (BusinessModeEnum.getByCode(businessMode) == null) {
            throw new RuntimeException("无效的业务模式");
        }
        
        // 检查是否已存在相同一级类目的明细
        if (existsByContractIdAndFirstCategory(contractId, firstCategory, null)) {
            throw new RuntimeException("该一级类目已存在，一种一级类目只可添加一条合同详情");
        }
        
        TContractSku contractSku = new TContractSku();
        contractSku.setContractId(contractId);
        contractSku.setFirstCategory(firstCategory);
        contractSku.setBusinessMode(businessMode);
        contractSku.setValid(1);
        
        return this.save(contractSku);
    }

    /**
     * 更新合同SKU明细
     *
     * @param id SKU明细ID
     * @param firstCategory 一级类目
     * @param businessMode 业务模式
     * @return 是否成功
     */
    public boolean updateContractSku(Long id, Integer firstCategory, Integer businessMode) {
        TContractSku existingSku = this.getById(id);
        if (existingSku == null || existingSku.getValid() != 1) {
            throw new RuntimeException("SKU明细不存在");
        }
        
        // 验证一级类目是否有效
        if (SkuFirstCategoryEnum.getByCode(firstCategory) == null) {
            throw new RuntimeException("无效的一级类目");
        }
        
        // 验证业务模式是否有效
        if (BusinessModeEnum.getByCode(businessMode) == null) {
            throw new RuntimeException("无效的业务模式");
        }
        
        // 检查是否已存在相同一级类目的明细（排除当前记录）
        if (existsByContractIdAndFirstCategory(existingSku.getContractId(), firstCategory, id)) {
            throw new RuntimeException("该一级类目已存在，一种一级类目只可添加一条合同详情");
        }
        
        TContractSku contractSku = new TContractSku();
        contractSku.setId(id);
        contractSku.setFirstCategory(firstCategory);
        contractSku.setBusinessMode(businessMode);
        
        return this.updateById(contractSku);
    }

    /**
     * 删除合同SKU明细
     *
     * @param id SKU明细ID
     * @return 是否成功
     */
    public boolean deleteContractSku(Long id) {
        TContractSku contractSku = new TContractSku();
        contractSku.setId(id);
        contractSku.setValid(0);
        
        return this.updateById(contractSku);
    }

    /**
     * 根据合同ID删除所有SKU明细
     *
     * @param contractId 合同ID
     * @return 是否成功
     */
    public boolean deleteByContractId(Long contractId) {
        LambdaQueryWrapper<TContractSku> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TContractSku::getContractId, contractId);
        
        List<TContractSku> skuList = this.list(wrapper);
        for (TContractSku sku : skuList) {
            sku.setValid(0);
        }
        
        return this.updateBatchById(skuList);
    }
}
