package com.yi.mapper.vo;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 发货需求详情查询VO
 */
@Data
public class ShippingDemandDetailVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 发运订单号
     */
    private String orderNo;

    /**
     * 发货状态：1000-待发货，2000-已发货，3000-已完结，4000-已取消
     */
    private Integer status;

    /**
     * 客户公司ID
     */
    private Long customerCompanyId;

    /**
     * 客户公司名称
     */
    private String customerCompanyName;

    /**
     * 收货仓库ID
     */
    private Long warehouseId;

    /**
     * 收货仓库名称
     */
    private String warehouseName;

    /**
     * 收货地址
     */
    private String receivingAddress;

    /**
     * 收货人
     */
    private String receiverName;

    /**
     * 一级类目 1:共享托盘
     */
    private Integer firstCategory;

    /**
     * 二级类目
     */
    private String secondCategory;

    /**
     * 需求数量
     */
    private Integer demandQuantity;

    /**
     * 待确认数量
     */
    private Integer pendingQuantity;

    /**
     * 发货数量
     */
    private Integer shippedQuantity;

    /**
     * 未执行数量
     */
    private Integer unexecutedQuantity;

    /**
     * 需求时间
     */
    private LocalDate demandTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 最后修改人
     */
    private String lastModifiedBy;

    /**
     * 最后修改时间
     */
    private LocalDateTime lastModifiedTime;

    /**
     * 有效性：1-有效，0-无效
     */
    private Integer valid;
}
