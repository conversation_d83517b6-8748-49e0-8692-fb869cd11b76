package com.yi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 仓库属性枚举
 */
@Getter
@AllArgsConstructor
public enum WarehouseAttributeEnum {

    SELF_OWNED(1, "自有"),
    THIRD_PARTY(2, "第三方");

    private final Integer key;
    private final String value;

    /**
     * 根据key获取value
     *
     * @param key 枚举key
     * @return 枚举value
     */
    public static String getValueByKey(Integer key) {
        if (key == null) {
            return "";
        }
        for (WarehouseAttributeEnum attributeEnum : values()) {
            if (attributeEnum.getKey().equals(key)) {
                return attributeEnum.getValue();
            }
        }
        return "";
    }

    /**
     * 根据key获取枚举
     *
     * @param key 枚举key
     * @return 枚举对象
     */
    public static WarehouseAttributeEnum getByKey(Integer key) {
        if (key == null) {
            return null;
        }
        for (WarehouseAttributeEnum attributeEnum : values()) {
            if (attributeEnum.getKey().equals(key)) {
                return attributeEnum;
            }
        }
        return null;
    }
}
