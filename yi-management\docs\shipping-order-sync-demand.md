# 发运订单同步发货需求单实现说明

## 概述

根据PRD中的业务需求，**订单新增后需要同步到发货需求单**。这是运营平台与仓储WMS系统之间的数据同步机制，确保订单创建后能够在WMS中生成对应的发货需求。

## 🎯 **业务背景**

### **PRD需求分析**：

**订单管理**：
- 发货中状态：仓储WMS中对应发货需求状态变更为【已发货】时，订单状态变更为【发货中】

**发货需求**：
- 待发货状态：由运营平台同步生成发运需求时的初始状态

**业务流程**：
```
运营平台创建订单 → 同步生成发货需求单 → WMS处理发货 → 状态回传更新订单
```

## 📋 **实现方案**

### **1. 数据流向设计**

```
TShippingOrder (发运订单)
├── 订单创建成功
├── 保存附件信息
└── 同步创建 TShippingDemand (发货需求单)
    ├── 生成需求单号 (FHD + yyyyMMdd + 4位序号)
    ├── 关联订单号
    ├── 复制基本信息 (客户、仓库、产品等)
    ├── 初始化数量信息
    └── 设置初始状态 (PENDING - 待发货)
```

### **2. 字段映射关系**

| 发运订单字段 | 发货需求单字段 | 映射逻辑 |
|-------------|---------------|----------|
| orderNo | orderNo | 直接关联 |
| customerCompanyId | customerCompanyId | 直接复制 |
| warehouseId | warehouseId | 直接复制 |
| firstCategory | firstCategory | 直接复制 |
| secondCategory | secondCategory | 直接复制 |
| count | demandQuantity | 订单数量 → 需求数量 |
| count | pendingQuantity | 订单数量 → 待确认数量 |
| - | shippedQuantity | 初始化为0 |
| - | unexecutedQuantity | 初始化为0 |
| demandTime | demandTime | 直接复制 |
| remark | remark | 直接复制 |
| - | status | 初始化为"PENDING" |

## 🔧 **核心实现**

### **1. 服务层修改**
**文件**: `yi-management/src/main/java/com/yi/service/TShippingOrderService.java`

**新增依赖注入**:
```java
@Autowired
private TShippingDemandService shippingDemandService;
```

**修改addShippingOrder方法**:
```java
public boolean addShippingOrder(ShippingOrderRequest orderRequest) {
    // ... 创建订单逻辑
    
    boolean success = this.save(order);

    if (success) {
        // 保存附件到通用文件表
        if (orderRequest.getAttachmentUrls() != null && !orderRequest.getAttachmentUrls().isEmpty()) {
            Long orderId = order.getId();
            generalFileService.saveShippingOrderAttachments(orderId, orderRequest.getAttachmentUrls());
        }
        
        // 同步创建发货需求单
        createShippingDemand(order);
    }

    return success;
}
```

**核心同步方法**:
```java
private void createShippingDemand(TShippingOrder order) {
    try {
        TShippingDemand demand = new TShippingDemand();
        
        // 生成需求单号
        demand.setDemandNo(generateDemandNo());
        
        // 关联订单号
        demand.setOrderNo(order.getOrderNo());
        
        // 基本信息
        demand.setCustomerCompanyId(order.getCustomerCompanyId());
        demand.setWarehouseId(order.getWarehouseId());
        demand.setFirstCategory(order.getFirstCategory());
        demand.setSecondCategory(order.getSecondCategory());
        
        // 数量信息
        demand.setDemandQuantity(order.getCount());
        demand.setPendingQuantity(order.getCount()); // 初始待确认数等于需求数量
        demand.setShippedQuantity(0); // 初始发货数量为0
        demand.setUnexecutedQuantity(0); // 初始未执行数量为0
        
        // 时间信息
        demand.setDemandTime(order.getDemandTime());
        
        // 状态信息
        demand.setStatus("PENDING"); // 初始状态：待发货
        demand.setRemark(order.getRemark());
        
        // 审计信息
        demand.setCreatedBy(getCurrentUser());
        demand.setLastModifiedBy(getCurrentUser());
        demand.setValid(1);
        
        // 保存发货需求
        shippingDemandService.save(demand);
        
        log.info("成功创建发货需求单，订单号: {}, 需求单号: {}", order.getOrderNo(), demand.getDemandNo());
        
    } catch (Exception e) {
        log.error("创建发货需求单失败，订单号: " + order.getOrderNo() + ", 错误: " + e.getMessage(), e);
        throw new RuntimeException("创建发货需求单失败: " + e.getMessage(), e);
    }
}
```

### **2. 需求单号生成逻辑**

**格式规范**: `FHD + yyyyMMdd + 4位序号`

**生成方法**:
```java
private String generateDemandNo() {
    final int maxRetries = 10;
    
    for (int attempt = 0; attempt < maxRetries; attempt++) {
        try {
            // 生成格式：FHD + yyyyMMdd + 4位序号
            LocalDate now = LocalDate.now();
            String dateStr = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String prefix = "FHD" + dateStr;
            
            // 查询当天最大序号
            int maxSeq = getDemandMaxSequenceOfDay(dateStr);
            String sequence = String.format("%04d", maxSeq + 1);
            String demandNo = prefix + sequence;
            
            // 验证需求单号唯一性
            if (isDemandNoUnique(demandNo)) {
                return demandNo;
            }
            
            // 如果不唯一，等待随机时间后重试
            Thread.sleep(10 + (long)(Math.random() * 20)); // 10-30ms随机等待
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("需求单号生成被中断", e);
        } catch (Exception e) {
            log.warn("需求单号生成失败，尝试次数: " + (attempt + 1) + ", 错误: " + e.getMessage());
        }
    }
    
    throw new RuntimeException("需求单号生成失败，已达到最大重试次数: " + maxRetries);
}
```

### **3. 数据访问层扩展**

**TShippingDemandService新增方法**:
```java
/**
 * 获取当天需求单号最大序号
 */
public int getMaxSequenceOfDay(String prefix) {
    return this.baseMapper.getMaxSequenceOfDay(prefix);
}

/**
 * 验证需求单号唯一性
 */
public boolean isDemandNoUnique(String demandNo) {
    LambdaQueryWrapper<TShippingDemand> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(TShippingDemand::getDemandNo, demandNo)
            .eq(TShippingDemand::getValid, 1);
    
    return this.count(wrapper) == 0;
}
```

**TShippingDemandMapper新增方法**:
```java
/**
 * 获取当天需求单号最大序号
 */
int getMaxSequenceOfDay(@Param("prefix") String prefix);
```

**对应的SQL实现**:
```xml
<!-- 获取当天需求单号最大序号 -->
<select id="getMaxSequenceOfDay" resultType="int">
    SELECT COALESCE(MAX(CAST(SUBSTRING(demand_no, LENGTH(#{prefix}) + 1) AS UNSIGNED)), 0)
    FROM t_shipping_demand
    WHERE demand_no LIKE CONCAT(#{prefix}, '%')
      AND valid = 1
      AND LENGTH(demand_no) = LENGTH(#{prefix}) + 4
</select>
```

## 📊 **业务流程示例**

### **1. 订单创建流程**

```
1. 用户提交订单创建请求
   ├── 订单号: XSD202412230001
   ├── 客户公司ID: 100
   ├── 仓库ID: 200
   ├── 产品类目: 1 (循环托盘)
   ├── 数量: 50
   └── 需求时间: 2024-12-31

2. 系统创建发运订单
   ├── 生成订单号: XSD202412230001
   ├── 保存订单基本信息
   └── 保存附件信息

3. 同步创建发货需求单
   ├── 生成需求单号: FHD202412230001
   ├── 关联订单号: XSD202412230001
   ├── 复制基本信息
   ├── 初始化数量: 需求50, 待确认50, 已发货0
   ├── 设置状态: PENDING (待发货)
   └── 保存到数据库

4. 返回创建成功结果
```

### **2. 数据同步结果**

**发运订单表 (t_shipping_order)**:
```json
{
  "id": 1,
  "orderNo": "XSD202412230001",
  "customerCompanyId": 100,
  "warehouseId": 200,
  "firstCategory": 1,
  "secondCategory": "标准托盘",
  "count": 50,
  "status": "PENDING",
  "demandTime": "2024-12-31"
}
```

**发货需求单表 (t_shipping_demand)**:
```json
{
  "id": 1,
  "demandNo": "FHD202412230001",
  "orderNo": "XSD202412230001",
  "customerCompanyId": 100,
  "warehouseId": 200,
  "firstCategory": 1,
  "secondCategory": "标准托盘",
  "demandQuantity": 50,
  "pendingQuantity": 50,
  "shippedQuantity": 0,
  "unexecutedQuantity": 0,
  "status": "PENDING",
  "demandTime": "2024-12-31"
}
```

## 🧪 **测试覆盖**

### **单元测试**
**文件**: `yi-management/src/test/java/com/yi/service/TShippingOrderSyncTest.java`

**测试场景**:
1. **正常同步测试** - 验证订单创建后成功同步发货需求单
2. **需求单号生成测试** - 验证需求单号格式和唯一性
3. **异常处理测试** - 验证发货需求单创建失败时的异常处理
4. **重试机制测试** - 验证需求单号重复时的重试逻辑
5. **无附件场景测试** - 验证没有附件时的正常处理

**关键验证点**:
```java
@Test
void testAddShippingOrder_ShouldCreateShippingDemand() {
    // 执行订单创建
    boolean result = shippingOrderService.addShippingOrder(orderRequest);
    
    // 验证发货需求单被创建
    ArgumentCaptor<TShippingDemand> demandCaptor = ArgumentCaptor.forClass(TShippingDemand.class);
    verify(shippingDemandService, times(1)).save(demandCaptor.capture());
    
    TShippingDemand capturedDemand = demandCaptor.getValue();
    
    // 验证字段映射正确性
    assertEquals(Long.valueOf(100), capturedDemand.getCustomerCompanyId());
    assertEquals(Integer.valueOf(50), capturedDemand.getDemandQuantity());
    assertEquals("PENDING", capturedDemand.getStatus());
}
```

## ✅ **实现优势**

### **1. 数据一致性**
- **原子性操作** - 订单创建和需求单同步在同一事务中
- **字段映射准确** - 确保订单信息正确传递到需求单
- **状态同步** - 初始状态设置符合业务流程

### **2. 可靠性保障**
- **异常处理** - 需求单创建失败时回滚订单创建
- **重试机制** - 需求单号冲突时自动重试
- **日志记录** - 完整的操作日志便于问题排查

### **3. 扩展性设计**
- **服务解耦** - 通过服务接口调用，便于后续扩展
- **配置化** - 需求单号格式可配置
- **异步支持** - 为后续异步处理预留接口

## ⚠️ **注意事项**

### **1. 事务管理**
- 确保订单创建和需求单同步在同一事务中
- 需求单创建失败时要回滚订单创建
- 考虑分布式事务的处理方案

### **2. 性能考虑**
- 需求单号生成的并发性能
- 大批量订单创建时的处理能力
- 数据库连接池的合理配置

### **3. 业务逻辑**
- 订单状态与需求单状态的同步机制
- 订单修改时需求单的更新策略
- 订单取消时需求单的处理逻辑

## 🚀 **后续扩展**

### **1. 异步处理**
```java
@Async
public void createShippingDemandAsync(TShippingOrder order) {
    // 异步创建发货需求单
}
```

### **2. 消息队列**
```java
// 发送订单创建消息
messageProducer.sendOrderCreatedMessage(order);

// 消费消息创建需求单
@RabbitListener(queues = "order.created")
public void handleOrderCreated(OrderCreatedMessage message) {
    createShippingDemand(message.getOrder());
}
```

### **3. 状态回调**
```java
// WMS状态回调接口
@PostMapping("/api/wms/callback/demand-status")
public void updateDemandStatus(@RequestBody DemandStatusCallback callback) {
    // 更新需求单状态
    // 同步更新订单状态
}
```

通过这种设计，实现了订单与发货需求单的自动同步，确保了运营平台与WMS系统之间的数据一致性，为后续的发货流程奠定了基础！🎉
