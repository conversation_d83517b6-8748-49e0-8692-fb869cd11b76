package com.yi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yi.controller.supplierwarehouse.model.SupplierWarehouseQueryRequest;
import com.yi.entity.TSupplierWarehouse;
import com.yi.mapper.vo.SupplierWarehousePageVO;
import com.yi.mapper.vo.SupplierWarehouseDetailVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 供应商仓库表 Mapper 接口
 */
@Mapper
public interface TSupplierWarehouseMapper extends BaseMapper<TSupplierWarehouse> {

    /**
     * 分页查询供应商仓库列表
     *
     * @param page 分页参数
     * @param request 查询条件
     * @return 分页结果
     */
    IPage<SupplierWarehousePageVO> selectSupplierWarehousePage(Page<SupplierWarehousePageVO> page,
                                                               @Param("request") SupplierWarehouseQueryRequest request);

    /**
     * 根据ID查询供应商仓库详情（联查供应商名称）
     *
     * @param id 供应商仓库ID
     * @return 供应商仓库详情
     */
    SupplierWarehouseDetailVO selectSupplierWarehouseDetailById(@Param("id") Long id);
}
