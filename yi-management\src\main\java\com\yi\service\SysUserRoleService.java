package com.yi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yi.entity.SysUserRole;

import java.util.List;

/**
 * 用户角色关联表 服务类
 */
public interface SysUserRoleService extends IService<SysUserRole> {

    /**
     * 根据用户ID删除用户角色关联
     *
     * @param userId 用户ID
     * @return 删除数量
     */
    int deleteByUserId(Long userId);

    /**
     * 根据角色ID删除用户角色关联
     *
     * @param roleId 角色ID
     * @return 删除数量
     */
    int deleteByRoleId(Long roleId);

    /**
     * 批量插入用户角色关联
     *
     * @param userRoles 用户角色关联列表
     * @return 插入数量
     */
    int batchInsert(List<SysUserRole> userRoles);

    /**
     * 根据用户ID查询角色ID列表
     *
     * @param userId 用户ID
     * @return 角色ID列表
     */
    List<Long> selectRoleIdsByUserId(Long userId);

    /**
     * 根据角色ID查询用户ID列表
     *
     * @param roleId 角色ID
     * @return 用户ID列表
     */
    List<Long> selectUserIdsByRoleId(Long roleId);

    /**
     * 检查用户是否拥有指定角色
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 是否存在
     */
    boolean existsByUserIdAndRoleId(Long userId, Long roleId);
}
