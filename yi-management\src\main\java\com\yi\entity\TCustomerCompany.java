package com.yi.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户公司表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_customer_company")
@ApiModel(value = "TCustomerCompany对象", description = "客户公司表")
public class TCustomerCompany extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 公司类型：1合约客户 2非合约客户
     */
    @ApiModelProperty(value = "公司类型：1合约客户 2非合约客户")
    private String customerCompanyType;



    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 省份ID
     */
    @ApiModelProperty(value = "省份ID")
    private Long provinceId;

    /**
     * 省份名称
     */
    @ApiModelProperty(value = "省份名称")
    private String provinceName;

    /**
     * 城市ID
     */
    @ApiModelProperty(value = "城市ID")
    private Long cityId;

    /**
     * 城市名称
     */
    @ApiModelProperty(value = "城市名称")
    private String cityName;

    /**
     * 区县ID
     */
    @ApiModelProperty(value = "区县ID")
    private Long areaId;

    /**
     * 区县名称
     */
    @ApiModelProperty(value = "区县名称")
    private String areaName;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    private String detailedAddress;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    private String contactPerson;

    /**
     * 联系方式
     */
    @ApiModelProperty(value = "联系方式")
    private String contactPhone;

    /**
     * 开票类型：1-后补，2-与客户公司一致
     */
    @ApiModelProperty(value = "开票类型：1-后补，2-与客户公司一致")
    private Integer invoiceType;

    /**
     * 开票公司名称
     */
    @ApiModelProperty(value = "开票公司名称")
    private String invoiceCompanyName;

    /**
     * 税号
     */
    @ApiModelProperty(value = "税号")
    private String taxNumber;

    /**
     * 发票联系人
     */
    @ApiModelProperty(value = "发票联系人")
    private String invoiceContactPerson;

    /**
     * 发票联系手机号
     */
    @ApiModelProperty(value = "发票联系手机号")
    private String invoiceMobile;

    /**
     * 发票邮箱
     */
    @ApiModelProperty(value = "发票邮箱")
    private String invoiceEmail;

    /**
     * 开户银行名称
     */
    @ApiModelProperty(value = "开户银行名称")
    private String bankName;

    /**
     * 银行账号
     */
    @ApiModelProperty(value = "银行账号")
    private String bankAccount;

    /**
     * 启用状态：1-启用，0-禁用
     */
    @ApiModelProperty(value = "启用状态：1-启用，0-禁用")
    private Integer enabled;
}
