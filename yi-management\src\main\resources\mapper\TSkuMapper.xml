<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yi.mapper.TSkuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yi.entity.TSku">
        <id column="id" property="id" />
        <result column="first_category" property="firstCategory" />
        <result column="second_category" property="secondCategory" />
        <result column="third_category" property="thirdCategory" />
        <result column="length" property="length" />
        <result column="width" property="width" />
        <result column="height" property="height" />
        <result column="weight" property="weight" />
        <result column="remark" property="remark" />
        <result column="enabled" property="enabled" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="last_modified_by" property="lastModifiedBy" />
        <result column="last_modified_time" property="lastModifiedTime" />
        <result column="valid" property="valid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, first_category, second_category, third_category, length, width, height, 
        weight, remark, enabled, created_by, created_time, last_modified_by, 
        last_modified_time, valid
    </sql>

    <!-- 分页查询SKU列表 -->
    <select id="selectSkuPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_sku
        WHERE valid = 1
        <if test="firstCategory != null">
            AND first_category = #{firstCategory}
        </if>
        <if test="secondCategory != null">
            AND second_category = #{secondCategory}
        </if>
        <if test="thirdCategory != null">
            AND third_category = #{thirdCategory}
        </if>
        <if test="enabled != null">
            AND enabled = #{enabled}
        </if>
        ORDER BY created_time DESC
    </select>

    <!-- 根据类目查询SKU -->
    <select id="selectByCategory" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_sku
        WHERE valid = 1
        <if test="firstCategory != null">
            AND first_category = #{firstCategory}
        </if>
        <if test="secondCategory != null">
            AND second_category = #{secondCategory}
        </if>
        <if test="thirdCategory != null">
            AND third_category = #{thirdCategory}
        </if>
        ORDER BY created_time DESC
    </select>

    <!-- 根据规格查询SKU -->
    <select id="selectBySize" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_sku
        WHERE valid = 1
        <if test="length != null">
            AND length = #{length}
        </if>
        <if test="width != null">
            AND width = #{width}
        </if>
        <if test="height != null">
            AND height = #{height}
        </if>
        ORDER BY created_time DESC
    </select>

</mapper>
