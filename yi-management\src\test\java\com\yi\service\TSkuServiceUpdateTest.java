package com.yi.service;

import com.yi.controller.sku.model.SkuRequest;
import com.yi.entity.TSku;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * SKU服务更新功能测试
 */
@ExtendWith(MockitoExtension.class)
@SpringBootTest
public class TSkuServiceUpdateTest {

    @InjectMocks
    private TSkuService skuService;

    private TSku existingSku;
    private SkuRequest updateRequest;

    @BeforeEach
    void setUp() {
        // 准备现有SKU数据
        existingSku = new TSku();
        existingSku.setId(1L);
        existingSku.setFirstCategory(1);
        existingSku.setSecondCategory("标准托盘");
        existingSku.setThirdCategory("1200*1000");
        existingSku.setLength(1200);
        existingSku.setWidth(1000);
        existingSku.setHeight(150);
        existingSku.setValid(1);

        // 准备更新请求
        updateRequest = new SkuRequest();
        updateRequest.setId("1");
        updateRequest.setFirstCategory("1");
        updateRequest.setSecondCategory("标准托盘");
        updateRequest.setThirdCategory("1200*1000");
        updateRequest.setLength("1200");
        updateRequest.setWidth("1000");
        updateRequest.setHeight("150");
        updateRequest.setRemark("测试备注");
    }

    @Test
    void testUpdateSku_Success() {
        // Mock getSkuById方法返回现有SKU
        when(skuService.getSkuById(1L)).thenReturn(existingSku);
        
        // Mock isCategoryExists方法返回false（类目组合不重复）
        when(skuService.isCategoryExists(any(), any(), any(), any())).thenReturn(false);
        
        // Mock updateById方法返回true
        when(skuService.updateById(any(TSku.class))).thenReturn(true);

        // 执行更新操作
        boolean result = skuService.updateSku(updateRequest);

        // 验证结果
        assertTrue(result);
        verify(skuService, times(1)).getSkuById(1L);
        verify(skuService, times(1)).updateById(any(TSku.class));
    }

    @Test
    void testUpdateSku_FirstCategoryModified_ShouldThrowException() {
        // Mock getSkuById方法返回现有SKU
        when(skuService.getSkuById(1L)).thenReturn(existingSku);

        // 修改一级类目
        updateRequest.setFirstCategory("2");

        // 执行更新操作并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            skuService.updateSku(updateRequest);
        });

        assertEquals("一级类目不允许修改", exception.getMessage());
        verify(skuService, times(1)).getSkuById(1L);
        verify(skuService, never()).updateById(any(TSku.class));
    }

    @Test
    void testUpdateSku_SecondCategoryModified_ShouldThrowException() {
        // Mock getSkuById方法返回现有SKU
        when(skuService.getSkuById(1L)).thenReturn(existingSku);

        // 修改二级类目
        updateRequest.setSecondCategory("非标准托盘");

        // 执行更新操作并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            skuService.updateSku(updateRequest);
        });

        assertEquals("二级类目不允许修改", exception.getMessage());
        verify(skuService, times(1)).getSkuById(1L);
        verify(skuService, never()).updateById(any(TSku.class));
    }

    @Test
    void testUpdateSku_SkuNotExists_ShouldThrowException() {
        // Mock getSkuById方法返回null
        when(skuService.getSkuById(1L)).thenReturn(null);

        // 执行更新操作并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            skuService.updateSku(updateRequest);
        });

        assertEquals("SKU不存在", exception.getMessage());
        verify(skuService, times(1)).getSkuById(1L);
        verify(skuService, never()).updateById(any(TSku.class));
    }

    @Test
    void testUpdateSku_CategoryCombinationExists_ShouldThrowException() {
        // Mock getSkuById方法返回现有SKU
        when(skuService.getSkuById(1L)).thenReturn(existingSku);
        
        // Mock isCategoryExists方法返回true（类目组合重复）
        when(skuService.isCategoryExists(any(), any(), any(), any())).thenReturn(true);

        // 执行更新操作并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            skuService.updateSku(updateRequest);
        });

        assertEquals("该类目组合已存在，不能重复", exception.getMessage());
        verify(skuService, times(1)).getSkuById(1L);
        verify(skuService, never()).updateById(any(TSku.class));
    }

    @Test
    void testUpdateSku_AllowThirdCategoryModification() {
        // Mock getSkuById方法返回现有SKU
        when(skuService.getSkuById(1L)).thenReturn(existingSku);
        
        // Mock isCategoryExists方法返回false（类目组合不重复）
        when(skuService.isCategoryExists(any(), any(), any(), any())).thenReturn(false);
        
        // Mock updateById方法返回true
        when(skuService.updateById(any(TSku.class))).thenReturn(true);

        // 修改三级类目（应该被允许）
        updateRequest.setThirdCategory("1100*1100");

        // 执行更新操作
        boolean result = skuService.updateSku(updateRequest);

        // 验证结果
        assertTrue(result);
        verify(skuService, times(1)).getSkuById(1L);
        verify(skuService, times(1)).updateById(any(TSku.class));
    }

    @Test
    void testUpdateSku_AllowDimensionModification() {
        // Mock getSkuById方法返回现有SKU
        when(skuService.getSkuById(1L)).thenReturn(existingSku);
        
        // Mock isCategoryExists方法返回false（类目组合不重复）
        when(skuService.isCategoryExists(any(), any(), any(), any())).thenReturn(false);
        
        // Mock updateById方法返回true
        when(skuService.updateById(any(TSku.class))).thenReturn(true);

        // 修改尺寸信息（应该被允许）
        updateRequest.setLength("1300");
        updateRequest.setWidth("1100");
        updateRequest.setHeight("160");

        // 执行更新操作
        boolean result = skuService.updateSku(updateRequest);

        // 验证结果
        assertTrue(result);
        verify(skuService, times(1)).getSkuById(1L);
        verify(skuService, times(1)).updateById(any(TSku.class));
    }
}
