package com.yi.mapper.vo;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 发运订单分页查询VO
 */
@Data
public class ShippingOrderPageVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 合同编号
     */
    private String contractCode;

    /**
     * 客户公司ID
     */
    private Long customerCompanyId;

    /**
     * 客户公司名称
     */
    private String customerCompanyName;

    /**
     * 收货仓库ID
     */
    private Long warehouseId;

    /**
     * 收货仓库名称
     */
    private String warehouseName;

    /**
     * 收货地址
     */
    private String warehouseAddress;

    /**
     * 收货人
     */
    private String receiverName;

    /**
     * 收货人联系方式
     */
    private String receiverPhone;

    /**
     * 一级类目 1:共享托盘
     */
    private Integer firstCategory;

    /**
     * 一级类目名称
     */
    private String firstCategoryName;

    /**
     * 二级类目
     */
    private String secondCategory;

    /**
     * 需求数量
     */
    private Integer count;

    /**
     * 发货数量
     */
    private Integer shippedQuantity;

    /**
     * 签收数量
     */
    private Integer receivedQuantity;

    /**
     * 订单状态：1000-待发货，2000-发货中，3000-已完结，4000-已取消
     */
    private Integer status;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 有效性：1-有效，0-无效
     */
    private Integer valid;
}
