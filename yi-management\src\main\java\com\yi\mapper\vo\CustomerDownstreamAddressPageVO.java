package com.yi.mapper.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 客户下游地址关联分页查询VO
 */
@Data
public class CustomerDownstreamAddressPageVO {

    /**
     * 关联ID
     */
    private Long relationId;

    /**
     * 公司ID
     */
    private Long companyId;

    /**
     * 下游客户公司ID
     */
    private Long downstreamCustomerCompanyId;

    /**
     * 下游客户公司名称
     */
    private String downstreamCompanyName;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 区县名称
     */
    private String areaName;

    /**
     * 详细地址
     */
    private String detailedAddress;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 手机号
     */
    private String mobilePhone;

    /**
     * 座机号
     */
    private String landlinePhone;

    /**
     * 启用状态：1-启用，0-禁用
     */
    private Integer enabled;

    /**
     * 最后修改人
     */
    private String lastModifiedBy;

    /**
     * 最后修改时间
     */
    private LocalDateTime lastModifiedTime;

    /**
     * 备注
     */
    private String remark;
}
