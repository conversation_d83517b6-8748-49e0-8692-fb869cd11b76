package com.yi.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 通用文件表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_general_file")
@ApiModel(value = "TGeneralFile对象", description = "通用文件表")
public class TGeneralFile extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "类型: 1.销售合同, 2.发运订单, 3.供应商营业执照")
    private Integer type;

    @ApiModelProperty(value = "文件类型")
    private String fileType;

    @ApiModelProperty(value = "关联Id")
    private Long relatedId;

    @ApiModelProperty(value = "文件路径")
    private String filePath;
}
