package com.yi.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yi.controller.contract.model.*;
import com.yi.entity.TContract;
import com.yi.entity.TContractLog;
import com.yi.entity.TContractSku;
import com.yi.entity.TGeneralFile;
import com.yi.enums.*;
import com.yi.mapper.TContractMapper;
import com.yi.utils.ExcelUtils;
import com.yi.utils.FormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 合同管理表 服务类
 */
@Service
public class TContractService extends ServiceImpl<TContractMapper, TContract> {

    @Autowired
    private TContractSkuService contractSkuService;

    @Autowired
    private TGeneralFileService generalFileService;

    @Autowired
    private TContractLogService contractLogService;

    /**
     * 分页查询合同列表（使用联合查询）
     *
     * @param request 查询条件
     * @return 分页结果
     */
    public IPage<ContractListVO> getContractPage(ContractQueryRequest request) {
        // 转换分页参数
        Integer current = FormatUtils.safeToInteger(request.getCurrent(), 1);
        Integer size = FormatUtils.safeToInteger(request.getSize(), 10);
        Page<ContractListVO> page = new Page<>(current, size);

        // 转换查询参数
        String contractNo = StringUtils.hasText(request.getContractNo()) ? request.getContractNo().trim() : null;
        String customerCompanyName = StringUtils.hasText(request.getCustomerCompanyName()) ? request.getCustomerCompanyName().trim() : null;
        Integer contractStatus = FormatUtils.safeToInteger(request.getContractStatus());

        // 使用联合查询
        return baseMapper.selectContractPageWithJoin(page, contractNo, customerCompanyName, contractStatus);
    }

    /**
     * 分页查询合同列表（返回Response格式）
     *
     * @param request 查询条件
     * @return 分页结果
     */
    public IPage<ContractPageResponse> getContractPageResponse(ContractQueryRequest request) {
        // 使用联合查询获取数据
        IPage<ContractListVO> originalPage = getContractPage(request);

        // 转换为Response对象
        List<ContractPageResponse> responseList = originalPage.getRecords().stream()
                .map(this::convertToPageResponse)
                .collect(Collectors.toList());

        // 构建返回的分页对象
        Page<ContractPageResponse> responsePage = new Page<>(originalPage.getCurrent(), originalPage.getSize(), originalPage.getTotal());
        responsePage.setRecords(responseList);

        return responsePage;
    }

    /**
     * 转换为分页Response对象
     *
     * @param contractVO 合同联合查询VO
     * @return 分页Response对象
     */
    private ContractPageResponse convertToPageResponse(ContractListVO contractVO) {
        ContractPageResponse response = new ContractPageResponse();

        // 基础字段转换
        response.setId(FormatUtils.safeToString(contractVO.getId()));
        response.setContractNo(FormatUtils.safeString(contractVO.getContractNo()));
        response.setEffectiveDate(FormatUtils.formatDate(contractVO.getEffectiveDate()));
        response.setExpiryDate(FormatUtils.formatDate(contractVO.getExpiryDate()));
        response.setCancelReason(FormatUtils.safeString(contractVO.getCancelReason()));
        response.setCreatedBy(FormatUtils.safeString(contractVO.getCreatedBy()));
        response.setCreatedTime(FormatUtils.formatDateTime(contractVO.getCreatedTime()));

        // 我司主体通过枚举转换
        response.setOutCustomerCompanyName(OurCompanyEnum.getNameByCode(contractVO.getOutCustomerCompanyId()));

        // 客户主体名称直接从联合查询结果获取
        response.setCustomerCompanyName(FormatUtils.safeString(contractVO.getCustomerCompanyName()));

        // 合同状态转换为中文
        response.setContractStatus(FormatUtils.safeToString(contractVO.getContractStatus()));
        response.setContractStatusName(getContractStatusName(contractVO.getContractStatus()));

        // 归档状态转换为中文
        response.setArchiveStatus(FormatUtils.safeToString(contractVO.getArchiveStatus()));
        response.setArchiveStatusName(getArchiveStatusName(contractVO.getArchiveStatus()));

        return response;
    }


    /**
     * 根据ID获取合同详情（返回Response格式）
     *
     * @param id 主键ID
     * @return 合同详情Response
     */
    public ContractDetailResponse getContractDetailById(Long id) {
        // 使用联合查询获取详情
        ContractListVO contractVO = baseMapper.selectContractDetailWithJoin(id);
        if (contractVO == null) {
            return null;
        }
        return convertToDetailResponse(contractVO);
    }

    /**
     * 新增合同
     *
     * @param request 合同信息
     * @return 是否成功
     */
    public boolean addContract(ContractRequest request) {
        // 校验合同编号是否重复
        if (isContractNoExists(request.getContractNo(), null)) {
            throw new RuntimeException("合同编号已存在，不能重复");
        }

        TContract contract = new TContract();
        
        // 基础字段设置
        contract.setContractNo(FormatUtils.safeString(request.getContractNo()));
        contract.setOutCustomerCompanyId(FormatUtils.safeToInteger(request.getOutCustomerCompanyId()));
        contract.setCustomerCompanyId(FormatUtils.safeToLong(request.getCustomerCompanyId()));
        contract.setEffectiveDate(FormatUtils.parseDate(request.getEffectiveDate()));
        contract.setExpiryDate(FormatUtils.parseDate(request.getExpiryDate()));
        contract.setRemark(FormatUtils.safeString(request.getRemark()));

        // 设置默认状态
        contract.setContractStatus(calculateContractStatus(contract.getEffectiveDate(), contract.getExpiryDate()));
        contract.setArchiveStatus(ArchiveStatusEnum.PENDING.getCode()); // 待归档
        contract.setValid(1);

        boolean success = this.save(contract);
        if (success) {
            Long contractId = contract.getId();

            // 保存SKU明细
            saveContractSkuDetails(contractId, request.getSkuDetails());

            // 保存合同附件
            generalFileService.saveContractAttachments(contractId, request.getAttachmentPaths());

            // 更新归档状态（直接根据是否有附件判断）
            updateArchiveStatus(contractId, request.getAttachmentPaths());
        }

        return success;
    }

    /**
     * 更新合同
     *
     * @param request 合同信息
     * @return 是否成功
     */
    public boolean updateContract(ContractRequest request) {
        Long id = FormatUtils.safeToLong(request.getId());

        // 校验合同编号是否重复（排除当前记录）
        if (isContractNoExists(request.getContractNo(), id)) {
            throw new RuntimeException("合同编号已存在，不能重复");
        }

        TContract contract = new TContract();
        contract.setId(id);
        
        // 基础字段设置
        contract.setContractNo(FormatUtils.safeString(request.getContractNo()));
        contract.setOutCustomerCompanyId(FormatUtils.safeToInteger(request.getOutCustomerCompanyId()));
        contract.setCustomerCompanyId(FormatUtils.safeToLong(request.getCustomerCompanyId()));
        contract.setEffectiveDate(FormatUtils.parseDate(request.getEffectiveDate()));
        contract.setExpiryDate(FormatUtils.parseDate(request.getExpiryDate()));
        contract.setRemark(FormatUtils.safeString(request.getRemark()));

        // 重新计算合同状态
        contract.setContractStatus(calculateContractStatus(contract.getEffectiveDate(), contract.getExpiryDate()));

        boolean success = this.updateById(contract);
        if (success) {
            // 更新SKU明细
            saveContractSkuDetails(id, request.getSkuDetails());

            // 更新合同附件
            generalFileService.saveContractAttachments(id, request.getAttachmentPaths());

            // 更新归档状态
            updateArchiveStatus(id,request.getAttachmentPaths());

            // 记录编辑日志
            contractLogService.recordLog(id, ContractActionEnum.EDIT, getCurrentUser(),null);
        }

        return success;
    }

    /**
     * 作废合同
     *
     * @param id 合同ID
     * @param cancelReason 作废原因
     * @return 是否成功
     */
    public boolean cancelContract(Long id, String cancelReason) {
        TContract contract = new TContract();
        contract.setId(id);
        contract.setContractStatus(ContractStatusEnum.CANCELLED.getCode()); // 已作废
        contract.setCancelReason(FormatUtils.safeString(cancelReason)); // 记录作废原因

        boolean success = this.updateById(contract);
        if (success) {
            // 记录作废日志
            contractLogService.recordLog(id, ContractActionEnum.CANCEL, getCurrentUser(),
                FormatUtils.safeString(cancelReason));
        }

        return success;
    }

    /**
     * 根据客户ID获取有效合同列表
     *
     * @param customerCompanyId 客户公司ID
     * @return 合同列表
     */
    public List<TContract> getValidContractsByCustomerId(Long customerCompanyId) {
        LambdaQueryWrapper<TContract> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TContract::getCustomerCompanyId, customerCompanyId)
                .eq(TContract::getValid, 1)
                .in(TContract::getContractStatus, ContractStatusEnum.PENDING.getCode(), ContractStatusEnum.ACTIVE.getCode()) // 待生效、生效中
                .orderByDesc(TContract::getCreatedTime);
        return this.list(wrapper);
    }

    /**
     * 检查合同编号是否唯一
     *
     * @param contractNo 合同编号
     * @param excludeId 排除的合同ID（编辑时使用）
     * @return 是否唯一
     */
    public Boolean checkContractNoUnique(String contractNo, Long excludeId) {
        return !isContractNoExists(contractNo, excludeId);
    }

    /**
     * 根据合同编号获取合同ID
     *
     * @param contractNo 合同编号
     * @return 合同ID
     */
    public Long getContractIdByNo(String contractNo) {
        if (contractNo == null || contractNo.trim().isEmpty()) {
            return null;
        }

        LambdaQueryWrapper<TContract> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TContract::getContractNo, contractNo.trim())
                .eq(TContract::getValid, 1)
                .select(TContract::getId)
                .last("LIMIT 1");

        TContract contract = this.getOne(wrapper);
        return contract != null ? contract.getId() : null;
    }

    /**
     * 导出合同列表
     *
     * @param request 导出条件
     * @param response HTTP响应
     */
    public void exportContractList(ContractExportRequest request, HttpServletResponse response) throws IOException {
        // 转换查询参数
        String contractNo = StringUtils.hasText(request.getContractNo()) ? request.getContractNo().trim() : null;
        String customerCompanyName = StringUtils.hasText(request.getCustomerCompanyName()) ? request.getCustomerCompanyName().trim() : null;
        Integer contractStatus = FormatUtils.safeToInteger(request.getContractStatus());

        // 使用联合查询获取所有数据（不分页）
        List<ContractListVO> dataList = baseMapper.selectContractListWithJoin(contractNo, customerCompanyName, contractStatus);

        // 转换为导出VO
        List<ContractExportVO> exportList = dataList.stream()
                .map(this::convertToExportVO)
                .collect(Collectors.toList());

        // 导出Excel
        ExcelUtils.exportExcelWithTimestamp(response, "合同列表", "合同列表",
                ContractExportVO.class, exportList);
    }

    // ========== 私有辅助方法 ==========

    /**
     * 检查合同编号是否存在
     */
    private boolean isContractNoExists(String contractNo, Long excludeId) {
        if (!StringUtils.hasText(contractNo)) {
            return false;
        }
        
        LambdaQueryWrapper<TContract> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TContract::getContractNo, contractNo)
                .eq(TContract::getValid, 1)
                .ne(TContract::getContractStatus, ContractStatusEnum.CANCELLED.getCode()); // 排除已作废的合同
        
        if (excludeId != null) {
            wrapper.ne(TContract::getId, excludeId);
        }
        
        return this.count(wrapper) > 0;
    }

    /**
     * 计算合同状态
     */
    private Integer calculateContractStatus(LocalDate effectiveDate, LocalDate expiryDate) {
        LocalDate now = LocalDate.now();
        
        if (now.isBefore(effectiveDate)) {
            return ContractStatusEnum.PENDING.getCode(); // 待生效
        } else if (now.isAfter(expiryDate)) {
            return ContractStatusEnum.EXPIRED.getCode(); // 已到期
        } else {
            return ContractStatusEnum.ACTIVE.getCode(); // 生效中
        }
    }

    /**
     * 获取归档状态名称
     */
    private String getArchiveStatusName(Integer status) {
        return ArchiveStatusEnum.getNameByCode(status);
    }

    /**
     * 获取合同状态名称
     */
    private String getContractStatusName(Integer status) {
        return ContractStatusEnum.getNameByCode(status);
    }

    /**
     * 转换为详情Response对象
     */
    private ContractDetailResponse convertToDetailResponse(ContractListVO contractVO) {
        ContractDetailResponse response = new ContractDetailResponse();

        // 基础字段转换（保持与数据库字段一致）
        response.setId(FormatUtils.safeToString(contractVO.getId()));
        response.setContractNo(FormatUtils.safeString(contractVO.getContractNo()));
        response.setOutCustomerCompanyId(FormatUtils.safeToString(contractVO.getOutCustomerCompanyId()));
        response.setCustomerCompanyId(FormatUtils.safeToString(contractVO.getCustomerCompanyId()));

        // 我司主体通过枚举转换
        response.setOutCustomerCompanyName(OurCompanyEnum.getNameByCode(contractVO.getOutCustomerCompanyId()));

        // 客户主体名称直接从联合查询结果获取
        response.setCustomerCompanyName(FormatUtils.safeString(contractVO.getCustomerCompanyName()));
        response.setContractStatus(FormatUtils.safeToString(contractVO.getContractStatus()));
        response.setArchiveStatus(FormatUtils.safeToString(contractVO.getArchiveStatus()));
        response.setEffectiveDate(FormatUtils.formatDate(contractVO.getEffectiveDate()));
        response.setExpiryDate(FormatUtils.formatDate(contractVO.getExpiryDate()));
        response.setCancelReason(FormatUtils.safeString(contractVO.getCancelReason()));
        response.setRemark(FormatUtils.safeString(contractVO.getRemark()));
        response.setCreatedBy(FormatUtils.safeString(contractVO.getCreatedBy()));
        response.setCreatedTime(FormatUtils.formatDateTime(contractVO.getCreatedTime()));
        response.setLastModifiedBy(FormatUtils.safeString(contractVO.getLastModifiedBy()));
        response.setLastModifiedTime(FormatUtils.formatDateTime(contractVO.getLastModifiedTime()));
        response.setValid(FormatUtils.safeToString(contractVO.getValid()));

        // 获取SKU明细列表
        response.setSkuDetails(getContractSkuList(contractVO.getId()));

        // 获取合同附件列表
        List<TGeneralFile> attachments = generalFileService.getContractAttachments(contractVO.getId());
        List<String> attachmentPaths = attachments.stream()
                .map(TGeneralFile::getFilePath)
                .collect(Collectors.toList());
        response.setAttachmentPaths(attachmentPaths);

        return response;
    }

    /**
     * 转换为导出VO对象
     */
    private ContractExportVO convertToExportVO(ContractListVO contractVO) {
        ContractExportVO exportVO = new ContractExportVO();

        // 基础字段转换
        exportVO.setContractNo(FormatUtils.safeString(contractVO.getContractNo()));
        exportVO.setEffectiveDate(FormatUtils.formatDate(contractVO.getEffectiveDate()));
        exportVO.setExpiryDate(FormatUtils.formatDate(contractVO.getExpiryDate()));
        exportVO.setCancelReason(FormatUtils.safeString(contractVO.getCancelReason()));
        exportVO.setCreatedBy(FormatUtils.safeString(contractVO.getCreatedBy()));
        exportVO.setCreatedTime(FormatUtils.formatDateTime(contractVO.getCreatedTime()));
        exportVO.setRemark(FormatUtils.safeString(contractVO.getRemark()));

        // 我司主体通过枚举转换
        exportVO.setOutCustomerCompanyName(OurCompanyEnum.getNameByCode(contractVO.getOutCustomerCompanyId()));

        // 客户主体名称直接从联合查询结果获取
        exportVO.setCustomerCompanyName(FormatUtils.safeString(contractVO.getCustomerCompanyName()));

        // 状态转换为中文
        exportVO.setContractStatusName(getContractStatusName(contractVO.getContractStatus()));
        exportVO.setArchiveStatusName(getArchiveStatusName(contractVO.getArchiveStatus()));

        return exportVO;
    }

    /**
     * 保存合同SKU明细
     *
     * @param contractId 合同ID
     * @param skuDetails SKU明细列表
     */
    private void saveContractSkuDetails(Long contractId, List<ContractSkuRequest> skuDetails) {
        if (skuDetails == null || skuDetails.isEmpty()) {
            return;
        }

        // 先删除原有的SKU明细
        contractSkuService.deleteByContractId(contractId);

        // 保存新的SKU明细
        for (ContractSkuRequest skuRequest : skuDetails) {
            Integer firstCategory = FormatUtils.safeToInteger(skuRequest.getFirstCategory());
            Integer businessMode = FormatUtils.safeToInteger(skuRequest.getBusinessMode());

            if (firstCategory != null && businessMode != null) {
                contractSkuService.addContractSku(contractId, firstCategory, businessMode);
            }
        }
    }

    /**
     * 更新归档状态
     *
     * @param contractId 合同ID
     * @param attachmentPaths 附件路径列表
     */
    private void updateArchiveStatus(Long contractId, List<String> attachmentPaths) {
        // 直接判断是否有附件路径
        boolean hasAttachments = attachmentPaths != null && !attachmentPaths.isEmpty() &&
                                attachmentPaths.stream().anyMatch(StringUtils::hasText);

        TContract contract = new TContract();
        contract.setId(contractId);
        contract.setArchiveStatus(hasAttachments ? ArchiveStatusEnum.ARCHIVED.getCode() : ArchiveStatusEnum.PENDING.getCode());

        this.updateById(contract);
    }

    /**
     * 获取合同SKU明细列表
     *
     * @param contractId 合同ID
     * @return SKU明细列表
     */
    public List<ContractSkuResponse> getContractSkuList(Long contractId) {
        List<TContractSku> skuList = contractSkuService.getByContractId(contractId);

        return skuList.stream()
                .map(this::convertToSkuResponse)
                .collect(Collectors.toList());
    }

    /**
     * 转换为SKU明细Response对象
     *
     * @param contractSku 合同SKU明细实体
     * @return SKU明细Response对象
     */
    private ContractSkuResponse convertToSkuResponse(TContractSku contractSku) {
        ContractSkuResponse response = new ContractSkuResponse();

        response.setId(FormatUtils.safeToString(contractSku.getId()));
        response.setContractId(FormatUtils.safeToString(contractSku.getContractId()));
        response.setFirstCategory(FormatUtils.safeToString(contractSku.getFirstCategory()));
        response.setFirstCategoryName(SkuFirstCategoryEnum.getDescriptionByCode(contractSku.getFirstCategory()));
        response.setBusinessMode(FormatUtils.safeToString(contractSku.getBusinessMode()));
        response.setBusinessModeName(BusinessModeEnum.getNameByCode(contractSku.getBusinessMode()));
        response.setCreatedTime(FormatUtils.formatDateTime(contractSku.getCreatedTime()));

        return response;
    }

    /**
     * 获取合同操作日志列表
     *
     * @param contractId 合同ID
     * @return 操作日志列表
     */
    public List<ContractLogResponse> getContractOperationLogs(Long contractId) {
        List<TContractLog> logs = contractLogService.getByContractId(contractId);

        return logs.stream()
                .map(this::convertToLogResponse)
                .collect(Collectors.toList());
    }

    /**
     * 转换为日志Response对象
     *
     * @param contractLog 合同日志实体
     * @return 日志Response对象
     */
    private ContractLogResponse convertToLogResponse(TContractLog contractLog) {
        ContractLogResponse response = new ContractLogResponse();

        response.setId(FormatUtils.safeToString(contractLog.getId()));
        response.setContractId(FormatUtils.safeToString(contractLog.getContractId()));
        response.setAction(FormatUtils.safeToString(contractLog.getAction()));
        response.setActionName(ContractActionEnum.getNameByCode(contractLog.getAction()));
        response.setOperator(FormatUtils.safeString(contractLog.getOperator()));
        response.setOperationTime(FormatUtils.formatDateTime(contractLog.getOperationTime()));
        response.setRemark(FormatUtils.safeString(contractLog.getRemark()));

        return response;
    }

    /**
     * 批量更新合同状态（定时任务使用）
     *
     * @param contractIds 合同ID列表
     * @param newStatus 新状态
     * @return 更新成功的数量
     */
    public int batchUpdateContractStatus(List<Long> contractIds, Integer newStatus) {
        if (contractIds == null || contractIds.isEmpty()) {
            return 0;
        }

        LambdaUpdateWrapper<TContract> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(TContract::getId, contractIds)
                .set(TContract::getContractStatus, newStatus);

        boolean success = this.update(updateWrapper);
        return success ? contractIds.size() : 0;
    }

    /**
     * 查询需要状态更新的合同
     *
     * @param currentStatus 当前状态
     * @param effectiveDateCondition 生效日期条件
     * @param expiryDateCondition 失效日期条件
     * @param today 当前日期
     * @return 合同列表
     */
    public List<TContract> getContractsForStatusUpdate(Integer currentStatus,
                                                      String effectiveDateCondition,
                                                      String expiryDateCondition,
                                                      LocalDate today) {
        LambdaQueryWrapper<TContract> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TContract::getContractStatus, currentStatus)
                .eq(TContract::getValid, 1);

        // 根据条件添加日期过滤
        if ("le".equals(effectiveDateCondition)) {
            wrapper.le(TContract::getEffectiveDate, today);
        } else if ("ge".equals(effectiveDateCondition)) {
            wrapper.ge(TContract::getEffectiveDate, today);
        }

        if ("ge".equals(expiryDateCondition)) {
            wrapper.ge(TContract::getExpiryDate, today);
        } else if ("lt".equals(expiryDateCondition)) {
            wrapper.lt(TContract::getExpiryDate, today);
        }

        return this.list(wrapper);
    }

    /**
     * 获取当前用户
     * TODO: 这里需要根据项目的用户认证方式来实现
     *
     * @return 当前用户名
     */
    private String getCurrentUser() {
        // TODO: 从SecurityContext或Session中获取当前用户
        // 这里先返回一个默认值，实际项目中需要根据认证方式来实现
        return "系统用户";
    }
}
