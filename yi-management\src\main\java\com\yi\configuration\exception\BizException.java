package com.yi.configuration.exception;


import lombok.Getter;

/**
 * 业务异常
 * 用于在处理业务逻辑时，进行抛出的异常。
 *
 */
@Getter
public class BizException extends RuntimeException {

    private static final long serialVersionUID = -342342342342L;

    /**
     * 异常信息
     */
    private String message;

    /**
     * 具体异常码
     */
    private int code;

    public BizException(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public BizException(BaseExceptionEnum exceptionEnum){
        this.code = exceptionEnum.getCode();
        this.message = exceptionEnum.getMessage();
    }

    public BizException(int code, String format, Object... args) {
        this.code = code;
        this.message = String.format(format, args);
    }

    @Override
    public String toString() {
        return "BizException [message=" + message + ", code=" + code + "]";
    }

}
