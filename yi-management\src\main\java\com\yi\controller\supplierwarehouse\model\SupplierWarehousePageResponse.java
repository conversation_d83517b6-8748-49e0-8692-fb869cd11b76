package com.yi.controller.supplierwarehouse.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 供应商仓库分页响应
 */
@Data
@ApiModel("供应商仓库分页响应")
public class SupplierWarehousePageResponse {

    @ApiModelProperty("主键ID")
    private String id;

    @ApiModelProperty("状态：1-启用，0-禁用")
    private Integer enabled;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("仓库名称")
    private String warehouseName;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("联系人")
    private String contactPerson;

    @ApiModelProperty("联系方式")
    private String contactPhone;

    @ApiModelProperty("创建人")
    private String createdBy;

    @ApiModelProperty("创建时间")
    private String createdTime;
}
