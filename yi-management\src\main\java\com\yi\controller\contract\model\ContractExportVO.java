package com.yi.controller.contract.model;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 合同导出VO
 */
@Data
@ApiModel(value = "ContractExportVO", description = "合同导出VO")
public class ContractExportVO {

    @ExcelProperty("合同编号")
    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    @ExcelProperty("我司主体")
    @ApiModelProperty(value = "我司主体名称")
    private String outCustomerCompanyName;

    @ExcelProperty("客户主体")
    @ApiModelProperty(value = "客户主体名称")
    private String customerCompanyName;

    @ExcelProperty("合同状态")
    @ApiModelProperty(value = "合同状态名称")
    private String contractStatusName;

    @ExcelProperty("归档状态")
    @ApiModelProperty(value = "归档状态名称")
    private String archiveStatusName;

    @ExcelProperty("生效日期")
    @ApiModelProperty(value = "生效日期")
    private String effectiveDate;

    @ExcelProperty("失效日期")
    @ApiModelProperty(value = "失效日期")
    private String expiryDate;

    @ExcelProperty("作废原因")
    @ApiModelProperty(value = "作废原因")
    private String cancelReason;

    @ExcelProperty("创建人")
    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ExcelProperty("创建时间")
    @ApiModelProperty(value = "创建时间")
    private String createdTime;

    @ExcelProperty("备注")
    @ApiModelProperty(value = "备注")
    private String remark;
}
