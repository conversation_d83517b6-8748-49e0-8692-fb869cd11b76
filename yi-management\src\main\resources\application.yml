server:
  port: 8080

spring:
  application:
    name: yi-management
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************************
    username: root
    password: 123xierongfei
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false

# MyBatis Plus 配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  mapper-locations: classpath*:mapper/**/*.xml
  type-aliases-package: com.yi.entity
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: valid
      logic-delete-value: 0
      logic-not-delete-value: 1

# 日志配置
logging:
  config: classpath:logback.xml
  level:
    com.yi.mapper: debug

auth:
  serviceId: sinopec-logistics-server
  client:
    id: sinopec-services
    token-header: token
    pub-key:
      path: client/pub.key

login:
  privateKey: MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAMrHDF5Pcxb/V7dtw44xfw7mEReamsj4L8/NlAFcvXDPxy2sAxTmdKxSDzrld+8TLGFcy4LE2MAWu3BuH24ziDavxZJ+DuoIyZvBwRN0odKuAy8/bmgcBPQrFdffcUmw53X51C4jegK+pt8STMlnTuNgZ4eiKCToD4yQxYlUt1j3AgMBAAECgYBKyMIs/gwqDR3ijl6y0FpVuk/PbrtrJu0rLFxLbFKJinegxnGUgbHB47muNPbYey/A8KI1pjdyEHAsdNCuqd6v9GcyykEvEijZikcDJVGVkU2KlwKxNP5vMMVSrtUOx0ZPWrWNcDSTeFSLfFoGogYt1XsHuGMGnllunCwzmcMzYQJBAOizBHa67m68SSzkvZBMbKoc/AZkWatXMPCZiCwwq6JQDabZcGc0z3fJU85YwdJbnl05JTKQKDY2cCk2t6477YkCQQDfFQVGbu9izo9H5V2ezWR1ntsdnVH/OZIBxcJyy18+usF5SlisK73OT6kKJjPP36JZfnHSNxWHNKI+pPU3qfJ/AkEAvMRcHqTNvu7mvZBhZqlYjTcuC1FknGYCwWsK2C4zTVx7dpp1YIOquZWMcpv1v1urQByXXEktFa1bl1NX9fOPYQJBAIebwKQrRaw7ws/Ct2GUm9AAbFSZUMIkngQj0mt0A4cfggMYVcT7phsnQ6vewzpHSOq8Oba/M4AIV+Ra5JIc5kMCQQCgU3RICOvsRNsVkKBF8rYm4E6Tx/aZzWyOWWEouBQelATxAH/gJ48BkwjIs8BeDdIJXHafLwM0EP1jlAnWqqi3
  publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDKxwxeT3MW/1e3bcOOMX8O5hEXmprI+C/PzZQBXL1wz8ctrAMU5nSsUg865XfvEyxhXMuCxNjAFrtwbh9uM4g2r8WSfg7qCMmbwcETdKHSrgMvP25oHAT0KxXX33FJsOd1+dQuI3oCvqbfEkzJZ07jYGeHoigk6A+MkMWJVLdY9wIDAQAB


#jwt配置
jwt:
  id: sinopec-logistics-server
  secret: sinopecnetworkfreight
  token-header: token
  expire: 7200
  appExp: 259200
  openExp: 10800
  pri-key:
    path: client/pri.key
  pub-key:
    path: client/pub.key
