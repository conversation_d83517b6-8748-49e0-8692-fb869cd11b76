package com.yi.controller.contract.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 合同作废请求
 */
@Data
@ApiModel(value = "ContractCancelRequest", description = "合同作废请求")
public class ContractCancelRequest {

    @NotBlank(message = "作废原因不能为空")
    @ApiModelProperty(value = "作废原因", required = true)
    private String cancelReason;
}
