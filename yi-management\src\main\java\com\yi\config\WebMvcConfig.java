package com.yi.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web MVC 配置
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Autowired
    private FileUploadConfig fileUploadConfig;

    /**
     * 配置静态资源处理
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置正式文件的静态资源访问
        registry.addResourceHandler(fileUploadConfig.getUrlPrefix() + "/**")
                .addResourceLocations("file:" + fileUploadConfig.getBasePath() + "/");

        // 配置临时文件的静态资源访问
        registry.addResourceHandler(fileUploadConfig.getTempUrlPrefix() + "/**")
                .addResourceLocations("file:" + fileUploadConfig.getTempPath() + "/");
    }
}
