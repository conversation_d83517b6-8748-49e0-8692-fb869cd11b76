﻿<!DOCTYPE html>
<html>
  <head>
    <title>确认出库</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/确认出库/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/确认出库/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (线段) -->
      <div id="u3005" class="ax_default line1">
        <img id="u3005_img" class="img " src="images/客户管理/u350.svg"/>
        <div id="u3005_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3006" class="ax_default box_1">
        <div id="u3006_div" class=""></div>
        <div id="u3006_text" class="text ">
          <p><span>确认出库&nbsp; &nbsp; X</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3007" class="ax_default link_button">
        <div id="u3007_div" class=""></div>
        <div id="u3007_text" class="text ">
          <p><span>刷新本页</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3008" class="ax_default box_21">
        <div id="u3008_div" class=""></div>
        <div id="u3008_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3009" class="ax_default heading_3">
        <div id="u3009_div" class=""></div>
        <div id="u3009_text" class="text ">
          <p><span>基本信息</span></p>
        </div>
      </div>

      <!-- Unnamed (表格) -->
      <div id="u3010" class="ax_default">

        <!-- Unnamed (单元格) -->
        <div id="u3011" class="ax_default table_cell">
          <img id="u3011_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u3011_text" class="text ">
            <p><span>状态</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u3012" class="ax_default table_cell">
          <img id="u3012_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u3012_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u3013" class="ax_default table_cell">
          <img id="u3013_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u3013_text" class="text ">
            <p><span>出库单号</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u3014" class="ax_default table_cell">
          <img id="u3014_img" class="img " src="images/确认发货/u2248.png"/>
          <div id="u3014_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u3015" class="ax_default table_cell">
          <img id="u3015_img" class="img " src="images/确认出库/u3015.png"/>
          <div id="u3015_text" class="text ">
            <p><span>配送方式</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u3016" class="ax_default table_cell">
          <img id="u3016_img" class="img " src="images/确认出库/u3015.png"/>
          <div id="u3016_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u3017" class="ax_default table_cell">
          <img id="u3017_img" class="img " src="images/确认出库/u3015.png"/>
          <div id="u3017_text" class="text ">
            <p><span>车牌号</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u3018" class="ax_default table_cell">
          <img id="u3018_img" class="img " src="images/确认出库/u3018.png"/>
          <div id="u3018_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u3019" class="ax_default table_cell">
          <img id="u3019_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u3019_text" class="text ">
            <p><span>出库仓库</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u3020" class="ax_default table_cell">
          <img id="u3020_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u3020_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u3021" class="ax_default table_cell">
          <img id="u3021_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u3021_text" class="text ">
            <p><span>仓库地址</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u3022" class="ax_default table_cell">
          <img id="u3022_img" class="img " src="images/确认发货/u2248.png"/>
          <div id="u3022_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u3023" class="ax_default table_cell">
          <img id="u3023_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u3023_text" class="text ">
            <p><span>计划出库数</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u3024" class="ax_default table_cell">
          <img id="u3024_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u3024_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u3025" class="ax_default table_cell">
          <img id="u3025_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u3025_text" class="text ">
            <p><span>待出库数</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u3026" class="ax_default table_cell">
          <img id="u3026_img" class="img " src="images/确认发货/u2248.png"/>
          <div id="u3026_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u3027" class="ax_default table_cell">
          <img id="u3027_img" class="img " src="images/确认发货/u2265.png"/>
          <div id="u3027_text" class="text ">
            <p><span>实际出库数</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u3028" class="ax_default table_cell">
          <img id="u3028_img" class="img " src="images/确认发货/u2265.png"/>
          <div id="u3028_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u3029" class="ax_default table_cell">
          <img id="u3029_img" class="img " src="images/确认发货/u2265.png"/>
          <div id="u3029_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u3030" class="ax_default table_cell">
          <img id="u3030_img" class="img " src="images/确认发货/u2268.png"/>
          <div id="u3030_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3031" class="ax_default label">
        <div id="u3031_div" class=""></div>
        <div id="u3031_text" class="text ">
          <p><span>*出库数量</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u3032" class="ax_default text_field">
        <div id="u3032_div" class=""></div>
        <input id="u3032_input" type="text" value="" class="u3032_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3033" class="ax_default primary_button">
        <div id="u3033_div" class=""></div>
        <div id="u3033_text" class="text ">
          <p><span>确认</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3034" class="ax_default label">
        <div id="u3034_div" class=""></div>
        <div id="u3034_text" class="text ">
          <p><span>*实际出库时间</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u3035" class="ax_default text_field">
        <div id="u3035_div" class=""></div>
        <input id="u3035_input" type="text" value="" class="u3035_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3036" class="ax_default label">
        <div id="u3036_div" class=""></div>
        <div id="u3036_text" class="text ">
          <p><span>*出库凭证</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3037" class="ax_default box_1">
        <div id="u3037_div" class=""></div>
        <div id="u3037_text" class="text ">
          <p><span>+</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3038" class="ax_default label">
        <div id="u3038_div" class=""></div>
        <div id="u3038_text" class="text ">
          <p><span>备注</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u3039" class="ax_default text_field">
        <div id="u3039_div" class=""></div>
        <input id="u3039_input" type="text" value="" class="u3039_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3040" class="ax_default label">
        <div id="u3040_div" class=""></div>
        <div id="u3040_text" class="text ">
          <p><span>*明细类型</span></p>
        </div>
      </div>

      <!-- Unnamed (下拉列表) -->
      <div id="u3041" class="ax_default droplist">
        <div id="u3041_div" class=""></div>
        <select id="u3041_input" class="u3041_input">
          <option class="u3041_input_option" value="一级类目 二级类目 三级类目">一级类目 二级类目 三级类目</option>
        </select>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3042" class="ax_default label">
        <div id="u3042_div" class=""></div>
        <div id="u3042_text" class="text ">
          <p><span>*出库数量</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u3043" class="ax_default text_field">
        <div id="u3043_div" class=""></div>
        <input id="u3043_input" type="text" value="" class="u3043_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3044" class="ax_default label">
        <div id="u3044_div" class=""></div>
        <div id="u3044_text" class="text ">
          <p><span>*明细类型</span></p>
        </div>
      </div>

      <!-- Unnamed (下拉列表) -->
      <div id="u3045" class="ax_default droplist">
        <div id="u3045_div" class=""></div>
        <select id="u3045_input" class="u3045_input">
          <option class="u3045_input_option" value="一级类目 二级类目 三级类目">一级类目 二级类目 三级类目</option>
        </select>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3046" class="ax_default label">
        <div id="u3046_div" class=""></div>
        <div id="u3046_text" class="text ">
          <p><span>待出库1000</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3047" class="ax_default label">
        <div id="u3047_div" class=""></div>
        <div id="u3047_text" class="text ">
          <p><span>待出库1000</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
