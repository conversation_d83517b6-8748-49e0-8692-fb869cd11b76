package com.yi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yi.entity.InboundOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 入库单表 Mapper 接口
 */
@Mapper
public interface InboundOrderMapper extends BaseMapper<InboundOrder> {

    /**
     * 分页查询入库单列表
     *
     * @param page 分页参数
     * @param orderNo 入库单号（模糊查询）
     * @param status 入库状态
     * @param inboundType 入库类型
     * @param inboundWarehouseId 入库仓库ID
     * @param senderWarehouseId 发货仓库ID
     * @param firstCategory 一级类目
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分页结果
     */
    IPage<InboundOrder> selectInboundOrderPage(Page<InboundOrder> page,
                                               @Param("orderNo") String orderNo,
                                               @Param("status") Integer status,
                                               @Param("inboundType") Integer inboundType,
                                               @Param("inboundWarehouseId") Long inboundWarehouseId,
                                               @Param("senderWarehouseId") Long senderWarehouseId,
                                               @Param("firstCategory") Integer firstCategory,
                                               @Param("startTime") LocalDateTime startTime,
                                               @Param("endTime") LocalDateTime endTime);

    /**
     * 根据入库单号查询入库单
     *
     * @param orderNo 入库单号
     * @return 入库单信息
     */
    InboundOrder selectByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 根据状态查询入库单列表
     *
     * @param status 入库状态
     * @return 入库单列表
     */
    List<InboundOrder> selectByStatus(@Param("status") Integer status);

    /**
     * 根据入库仓库ID查询入库单列表
     *
     * @param inboundWarehouseId 入库仓库ID
     * @return 入库单列表
     */
    List<InboundOrder> selectByInboundWarehouseId(@Param("inboundWarehouseId") Long inboundWarehouseId);

    /**
     * 根据发货仓库ID查询入库单列表
     *
     * @param senderWarehouseId 发货仓库ID
     * @return 入库单列表
     */
    List<InboundOrder> selectBySenderWarehouseId(@Param("senderWarehouseId") Long senderWarehouseId);

    /**
     * 根据关联出库单ID查询入库单
     *
     * @param outboundOrderId 出库单ID
     * @return 入库单信息
     */
    InboundOrder selectByOutboundOrderId(@Param("outboundOrderId") Long outboundOrderId);

    /**
     * 统计各状态的入库单数量
     *
     * @return 状态统计结果
     */
    List<java.util.Map<String, Object>> selectStatusStatistics();

    /**
     * 统计各类型的入库单数量
     *
     * @return 类型统计结果
     */
    List<java.util.Map<String, Object>> selectTypeStatistics();

    /**
     * 查询指定时间范围内的入库单
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 入库单列表
     */
    List<InboundOrder> selectByTimeRange(@Param("startTime") LocalDateTime startTime,
                                         @Param("endTime") LocalDateTime endTime);

    /**
     * 查询待入库的订单（状态为待入库）
     *
     * @return 待入库订单列表
     */
    List<InboundOrder> selectPendingOrders();

    /**
     * 查询部分入库的订单（状态为部分入库）
     *
     * @return 部分入库订单列表
     */
    List<InboundOrder> selectPartialOrders();

    /**
     * 更新入库单状态
     *
     * @param id 入库单ID
     * @param status 新状态
     * @param actualQuantity 实际入库数
     * @param inboundTime 入库时间
     * @param lastModifiedBy 最后修改人
     * @return 更新行数
     */
    int updateStatus(@Param("id") Long id,
                     @Param("status") Integer status,
                     @Param("actualQuantity") Integer actualQuantity,
                     @Param("inboundTime") LocalDateTime inboundTime,
                     @Param("lastModifiedBy") String lastModifiedBy);
}
