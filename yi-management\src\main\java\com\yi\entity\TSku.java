package com.yi.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * SKU表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_sku")
@ApiModel(value = "TSku对象", description = "SKU表")
public class TSku extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 一级类目 1:共享托盘
     */
    @ApiModelProperty(value = "一级类目 1:共享托盘")
    private Integer firstCategory;

    /**
     * 二级类目
     */
    @ApiModelProperty(value = "二级类目")
    private String secondCategory;

    /**
     * 三级类目
     */
    @ApiModelProperty(value = "三级类目")
    private String thirdCategory;

    /**
     * 长度(mm)
     */
    @ApiModelProperty(value = "长度(mm)")
    private Integer length;

    /**
     * 宽度(mm)
     */
    @ApiModelProperty(value = "宽度(mm)")
    private Integer width;

    /**
     * 高度(mm)
     */
    @ApiModelProperty(value = "高度(mm)")
    private Integer height;

    /**
     * 重量(Kg)
     */
    @ApiModelProperty(value = "重量(Kg)")
    private BigDecimal weight;

    /**
     * 启用状态：1-启用，0-禁用
     */
    @ApiModelProperty(value = "启用状态：1-启用，0-禁用")
    private Integer enabled;
}
