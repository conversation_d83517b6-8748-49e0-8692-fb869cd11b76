package com.yi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yi.entity.SysDept;

import java.util.List;

/**
 * 部门表 服务类
 */
public interface SysDeptService extends IService<SysDept> {

    /**
     * 查询部门树形结构
     *
     * @param status 状态
     * @return 部门列表
     */
    List<SysDept> selectDeptTree(Integer status);

    /**
     * 根据父ID查询子部门
     *
     * @param parentId 父部门ID
     * @return 部门列表
     */
    List<SysDept> selectByParentId(Long parentId);

    /**
     * 根据部门编码查询部门
     *
     * @param deptCode 部门编码
     * @return 部门信息
     */
    SysDept selectByDeptCode(String deptCode);

    /**
     * 查询部门及其所有子部门ID
     *
     * @param deptId 部门ID
     * @return 部门ID列表
     */
    List<Long> selectDeptAndChildrenIds(Long deptId);

    /**
     * 查询所有启用的部门
     *
     * @return 部门列表
     */
    List<SysDept> selectEnabledDepts();

    /**
     * 检查部门是否有子部门
     *
     * @param deptId 部门ID
     * @return 是否有子部门
     */
    boolean hasChildren(Long deptId);

    /**
     * 检查部门是否有用户
     *
     * @param deptId 部门ID
     * @return 是否有用户
     */
    boolean hasUsers(Long deptId);

    /**
     * 创建部门
     *
     * @param dept 部门信息
     * @return 是否成功
     */
    boolean createDept(SysDept dept);

    /**
     * 更新部门
     *
     * @param dept 部门信息
     * @return 是否成功
     */
    boolean updateDept(SysDept dept);

    /**
     * 删除部门
     *
     * @param deptId 部门ID
     * @return 是否成功
     */
    boolean deleteDept(Long deptId);

    /**
     * 检查部门编码是否存在
     *
     * @param deptCode 部门编码
     * @param excludeId 排除的部门ID
     * @return 是否存在
     */
    boolean checkDeptCodeExists(String deptCode, Long excludeId);
}
