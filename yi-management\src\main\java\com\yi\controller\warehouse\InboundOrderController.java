package com.yi.controller.warehouse;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yi.common.Result;
import com.yi.controller.warehouse.model.*;
import com.yi.entity.InboundOrder;
import com.yi.enums.FirstCategoryEnum;
import com.yi.enums.InboundStatusEnum;
import com.yi.enums.InboundTypeEnum;
import com.yi.service.InboundOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 入库单管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/warehouse/inbound")
@Api(tags = "入库单管理")
@Validated
public class InboundOrderController {

    @Autowired
    private InboundOrderService inboundOrderService;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @PostMapping("/page")
    @ApiOperation("分页查询入库单列表")
    public Result<IPage<InboundOrderResponse>> page(@RequestBody InboundOrderQueryRequest request) {
        try {
            // 构建分页参数
            Page<InboundOrder> page = new Page<>(
                Long.parseLong(request.getCurrent()),
                Long.parseLong(request.getSize())
            );

            // 时间参数转换
            LocalDateTime startTime = null;
            LocalDateTime endTime = null;
            if (StringUtils.hasText(request.getStartTime())) {
                startTime = LocalDateTime.parse(request.getStartTime(), DATE_TIME_FORMATTER);
            }
            if (StringUtils.hasText(request.getEndTime())) {
                endTime = LocalDateTime.parse(request.getEndTime(), DATE_TIME_FORMATTER);
            }

            // 查询数据
            IPage<InboundOrder> result = inboundOrderService.selectInboundOrderPage(
                page, request.getOrderNo(), request.getStatus(), request.getInboundType(),
                request.getInboundWarehouseId(), request.getSenderWarehouseId(),
                request.getFirstCategory(), startTime, endTime
            );

            // 转换为响应对象
            IPage<InboundOrderResponse> responseResult = result.convert(this::convertToResponse);

            return Result.success(responseResult);
        } catch (Exception e) {
            log.error("分页查询入库单失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    @GetMapping("/{id}")
    @ApiOperation("根据ID查询入库单详情")
    public Result<InboundOrderResponse> getById(@ApiParam("入库单ID") @PathVariable Long id) {
        try {
            InboundOrder order = inboundOrderService.getById(id);
            if (order == null) {
                return Result.failed("入库单不存在");
            }
            return Result.success(convertToResponse(order));
        } catch (Exception e) {
            log.error("查询入库单详情失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    @GetMapping("/orderNo/{orderNo}")
    @ApiOperation("根据单号查询入库单详情")
    public Result<InboundOrderResponse> getByOrderNo(@ApiParam("入库单号") @PathVariable String orderNo) {
        try {
            InboundOrder order = inboundOrderService.selectByOrderNo(orderNo);
            if (order == null) {
                return Result.failed("入库单不存在");
            }
            return Result.success(convertToResponse(order));
        } catch (Exception e) {
            log.error("查询入库单详情失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    @GetMapping("/outbound/{outboundOrderId}")
    @ApiOperation("根据出库单ID查询入库单")
    public Result<InboundOrderResponse> getByOutboundOrderId(@ApiParam("出库单ID") @PathVariable Long outboundOrderId) {
        try {
            InboundOrder order = inboundOrderService.selectByOutboundOrderId(outboundOrderId);
            if (order == null) {
                return Result.failed("入库单不存在");
            }
            return Result.success(convertToResponse(order));
        } catch (Exception e) {
            log.error("查询入库单详情失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    @PostMapping
    @ApiOperation("创建入库单")
    public Result<String> create(@Valid @RequestBody InboundOrderRequest request) {
        try {
            InboundOrder order = convertToEntity(request);
            order.setCreatedBy("admin"); // TODO: 从当前登录用户获取

            boolean success = inboundOrderService.createInboundOrder(order);
            if (success) {
                return Result.success("创建成功", order.getOrderNo());
            } else {
                return Result.failed("创建失败");
            }
        } catch (Exception e) {
            log.error("创建入库单失败", e);
            return Result.failed("创建失败：" + e.getMessage());
        }
    }

    @PutMapping
    @ApiOperation("更新入库单")
    public Result<String> update(@Valid @RequestBody InboundOrderRequest request) {
        try {
            if (request.getId() == null) {
                return Result.failed("入库单ID不能为空");
            }

            InboundOrder order = convertToEntity(request);
            order.setLastModifiedBy("admin"); // TODO: 从当前登录用户获取

            boolean success = inboundOrderService.updateInboundOrder(order);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.failed("更新失败");
            }
        } catch (Exception e) {
            log.error("更新入库单失败", e);
            return Result.failed("更新失败：" + e.getMessage());
        }
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除入库单")
    public Result<String> delete(@ApiParam("入库单ID") @PathVariable Long id) {
        try {
            boolean success = inboundOrderService.deleteInboundOrder(id);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.failed("删除失败");
            }
        } catch (Exception e) {
            log.error("删除入库单失败", e);
            return Result.failed("删除失败：" + e.getMessage());
        }
    }

    @DeleteMapping("/batch")
    @ApiOperation("批量删除入库单")
    public Result<String> deleteBatch(@RequestBody List<Long> ids) {
        try {
            boolean success = inboundOrderService.deleteInboundOrders(ids);
            if (success) {
                return Result.success("批量删除成功");
            } else {
                return Result.failed("批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除入库单失败", e);
            return Result.failed("批量删除失败：" + e.getMessage());
        }
    }

    @PostMapping("/partial")
    @ApiOperation("部分入库（待入库 → 部分入库）")
    public Result<String> partialInbound(@Valid @RequestBody InboundStatusUpdateRequest request) {
        try {
            boolean success = inboundOrderService.partialInbound(
                request.getId(),
                request.getActualQuantity(),
                "admin" // TODO: 从当前登录用户获取
            );

            if (success) {
                return Result.success("部分入库成功");
            } else {
                return Result.failed("部分入库失败");
            }
        } catch (Exception e) {
            log.error("部分入库失败", e);
            return Result.failed("部分入库失败：" + e.getMessage());
        }
    }

    @PostMapping("/complete")
    @ApiOperation("完成入库（部分入库/待入库 → 已入库）")
    public Result<String> completeInbound(@Valid @RequestBody InboundStatusUpdateRequest request) {
        try {
            boolean success = inboundOrderService.completeInbound(
                request.getId(),
                request.getActualQuantity(),
                "admin" // TODO: 从当前登录用户获取
            );

            if (success) {
                return Result.success("完成入库成功");
            } else {
                return Result.failed("完成入库失败");
            }
        } catch (Exception e) {
            log.error("完成入库失败", e);
            return Result.failed("完成入库失败：" + e.getMessage());
        }
    }

    @PostMapping("/cancel")
    @ApiOperation("取消入库（部分入库 → 待入库）")
    public Result<String> cancelInbound(@Valid @RequestBody InboundStatusUpdateRequest request) {
        try {
            boolean success = inboundOrderService.cancelInbound(
                request.getId(),
                "admin" // TODO: 从当前登录用户获取
            );

            if (success) {
                return Result.success("取消入库成功");
            } else {
                return Result.failed("取消入库失败");
            }
        } catch (Exception e) {
            log.error("取消入库失败", e);
            return Result.failed("取消入库失败：" + e.getMessage());
        }
    }

    @GetMapping("/statistics/status")
    @ApiOperation("查询各状态的入库单数量统计")
    public Result<List<Map<String, Object>>> getStatusStatistics() {
        try {
            List<Map<String, Object>> statistics = inboundOrderService.getStatusStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("查询状态统计失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    @GetMapping("/statistics/type")
    @ApiOperation("查询各类型的入库单数量统计")
    public Result<List<Map<String, Object>>> getTypeStatistics() {
        try {
            List<Map<String, Object>> statistics = inboundOrderService.getTypeStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("查询类型统计失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    @GetMapping("/pending")
    @ApiOperation("查询待入库的订单")
    public Result<List<InboundOrderResponse>> getPendingOrders() {
        try {
            List<InboundOrder> orders = inboundOrderService.getPendingOrders();
            List<InboundOrderResponse> responses = orders.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
            return Result.success(responses);
        } catch (Exception e) {
            log.error("查询待入库订单失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    @GetMapping("/partial")
    @ApiOperation("查询部分入库的订单")
    public Result<List<InboundOrderResponse>> getPartialOrders() {
        try {
            List<InboundOrder> orders = inboundOrderService.getPartialOrders();
            List<InboundOrderResponse> responses = orders.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
            return Result.success(responses);
        } catch (Exception e) {
            log.error("查询部分入库订单失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

    /**
     * 实体转换为响应对象
     */
    private InboundOrderResponse convertToResponse(InboundOrder order) {
        InboundOrderResponse response = new InboundOrderResponse();
        BeanUtils.copyProperties(order, response);
        
        // 设置枚举描述
        response.setStatusDesc(InboundStatusEnum.getDescByCode(order.getStatus()));
        response.setInboundTypeDesc(InboundTypeEnum.getDescByCode(order.getInboundType()));
        response.setFirstCategoryDesc(FirstCategoryEnum.getDescByCode(order.getFirstCategory()));
        
        return response;
    }

    /**
     * 请求对象转换为实体
     */
    private InboundOrder convertToEntity(InboundOrderRequest request) {
        InboundOrder order = new InboundOrder();
        BeanUtils.copyProperties(request, order);
        return order;
    }
}
