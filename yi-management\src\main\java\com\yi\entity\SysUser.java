package com.yi.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 系统用户表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_sys_user")
@ApiModel(value = "SysUser对象", description = "系统用户表")
public class SysUser extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    @TableField("username")
    private String username;

    /**
     * 密码
     */
    @ApiModelProperty(value = "密码")
    @TableField("password")
    private String password;

    /**
     * 真实姓名
     */
    @ApiModelProperty(value = "真实姓名")
    @TableField("real_name")
    private String realName;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    @TableField("email")
    private String email;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    @TableField("phone")
    private String phone;

    /**
     * 头像地址
     */
    @ApiModelProperty(value = "头像地址")
    @TableField("avatar")
    private String avatar;

    /**
     * 性别：0-女，1-男
     */
    @ApiModelProperty(value = "性别：0-女，1-男")
    @TableField("gender")
    private Integer gender;

    /**
     * 生日
     */
    @ApiModelProperty(value = "生日")
    @TableField("birthday")
    private LocalDate birthday;

    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门ID")
    @TableField("dept_id")
    private Long deptId;

    /**
     * 状态：0-禁用，1-启用
     */
    @ApiModelProperty(value = "状态：0-禁用，1-启用")
    @TableField("status")
    private Integer status;

    /**
     * 最后登录IP
     */
    @ApiModelProperty(value = "最后登录IP")
    @TableField("login_ip")
    private String loginIp;

    /**
     * 最后登录时间
     */
    @ApiModelProperty(value = "最后登录时间")
    @TableField("login_time")
    private LocalDateTime loginTime;
}
