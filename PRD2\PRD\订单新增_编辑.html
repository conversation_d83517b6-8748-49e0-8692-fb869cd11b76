﻿<!DOCTYPE html>
<html>
  <head>
    <title>订单新增/编辑</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/订单新增_编辑/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/订单新增_编辑/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (线段) -->
      <div id="u1322" class="ax_default line1">
        <img id="u1322_img" class="img " src="images/客户管理/u350.svg"/>
        <div id="u1322_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1323" class="ax_default box_1">
        <div id="u1323_div" class=""></div>
        <div id="u1323_text" class="text ">
          <p><span>订单创建/编辑&nbsp; &nbsp; X</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1324" class="ax_default link_button">
        <div id="u1324_div" class=""></div>
        <div id="u1324_text" class="text ">
          <p><span>刷新本页</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1325" class="ax_default label">
        <div id="u1325_div" class=""></div>
        <div id="u1325_text" class="text ">
          <p><span>备注信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1326" class="ax_default box_1">
        <div id="u1326_div" class=""></div>
        <div id="u1326_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1327" class="ax_default primary_button">
        <div id="u1327_div" class=""></div>
        <div id="u1327_text" class="text ">
          <p><span>保存</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1328" class="ax_default label">
        <div id="u1328_div" class=""></div>
        <div id="u1328_text" class="text ">
          <p><span>*客户</span></p>
        </div>
      </div>

      <!-- Unnamed (下拉列表) -->
      <div id="u1329" class="ax_default droplist">
        <div id="u1329_div" class=""></div>
        <select id="u1329_input" class="u1329_input">
          <option class="u1329_input_option" value="列表项1">列表项1</option>
          <option class="u1329_input_option" value="列表项2">列表项2</option>
          <option class="u1329_input_option" value="列表项3">列表项3</option>
          <option class="u1329_input_option" value="列表项4">列表项4</option>
        </select>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1330" class="ax_default label">
        <div id="u1330_div" class=""></div>
        <div id="u1330_text" class="text ">
          <p><span>*合同编号</span></p>
        </div>
      </div>

      <!-- Unnamed (下拉列表) -->
      <div id="u1331" class="ax_default droplist">
        <div id="u1331_div" class=""></div>
        <select id="u1331_input" class="u1331_input">
          <option class="u1331_input_option" value="列表项1">列表项1</option>
          <option class="u1331_input_option" value="列表项2">列表项2</option>
          <option class="u1331_input_option" value="列表项3">列表项3</option>
          <option class="u1331_input_option" value="列表项4">列表项4</option>
        </select>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1332" class="ax_default label">
        <div id="u1332_div" class=""></div>
        <div id="u1332_text" class="text ">
          <p><span>*附件上传</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1333" class="ax_default box_1">
        <div id="u1333_div" class=""></div>
        <div id="u1333_text" class="text ">
          <p><span>+</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1334" class="ax_default label">
        <div id="u1334_div" class=""></div>
        <div id="u1334_text" class="text ">
          <p><span>*地址详情</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u1335" class="ax_default text_field">
        <div id="u1335_div" class=""></div>
        <input id="u1335_input" type="text" value="" class="u1335_input"/>
      </div>

      <!-- Unnamed (下拉列表) -->
      <div id="u1336" class="ax_default droplist">
        <div id="u1336_div" class=""></div>
        <select id="u1336_input" class="u1336_input">
        </select>
      </div>

      <!-- Unnamed (下拉列表) -->
      <div id="u1337" class="ax_default droplist">
        <div id="u1337_div" class=""></div>
        <select id="u1337_input" class="u1337_input">
        </select>
      </div>

      <!-- Unnamed (下拉列表) -->
      <div id="u1338" class="ax_default droplist">
        <div id="u1338_div" class=""></div>
        <select id="u1338_input" class="u1338_input">
        </select>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1339" class="ax_default label">
        <div id="u1339_div" class=""></div>
        <div id="u1339_text" class="text ">
          <p><span>省</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1340" class="ax_default label">
        <div id="u1340_div" class=""></div>
        <div id="u1340_text" class="text ">
          <p><span>市</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1341" class="ax_default label">
        <div id="u1341_div" class=""></div>
        <div id="u1341_text" class="text ">
          <p><span>区</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1342" class="ax_default label">
        <div id="u1342_div" class=""></div>
        <div id="u1342_text" class="text ">
          <p><span>*收货人</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u1343" class="ax_default text_field">
        <div id="u1343_div" class=""></div>
        <input id="u1343_input" type="text" value="" class="u1343_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1344" class="ax_default label">
        <div id="u1344_div" class=""></div>
        <div id="u1344_text" class="text ">
          <p><span>*手机号</span></p>
        </div>
      </div>

      <!-- Unnamed (占位符) -->
      <div id="u1345" class="ax_default placeholder">
        <img id="u1345_img" class="img " src="images/订单新增_编辑/u1345.svg"/>
        <div id="u1345_text" class="text ">
          <p><span>PDF/JPG/PNG等</span></p>
        </div>
      </div>

      <!-- Unnamed (SVG) -->
      <div id="u1346" class="ax_default image">
        <img id="u1346_img" class="img " src="images/合同新增_编辑/u1019.svg"/>
        <div id="u1346_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1347" class="ax_default label">
        <div id="u1347_div" class=""></div>
        <div id="u1347_text" class="text ">
          <p><span>*需求时间</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u1348" class="ax_default text_field">
        <div id="u1348_div" class=""></div>
        <input id="u1348_input" type="text" value="" class="u1348_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1349" class="ax_default label">
        <div id="u1349_div" class=""></div>
        <div id="u1349_text" class="text ">
          <p><span>*收货仓库</span></p>
        </div>
      </div>

      <!-- Unnamed (下拉列表) -->
      <div id="u1350" class="ax_default droplist">
        <div id="u1350_div" class=""></div>
        <select id="u1350_input" class="u1350_input">
          <option class="u1350_input_option" value="列表项1">列表项1</option>
          <option class="u1350_input_option" value="列表项2">列表项2</option>
          <option class="u1350_input_option" value="列表项3">列表项3</option>
          <option class="u1350_input_option" value="列表项4">列表项4</option>
        </select>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u1351" class="ax_default text_field">
        <div id="u1351_div" class=""></div>
        <input id="u1351_input" type="text" value="" class="u1351_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1352" class="ax_default label">
        <div id="u1352_div" class=""></div>
        <div id="u1352_text" class="text ">
          <p><span>*SKU类型</span></p>
        </div>
      </div>

      <!-- Unnamed (下拉列表) -->
      <div id="u1353" class="ax_default droplist">
        <div id="u1353_div" class=""></div>
        <select id="u1353_input" class="u1353_input">
          <option class="u1353_input_option" value="一级类目 二级类目">一级类目 二级类目</option>
          <option class="u1353_input_option" value="列表项1">列表项1</option>
          <option class="u1353_input_option" value="列表项2">列表项2</option>
          <option class="u1353_input_option" value="列表项3">列表项3</option>
          <option class="u1353_input_option" value="列表项4">列表项4</option>
        </select>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1354" class="ax_default label">
        <div id="u1354_div" class=""></div>
        <div id="u1354_text" class="text ">
          <p><span>*需求数量</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u1355" class="ax_default text_field">
        <div id="u1355_div" class=""></div>
        <input id="u1355_input" type="text" value="" class="u1355_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1356" class="ax_default sticky_1">
        <div id="u1356_div" class=""></div>
        <div id="u1356_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u1357" class="ax_default label">
        <div id="u1357_div" class=""></div>
        <div id="u1357_text" class="text ">
          <p><span>核心字段说明：<br>&nbsp; &nbsp; 1、【保存】按钮：成功后生成订单同时同步到仓储WMS，生成发运需求单；</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
