<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yi.mapper.InboundOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yi.entity.InboundOrder">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="status" property="status" />
        <result column="inbound_type" property="inboundType" />
        <result column="inbound_warehouse_id" property="inboundWarehouseId" />
        <result column="inbound_warehouse_name" property="inboundWarehouseName" />
        <result column="delivery_method" property="deliveryMethod" />
        <result column="vehicle_number" property="vehicleNumber" />
        <result column="driver_name" property="driverName" />
        <result column="driver_phone" property="driverPhone" />
        <result column="sender_warehouse_id" property="senderWarehouseId" />
        <result column="sender_warehouse_name" property="senderWarehouseName" />
        <result column="sender_address" property="senderAddress" />
        <result column="first_category" property="firstCategory" />
        <result column="second_category" property="secondCategory" />
        <result column="planned_quantity" property="plannedQuantity" />
        <result column="actual_quantity" property="actualQuantity" />
        <result column="inbound_time" property="inboundTime" />
        <result column="outbound_order_id" property="outboundOrderId" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="last_modified_by" property="lastModifiedBy" />
        <result column="last_modified_time" property="lastModifiedTime" />
        <result column="valid" property="valid" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_no, status, inbound_type, inbound_warehouse_id, inbound_warehouse_name,
        delivery_method, vehicle_number, driver_name, driver_phone, sender_warehouse_id,
        sender_warehouse_name, sender_address, first_category, second_category,
        planned_quantity, actual_quantity, inbound_time, outbound_order_id,
        created_by, created_time, last_modified_by, last_modified_time, valid, remark
    </sql>

    <!-- 分页查询入库单列表 -->
    <select id="selectInboundOrderPage" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM t_inbound_order
        WHERE valid = 1
        <if test="orderNo != null and orderNo != ''">
            AND order_no LIKE CONCAT('%', #{orderNo}, '%')
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="inboundType != null">
            AND inbound_type = #{inboundType}
        </if>
        <if test="inboundWarehouseId != null">
            AND inbound_warehouse_id = #{inboundWarehouseId}
        </if>
        <if test="senderWarehouseId != null">
            AND sender_warehouse_id = #{senderWarehouseId}
        </if>
        <if test="firstCategory != null">
            AND first_category = #{firstCategory}
        </if>
        <if test="startTime != null">
            AND created_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND created_time &lt;= #{endTime}
        </if>
        ORDER BY created_time DESC
    </select>

    <!-- 根据入库单号查询入库单 -->
    <select id="selectByOrderNo" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM t_inbound_order
        WHERE order_no = #{orderNo} AND valid = 1
    </select>

    <!-- 根据状态查询入库单列表 -->
    <select id="selectByStatus" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM t_inbound_order
        WHERE status = #{status} AND valid = 1
        ORDER BY created_time DESC
    </select>

    <!-- 根据入库仓库ID查询入库单列表 -->
    <select id="selectByInboundWarehouseId" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM t_inbound_order
        WHERE inbound_warehouse_id = #{inboundWarehouseId} AND valid = 1
        ORDER BY created_time DESC
    </select>

    <!-- 根据发货仓库ID查询入库单列表 -->
    <select id="selectBySenderWarehouseId" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM t_inbound_order
        WHERE sender_warehouse_id = #{senderWarehouseId} AND valid = 1
        ORDER BY created_time DESC
    </select>

    <!-- 根据关联出库单ID查询入库单 -->
    <select id="selectByOutboundOrderId" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM t_inbound_order
        WHERE outbound_order_id = #{outboundOrderId} AND valid = 1
    </select>

    <!-- 统计各状态的入库单数量 -->
    <select id="selectStatusStatistics" resultType="java.util.Map">
        SELECT 
            status,
            COUNT(*) as count
        FROM t_inbound_order
        WHERE valid = 1
        GROUP BY status
    </select>

    <!-- 统计各类型的入库单数量 -->
    <select id="selectTypeStatistics" resultType="java.util.Map">
        SELECT 
            inbound_type,
            COUNT(*) as count
        FROM t_inbound_order
        WHERE valid = 1
        GROUP BY inbound_type
    </select>

    <!-- 查询指定时间范围内的入库单 -->
    <select id="selectByTimeRange" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM t_inbound_order
        WHERE valid = 1
        <if test="startTime != null">
            AND created_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND created_time &lt;= #{endTime}
        </if>
        ORDER BY created_time DESC
    </select>

    <!-- 查询待入库的订单 -->
    <select id="selectPendingOrders" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM t_inbound_order
        WHERE status = 1 AND valid = 1
        ORDER BY created_time ASC
    </select>

    <!-- 查询部分入库的订单 -->
    <select id="selectPartialOrders" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM t_inbound_order
        WHERE status = 2 AND valid = 1
        ORDER BY inbound_time ASC
    </select>

    <!-- 更新入库单状态 -->
    <update id="updateStatus">
        UPDATE t_inbound_order 
        SET status = #{status},
            <if test="actualQuantity != null">
                actual_quantity = #{actualQuantity},
            </if>
            <if test="inboundTime != null">
                inbound_time = #{inboundTime},
            </if>
            last_modified_by = #{lastModifiedBy},
            last_modified_time = NOW()
        WHERE id = #{id} AND valid = 1
    </update>

</mapper>
