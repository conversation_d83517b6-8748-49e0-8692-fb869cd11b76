package com.yi.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yi.controller.customerdownstreamaddress.model.*;
import com.yi.entity.TCustomerDownstreamAddress;
import com.yi.mapper.TCustomerDownstreamAddressMapper;
import com.yi.mapper.vo.CustomerDownstreamAddressPageVO;
import com.yi.utils.ExcelUtils;
import com.yi.utils.FormatUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 客户下游地址关联表 服务实现类
 */
@Service
public class TCustomerDownstreamAddressService extends ServiceImpl<TCustomerDownstreamAddressMapper, TCustomerDownstreamAddress> {

    /**
     * 新增客户下游地址关联
     *
     * @param request 关联信息
     * @return 是否成功
     */
    public boolean addCustomerDownstreamAddress(CustomerDownstreamAddressRequest request) {
        // 校验是否已存在相同的关联关系
        if (isRelationExists(request.getCompanyId(), request.getDownstreamCustomerCompanyId(), request.getWarehouseId(), null)) {
            throw new RuntimeException("该公司与下游客户仓库的关联关系已存在，不能重复");
        }
        
        TCustomerDownstreamAddress address = new TCustomerDownstreamAddress();
        
        // 手动转换字段类型
        address.setCompanyId(FormatUtils.safeToLong(request.getCompanyId()));
        address.setDownstreamCustomerCompanyId(FormatUtils.safeToLong(request.getDownstreamCustomerCompanyId()));
        address.setWarehouseId(FormatUtils.safeToLong(request.getWarehouseId()));

        // 设置默认值
        address.setEnabled(1); // 默认启用状态
        address.setValid(1);
        address.setCreatedTime(LocalDateTime.now());
        address.setLastModifiedTime(LocalDateTime.now());
        
        return this.save(address);
    }



    /**
     * 启用/禁用客户下游地址关联
     *
     * @param id 主键ID
     * @param enabled 启用状态
     * @return 是否成功
     */
    public boolean updateCustomerDownstreamAddressStatus(Long id, Integer enabled) {
        TCustomerDownstreamAddress address = new TCustomerDownstreamAddress();
        address.setId(id);
        address.setEnabled(enabled);
        address.setLastModifiedTime(LocalDateTime.now());

        return this.updateById(address);
    }

    /**
     * 软删除客户下游地址关联
     *
     * @param id 主键ID
     * @return 是否成功
     */
    public boolean deleteCustomerDownstreamAddress(Long id) {
        TCustomerDownstreamAddress address = new TCustomerDownstreamAddress();
        address.setId(id);
        address.setValid(0);
        address.setLastModifiedTime(LocalDateTime.now());

        return this.updateById(address);
    }

    /**
     * 校验关联关系是否存在
     *
     * @param companyId 公司ID
     * @param downstreamCustomerCompanyId 下游客户ID
     * @param warehouseId 仓库ID
     * @param excludeId 排除的ID（更新时使用，新增时传null）
     * @return 是否存在
     */
    private boolean isRelationExists(String companyId, String downstreamCustomerCompanyId, String warehouseId, Long excludeId) {
        // 转换参数
        Long companyIdLong = FormatUtils.safeToLong(companyId);
        Long downstreamCustomerCompanyIdLong = FormatUtils.safeToLong(downstreamCustomerCompanyId);
        Long warehouseIdLong = FormatUtils.safeToLong(warehouseId);
        
        // 如果必填字段为空，不进行校验
        if (companyIdLong == null || downstreamCustomerCompanyIdLong == null || warehouseIdLong == null) {
            return false;
        }
        
        LambdaQueryWrapper<TCustomerDownstreamAddress> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TCustomerDownstreamAddress::getValid, 1)
                .eq(TCustomerDownstreamAddress::getCompanyId, companyIdLong)
                .eq(TCustomerDownstreamAddress::getDownstreamCustomerCompanyId, downstreamCustomerCompanyIdLong)
                .eq(TCustomerDownstreamAddress::getWarehouseId, warehouseIdLong);
        
        // 更新时排除当前记录
        if (excludeId != null) {
            wrapper.ne(TCustomerDownstreamAddress::getId, excludeId);
        }
        
        return this.count(wrapper) > 0;
    }

    /**
     * 分页查询客户下游地址关联列表（返回Response格式）
     *
     * @param request 查询条件
     * @return 分页结果
     */
    public IPage<CustomerDownstreamAddressPageResponse> getCustomerDownstreamAddressPageResponse(CustomerDownstreamAddressQueryRequest request) {
        // 转换分页参数
        Integer current = FormatUtils.safeToInteger(request.getCurrent(), 1);
        Integer size = FormatUtils.safeToInteger(request.getSize(), 10);
        Page<CustomerDownstreamAddressPageVO> page = new Page<>(current, size);

        // 使用统一的SQL联查直接获取结果（分页模式）
        IPage<CustomerDownstreamAddressPageVO> originalPage = baseMapper.selectCustomerDownstreamAddressWithDetail(page, request);

        // 转换为Response对象
        List<CustomerDownstreamAddressPageResponse> responseList = originalPage.getRecords().stream()
                .map(this::convertVOToPageResponse)
                .collect(Collectors.toList());

        // 构建返回的分页对象
        Page<CustomerDownstreamAddressPageResponse> responsePage = new Page<>(originalPage.getCurrent(), originalPage.getSize(), originalPage.getTotal());
        responsePage.setRecords(responseList);

        return responsePage;
    }

    /**
     * 根据ID获取客户下游地址关联详情（返回Response格式）
     *
     * @param id 主键ID
     * @return 关联详情Response
     */
    public CustomerDownstreamAddressDetailResponse getCustomerDownstreamAddressDetailById(Long id) {
        // 使用联查获取完整的关联详情信息
        CustomerDownstreamAddressPageVO detailVO = baseMapper.selectCustomerDownstreamAddressDetailById(id);
        if (detailVO == null) {
            return null;
        }
        return convertVOToDetailResponse(detailVO);
    }

    /**
     * 导出客户下游地址关联列表
     *
     * @param request 查询条件
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    public void exportCustomerDownstreamAddressList(CustomerDownstreamAddressQueryRequest request, HttpServletResponse response) throws IOException {
        // 使用统一的SQL联查直接获取所有符合条件的数据（不分页模式）
        List<CustomerDownstreamAddressPageVO> dataList = baseMapper.selectCustomerDownstreamAddressWithDetail(request);

        // 转换为导出VO
        List<CustomerDownstreamAddressExportVO> exportList = dataList.stream()
                .map(this::convertVOToExportVO)
                .collect(Collectors.toList());

        // 使用EasyExcel导出（文件名已包含时间戳）
        ExcelUtils.exportExcelWithTimestamp(response, "客户下游地址关联列表", "客户下游地址关联列表",
                CustomerDownstreamAddressExportVO.class, exportList);
    }

    /**
     * 转换VO为分页Response对象
     *
     * @param vo 客户下游地址关联分页查询VO
     * @return 分页Response对象
     */
    private CustomerDownstreamAddressPageResponse convertVOToPageResponse(CustomerDownstreamAddressPageVO vo) {
        CustomerDownstreamAddressPageResponse response = new CustomerDownstreamAddressPageResponse();

        // 基础字段转换
        response.setId(FormatUtils.safeToString(vo.getRelationId()));
        response.setDownstreamCustomerName(FormatUtils.safeString(vo.getDownstreamCompanyName()));
        response.setWarehouseName(FormatUtils.safeString(vo.getWarehouseName()));
        response.setLastModifiedBy(FormatUtils.safeString(vo.getLastModifiedBy()));

        // 状态转换为中文
        response.setStatus(FormatUtils.getEnabledStatusDescription(vo.getEnabled()));

        // 地址拼接
        response.setDetailedAddress(FormatUtils.buildFullAddress(
                vo.getProvinceName(),
                vo.getCityName(),
                vo.getAreaName(),
                vo.getDetailedAddress()
        ));

        // 最近编辑时间格式化
        response.setLastModifiedTime(FormatUtils.formatDateTime(vo.getLastModifiedTime()));

        return response;
    }

    /**
     * 转换VO为导出VO对象
     *
     * @param vo 客户下游地址关联分页查询VO
     * @return 导出VO对象
     */
    private CustomerDownstreamAddressExportVO convertVOToExportVO(CustomerDownstreamAddressPageVO vo) {
        CustomerDownstreamAddressExportVO exportVO = new CustomerDownstreamAddressExportVO();

        // 基础字段转换
        exportVO.setDownstreamCustomerName(FormatUtils.safeString(vo.getDownstreamCompanyName()));
        exportVO.setWarehouseName(FormatUtils.safeString(vo.getWarehouseName()));
        exportVO.setLastModifiedBy(FormatUtils.safeString(vo.getLastModifiedBy()));
        exportVO.setRemark(FormatUtils.safeString(vo.getRemark()));

        // 状态转换为中文
        exportVO.setStatus(FormatUtils.getEnabledStatusDescription(vo.getEnabled()));

        // 地址拼接
        exportVO.setDetailedAddress(FormatUtils.buildFullAddress(
                vo.getProvinceName(),
                vo.getCityName(),
                vo.getAreaName(),
                vo.getDetailedAddress()
        ));

        // 最近编辑时间格式化
        exportVO.setLastModifiedTime(FormatUtils.formatDateTime(vo.getLastModifiedTime()));

        return exportVO;
    }

    /**
     * 转换VO为详情Response对象
     *
     * @param vo 客户下游地址关联分页查询VO
     * @return 详情Response对象
     */
    private CustomerDownstreamAddressDetailResponse convertVOToDetailResponse(CustomerDownstreamAddressPageVO vo) {
        CustomerDownstreamAddressDetailResponse response = new CustomerDownstreamAddressDetailResponse();

        // 基础字段转换
        response.setId(FormatUtils.safeToString(vo.getRelationId()));
        response.setDownstreamCompanyName(FormatUtils.safeString(vo.getDownstreamCompanyName()));
        response.setWarehouseName(FormatUtils.safeString(vo.getWarehouseName()));
        response.setContactPerson(FormatUtils.safeString(vo.getContactPerson()));
        response.setMobilePhone(FormatUtils.safeString(vo.getMobilePhone()));
        response.setLandlinePhone(FormatUtils.safeString(vo.getLandlinePhone()));
        response.setRemark(FormatUtils.safeString(vo.getRemark()));

        // 仓库地址拼接
        response.setWarehouseAddress(FormatUtils.buildFullAddress(
                vo.getProvinceName(),
                vo.getCityName(),
                vo.getAreaName(),
                vo.getDetailedAddress()
        ));

        return response;
    }
}
