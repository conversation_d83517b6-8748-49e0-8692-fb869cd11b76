﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-30px;
  width:1300px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u4784_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1301px;
  height:2px;
}
#u4784 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:37px;
  width:1300px;
  height:1px;
  display:flex;
}
#u4784 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4784_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4785_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4785 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:7px;
  width:150px;
  height:30px;
  display:flex;
}
#u4785 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4785_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4786_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4786 {
  border-width:0px;
  position:absolute;
  left:1274px;
  top:18px;
  width:56px;
  height:19px;
  display:flex;
}
#u4786 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4786_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4787_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1300px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4787 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:40px;
  width:1300px;
  height:50px;
  display:flex;
}
#u4787 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4787_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4788_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u4788 {
  border-width:0px;
  position:absolute;
  left:68px;
  top:55px;
  width:72px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u4788 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4788_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4789 {
  border-width:0px;
  position:absolute;
  left:68px;
  top:105px;
  width:1232px;
  height:153px;
}
#u4790_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:308px;
  height:30px;
}
#u4790 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:308px;
  height:30px;
  display:flex;
}
#u4790 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4790_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4791_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:308px;
  height:30px;
}
#u4791 {
  border-width:0px;
  position:absolute;
  left:308px;
  top:0px;
  width:308px;
  height:30px;
  display:flex;
}
#u4791 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4791_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4792_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:308px;
  height:30px;
}
#u4792 {
  border-width:0px;
  position:absolute;
  left:616px;
  top:0px;
  width:308px;
  height:30px;
  display:flex;
}
#u4792 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4792_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4793_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:308px;
  height:30px;
}
#u4793 {
  border-width:0px;
  position:absolute;
  left:924px;
  top:0px;
  width:308px;
  height:30px;
  display:flex;
}
#u4793 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4793_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4794_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:308px;
  height:33px;
}
#u4794 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:30px;
  width:308px;
  height:33px;
  display:flex;
}
#u4794 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4794_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4795_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:308px;
  height:33px;
}
#u4795 {
  border-width:0px;
  position:absolute;
  left:308px;
  top:30px;
  width:308px;
  height:33px;
  display:flex;
}
#u4795 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4795_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4796_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:308px;
  height:33px;
}
#u4796 {
  border-width:0px;
  position:absolute;
  left:616px;
  top:30px;
  width:308px;
  height:33px;
  display:flex;
}
#u4796 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4796_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4797_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:308px;
  height:33px;
}
#u4797 {
  border-width:0px;
  position:absolute;
  left:924px;
  top:30px;
  width:308px;
  height:33px;
  display:flex;
}
#u4797 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4797_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4798_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:308px;
  height:30px;
}
#u4798 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:63px;
  width:308px;
  height:30px;
  display:flex;
}
#u4798 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4798_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4799_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:308px;
  height:30px;
}
#u4799 {
  border-width:0px;
  position:absolute;
  left:308px;
  top:63px;
  width:308px;
  height:30px;
  display:flex;
}
#u4799 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4799_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4800_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:308px;
  height:30px;
}
#u4800 {
  border-width:0px;
  position:absolute;
  left:616px;
  top:63px;
  width:308px;
  height:30px;
  display:flex;
}
#u4800 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4800_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4801_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:308px;
  height:30px;
}
#u4801 {
  border-width:0px;
  position:absolute;
  left:924px;
  top:63px;
  width:308px;
  height:30px;
  display:flex;
}
#u4801 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4801_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4802_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:308px;
  height:30px;
}
#u4802 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:93px;
  width:308px;
  height:30px;
  display:flex;
}
#u4802 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4802_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4803_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:308px;
  height:30px;
}
#u4803 {
  border-width:0px;
  position:absolute;
  left:308px;
  top:93px;
  width:308px;
  height:30px;
  display:flex;
}
#u4803 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4803_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4804_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:308px;
  height:30px;
}
#u4804 {
  border-width:0px;
  position:absolute;
  left:616px;
  top:93px;
  width:308px;
  height:30px;
  display:flex;
}
#u4804 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4804_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4805_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:308px;
  height:30px;
}
#u4805 {
  border-width:0px;
  position:absolute;
  left:924px;
  top:93px;
  width:308px;
  height:30px;
  display:flex;
}
#u4805 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4805_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4806_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:308px;
  height:30px;
}
#u4806 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:123px;
  width:308px;
  height:30px;
  display:flex;
}
#u4806 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4806_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4807_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:308px;
  height:30px;
}
#u4807 {
  border-width:0px;
  position:absolute;
  left:308px;
  top:123px;
  width:308px;
  height:30px;
  display:flex;
}
#u4807 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4807_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4808_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:308px;
  height:30px;
}
#u4808 {
  border-width:0px;
  position:absolute;
  left:616px;
  top:123px;
  width:308px;
  height:30px;
  display:flex;
}
#u4808 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4808_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4809_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:308px;
  height:30px;
}
#u4809 {
  border-width:0px;
  position:absolute;
  left:924px;
  top:123px;
  width:308px;
  height:30px;
  display:flex;
}
#u4809 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4809_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4810_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4810 {
  border-width:0px;
  position:absolute;
  left:95px;
  top:302px;
  width:186px;
  height:19px;
  display:flex;
}
#u4810 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4810_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4811_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4811 {
  border-width:0px;
  position:absolute;
  left:720px;
  top:301px;
  width:56px;
  height:16px;
  display:flex;
}
#u4811 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4811_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4812_input {
  position:absolute;
  left:0px;
  top:0px;
  width:194px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4812_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:194px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4812_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:194px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4812 {
  border-width:0px;
  position:absolute;
  left:792px;
  top:297px;
  width:194px;
  height:24px;
  display:flex;
}
#u4812 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4812_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:194px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4812.disabled {
}
#u4813_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4813 {
  border-width:0px;
  position:absolute;
  left:544px;
  top:830px;
  width:140px;
  height:40px;
  display:flex;
}
#u4813 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4813_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4814_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4814 {
  border-width:0px;
  position:absolute;
  left:361px;
  top:305px;
  width:99px;
  height:16px;
  display:flex;
}
#u4814 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4814_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4815_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4815 {
  border-width:0px;
  position:absolute;
  left:540px;
  top:305px;
  width:86px;
  height:16px;
  display:flex;
}
#u4815 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4815_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4816_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4816 {
  border-width:0px;
  position:absolute;
  left:95px;
  top:356px;
  width:186px;
  height:19px;
  display:flex;
}
#u4816 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4816_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4817_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4817 {
  border-width:0px;
  position:absolute;
  left:720px;
  top:355px;
  width:56px;
  height:16px;
  display:flex;
}
#u4817 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4817_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4818_input {
  position:absolute;
  left:0px;
  top:0px;
  width:194px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4818_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:194px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4818_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:194px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4818 {
  border-width:0px;
  position:absolute;
  left:792px;
  top:351px;
  width:194px;
  height:24px;
  display:flex;
}
#u4818 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4818_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:194px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4818.disabled {
}
#u4819_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4819 {
  border-width:0px;
  position:absolute;
  left:361px;
  top:359px;
  width:99px;
  height:16px;
  display:flex;
}
#u4819 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4819_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4820_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4820 {
  border-width:0px;
  position:absolute;
  left:540px;
  top:359px;
  width:86px;
  height:16px;
  display:flex;
}
#u4820 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4820_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4821_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4821 {
  border-width:0px;
  position:absolute;
  left:95px;
  top:410px;
  width:186px;
  height:19px;
  display:flex;
}
#u4821 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4821_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4822_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4822 {
  border-width:0px;
  position:absolute;
  left:720px;
  top:409px;
  width:56px;
  height:16px;
  display:flex;
}
#u4822 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4822_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4823_input {
  position:absolute;
  left:0px;
  top:0px;
  width:194px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4823_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:194px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4823_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:194px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4823 {
  border-width:0px;
  position:absolute;
  left:792px;
  top:405px;
  width:194px;
  height:24px;
  display:flex;
}
#u4823 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4823_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:194px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4823.disabled {
}
#u4824_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4824 {
  border-width:0px;
  position:absolute;
  left:361px;
  top:413px;
  width:100px;
  height:16px;
  display:flex;
}
#u4824 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4824_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4825_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4825 {
  border-width:0px;
  position:absolute;
  left:540px;
  top:413px;
  width:86px;
  height:16px;
  display:flex;
}
#u4825 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4825_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4826_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u4826 {
  border-width:0px;
  position:absolute;
  left:95px;
  top:464px;
  width:90px;
  height:16px;
  display:flex;
  color:#000000;
}
#u4826 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4826_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4827_input {
  position:absolute;
  left:0px;
  top:0px;
  width:431px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4827_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:431px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4827_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:431px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4827 {
  border-width:0px;
  position:absolute;
  left:195px;
  top:459px;
  width:431px;
  height:26px;
  display:flex;
}
#u4827 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4827_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:431px;
  height:26px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4827.disabled {
}
#u4828_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u4828 {
  border-width:0px;
  position:absolute;
  left:123px;
  top:514px;
  width:62px;
  height:16px;
  display:flex;
  color:#000000;
}
#u4828 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4828_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4829_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:170px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:30px;
}
#u4829 {
  border-width:0px;
  position:absolute;
  left:195px;
  top:515px;
  width:300px;
  height:170px;
  display:flex;
  font-size:30px;
}
#u4829 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4829_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4830_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u4830 {
  border-width:0px;
  position:absolute;
  left:157px;
  top:715px;
  width:28px;
  height:16px;
  display:flex;
  color:#000000;
}
#u4830 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4830_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4831_input {
  position:absolute;
  left:0px;
  top:0px;
  width:431px;
  height:77px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4831_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:431px;
  height:77px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4831_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:431px;
  height:77px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4831 {
  border-width:0px;
  position:absolute;
  left:195px;
  top:715px;
  width:431px;
  height:77px;
  display:flex;
}
#u4831 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4831_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:431px;
  height:77px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4831.disabled {
}
