package com.yi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 发运订单表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_shipping_order")
public class TShippingOrder extends BaseEntity {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 合同编号
     */
    private String contractCode;

    /**
     * 客户公司ID
     */
    private Long customerCompanyId;

    /**
     * 收货仓库ID
     */
    private Long warehouseId;

    /**
     * 一级类目 1:共享托盘
     */
    private Integer firstCategory;

    /**
     * 二级类目
     */
    private String secondCategory;

    /**
     * 需求数量
     */
    private Integer count;

    /**
     * 发货数量
     */
    private Integer shippedQuantity;

    /**
     * 签收数量
     */
    private Integer receivedQuantity;

    /**
     * 需求时间
     */
    private LocalDate demandTime;

    /**
     * 订单状态：1000-待发货，2000-发货中，3000-已完结，4000-已取消
     */
    private Integer status;

    /**
     * 取消原因
     */
    private String cancelReason;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 最后修改人
     */
    private String lastModifiedBy;

    /**
     * 最后修改时间
     */
    private LocalDateTime lastModifiedTime;

    /**
     * 有效性：1-有效，0-无效
     */
    private Integer valid;
}
