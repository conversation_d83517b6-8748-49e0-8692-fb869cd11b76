package com.yi.controller.sku.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * SKU请求
 */
@Data
@ApiModel(value = "SkuRequest", description = "SKU请求")
public class SkuRequest {

    @ApiModelProperty(value = "主键ID")
    private String id;

    @NotBlank(message = "一级类目不能为空")
    @ApiModelProperty(value = "一级类目：1-循环托盘", required = true)
    private String firstCategory;

    @ApiModelProperty(value = "二级类目（与一级、三级类目组合必须唯一）")
    private String secondCategory;

    @ApiModelProperty(value = "三级类目（与一级、二级类目组合必须唯一）")
    private String thirdCategory;

    @NotBlank(message = "长度不能为空")
    @ApiModelProperty(value = "长度(mm)", required = true)
    private String length;

    @NotBlank(message = "宽度不能为空")
    @ApiModelProperty(value = "宽度(mm)", required = true)
    private String width;

    @NotBlank(message = "高度不能为空")
    @ApiModelProperty(value = "高度(mm)", required = true)
    private String height;

    @ApiModelProperty(value = "重量(Kg)")
    private String weight;

    @ApiModelProperty(value = "备注")
    private String remark;
}
