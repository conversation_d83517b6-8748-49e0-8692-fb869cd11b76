# 发运订单详情接口重构说明

## 概述

根据您的建议，重新设计了发运订单详情接口的实现方案。**不修改TShippingOrder实体类**，而是通过关联查询TCustomerWarehouse表获取收货人和地址信息，保持实体类与数据库表的一一对应关系。

## 🎯 **设计原则**

### **1. 实体类不变原则**
- ✅ **TShippingOrder实体类保持不变** - 与数据库表一一对应
- ✅ **通过关联查询获取扩展信息** - 收货人和地址信息从TCustomerWarehouse表获取
- ✅ **代码生成友好** - 不影响后续的代码生成工具

### **2. 数据来源设计**
```
发运订单基本信息 ← TShippingOrder表
收货人和地址信息 ← TCustomerWarehouse表（通过warehouseId关联）
附件信息 ← TGeneralFile表（通过订单ID关联）
```

## 📋 **字段映射关系**

### **TShippingOrder → ShippingOrderDetailResponse**
```java
// 基本信息
response.setId(order.getId())
response.setOrderNo(order.getOrderNo())
response.setContractCode(order.getContractCode())
response.setCustomerCompanyId(order.getCustomerCompanyId())
response.setWarehouseId(order.getWarehouseId())

// 产品信息
response.setFirstCategory(order.getFirstCategory())
response.setSecondCategory(order.getSecondCategory())
response.setCount(order.getCount())
response.setDemandTime(order.getDemandTime())
response.setRemark(order.getRemark())
```

### **TCustomerWarehouse → ShippingOrderDetailResponse**
```java
// 收货人信息
response.setReceiverName(warehouse.getContactPerson())
response.setReceiverPhone(warehouse.getMobilePhone())

// 地址信息
response.setProvince(warehouse.getProvinceName())
response.setCity(warehouse.getCityName())
response.setDistrict(warehouse.getAreaName())
response.setDetailAddress(warehouse.getDetailedAddress())
response.setFullAddress(buildFullAddress(warehouse))

// 仓库信息
response.setWarehouseName(warehouse.getWarehouseName())
```

### **TGeneralFile → ShippingOrderDetailResponse**
```java
// 附件信息
response.setAttachmentUrls(files.stream()
    .map(file -> file.getFilePath())
    .collect(Collectors.toList()))
```

## 🔧 **核心实现**

### **1. 服务层修改**
**文件**: `yi-management/src/main/java/com/yi/service/TShippingOrderService.java`

**新增依赖注入**:
```java
@Autowired
private TCustomerWarehouseService customerWarehouseService;
```

**convertToDetailResponse方法**:
```java
private ShippingOrderDetailResponse convertToDetailResponse(TShippingOrder order) {
    ShippingOrderDetailResponse response = new ShippingOrderDetailResponse();

    // 基本信息映射
    response.setId(FormatUtils.safeToString(order.getId()));
    response.setOrderNo(FormatUtils.safeString(order.getOrderNo()));
    // ... 其他基本字段

    // 通过关联查询获取收货人和地址信息
    populateWarehouseInfo(response, order.getWarehouseId());
    
    // 查询附件信息
    response.setAttachmentUrls(getOrderAttachments(order.getId()));

    return response;
}
```

**populateWarehouseInfo方法**:
```java
private void populateWarehouseInfo(ShippingOrderDetailResponse response, Long warehouseId) {
    if (warehouseId == null) {
        return;
    }
    
    try {
        TCustomerWarehouse warehouse = customerWarehouseService.getById(warehouseId);
        if (warehouse != null) {
            // 收货人信息
            response.setReceiverName(FormatUtils.safeString(warehouse.getContactPerson()));
            response.setReceiverPhone(FormatUtils.safeString(warehouse.getMobilePhone()));
            
            // 地址信息
            response.setProvince(FormatUtils.safeString(warehouse.getProvinceName()));
            response.setCity(FormatUtils.safeString(warehouse.getCityName()));
            response.setDistrict(FormatUtils.safeString(warehouse.getAreaName()));
            response.setDetailAddress(FormatUtils.safeString(warehouse.getDetailedAddress()));
            response.setFullAddress(buildFullAddress(warehouse));
            
            // 仓库名称
            response.setWarehouseName(FormatUtils.safeString(warehouse.getWarehouseName()));
        }
    } catch (Exception e) {
        log.warn("获取仓库信息失败，仓库ID: " + warehouseId + ", 错误: " + e.getMessage());
    }
}
```

**buildFullAddress方法**:
```java
private String buildFullAddress(TCustomerWarehouse warehouse) {
    StringBuilder fullAddress = new StringBuilder();
    
    if (warehouse.getProvinceName() != null && !warehouse.getProvinceName().trim().isEmpty()) {
        fullAddress.append(warehouse.getProvinceName());
    }
    if (warehouse.getCityName() != null && !warehouse.getCityName().trim().isEmpty()) {
        fullAddress.append(warehouse.getCityName());
    }
    if (warehouse.getAreaName() != null && !warehouse.getAreaName().trim().isEmpty()) {
        fullAddress.append(warehouse.getAreaName());
    }
    if (warehouse.getDetailedAddress() != null && !warehouse.getDetailedAddress().trim().isEmpty()) {
        fullAddress.append(warehouse.getDetailedAddress());
    }
    
    return fullAddress.toString();
}
```

### **2. 请求类简化**
**文件**: `yi-management/src/main/java/com/yi/controller/shippingorder/model/ShippingOrderRequest.java`

**移除不需要的字段**:
```java
// 移除了以下字段，因为这些信息从仓库表中获取
// private String receiverName;
// private String receiverPhone;
// private String province;
// private String city;
// private String district;
// private String detailAddress;
```

**保留的字段**:
```java
@ApiModelProperty(value = "收货仓库ID", required = true)
@NotBlank(message = "收货仓库不能为空")
private String warehouseId;  // 通过这个ID关联查询收货人和地址信息
```

### **3. 响应类保持不变**
**文件**: `yi-management/src/main/java/com/yi/controller/shippingorder/model/ShippingOrderDetailResponse.java`

响应类的字段保持不变，但数据来源发生了变化：
- 基本信息来自TShippingOrder
- 收货人和地址信息来自TCustomerWarehouse
- 附件信息来自TGeneralFile

## 📊 **接口响应示例**

### **请求**
```http
GET /api/shipping-order/1
```

### **响应**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": "1",
    "orderNo": "XSD202412230001",
    "customerCompanyId": "100",
    "contractCode": "CT001",
    "warehouseId": "200",
    "warehouseName": "深圳仓库",
    "receiverName": "张三",
    "receiverPhone": "13800138000",
    "province": "广东省",
    "city": "深圳市",
    "district": "南山区",
    "detailAddress": "科技园南区高新南一道999号",
    "fullAddress": "广东省深圳市南山区科技园南区高新南一道999号",
    "firstCategory": "1",
    "firstCategoryName": "共享托盘",
    "secondCategory": "标准托盘",
    "count": "100",
    "demandTime": "2024-12-31",
    "remark": "测试订单备注",
    "attachmentUrls": ["contract.pdf", "photo.jpg"],
    "status": "PENDING",
    "statusName": "待发货"
  }
}
```

## 🧪 **测试覆盖**

### **单元测试修改**
**文件**: `yi-management/src/test/java/com/yi/service/TShippingOrderDetailTest.java`

**测试策略调整**:
```java
@Mock
private TCustomerWarehouseService customerWarehouseService;

@BeforeEach
void setUp() {
    // 准备订单测试数据
    mockOrder = new TShippingOrder();
    mockOrder.setWarehouseId(200L);
    
    // 准备仓库测试数据
    mockWarehouse = new TCustomerWarehouse();
    mockWarehouse.setId(200L);
    mockWarehouse.setContactPerson("张三");
    mockWarehouse.setMobilePhone("13800138000");
    mockWarehouse.setProvinceName("广东省");
    // ... 其他字段
}

@Test
void testGetShippingOrderDetailById_Success() {
    // Mock 订单查询
    when(shippingOrderService.getById(1L)).thenReturn(mockOrder);
    
    // Mock 仓库查询
    when(customerWarehouseService.getById(200L)).thenReturn(mockWarehouse);
    
    // Mock 附件查询
    when(generalFileService.getShippingOrderAttachments(1L)).thenReturn(mockFiles);

    // 执行查询
    ShippingOrderDetailResponse response = shippingOrderService.getShippingOrderDetailById(1L);

    // 验证结果
    assertEquals("张三", response.getReceiverName());
    assertEquals("13800138000", response.getReceiverPhone());
    // ... 其他验证
    
    // 验证服务调用
    verify(customerWarehouseService, times(1)).getById(200L);
}
```

## ✅ **方案优势**

### **1. 架构清晰**
- **职责分离** - 每个实体类只负责对应的数据表
- **关联查询** - 通过服务层组装完整的业务数据
- **扩展性好** - 新增字段时不影响核心实体类

### **2. 维护性强**
- **代码生成友好** - 实体类不被手动修改，支持重新生成
- **数据一致性** - 收货人和地址信息统一从仓库表管理
- **异常处理** - 关联查询失败时有完善的异常处理

### **3. 业务合理**
- **数据规范化** - 避免数据冗余，收货信息统一管理
- **业务逻辑清晰** - 仓库信息变更时，所有相关订单自动生效
- **权限控制** - 可以基于仓库权限控制订单访问

## ⚠️ **注意事项**

### **1. 性能考虑**
- **N+1查询问题** - 批量查询时需要优化仓库信息的查询
- **缓存策略** - 可以对仓库信息添加缓存
- **索引优化** - 确保warehouseId字段有合适的索引

### **2. 数据完整性**
- **外键约束** - 建议添加warehouseId的外键约束
- **数据校验** - 确保仓库信息的完整性
- **异常处理** - 仓库不存在时的处理策略

### **3. 业务逻辑**
- **仓库变更** - 仓库信息变更时对历史订单的影响
- **权限控制** - 基于仓库权限的订单访问控制
- **数据迁移** - 如果有历史数据需要考虑迁移策略

## 🚀 **后续优化**

### **1. 性能优化**
- 实现仓库信息的批量查询
- 添加Redis缓存层
- 优化数据库查询性能

### **2. 功能扩展**
- 实现客户公司名称的关联查询
- 添加更多的业务字段关联
- 支持更复杂的数据组装逻辑

### **3. 监控告警**
- 添加关联查询的性能监控
- 设置异常情况的告警机制
- 监控数据一致性问题

通过这种设计，既满足了界面的数据需求，又保持了代码的可维护性和扩展性，同时遵循了不修改实体类的原则！🎉
