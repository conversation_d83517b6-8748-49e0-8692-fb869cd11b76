package com.yi.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yi.entity.TContractLog;
import com.yi.enums.ContractActionEnum;
import com.yi.mapper.TContractLogMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 合同操作日志表 服务类
 */
@Service
public class TContractLogService extends ServiceImpl<TContractLogMapper, TContractLog> {

    /**
     * 记录合同操作日志
     *
     * @param contractId 合同ID
     * @param action 操作动作
     * @param operator 操作人
     * @param remark 备注信息
     */
    public void recordLog(Long contractId, ContractActionEnum action, String operator, String remark) {
        TContractLog log = new TContractLog();
        log.setContractId(contractId);
        log.setAction(action.getCode());
        log.setOperator(operator);
        log.setOperationTime(LocalDateTime.now());
        log.setRemark(remark);
        log.setValid(1);
        
        this.save(log);
    }

    /**
     * 根据合同ID查询操作日志列表
     *
     * @param contractId 合同ID
     * @return 操作日志列表
     */
    public List<TContractLog> getByContractId(Long contractId) {
        LambdaQueryWrapper<TContractLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TContractLog::getContractId, contractId)
                .eq(TContractLog::getValid, 1)
                .orderByDesc(TContractLog::getOperationTime);
        return this.list(wrapper);
    }
}
