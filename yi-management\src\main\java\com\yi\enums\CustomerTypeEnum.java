package com.yi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 客户类型枚举
 */
@Getter
@AllArgsConstructor
public enum CustomerTypeEnum {

    /**
     * 合约客户
     */
    CONTRACT_CUSTOMER(1, "合约客户"),

    /**
     * 非合约客户
     */
    NON_CONTRACT_CUSTOMER(2, "非合约客户");

    /**
     * 类型键
     */
    private final Integer key;

    /**
     * 类型值
     */
    private final String value;

    /**
     * 根据键获取枚举
     *
     * @param key 键
     * @return 枚举
     */
    public static CustomerTypeEnum getByKey(Integer key) {
        if (key == null) {
            return null;
        }
        for (CustomerTypeEnum customerType : CustomerTypeEnum.values()) {
            if (customerType.getKey().equals(key)) {
                return customerType;
            }
        }
        return null;
    }

    /**
     * 根据键获取值
     *
     * @param key 键
     * @return 值
     */
    public static String getValueByKey(Integer key) {
        CustomerTypeEnum customerType = getByKey(key);
        return customerType != null ? customerType.getValue() : null;
    }

    /**
     * 判断是否为合约客户
     *
     * @param key 键
     * @return 是否为合约客户
     */
    public static boolean isContractCustomer(Integer key) {
        return CONTRACT_CUSTOMER.getKey().equals(key);
    }

    /**
     * 判断是否为非合约客户
     *
     * @param key 键
     * @return 是否为非合约客户
     */
    public static boolean isNonContractCustomer(Integer key) {
        return NON_CONTRACT_CUSTOMER.getKey().equals(key);
    }
}
