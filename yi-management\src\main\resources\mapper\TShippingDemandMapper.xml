<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yi.mapper.TShippingDemandMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yi.entity.TShippingDemand">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="status" property="status" />
        <result column="customer_company_id" property="customerCompanyId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="first_category" property="firstCategory" />
        <result column="second_category" property="secondCategory" />
        <result column="demand_quantity" property="demandQuantity" />
        <result column="pending_quantity" property="pendingQuantity" />
        <result column="shipped_quantity" property="shippedQuantity" />
        <result column="unexecuted_quantity" property="unexecutedQuantity" />
        <result column="demand_time" property="demandTime" />
        <result column="remark" property="remark" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="last_modified_by" property="lastModifiedBy" />
        <result column="last_modified_time" property="lastModifiedTime" />
        <result column="valid" property="valid" />
    </resultMap>

    <!-- 分页查询结果映射 -->
    <resultMap id="PageResultMap" type="com.yi.mapper.vo.ShippingDemandPageVO">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="status" property="status" />
        <result column="customer_company_name" property="customerCompanyName" />
        <result column="warehouse_name" property="warehouseName" />
        <result column="warehouse_address" property="receivingAddress" />
        <result column="receiver_name" property="receiverName" />
        <result column="first_category" property="firstCategory" />
        <result column="second_category" property="secondCategory" />
        <result column="demand_quantity" property="demandQuantity" />
        <result column="pending_quantity" property="pendingQuantity" />
        <result column="shipped_quantity" property="shippedQuantity" />
        <result column="unexecuted_quantity" property="unexecutedQuantity" />
        <result column="demand_time" property="demandTime" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
    </resultMap>

    <!-- 详情查询结果映射 -->
    <resultMap id="DetailResultMap" type="com.yi.mapper.vo.ShippingDemandDetailVO">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="status" property="status" />
        <result column="customer_company_id" property="customerCompanyId" />
        <result column="customer_company_name" property="customerCompanyName" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="warehouse_name" property="warehouseName" />
        <result column="warehouse_address" property="receivingAddress" />
        <result column="receiver_name" property="receiverName" />
        <result column="first_category" property="firstCategory" />
        <result column="second_category" property="secondCategory" />
        <result column="demand_quantity" property="demandQuantity" />
        <result column="pending_quantity" property="pendingQuantity" />
        <result column="shipped_quantity" property="shippedQuantity" />
        <result column="unexecuted_quantity" property="unexecutedQuantity" />
        <result column="demand_time" property="demandTime" />
        <result column="remark" property="remark" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="last_modified_by" property="lastModifiedBy" />
        <result column="last_modified_time" property="lastModifiedTime" />
        <result column="valid" property="valid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, demand_no, order_no, status, customer_company_id, warehouse_id, first_category,
        second_category, demand_quantity, pending_quantity, shipped_quantity, unexecuted_quantity,
        demand_time, remark, created_by, created_time, last_modified_by, last_modified_time, valid
    </sql>

    <!-- 分页查询发货需求列表 -->
    <select id="selectShippingDemandPage" resultMap="PageResultMap">
        SELECT
            sd.id,
            sd.order_no,
            sd.status,
            cc.company_name as customer_company_name,
            w.warehouse_name,
            CONCAT(w.province_name, ' ', w.city_name, ' ', w.area_name, ' ', w.detailed_address) as warehouse_address,
            w.contact_person as receiver_name,
            sd.first_category,
            sd.second_category,
            sd.demand_quantity,
            sd.pending_quantity,
            sd.shipped_quantity,
            sd.unexecuted_quantity,
            sd.demand_time,
            sd.created_by,
            sd.created_time
        FROM t_shipping_demand sd
        LEFT JOIN t_customer_company cc ON sd.customer_company_id = cc.id AND cc.valid = 1
        LEFT JOIN t_customer_warehouse w ON sd.warehouse_id = w.id AND w.valid = 1
        WHERE sd.valid = 1
        <if test="request.status != null and request.status != ''">
            AND sd.status = #{request.status}
        </if>
        <if test="request.orderNo != null and request.orderNo != ''">
            AND sd.order_no LIKE CONCAT('%', #{request.orderNo}, '%')
        </if>
        <if test="request.customerCompanyName != null and request.customerCompanyName != ''">
            AND cc.company_name LIKE CONCAT('%', #{request.customerCompanyName}, '%')
        </if>
        <if test="request.warehouseName != null and request.warehouseName != ''">
            AND w.warehouse_name LIKE CONCAT('%', #{request.warehouseName}, '%')
        </if>
        ORDER BY sd.created_time DESC
    </select>

    <!-- 查询发货需求列表（不分页） -->
    <select id="selectShippingDemandList" resultMap="PageResultMap">
        SELECT
            sd.id,
            sd.order_no,
            sd.status,
            cc.company_name as customer_company_name,
            w.warehouse_name,
            CONCAT(w.province_name, ' ', w.city_name, ' ', w.area_name, ' ', w.detailed_address) as warehouse_address,
            w.contact_person as receiver_name,
            sd.first_category,
            sd.second_category,
            sd.demand_quantity,
            sd.pending_quantity,
            sd.shipped_quantity,
            sd.unexecuted_quantity,
            sd.demand_time,
            sd.created_by,
            sd.created_time
        FROM t_shipping_demand sd
        LEFT JOIN t_customer_company cc ON sd.customer_company_id = cc.id AND cc.valid = 1
        LEFT JOIN t_customer_warehouse w ON sd.warehouse_id = w.id AND w.valid = 1
        WHERE sd.valid = 1
        <if test="request.status != null and request.status != ''">
            AND sd.status = #{request.status}
        </if>
        <if test="request.orderNo != null and request.orderNo != ''">
            AND sd.order_no LIKE CONCAT('%', #{request.orderNo}, '%')
        </if>
        <if test="request.customerCompanyName != null and request.customerCompanyName != ''">
            AND cc.company_name LIKE CONCAT('%', #{request.customerCompanyName}, '%')
        </if>
        <if test="request.warehouseName != null and request.warehouseName != ''">
            AND w.warehouse_name LIKE CONCAT('%', #{request.warehouseName}, '%')
        </if>
        ORDER BY sd.created_time DESC
    </select>

    <!-- 根据ID查询发货需求详情 -->
    <select id="selectShippingDemandDetailById" resultMap="DetailResultMap">
        SELECT
            sd.id,
            sd.order_no,
            sd.status,
            sd.customer_company_id,
            cc.company_name as customer_company_name,
            sd.warehouse_id,
            w.warehouse_name,
            CONCAT(w.province_name, ' ', w.city_name, ' ', w.area_name, ' ', w.detailed_address) as warehouse_address,
            w.contact_person as receiver_name,
            sd.first_category,
            sd.second_category,
            sd.demand_quantity,
            sd.pending_quantity,
            sd.shipped_quantity,
            sd.unexecuted_quantity,
            sd.demand_time,
            sd.remark,
            sd.created_by,
            sd.created_time,
            sd.last_modified_by,
            sd.last_modified_time,
            sd.valid
        FROM t_shipping_demand sd
        LEFT JOIN t_customer_company cc ON sd.customer_company_id = cc.id AND cc.valid = 1
        LEFT JOIN t_customer_warehouse w ON sd.warehouse_id = w.id AND w.valid = 1
        WHERE sd.id = #{id} AND sd.valid = 1
    </select>



    <!-- 根据订单号查询发货需求列表 -->
    <select id="selectByOrderNo" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_shipping_demand
        WHERE order_no = #{orderNo} AND valid = 1
        ORDER BY created_time DESC
    </select>

    <!-- 根据客户公司ID查询发货需求数量 -->
    <select id="countByCustomerCompanyId" resultType="int">
        SELECT COUNT(*)
        FROM t_shipping_demand
        WHERE customer_company_id = #{customerCompanyId} AND valid = 1
    </select>

    <!-- 根据仓库ID查询发货需求数量 -->
    <select id="countByWarehouseId" resultType="int">
        SELECT COUNT(*)
        FROM t_shipping_demand
        WHERE warehouse_id = #{warehouseId} AND valid = 1
    </select>

    <!-- 根据状态查询发货需求列表 -->
    <select id="selectByStatus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_shipping_demand
        WHERE status = #{status} AND valid = 1
        ORDER BY created_time DESC
    </select>

    <!-- 更新发货需求状态 -->
    <update id="updateStatus">
        UPDATE t_shipping_demand
        SET status = #{status},
            last_modified_by = #{lastModifiedBy},
            last_modified_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 更新发货数量 -->
    <update id="updateShippedQuantity">
        UPDATE t_shipping_demand
        SET shipped_quantity = #{shippedQuantity},
            last_modified_by = #{lastModifiedBy},
            last_modified_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 更新待确认数量 -->
    <update id="updatePendingQuantity">
        UPDATE t_shipping_demand
        SET pending_quantity = #{pendingQuantity},
            last_modified_by = #{lastModifiedBy},
            last_modified_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 更新未执行数量 -->
    <update id="updateUnexecutedQuantity">
        UPDATE t_shipping_demand
        SET unexecuted_quantity = #{unexecutedQuantity},
            last_modified_by = #{lastModifiedBy},
            last_modified_time = NOW()
        WHERE id = #{id}
    </update>

</mapper>
