package com.yi.controller.warehouse.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 出库单响应
 */
@Data
@ApiModel(value = "OutboundOrderResponse", description = "出库单响应")
public class OutboundOrderResponse {

    @ApiModelProperty(value = "出库单ID")
    private Long id;

    @ApiModelProperty(value = "出库单号")
    private String orderNo;

    @ApiModelProperty(value = "出库状态：1-待出库，2-运输中，3-已出库")
    private Integer status;

    @ApiModelProperty(value = "出库状态描述")
    private String statusDesc;

    @ApiModelProperty(value = "出库类型：1-销售出库，2-调拨出库")
    private Integer outboundType;

    @ApiModelProperty(value = "出库类型描述")
    private String outboundTypeDesc;

    @ApiModelProperty(value = "出库公司ID")
    private Long outboundCompanyId;

    @ApiModelProperty(value = "出库公司名称")
    private String outboundCompanyName;

    @ApiModelProperty(value = "出库地址")
    private String outboundAddress;

    @ApiModelProperty(value = "配送方式")
    private String deliveryMethod;

    @ApiModelProperty(value = "车牌号")
    private String vehicleNumber;

    @ApiModelProperty(value = "司机姓名")
    private String driverName;

    @ApiModelProperty(value = "司机联系方式")
    private String driverPhone;

    @ApiModelProperty(value = "收货公司ID")
    private Long receiveCompanyId;

    @ApiModelProperty(value = "收货公司名称")
    private String receiveCompanyName;

    @ApiModelProperty(value = "收货地址")
    private String receiveAddress;

    @ApiModelProperty(value = "一级类目：1-共享托盘")
    private Integer firstCategory;

    @ApiModelProperty(value = "一级类目描述")
    private String firstCategoryDesc;

    @ApiModelProperty(value = "二级类目")
    private String secondCategory;

    @ApiModelProperty(value = "计划出库数")
    private Integer plannedQuantity;

    @ApiModelProperty(value = "实际出库数")
    private Integer actualQuantity;

    @ApiModelProperty(value = "出库时间")
    private LocalDateTime outboundTime;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "最后修改人")
    private String lastModifiedBy;

    @ApiModelProperty(value = "最后修改时间")
    private LocalDateTime lastModifiedTime;

    @ApiModelProperty(value = "备注")
    private String remark;
}
