package com.yi.service;

import com.yi.entity.TShippingOrder;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 发运订单号并发安全测试
 */
@ExtendWith(MockitoExtension.class)
@SpringBootTest
public class TShippingOrderConcurrencyTest {

    @InjectMocks
    private TShippingOrderService shippingOrderService;

    @Test
    void testConcurrentOrderNoGeneration() throws InterruptedException {
        // 测试参数
        int threadCount = 10;
        int ordersPerThread = 20;
        int totalOrders = threadCount * ordersPerThread;
        
        // 结果收集
        Set<String> orderNos = Collections.synchronizedSet(new HashSet<>());
        List<Exception> exceptions = Collections.synchronizedList(new ArrayList<>());
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicInteger successCount = new AtomicInteger(0);

        // 模拟数据库查询返回递增的序号
        AtomicInteger mockSeq = new AtomicInteger(0);
        when(shippingOrderService.getOne(any())).thenAnswer(invocation -> {
            // 模拟查询到的最大订单号
            int currentSeq = mockSeq.get();
            if (currentSeq == 0) {
                return null; // 没有现有订单
            }
            
            TShippingOrder mockOrder = new TShippingOrder();
            String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            mockOrder.setOrderNo("XSD" + today + String.format("%04d", currentSeq));
            return mockOrder;
        });

        // 模拟唯一性检查
        when(shippingOrderService.count(any())).thenAnswer(invocation -> {
            // 模拟数据库唯一性检查，有小概率返回重复
            return Math.random() < 0.1 ? 1L : 0L; // 10%概率模拟重复
        });

        // 启动多线程测试
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            new Thread(() -> {
                try {
                    for (int j = 0; j < ordersPerThread; j++) {
                        try {
                            String orderNo = shippingOrderService.generateOrderNo();
                            
                            // 检查订单号格式
                            assertTrue(orderNo.matches("XSD\\d{8}\\d{4}"), 
                                     "订单号格式不正确: " + orderNo);
                            
                            // 收集结果
                            boolean isUnique = orderNos.add(orderNo);
                            if (!isUnique) {
                                exceptions.add(new RuntimeException(
                                    "线程" + threadId + "生成重复订单号: " + orderNo));
                            } else {
                                successCount.incrementAndGet();
                                mockSeq.incrementAndGet(); // 模拟序号递增
                            }
                            
                        } catch (Exception e) {
                            exceptions.add(new RuntimeException(
                                "线程" + threadId + "生成订单号失败: " + e.getMessage(), e));
                        }
                    }
                } finally {
                    latch.countDown();
                }
            }, "OrderNoGenerator-" + i).start();
        }

        // 等待所有线程完成
        boolean completed = latch.await(30, TimeUnit.SECONDS);
        assertTrue(completed, "测试超时，未能在30秒内完成");

        // 验证结果
        System.out.println("=== 并发测试结果 ===");
        System.out.println("总线程数: " + threadCount);
        System.out.println("每线程订单数: " + ordersPerThread);
        System.out.println("预期总订单数: " + totalOrders);
        System.out.println("成功生成订单数: " + successCount.get());
        System.out.println("唯一订单号数: " + orderNos.size());
        System.out.println("异常数量: " + exceptions.size());

        if (!exceptions.isEmpty()) {
            System.out.println("=== 异常详情 ===");
            exceptions.forEach(e -> System.out.println(e.getMessage()));
        }

        // 断言验证
        assertTrue(exceptions.isEmpty() || exceptions.size() < totalOrders * 0.1, 
                  "异常数量过多: " + exceptions.size());
        assertTrue(orderNos.size() >= totalOrders * 0.8, 
                  "成功生成的唯一订单号数量过少: " + orderNos.size());
    }

    @Test
    void testOrderNoUniquenessCheck() {
        // 测试订单号唯一性检查方法
        String testOrderNo = "XSD202412230001";
        
        // 模拟订单号不存在
        when(shippingOrderService.count(any())).thenReturn(0L);
        assertTrue(shippingOrderService.isOrderNoUnique(testOrderNo), 
                  "订单号应该是唯一的");
        
        // 模拟订单号已存在
        when(shippingOrderService.count(any())).thenReturn(1L);
        assertFalse(shippingOrderService.isOrderNoUnique(testOrderNo), 
                   "订单号应该不是唯一的");
    }

    @Test
    void testGenerateOrderNoRetryMechanism() {
        // 测试重试机制
        AtomicInteger attemptCount = new AtomicInteger(0);
        
        // 模拟前几次都返回重复，最后一次返回唯一
        when(shippingOrderService.count(any())).thenAnswer(invocation -> {
            int attempt = attemptCount.incrementAndGet();
            return attempt < 3 ? 1L : 0L; // 前2次重复，第3次唯一
        });
        
        when(shippingOrderService.getOne(any())).thenReturn(null);
        
        // 执行生成订单号
        String orderNo = shippingOrderService.generateOrderNo();
        
        // 验证结果
        assertNotNull(orderNo, "应该成功生成订单号");
        assertTrue(orderNo.matches("XSD\\d{8}\\d{4}"), "订单号格式应该正确");
        assertTrue(attemptCount.get() >= 3, "应该进行了重试");
    }

    @Test
    void testGenerateOrderNoMaxRetriesExceeded() {
        // 测试超过最大重试次数的情况
        
        // 模拟总是返回重复
        when(shippingOrderService.count(any())).thenReturn(1L);
        when(shippingOrderService.getOne(any())).thenReturn(null);
        
        // 执行生成订单号，应该抛出异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            shippingOrderService.generateOrderNo();
        });
        
        assertTrue(exception.getMessage().contains("已达到最大重试次数"), 
                  "异常信息应该包含重试次数相关内容");
    }

    @Test
    void testOrderNoFormatConsistency() {
        // 测试订单号格式一致性
        when(shippingOrderService.getOne(any())).thenReturn(null);
        when(shippingOrderService.count(any())).thenReturn(0L);
        
        Set<String> prefixes = new HashSet<>();
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String expectedPrefix = "XSD" + today;
        
        // 生成多个订单号
        for (int i = 0; i < 100; i++) {
            String orderNo = shippingOrderService.generateOrderNo();
            
            // 验证格式
            assertTrue(orderNo.startsWith(expectedPrefix), 
                      "订单号应该以" + expectedPrefix + "开头");
            assertEquals(15, orderNo.length(), 
                        "订单号长度应该是15位");
            
            // 提取前缀
            String prefix = orderNo.substring(0, 11);
            prefixes.add(prefix);
        }
        
        // 验证所有订单号都有相同的前缀（同一天）
        assertEquals(1, prefixes.size(), "同一天的订单号应该有相同的前缀");
        assertTrue(prefixes.contains(expectedPrefix), "前缀应该正确");
    }

    @Test
    void testSequenceIncrement() {
        // 测试序号递增逻辑
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        
        // 模拟序号递增
        AtomicInteger currentSeq = new AtomicInteger(0);
        when(shippingOrderService.getOne(any())).thenAnswer(invocation -> {
            int seq = currentSeq.get();
            if (seq == 0) {
                return null;
            }
            
            TShippingOrder mockOrder = new TShippingOrder();
            mockOrder.setOrderNo("XSD" + today + String.format("%04d", seq));
            return mockOrder;
        });
        
        when(shippingOrderService.count(any())).thenReturn(0L);
        
        List<String> orderNos = new ArrayList<>();
        
        // 生成多个订单号
        for (int i = 1; i <= 5; i++) {
            String orderNo = shippingOrderService.generateOrderNo();
            orderNos.add(orderNo);
            currentSeq.set(i); // 更新当前序号
            
            // 验证序号正确
            String expectedOrderNo = "XSD" + today + String.format("%04d", i);
            assertEquals(expectedOrderNo, orderNo, "订单号序号应该正确递增");
        }
        
        // 验证所有订单号都不同
        Set<String> uniqueOrderNos = new HashSet<>(orderNos);
        assertEquals(orderNos.size(), uniqueOrderNos.size(), "所有订单号应该唯一");
    }
}
