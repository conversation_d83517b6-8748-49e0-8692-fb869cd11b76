package com.yi.controller.user.io;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class UserResponseModel {
    @ApiModelProperty("员工id")
    private Long userId;
    @ApiModelProperty("用户编码")
    private String userCode;
    @ApiModelProperty("员工名称")
    private String userName;
    @ApiModelProperty("员工账号")
    private String userAccount;
    @ApiModelProperty("手机号码")
    private String  mobilePhone;
    @ApiModelProperty("禁启用状态")
    private Integer enabled;
    @ApiModelProperty("角色")
    private List<UserRoleResponseModel> roleInfo;
}
