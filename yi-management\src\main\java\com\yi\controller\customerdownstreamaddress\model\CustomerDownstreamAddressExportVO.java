package com.yi.controller.customerdownstreamaddress.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

/**
 * 客户下游地址关联导出VO
 */
@Data
@HeadRowHeight(20)
@ContentRowHeight(18)
public class CustomerDownstreamAddressExportVO {

    @ExcelProperty(value = "关联状态")
    @ColumnWidth(10)
    private String status;

    @ExcelProperty(value = "下游客户")
    @ColumnWidth(25)
    private String downstreamCustomerName;

    @ExcelProperty(value = "仓库名称")
    @ColumnWidth(20)
    private String warehouseName;

    @ExcelProperty(value = "详细地址")
    @ColumnWidth(40)
    private String detailedAddress;

    @ExcelProperty(value = "备注")
    @ColumnWidth(30)
    private String remark;

    @ExcelProperty(value = "最近编辑人")
    @ColumnWidth(12)
    private String lastModifiedBy;

    @ExcelProperty(value = "最近编辑时间")
    @ColumnWidth(20)
    private String lastModifiedTime;
}
