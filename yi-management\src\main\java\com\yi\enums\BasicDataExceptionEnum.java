package com.yi.enums;


import com.yi.configuration.exception.BaseExceptionEnum;

public enum BasicDataExceptionEnum implements BaseExceptionEnum {

    SMS_VERIFICATION_CODE_TEMPLATE_NOT_EXIST(10001, "验证码短信模板不存在"),
    SMS_SEND_SWITCH(10002, "短信发送开关已关闭"),
    SMS_ACCESS_KEY_ID(10003, "短信ACCESSKETID不存在"),
    SMS_ACCESS_KEY_SECRET(10004, "短信ACCESSSECRET不存在"),
    SMS_SIGN_NAME(10005, "短信signname不存在"),
    ALIYUN_SMS_SEND_EXCEPTION(10005, "发送短消息异常"),
    ALIYUN_SMS_ADD_CLIENT_ENDPOINT_EXCEPTION(10006, "发送短消息添加节点异常"),
    ALIYUN_SMS_SEND_LIMIT(10007, "当天获取验证码次数到达上限"),

    MISSING_JPUSH_KEY(10007, "缺少极光推送开关"),

    VERIFY_CODE_USER(10011, "验证码已使用"),
    VERIFY_CODE_ERROR_OR_FAILURE(10012, "验证码错误或已失效"),
    SEND_SMS_THREE_IN_TEN(10013, "十分钟内最多获取三次验证码，请勿频繁获取"),
    CHECK_VERIFY_CODE_ERROR(10014, "请获取验证码"),
    VERIFY_CODE_ERROR_2(10014, "验证码已失效,请您重新获取"),

    MOBILE_FORMAT_ERROR(10014, "手机号格式不正确"),
    MOBILE_NOT_EXIST(10015, "该手机号不存在"),
    MOBILE_NOT_PERMISSION(10016, "该手机号无登录权限"),
    USER_MOBILE_EXIST(10017, "该手机号已存在"),

    USER_INFO_ERROR(10018, "查询用户信息失败"),
    USER_INFO_NOT_EXIST(10019, "员工信息不存在"),
    USER_ORG_ONE_ONLY(10020, "每个用户只能有一个部门"),
    USER_ROLE_ONE_ONLY(10021, "每个用户只能有一个角色"),
    USER_ACCOUNT_EXIST(10022, "该账号已存在"),
    USER_PASSWORD_INCONFORMITY(10023, "两次密码不一致"),
    USER_OLD_PASSWORD_ERROR(10024, "请输入正确原密码"),

    ROLE_INFO_NOT_EXIST(10025, "角色信息不存在"),
    ROLE_INFO_EXIST_RELATE_USER(10026, "角色存在关联用户，不能进行该操作！"),
    ROLE_CANT_BE_DISABLED_OR_NULL(10027, "角色被禁用或不存在"),
    ROLE_INFO_EXIST(10028, "该角色已存在"),

    PARENT_ORGANIZATION_NOT_EXIST(10029, "父级部门不存在"),
    ORGANIZATION_INFO_NOT_EXIST(10030, "部门信息不存在"),
    ORGANIZATION_EXIST_RELATE_INFO(10031, "该部门下存在员工，无法删除"),
    ORGANIZATION_NAME_EXIST(10032, "部门名称不能相同"),
    ORGANIZATION_CHILD_MAX(10033, "最多只能添加至第四层级"),

    PERMISSION_CODE_EXIST(10034, "菜单编号已存在"),
    PERMISSION_EXIST_CHILD_PERMISSION(10035, "当前有下级菜单，请删除下级菜单后再进行删除"),
    PERMISSION_NAME_EXIST(10036, "同级菜单下子菜单名称不能相同"),

    CUSTOMER_ACCOUNT_NOT_EXIST(10037, "账号不存在"),
    CUSTOMER_COMPANY_NAME_NOT_EXIST(10038, "公司不存在"),
    CUSTOMER_COMPANY_NOT_SUBMIT_BASIC_INFO(10039, "您的账号还未提交基本信息，请先完善基础信息"),
    BUSINESS_CODE_NOT_EXIST(10040, "配置不存在"),
    CUSTOMER_ACCOUNT_DISABLED(10041, "该账号已被禁用"),
    MOBILE_NOT_AUTHORIZED(10042, "该手机号没有被授予权限，请联系客服"),
    REQUEST_PARAMS_ERROR(10044, "请求参数错误"),
    ACQUIRE_VERIFICATION_FREQUENTLY(10046, "距上次获取验证码不足一分钟，请一分钟后重新获取"),
    LACK_CONFIGURATION(10047, "缺少配置"),

    FILE_NOT_EXIST(10048, "复制文件失败：源文件不存在"),
    UPLOAD_ERROR(100408, "上传失败"),
    TELEPHONE_IS_NOT_TRUE(10049, "座机格式不正确"),
    FAX_IS_NOT_TRUE(10050, "传真格式不正确"),

    CUSTOMER_COMPANY_NOT_EXIST(10051, "该客户不存在"),
    CUSTOMER_COMPANY_EXIST(10052, "该客户已存在"),
    CUSTOMER_COMPANY_NAME_EXIST(10053, "公司已存在"),
    CUSTOMER_COMPANY_DISABLED_NOT_ALLOW_SUBMIT(10054, "该企业被禁用，请联系平台运营人员"),


    PASSWORD_UPDATE_FAIL(10065, "密码更新失败"),
    RECEIVER_ADDRESS_EMPTY(10066, "未搜索到正确的地址"),
    COMMON_IO_EXCEPTION(10067, "IO异常"),
    OLD_PASSWORD_NOT_TRUE(10068, "旧密码不正确，请重新输入"),
    OLE_PASSWORD_NOT_EQUALS_NEW_PASSWORD(10069, "新旧密码一致，请重新输入"),
    ACCOUNT_RELATION_NOT_EXIST(10070, "账号关系不存在"),

    BANK_ACCOUNT_HAS_EXISTS_UNDER_COMPANY(10071, "已有银行卡号信息，请编辑原有银行卡号信息"),
    BANK_ACCOUNT_NOT_EXISTS(10072, "开户银行信息不存在"),

    USER_CODE_EXIST(10080, "该用户编码已存在"),




    FILE_TYPE_NOT_SUPPORT(10081, "该文件类型不允许上传"),
    EMAIL_FORMAT_ERROR(10083, "邮箱格式不正确"),
    USER_EMAIL_EXIST(10084, "该邮箱已存在"),
    USER_EMAIL_NOT_EXIST(10085, "该邮箱不存在"),
    EMAIL_SEND_SWITCH(10086, "短信发送开关已关闭"),
    EMAIL_TEMPLATE_ENUM_NOT_EXISTS(10087, "邮箱模板不存在"),
    PLEASE_INPUT_MOBILE_OR_EMAIL(10088, "请输入手机号/邮箱"),
    MOBILE_PHONE_NOT_EXIST(10089, "手机号码不存在,请添加手机号码"),
    USER_ACCOUNT_NOT_EXIST(10090, "账号与手机不匹配"),

    USER_PUBLIC_KEY_PRIVATE_KEY_ERROR(10091, "公钥私钥已经生成无需重复生成"),

    USER_REPEAT_ATTESTATION_ERROR(10092, "请勿重复操作启用/禁用验签"),
    OSS_STS_ERROR(10093, "oss获取sts配置异常"),
    USER_NO_PERMISSION(10094, "该用户无权限"),
    CRM_ID_EXIST(10098, "CRMID已存在"),


    ORGANIZATION_CHARACTER_ERROR(10101, "部门名称必须包含中文或英文，请检查修改后重试"),





    ENCRYPTION_FAILURE(70014, "加密失败"),
    DECRYPTION_FAILURE(70015, "解密失败"),
    USER_NAME_PWD_ERROR(4010, "账号或者密码错误"),
    LOGIN_ERROR_COUNT_MAX(70002, "您的账号已被锁定，{0}分钟后自动解锁，或者联系后台管理员修改密码"),
    USER_LOGIN_SYSTEM_ERROR(70009, "登录错误,请稍后在试"),
    LOGIN_ERROR_MSG(70003, "您的账号或密码错误，{0}次输入错误后账号将被锁定，10分钟后自动解锁，或者联系后台管理员修改密码"),
    USER_MOBILE_ERROR(70010, "[请维护手机号]"),
    USER_PASSWORD_ERROR(70011, "[密码只能填写8~16位包含数字、字母（含大小写）的字符]"),
    USER_ACCOUNT_ERROR(70012, "[请维护账号]"),
    USER_MOBILE_SIZE_ERROR(70013, "手机号长度为11位"),

    QR_CODE_LABEL_INFO_NOT_EXIST(80001, "二维码标签信息不存在"),
    CONFIG_FILED_NAME_IS_EXIST(80002, "字段名称已存在"),

    DYNAMICS_CONFIG_NOT_EXIST(80003, "基本信息配置不存在"),

    DEFAULT_NAME_CAN_NOT_UPDATE(80004, "默认字段不能更改"),
    FIELD_TYPE_CAN_NOT_UPDATE(80005, "字段类型不允许修改"),
    UNIT_NOT_EXIST(80006, "单位不存在"),
    UNIT_EXIST(80007, "单位已存在"),

    WARNING_LEVEL_NOT_EXIST(80008, "预警等级不存在"),
    WARNING_LEVEL_EXIST(80009, "预警等级已存在"),
    SETTLEMENT_RULE_NOT_EXIST(80010, "结算规则不存在"),
    SETTLEMENT_RULE_EXIST(80011, "结算规则已存在"),

    FACTORY_NOT_EXIST(80012, "工厂不存在"),
    FACTORY_EXIST(80013, "工厂已存在"),

    SCENE_EXIST(80014, "适用场景已存在"),
    CONFIG_FILED_MORE_THAN_50(80015, "字段总数量上限50个"),

    CONFIG_FILE_IS_SEARCH_MORE_THAN_20(80016, "每个列表的查询项上限20个，当前已达上限，可先变更其他字段的查询项为【否】"),



    CUSTOMER_NOT_EXIST(80014, "客户不存在"),
    CUSTOMER_ADDRESS_NOT_EXIST(80015, "客户地址不存在"),
    CUSTOMER_CONFIG_VALUE_NOT_EXIST(80016, "客户配置值不存在"),
    CUSTOMER_CONTACTS_NOT_EXIST(80017, "客户联系人不存在"),




    SUPPLIER_NOT_EXIST(80018, "供应商不存在"),
    SUPPLIER_ADDRESS_NOT_EXIST(80019, "供应商地址不存在"),
    SUPPLIER_CONFIG_VALUE_NOT_EXIST(80020, "供应商配置值不存在"),
    SUPPLIER_CONTACTS_NOT_EXIST(80021, "供应商联系人不存在"),
    ON_ACTION_PLEASE_WAIT(80022, "操作执行中,请稍后再试!"),

    CODE_REPEAT(80023, "编码重复"),
    CONTACTS_EXIST(80024, "该联系人已存在"),
    ADDRESS_EXIST(80024, "该地址已存在"),

    FACTORY_CONTACTS_MORE_THAN_5(80025, "联系人数量不能超过5个"),
    FACTORY_CONTACTS_IS_EXIST(80026, "该联系人已存在"),
    FACTORY_ADDRESS_MORE_THAN_10(80027, "地址数量不能超过10个"),
    FACTORY_ADDRESS_IS_EXIST(80026, "该地址已存在"),
    FACTORY_CONTACTS_IS_NOT_EXIST(80028, "该联系人不存在"),


    QR_CODE_LABEL_NAME_EXIST(80029, "二维码标签名称已存在"),
    SETTLEMENT_RULE_CUSTOMIZE_PARTNER_EXIST(80030, "存在绑定的结算规则，无法删除"),

    OTHER_BEACH_ON_WEIGHTING_PLEASE_WAIT(80031, "其他批次称重中，请稍后再试"),
    END_WEIGHTING_FAIL_PLEASE_TRY_AGAIN(80032, "结束称重失败，请重试"),
    INBOUND_ORDER_NOT_EXIST(80035, "入库单不存在"),
    INBOUND_ORDER_DETAIL_NOT_EXIST(80033, "入库单货物明细不存在"),
    INBOUND_ORDER_OPERATE_LOG_NOT_EXIST(80034, "入库单操作记录不存在"),
    INBOUND_ORDER_STATE_NOT_MATCH(80036, "入库单状态不匹配"),
    WEIGHING_ORDER_NOT_EXIST(80037, "磅单不存在"),
    WEIGHING_ORDER_DETAIL_NOT_EXIST(80038, "磅单明细不存在"),

    SPECIFICATION_NOT_EXIST(80039, "sku规格不存在"),
    MATERIAL_NOT_EXIST(80040, "物料不存在"),
    PURCHASE_ORDER_NOT_EXIST(80041, "采购单不存在"),
    PURCHASE_ORDER_SKU_NOT_EXIST(80042, "采购单sku不存在"),
    SKU_NOT_EXIST(80043, "sku不存在"),
    NOT_FINISH_WEIGHING(80044, "未完成称重"),
    WEIGHING_STATE_NOT_MATCH(80044, "称重状态不匹配"),
    INBOUND_ORDER_HAS_SAMPLING(80045, "入库单已抽样"),
    INBOUND_ORDER_SAMPLING_NOT_EXIST(80046, "入库单抽样用例不存在"),
    WEIGHING_TYPE_NOT_MATCH(80047, "过磅单类型不匹配"),
    STOCK_IN_NULL_PLEASE_RETRY(80048, "暂无货物入库，请确认后再试"),



    PURCHASE_BATCH_NOT_EXIST(90001, "采购批次不存在"),

    FACTORY_ADDRESS_NOT_EXIST(90002, "工厂地址不存在"),

    LICENSE_PLATE_NOT_HAS_ORDER(90003, "车牌号当天不存在相应订单"),

    LICENSE_PLATE_NOT_HAS_BATCH(90004, "车牌号当天不存在相应批次"),

    MULTIPLE_SKU_CANNOT_OPEN(90005, "混装榜单不可以打开"),
    CONFIG_EXIST_CUSTOMER(90006, "结算规则已关联客户或结算账单时，不允许删除"),








    ;

    @Override
    public int getCode() {
        return key;
    }

    @Override
    public String getMessage() {
        return value;
    }

    private final Integer key;
    private final String value;

    BasicDataExceptionEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
