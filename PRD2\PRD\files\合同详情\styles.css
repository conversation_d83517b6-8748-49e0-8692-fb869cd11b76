﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-30px;
  width:1300px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u1055_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1301px;
  height:2px;
}
#u1055 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:37px;
  width:1300px;
  height:1px;
  display:flex;
}
#u1055 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1055_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1056_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:216px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1056 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:7px;
  width:216px;
  height:30px;
  display:flex;
}
#u1056 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1056_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1057_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1057 {
  border-width:0px;
  position:absolute;
  left:1274px;
  top:18px;
  width:56px;
  height:19px;
  display:flex;
}
#u1057 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1057_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1058_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1058 {
  border-width:0px;
  position:absolute;
  left:71px;
  top:114px;
  width:62px;
  height:16px;
  display:flex;
}
#u1058 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1058_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1059_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1059 {
  border-width:0px;
  position:absolute;
  left:71px;
  top:159px;
  width:62px;
  height:16px;
  display:flex;
}
#u1059 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1059_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1060_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1060 {
  border-width:0px;
  position:absolute;
  left:77px;
  top:246px;
  width:56px;
  height:16px;
  display:flex;
}
#u1060 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1060_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1061_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1007px;
  height:84px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1061 {
  border-width:0px;
  position:absolute;
  left:138px;
  top:246px;
  width:1007px;
  height:84px;
  display:flex;
}
#u1061 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1061_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1062_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1300px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1062 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:40px;
  width:1300px;
  height:50px;
  display:flex;
}
#u1062 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1062_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1063_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1063 {
  border-width:0px;
  position:absolute;
  left:57px;
  top:55px;
  width:72px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1063 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1063_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1064_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1064 {
  border-width:0px;
  position:absolute;
  left:488px;
  top:114px;
  width:62px;
  height:16px;
  display:flex;
}
#u1064 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1064_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1065_input {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1065_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1065_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1065 {
  border-width:0px;
  position:absolute;
  left:138px;
  top:155px;
  width:200px;
  height:24px;
  display:flex;
}
#u1065 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1065_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1065.disabled {
}
#u1066_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1066 {
  border-width:0px;
  position:absolute;
  left:460px;
  top:159px;
  width:90px;
  height:16px;
  display:flex;
}
#u1066 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1066_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1067_input {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1067_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1067_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1067 {
  border-width:0px;
  position:absolute;
  left:555px;
  top:155px;
  width:200px;
  height:24px;
  display:flex;
}
#u1067 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1067_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1067.disabled {
}
#u1068_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1068 {
  border-width:0px;
  position:absolute;
  left:852px;
  top:159px;
  width:90px;
  height:16px;
  display:flex;
}
#u1068 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1068_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1069_input {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1069_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1069_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1069 {
  border-width:0px;
  position:absolute;
  left:947px;
  top:155px;
  width:200px;
  height:24px;
  display:flex;
}
#u1069 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1069_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1069.disabled {
}
#u1070_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1070 {
  border-width:0px;
  position:absolute;
  left:488px;
  top:203px;
  width:62px;
  height:16px;
  display:flex;
}
#u1070 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1070_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1071_input {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1071_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1071_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1071 {
  border-width:0px;
  position:absolute;
  left:555px;
  top:199px;
  width:200px;
  height:24px;
  display:flex;
}
#u1071 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1071_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1071.disabled {
}
.u1071_input_option {
}
#u1072_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1300px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1072 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:555px;
  width:1300px;
  height:50px;
  display:flex;
}
#u1072 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1072_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1073_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1073 {
  border-width:0px;
  position:absolute;
  left:57px;
  top:570px;
  width:72px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1073 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1073_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1074_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:180px;
}
#u1074 {
  border-width:0px;
  position:absolute;
  left:57px;
  top:621px;
  width:150px;
  height:180px;
  display:flex;
}
#u1074 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1074_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1075_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:180px;
}
#u1075 {
  border-width:0px;
  position:absolute;
  left:217px;
  top:621px;
  width:150px;
  height:180px;
  display:flex;
}
#u1075 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1075_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1076_input {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1076_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1076_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1076 {
  border-width:0px;
  position:absolute;
  left:138px;
  top:110px;
  width:200px;
  height:24px;
  display:flex;
}
#u1076 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1076_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1076.disabled {
}
.u1076_input_option {
}
#u1077_input {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1077_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1077_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1077 {
  border-width:0px;
  position:absolute;
  left:555px;
  top:110px;
  width:200px;
  height:24px;
  display:flex;
}
#u1077 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1077_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1077.disabled {
}
.u1077_input_option {
}
#u1078_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1078 {
  border-width:0px;
  position:absolute;
  left:71px;
  top:203px;
  width:62px;
  height:16px;
  display:flex;
}
#u1078 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1078_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1079_input {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1079_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1079_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1079 {
  border-width:0px;
  position:absolute;
  left:138px;
  top:199px;
  width:200px;
  height:24px;
  display:flex;
}
#u1079 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1079_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1079.disabled {
}
.u1079_input_option {
}
#u1080_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:32px;
}
#u1080 {
  border-width:0px;
  position:absolute;
  left:149px;
  top:639px;
  width:32px;
  height:32px;
  display:flex;
}
#u1080 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1080_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1081_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:32px;
}
#u1081 {
  border-width:0px;
  position:absolute;
  left:319px;
  top:639px;
  width:32px;
  height:32px;
  display:flex;
}
#u1081 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1081_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1082_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1300px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1082 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:821px;
  width:1300px;
  height:50px;
  display:flex;
}
#u1082 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1082_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1083_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1083 {
  border-width:0px;
  position:absolute;
  left:57px;
  top:836px;
  width:72px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1083 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1083_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1084 {
  border-width:0px;
  position:absolute;
  left:57px;
  top:886px;
  width:1088px;
  height:90px;
}
#u1085_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:30px;
}
#u1085 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:30px;
  display:flex;
}
#u1085 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1085_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1086_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:203px;
  height:30px;
}
#u1086 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:0px;
  width:203px;
  height:30px;
  display:flex;
}
#u1086 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1086_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1087_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
}
#u1087 {
  border-width:0px;
  position:absolute;
  left:239px;
  top:0px;
  width:119px;
  height:30px;
  display:flex;
}
#u1087 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1087_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1088_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:187px;
  height:30px;
}
#u1088 {
  border-width:0px;
  position:absolute;
  left:358px;
  top:0px;
  width:187px;
  height:30px;
  display:flex;
}
#u1088 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1088_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1089_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:543px;
  height:30px;
}
#u1089 {
  border-width:0px;
  position:absolute;
  left:545px;
  top:0px;
  width:543px;
  height:30px;
  display:flex;
}
#u1089 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1089_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1090_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:30px;
}
#u1090 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:30px;
  width:36px;
  height:30px;
  display:flex;
}
#u1090 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1090_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1091_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:203px;
  height:30px;
}
#u1091 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:30px;
  width:203px;
  height:30px;
  display:flex;
}
#u1091 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1091_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1092_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
}
#u1092 {
  border-width:0px;
  position:absolute;
  left:239px;
  top:30px;
  width:119px;
  height:30px;
  display:flex;
}
#u1092 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1092_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1093_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:187px;
  height:30px;
}
#u1093 {
  border-width:0px;
  position:absolute;
  left:358px;
  top:30px;
  width:187px;
  height:30px;
  display:flex;
}
#u1093 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1093_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1094_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:543px;
  height:30px;
}
#u1094 {
  border-width:0px;
  position:absolute;
  left:545px;
  top:30px;
  width:543px;
  height:30px;
  display:flex;
}
#u1094 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1094_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1095_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:30px;
}
#u1095 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:60px;
  width:36px;
  height:30px;
  display:flex;
}
#u1095 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1095_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1096_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:203px;
  height:30px;
}
#u1096 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:60px;
  width:203px;
  height:30px;
  display:flex;
}
#u1096 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1096_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1097_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
}
#u1097 {
  border-width:0px;
  position:absolute;
  left:239px;
  top:60px;
  width:119px;
  height:30px;
  display:flex;
}
#u1097 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1097_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1098_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:187px;
  height:30px;
}
#u1098 {
  border-width:0px;
  position:absolute;
  left:358px;
  top:60px;
  width:187px;
  height:30px;
  display:flex;
}
#u1098 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1098_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1099_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:543px;
  height:30px;
}
#u1099 {
  border-width:0px;
  position:absolute;
  left:545px;
  top:60px;
  width:543px;
  height:30px;
  display:flex;
}
#u1099 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1099_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1100_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1300px;
  height:80px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  font-size:14px;
  line-height:20px;
}
#u1100 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:1022px;
  width:1300px;
  height:80px;
  display:flex;
  font-size:14px;
  line-height:20px;
}
#u1100 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u1100_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1101_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:315px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#000000;
  line-height:19px;
}
#u1101 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:1030px;
  width:315px;
  height:19px;
  display:flex;
  font-size:15px;
  color:#000000;
  line-height:19px;
}
#u1101 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1101_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1102 {
  border-width:0px;
  position:absolute;
  left:57px;
  top:409px;
  width:1090px;
  height:93px;
}
#u1103_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:34px;
}
#u1103 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:34px;
  display:flex;
}
#u1103 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1103_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1104_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:230px;
  height:34px;
}
#u1104 {
  border-width:0px;
  position:absolute;
  left:99px;
  top:0px;
  width:230px;
  height:34px;
  display:flex;
}
#u1104 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1104_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1105_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:370px;
  height:34px;
}
#u1105 {
  border-width:0px;
  position:absolute;
  left:329px;
  top:0px;
  width:370px;
  height:34px;
  display:flex;
}
#u1105 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1105_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1106_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:391px;
  height:34px;
}
#u1106 {
  border-width:0px;
  position:absolute;
  left:699px;
  top:0px;
  width:391px;
  height:34px;
  display:flex;
}
#u1106 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1106_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1107_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:29px;
}
#u1107 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:34px;
  width:99px;
  height:29px;
  display:flex;
}
#u1107 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1107_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1108_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:230px;
  height:29px;
}
#u1108 {
  border-width:0px;
  position:absolute;
  left:99px;
  top:34px;
  width:230px;
  height:29px;
  display:flex;
}
#u1108 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1108_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1109_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:370px;
  height:29px;
}
#u1109 {
  border-width:0px;
  position:absolute;
  left:329px;
  top:34px;
  width:370px;
  height:29px;
  display:flex;
}
#u1109 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1109_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1110_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:391px;
  height:29px;
}
#u1110 {
  border-width:0px;
  position:absolute;
  left:699px;
  top:34px;
  width:391px;
  height:29px;
  display:flex;
}
#u1110 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1110_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1111_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:30px;
}
#u1111 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:63px;
  width:99px;
  height:30px;
  display:flex;
}
#u1111 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1111_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1112_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:230px;
  height:30px;
}
#u1112 {
  border-width:0px;
  position:absolute;
  left:99px;
  top:63px;
  width:230px;
  height:30px;
  display:flex;
}
#u1112 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1112_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1113_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:370px;
  height:30px;
}
#u1113 {
  border-width:0px;
  position:absolute;
  left:329px;
  top:63px;
  width:370px;
  height:30px;
  display:flex;
}
#u1113 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1113_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1114_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:391px;
  height:30px;
}
#u1114 {
  border-width:0px;
  position:absolute;
  left:699px;
  top:63px;
  width:391px;
  height:30px;
  display:flex;
}
#u1114 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1114_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1115_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1300px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1115 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:342px;
  width:1300px;
  height:50px;
  display:flex;
}
#u1115 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1115_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1116_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1116 {
  border-width:0px;
  position:absolute;
  left:57px;
  top:357px;
  width:75px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1116 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1116_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1117_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1117 {
  border-width:0px;
  position:absolute;
  left:227px;
  top:448px;
  width:28px;
  height:16px;
  display:flex;
}
#u1117 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1117_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1118_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1118 {
  border-width:0px;
  position:absolute;
  left:275px;
  top:448px;
  width:28px;
  height:16px;
  display:flex;
}
#u1118 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1118_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1119_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:238px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#D9001B;
  text-align:center;
}
#u1119 {
  border-width:0px;
  position:absolute;
  left:160px;
  top:357px;
  width:238px;
  height:16px;
  display:flex;
  color:#D9001B;
  text-align:center;
}
#u1119 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1119_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1120_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1120 {
  border-width:0px;
  position:absolute;
  left:1180px;
  top:50px;
  width:120px;
  height:30px;
  display:flex;
}
#u1120 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1120_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1121 {
  border-width:0px;
  position:absolute;
  left:358px;
  top:109px;
  width:700px;
  height:400px;
  visibility:hidden;
}
#u1121_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:700px;
  height:400px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1121_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1122_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:200px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1122 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:200px;
  display:flex;
}
#u1122 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1122_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1123_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1123 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:50px;
  display:flex;
}
#u1123 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1123_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1124_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1124 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:14px;
  width:72px;
  height:22px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1124 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1124_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1125_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:13px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1125 {
  border-width:0px;
  position:absolute;
  left:463px;
  top:14px;
  width:13px;
  height:22px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1125 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1125_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1126_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:76px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1126 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:63px;
  width:76px;
  height:16px;
  display:flex;
}
#u1126 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1126_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1127_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1127 {
  border-width:0px;
  position:absolute;
  left:297px;
  top:151px;
  width:80px;
  height:30px;
  display:flex;
}
#u1127 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1127_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1128_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1128 {
  border-width:0px;
  position:absolute;
  left:396px;
  top:151px;
  width:80px;
  height:30px;
  display:flex;
}
#u1128 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1128_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1129_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:435px;
  height:59px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1129 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:84px;
  width:435px;
  height:59px;
  display:flex;
}
#u1129 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1129_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
