package com.yi.configuration.jwt;


import com.alibaba.fastjson.JSONObject;
import com.yi.configuration.exception.AuthExceptionCodeEnum;
import com.yi.configuration.exception.BizException;
import com.yi.configuration.rsa.RsaUtils;
import com.yi.constant.CommonConstant;
import com.yi.utils.StringHelper;
import io.jsonwebtoken.*;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.Date;
import java.util.List;

public class JWTHelper {

    private JWTHelper() {
    }

    private static final Logger log = LoggerFactory.getLogger(JWTHelper.class);
    private static final RsaUtils RSA_UTILS = new RsaUtils();

    /**
     * 密钥加密token
     *
     * @param jwtInfo    jwtkits 帐号信息
     * @param priKeyPath 私钥地址
     * @param expire     过期时间
     * @return
     * @throws Exception
     */
    public static TokenVo
    generateToken(IJWTInfo jwtInfo, String priKeyPath, int expire) {
        try {
            Date expiration = DateTime.now().plusSeconds(expire).toDate();
            String compactJws =
                    //返回的字符串便是我们的jwt串了
                    Jwts.builder()
                            //设置过期时间
                            .setExpiration(expiration)
                            //设置主题内容
                            .setSubject("MES系统")
                            .claim(JwtConstants.JWT_KEY_APP_ID, jwtInfo.getAppId())
                            .claim(JwtConstants.JWT_KEY_USER_ID, jwtInfo.getUserId())
                            .claim(JwtConstants.JWT_KEY_USER_NAME, jwtInfo.getUserName())
                            .claim(JwtConstants.JWT_KEY_NICK_NAME, jwtInfo.getNickName())
                            .claim(JwtConstants.JWT_KEY_USER_COMPANY, jwtInfo.getCustomerCompanyId())
                            .claim(JwtConstants.JWT_KEY_USER_CODE, jwtInfo.getUserCode())
                            .claim(JwtConstants.JWT_KEY_USER_ROLES, jwtInfo.getUserRoles())
                            .claim(JwtConstants.JWT_KEY_FACTORY_ID, jwtInfo.getFactoryId())
                            .claim(JwtConstants.JWT_KEY_SUPPLIER_ID, jwtInfo.getSupplierId())
                            //设置算法（必须）
                            .signWith(SignatureAlgorithm.RS256, RSA_UTILS.getPrivateKey(priKeyPath))
                            //这个是全部设置完成后拼成jwt串的方法
                            .compact();
            return new TokenVo(compactJws, expire, expiration);
        } catch (IOException | NoSuchAlgorithmException | InvalidKeySpecException e) {
            log.error("generateToken error message:", e);
            throw new BizException(AuthExceptionCodeEnum.JWT_GEN_TOKEN_FAIL.getCode(), AuthExceptionCodeEnum.JWT_GEN_TOKEN_FAIL.getMessage());
        }
    }

    /**
     * 公钥解析token
     *
     * @param token
     * @param pubKeyPath 公钥路径
     * @return
     * @throws Exception
     */
    public static Jws<Claims> parserToken(String token, String pubKeyPath) {
        try {
            return Jwts.parser().setSigningKey(RSA_UTILS.getPublicKey(pubKeyPath)).parseClaimsJws(token);
        } catch (ExpiredJwtException ex) {
            log.info("ExpiredJwtException:", ex);
            //过期
            throw new BizException(AuthExceptionCodeEnum.JWT_TOKEN_EXPIRED.getCode(), AuthExceptionCodeEnum.JWT_TOKEN_EXPIRED.getMessage());
        } catch (SignatureException ex) {
            log.error("SignatureException:", ex);
            //签名错误
            throw new BizException(AuthExceptionCodeEnum.JWT_SIGNATURE.getCode(), AuthExceptionCodeEnum.JWT_SIGNATURE.getMessage());
        } catch (IllegalArgumentException ex) {
            log.error("IllegalArgumentException:", ex);
            //token 为空
            throw new BizException(AuthExceptionCodeEnum.JWT_ILLEGAL_ARGUMENT.getCode(), AuthExceptionCodeEnum.JWT_ILLEGAL_ARGUMENT.getMessage());
        } catch (Exception e) {
            log.error("message:", e);
            throw new BizException(AuthExceptionCodeEnum.JWT_PARSER_TOKEN_FAIL.getCode(), AuthExceptionCodeEnum.JWT_PARSER_TOKEN_FAIL.getMessage());
        }
    }

    /**
     * 获取token中的用户信息
     * get("userId") -> userId
     * get("name") -> userName
     * getSubject() -> uniqueName
     *
     * @param token      token
     * @param pubKeyPath 公钥路径
     * @return
     * @throws Exception
     */
    public static IJWTInfo getInfoFromToken(String token, String pubKeyPath) {
        Jws<Claims> claimsJws = parserToken(token, pubKeyPath);
        long tempUserId = -1L;
        long tempCustomerCompanyId = -1L;
        Claims body = claimsJws.getBody();
        String userId = StringHelper.getObjectValue(body.get(JwtConstants.JWT_KEY_USER_ID));
        String userName = StringHelper.getObjectValue(body.get(JwtConstants.JWT_KEY_USER_NAME));
        String customerCompanyId = StringHelper.getObjectValue(body.get(JwtConstants.JWT_KEY_USER_COMPANY));
        String userCode = StringHelper.getObjectValue(body.get(JwtConstants.JWT_KEY_USER_CODE));
        String userRoleStr = StringHelper.getObjectValue(body.get(JwtConstants.JWT_KEY_USER_ROLES));
        String factoryId = StringHelper.getObjectValueAndDefaultValue(body.get(JwtConstants.JWT_KEY_FACTORY_ID),
                CommonConstant.ZERO);
        String supplierId = StringHelper.getObjectValueAndDefaultValue(body.get(JwtConstants.JWT_KEY_SUPPLIER_ID),
                CommonConstant.ZERO);
        List<Long> userRoles = JSONObject.parseArray(userRoleStr, Long.class);

        try {
            tempUserId = userId == null || userId.isEmpty() ? -1L : Long.parseLong(userId);
            tempCustomerCompanyId = customerCompanyId == null || customerCompanyId.isEmpty() ? -1L : Long.parseLong(customerCompanyId);
        } catch (Exception e) {
            log.error("getInfoFromToken error message:", e);
        }
        return new JWTInfo(Long.parseLong(supplierId), Long.parseLong(factoryId), userName,
                tempUserId,
                StringHelper.getObjectValue(body.get(JwtConstants.JWT_KEY_NICK_NAME)),
                tempCustomerCompanyId,
                StringHelper.getObjectValue(body.get(JwtConstants.JWT_KEY_APP_ID)),
                userCode,
                userRoles
        );
    }

}
