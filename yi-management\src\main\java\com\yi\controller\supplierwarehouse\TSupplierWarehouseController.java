package com.yi.controller.supplierwarehouse;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yi.common.Result;
import com.yi.controller.supplierwarehouse.model.*;
import com.yi.service.TSupplierWarehouseService;
import com.yi.service.TSupplierService;
import com.yi.entity.TSupplier;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;

/**
 * 供应商仓库表 前端控制器
 */
@RestController
@RequestMapping("/api/supplier-warehouse")
@Api(tags = "供应商仓库管理")
public class TSupplierWarehouseController {

    @Autowired
    private TSupplierWarehouseService supplierWarehouseService;

    @Autowired
    private TSupplierService supplierService;

    @ApiOperation("分页查询供应商仓库列表")
    @PostMapping("/page")
    public Result<IPage<SupplierWarehousePageResponse>> getSupplierWarehousePage(@RequestBody SupplierWarehouseQueryRequest request) {
        IPage<SupplierWarehousePageResponse> page = supplierWarehouseService.getSupplierWarehousePageResponse(request);
        return Result.success(page);
    }

    @ApiOperation("导出供应商仓库列表")
    @PostMapping("/export")
    public void exportSupplierWarehouseList(@RequestBody SupplierWarehouseQueryRequest request,
                                           HttpServletResponse response) throws IOException {
        supplierWarehouseService.exportSupplierWarehouseList(request, response);
    }

    @ApiOperation("根据ID获取供应商仓库详情")
    @GetMapping("/{id}")
    public Result<SupplierWarehouseDetailResponse> getSupplierWarehouseById(@ApiParam("供应商仓库ID") @PathVariable String id) {
        SupplierWarehouseDetailResponse detail = supplierWarehouseService.getSupplierWarehouseDetailById(Long.valueOf(id));
        if (detail == null) {
            return Result.failed("供应商仓库不存在");
        }
        return Result.success(detail);
    }

    @ApiOperation("新增供应商仓库")
    @PostMapping
    public Result<Boolean> addSupplierWarehouse(@Valid @RequestBody SupplierWarehouseSaveRequest request) {
        boolean success = supplierWarehouseService.addSupplierWarehouse(request);
        if (success) {
            return Result.success("新增成功", true);
        }
        return Result.failed("新增失败");
    }

    @ApiOperation("更新供应商仓库")
    @PutMapping
    public Result<Boolean> updateSupplierWarehouse(@Valid @RequestBody SupplierWarehouseSaveRequest request) {
        boolean success = supplierWarehouseService.updateSupplierWarehouse(request);
        if (success) {
            return Result.success("更新成功", true);
        }
        return Result.failed("更新失败");
    }

    @ApiOperation("启用/禁用供应商仓库")
    @PutMapping("/{id}/status")
    public Result<Boolean> updateSupplierWarehouseStatus(@ApiParam("供应商仓库ID") @PathVariable String id,
                                                         @ApiParam("启用状态") @RequestParam String enabled) {
        boolean success = supplierWarehouseService.updateSupplierWarehouseStatus(Long.valueOf(id), Integer.valueOf(enabled));
        if (success) {
            String message = "1".equals(enabled) ? "启用成功" : "禁用成功";
            return Result.success(message, true);
        }
        return Result.failed("状态更新失败");
    }
}
