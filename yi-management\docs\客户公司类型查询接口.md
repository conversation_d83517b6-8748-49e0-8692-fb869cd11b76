# 客户公司类型查询接口

## 接口信息

**接口地址**: `GET /api/customer-company/by-type`

**接口描述**: 根据公司类型查询客户公司列表，支持查询指定类型或所有类型的公司

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| customerCompanyType | String | 否 | 公司类型，不传则查询所有类型的公司 |

### 公司类型枚举

| 类型值 | 类型名称 |
|--------|----------|
| 1 | 委托方 |
| 2 | 承运商 |
| 3 | 托盘供应商 |
| 4 | C客户 |
| 5 | C1客户 |
| 6 | 设备供应商 |

## 返回参数

### 成功响应

```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "id": 1,
            "companyName": "北京测试公司",
            "customerCompanyType": "1",
            "registrationAddress": "北京市朝阳区建国路1号",
            "contactPerson": "张三",
            "contactPhone": "13812345678",
            "enabled": 1,
            "createdBy": "admin",
            "createdTime": "2024-01-01T10:00:00",
            "lastModifiedBy": "admin",
            "lastModifiedTime": "2024-01-01T10:00:00",
            "valid": 1
        },
        {
            "id": 2,
            "companyName": "上海测试公司",
            "customerCompanyType": "2",
            "registrationAddress": "上海市浦东新区陆家嘴1号",
            "contactPerson": "李四",
            "contactPhone": "13987654321",
            "enabled": 1,
            "createdBy": "admin",
            "createdTime": "2024-01-01T11:00:00",
            "lastModifiedBy": "admin",
            "lastModifiedTime": "2024-01-01T11:00:00",
            "valid": 1
        }
    ]
}
```

### 字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Long | 主键ID |
| companyName | String | 公司名称 |
| customerCompanyType | String | 公司类型 |
| registrationAddress | String | 注册地址 |
| contactPerson | String | 联系人 |
| contactPhone | String | 联系电话 |
| enabled | Integer | 启用状态：1-启用，0-禁用 |
| createdBy | String | 创建人 |
| createdTime | String | 创建时间 |
| lastModifiedBy | String | 最后修改人 |
| lastModifiedTime | String | 最后修改时间 |
| valid | Integer | 有效性：1-有效，0-无效 |

## 使用场景

### 1. 新增仓库下拉框
在新增仓库时，需要选择关联的客户公司，可以不传公司类型参数，获取所有启用的客户公司。

### 2. 关联下游客户仓库
在关联下游客户仓库时，通常需要查询所有类型的客户公司，不传公司类型参数即可。

### 3. 合同新增下拉框
在新增合同时，可能需要选择特定类型的客户公司，传入对应的公司类型参数。

## 使用示例

### 查询所有类型的公司

```bash
# 不传参数，查询所有类型
GET /api/customer-company/by-type

# 或者传空参数
GET /api/customer-company/by-type?customerCompanyType=
```

### 查询指定类型的公司

```bash
# 查询委托方公司
GET /api/customer-company/by-type?customerCompanyType=1

# 查询承运商公司
GET /api/customer-company/by-type?customerCompanyType=2

# 查询C客户公司
GET /api/customer-company/by-type?customerCompanyType=4

# 查询C1客户公司
GET /api/customer-company/by-type?customerCompanyType=5
```

## 数据库查询

### SQL逻辑

```sql
SELECT * 
FROM t_customer_company 
WHERE valid = 1 
  AND enabled = 1
  -- 如果传入了公司类型参数，则添加类型过滤条件
  AND (#{customerCompanyType} IS NULL OR #{customerCompanyType} = '' OR customer_company_type = #{customerCompanyType})
ORDER BY created_time DESC
```

### 查询条件说明

1. **基础条件**:
   - `valid = 1`: 只查询有效的记录
   - `enabled = 1`: 只查询启用状态的公司

2. **类型过滤**:
   - 如果`customerCompanyType`为null或空字符串，则不添加类型过滤条件
   - 如果`customerCompanyType`有值，则只查询对应类型的公司

3. **排序**:
   - 按创建时间倒序排列，最新创建的公司排在前面

## 接口变更说明

### 变更前
- 公司类型参数为必填参数
- 必须传入具体的公司类型值才能查询

### 变更后 ✅
- 公司类型参数改为可选参数（`@RequestParam(required = false)`）
- 不传参数时查询所有类型的公司
- 传入参数时查询指定类型的公司
- 向下兼容，原有调用方式仍然有效

### 兼容性
- ✅ **向下兼容**: 原有传入公司类型参数的调用方式仍然有效
- ✅ **新增功能**: 支持不传参数查询所有类型
- ✅ **参数验证**: 支持null和空字符串的处理

## 注意事项

1. **性能考虑**: 不传公司类型参数时会查询所有类型的公司，数据量可能较大，建议在前端做适当的分页或限制
2. **权限控制**: 接口会返回所有启用的公司，请确保调用方有相应的数据访问权限
3. **缓存策略**: 由于公司数据相对稳定，建议在前端或应用层做适当的缓存
4. **数据一致性**: 返回的数据只包含启用且有效的公司记录
