package com.yi.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户下游地址关联表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_customer_relate_downstream_address")
@ApiModel(value = "TCustomerDownstreamAddress对象", description = "客户下游地址关联表")
public class TCustomerDownstreamAddress extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 公司ID
     */
    @ApiModelProperty(value = "公司ID")
    private Long companyId;

    /**
     * 下游客户ID
     */
    @ApiModelProperty(value = "下游客户ID")
    private Long downstreamCustomerCompanyId;

    /**
     * 下游客户仓库ID
     */
    @ApiModelProperty(value = "下游客户仓库ID")
    private Long warehouseId;

    /**
     * 启用状态：1-启用，0-禁用
     */
    @ApiModelProperty(value = "启用状态：1-启用，0-禁用")
    private Integer enabled;
}
