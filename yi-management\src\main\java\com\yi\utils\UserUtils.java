package com.yi.utils;

import com.yi.constant.UserConstant;
import com.yi.enums.GenderEnum;
import com.yi.enums.UserStatusEnum;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import java.util.regex.Pattern;

/**
 * 用户管理工具类
 */
public class UserUtils {

    /**
     * 手机号正则表达式
     */
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");

    /**
     * 邮箱正则表达式
     */
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");

    /**
     * 用户名正则表达式（字母、数字、下划线，4-20位）
     */
    private static final Pattern USERNAME_PATTERN = Pattern.compile("^[a-zA-Z0-9_]{4,20}$");

    /**
     * 密码加密
     *
     * @param password 原始密码
     * @return 加密后的密码
     */
    public static String encryptPassword(String password) {
        if (!StringUtils.hasText(password)) {
            return "";
        }
        return DigestUtils.md5DigestAsHex((password + UserConstant.PASSWORD_SALT).getBytes());
    }

    /**
     * 验证密码
     *
     * @param rawPassword 原始密码
     * @param encodedPassword 加密后的密码
     * @return 是否匹配
     */
    public static boolean verifyPassword(String rawPassword, String encodedPassword) {
        return encryptPassword(rawPassword).equals(encodedPassword);
    }

    /**
     * 验证手机号格式
     *
     * @param phone 手机号
     * @return 是否有效
     */
    public static boolean isValidPhone(String phone) {
        return StringUtils.hasText(phone) && PHONE_PATTERN.matcher(phone).matches();
    }

    /**
     * 验证邮箱格式
     *
     * @param email 邮箱
     * @return 是否有效
     */
    public static boolean isValidEmail(String email) {
        return StringUtils.hasText(email) && EMAIL_PATTERN.matcher(email).matches();
    }

    /**
     * 验证用户名格式
     *
     * @param username 用户名
     * @return 是否有效
     */
    public static boolean isValidUsername(String username) {
        return StringUtils.hasText(username) && USERNAME_PATTERN.matcher(username).matches();
    }

    /**
     * 获取性别描述
     *
     * @param gender 性别代码
     * @return 性别描述
     */
    public static String getGenderDesc(Integer gender) {
        return GenderEnum.getDescByCode(gender);
    }

    /**
     * 获取状态描述
     *
     * @param status 状态代码
     * @return 状态描述
     */
    public static String getStatusDesc(Integer status) {
        return UserStatusEnum.getDescByCode(status);
    }

    /**
     * 生成默认密码
     *
     * @return 默认密码
     */
    public static String generateDefaultPassword() {
        return UserConstant.DEFAULT_PASSWORD;
    }

    /**
     * 脱敏手机号
     *
     * @param phone 手机号
     * @return 脱敏后的手机号
     */
    public static String maskPhone(String phone) {
        if (!StringUtils.hasText(phone) || phone.length() != 11) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }

    /**
     * 脱敏邮箱
     *
     * @param email 邮箱
     * @return 脱敏后的邮箱
     */
    public static String maskEmail(String email) {
        if (!StringUtils.hasText(email) || !email.contains("@")) {
            return email;
        }
        String[] parts = email.split("@");
        String username = parts[0];
        String domain = parts[1];
        
        if (username.length() <= 2) {
            return email;
        }
        
        String maskedUsername = username.substring(0, 1) + "***" + username.substring(username.length() - 1);
        return maskedUsername + "@" + domain;
    }
}
