package com.yi.configuration.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.beans.factory.annotation.Value;

/**
 * 鉴权配置
 */
@Getter
@Setter
@Configurable
public class AppAuthConfig {

    @Value("${auth.client.pub-key.path}")
    private String pubKeyPath;

    @Value("${auth.client.token-header}")
    private String tokenHeader;

    @Value("${auth.filter.methods:''}")
    private String methods;
}
