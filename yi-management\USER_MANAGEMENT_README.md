# 账号管理模块使用说明

## 概述

账号管理模块提供了完整的用户管理功能，包括用户的增删改查、状态管理、密码管理、角色分配、数据导出等功能。

## 功能特性

### 🔍 查询功能
- **分页查询**：支持按用户名、真实姓名、邮箱、手机号、部门、状态进行筛选
- **状态筛选**：全部、启用、禁用三种状态
- **详情查看**：查看用户完整信息，包括角色和权限

### ➕ 用户管理
- **添加用户**：创建新用户，支持基本信息录入和角色分配
- **编辑用户**：修改用户基本信息和角色
- **删除用户**：单个删除或批量删除（逻辑删除）
- **状态管理**：启用/禁用用户

### 🔐 密码管理
- **密码重置**：管理员重置用户密码
- **密码修改**：用户修改自己的密码
- **默认密码**：新用户默认密码为123456

### 📊 数据导出
- **Excel导出**：根据筛选条件导出用户列表
- **字段完整**：包含用户基本信息、部门、角色、登录信息等

### ✅ 数据验证
- **唯一性检查**：用户名、邮箱、手机号唯一性验证
- **格式验证**：邮箱、手机号格式验证
- **业务规则**：各种业务规则验证

## API接口

### 基础CRUD

```http
# 分页查询用户列表
POST /api/sys-user/page
Content-Type: application/json

{
  "current": 1,
  "size": 10,
  "username": "admin",
  "realName": "管理员",
  "status": 1
}

# 获取用户详情
GET /api/sys-user/{id}

# 新增用户
POST /api/sys-user/add
Content-Type: application/json

{
  "username": "testuser",
  "password": "123456",
  "realName": "测试用户",
  "email": "<EMAIL>",
  "phone": "13800138000",
  "gender": 1,
  "deptId": 1,
  "status": 1,
  "roleIds": [1, 2]
}

# 编辑用户
PUT /api/sys-user/edit
Content-Type: application/json

{
  "id": 1,
  "username": "testuser",
  "realName": "测试用户",
  "email": "<EMAIL>",
  "phone": "13800138000",
  "gender": 1,
  "deptId": 1,
  "status": 1,
  "roleIds": [1, 2]
}

# 删除用户
DELETE /api/sys-user/{id}

# 批量删除用户
DELETE /api/sys-user/batch
Content-Type: application/json

[1, 2, 3]
```

### 状态管理

```http
# 启用/禁用用户
PUT /api/sys-user/{id}/status?status=1

# 分配用户角色
PUT /api/sys-user/{id}/roles
Content-Type: application/json

[1, 2, 3]
```

### 密码管理

```http
# 重置密码
PUT /api/sys-user/reset-password
Content-Type: application/json

{
  "userId": 1,
  "newPassword": "123456"
}

# 修改密码
PUT /api/sys-user/change-password
Content-Type: application/json

{
  "userId": 1,
  "oldPassword": "123456",
  "newPassword": "newpassword"
}
```

### 数据验证

```http
# 检查用户名是否存在
GET /api/sys-user/check-username?username=admin&excludeId=1

# 检查邮箱是否存在
GET /api/sys-user/check-email?email=<EMAIL>&excludeId=1

# 检查手机号是否存在
GET /api/sys-user/check-phone?phone=13800138000&excludeId=1
```

### 数据导出

```http
# 导出用户列表
POST /api/sys-user/export
Content-Type: application/json

{
  "username": "admin",
  "status": 1
}
```

## 数据模型

### 用户查询请求 (UserQueryRequest)
```json
{
  "current": 1,           // 当前页码
  "size": 10,            // 每页大小
  "username": "admin",    // 用户名（模糊查询）
  "realName": "管理员",   // 真实姓名（模糊查询）
  "email": "admin@",     // 邮箱（模糊查询）
  "phone": "138",        // 手机号（模糊查询）
  "deptId": 1,           // 部门ID
  "status": 1            // 状态：null-全部，1-启用，0-禁用
}
```

### 用户请求 (UserRequest)
```json
{
  "id": 1,                    // 用户ID（编辑时必填）
  "username": "testuser",     // 用户名
  "password": "123456",       // 密码（新增时必填）
  "realName": "测试用户",     // 真实姓名
  "email": "<EMAIL>", // 邮箱
  "phone": "13800138000",     // 手机号
  "avatar": "avatar.jpg",     // 头像地址
  "gender": 1,                // 性别：0-女，1-男
  "birthday": "1990-01-01",   // 生日
  "deptId": 1,                // 部门ID
  "status": 1,                // 状态：0-禁用，1-启用
  "roleIds": [1, 2]           // 角色ID列表
}
```

### 用户分页响应 (UserPageResponse)
```json
{
  "id": 1,
  "username": "admin",
  "realName": "管理员",
  "email": "<EMAIL>",
  "phone": "13800138000",
  "genderDesc": "男",
  "deptName": "技术部",
  "statusDesc": "启用",
  "roles": [
    {
      "roleId": 1,
      "roleCode": "ADMIN",
      "roleName": "管理员"
    }
  ],
  "loginIp": "127.0.0.1",
  "loginTime": "2023-12-01 10:00:00",
  "createdBy": "system",
  "createdTime": "2023-12-01 09:00:00"
}
```

## 业务规则

### 用户名规则
- 长度：4-20位
- 字符：字母、数字、下划线
- 唯一性：系统内唯一

### 密码规则
- 默认密码：123456
- 加密方式：MD5 + 盐值
- 重置：管理员可重置任意用户密码

### 邮箱规则
- 格式：标准邮箱格式
- 唯一性：系统内唯一（可选填）

### 手机号规则
- 格式：11位中国大陆手机号
- 唯一性：系统内唯一（可选填）

### 状态管理
- 启用：用户可正常登录使用
- 禁用：用户无法登录，但数据保留

### 删除规则
- 逻辑删除：不物理删除数据
- 级联处理：删除用户时同时删除用户角色关联

## 权限控制

### 操作权限
- 查看用户列表：`system:user:list`
- 新增用户：`system:user:add`
- 编辑用户：`system:user:edit`
- 删除用户：`system:user:delete`
- 重置密码：`system:user:resetPwd`
- 导出用户：`system:user:export`

### 数据权限
- 全部数据权限：可查看所有用户
- 部门数据权限：只能查看本部门及子部门用户
- 个人数据权限：只能查看自己的信息

## 使用示例

### 前端集成示例

```javascript
// 分页查询用户列表
const getUserList = async (params) => {
  const response = await axios.post('/api/sys-user/page', params);
  return response.data;
};

// 新增用户
const addUser = async (userData) => {
  const response = await axios.post('/api/sys-user/add', userData);
  return response.data;
};

// 启用/禁用用户
const updateUserStatus = async (userId, status) => {
  const response = await axios.put(`/api/sys-user/${userId}/status?status=${status}`);
  return response.data;
};

// 导出用户列表
const exportUsers = async (params) => {
  const response = await axios.post('/api/sys-user/export', params, {
    responseType: 'blob'
  });
  
  // 创建下载链接
  const url = window.URL.createObjectURL(new Blob([response.data]));
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', '用户列表.xlsx');
  document.body.appendChild(link);
  link.click();
  link.remove();
};
```

## 注意事项

1. **密码安全**：密码使用MD5加密存储，建议生产环境使用更安全的加密方式
2. **数据验证**：前端和后端都需要进行数据格式验证
3. **权限控制**：确保用户只能操作有权限的数据
4. **日志记录**：重要操作需要记录操作日志
5. **性能优化**：大量数据查询时注意分页和索引优化

这个账号管理模块提供了完整的用户管理功能，可以根据实际需求进行扩展和定制。
