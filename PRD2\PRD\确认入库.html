﻿<!DOCTYPE html>
<html>
  <head>
    <title>确认入库</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/确认入库/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/确认入库/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (线段) -->
      <div id="u4784" class="ax_default line1">
        <img id="u4784_img" class="img " src="images/客户管理/u350.svg"/>
        <div id="u4784_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4785" class="ax_default box_1">
        <div id="u4785_div" class=""></div>
        <div id="u4785_text" class="text ">
          <p><span>确认入库&nbsp; &nbsp; X</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4786" class="ax_default link_button">
        <div id="u4786_div" class=""></div>
        <div id="u4786_text" class="text ">
          <p><span>刷新本页</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4787" class="ax_default box_21">
        <div id="u4787_div" class=""></div>
        <div id="u4787_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4788" class="ax_default heading_3">
        <div id="u4788_div" class=""></div>
        <div id="u4788_text" class="text ">
          <p><span>基本信息</span></p>
        </div>
      </div>

      <!-- Unnamed (表格) -->
      <div id="u4789" class="ax_default">

        <!-- Unnamed (单元格) -->
        <div id="u4790" class="ax_default table_cell">
          <img id="u4790_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u4790_text" class="text ">
            <p><span>状态</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u4791" class="ax_default table_cell">
          <img id="u4791_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u4791_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u4792" class="ax_default table_cell">
          <img id="u4792_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u4792_text" class="text ">
            <p><span>入库单号</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u4793" class="ax_default table_cell">
          <img id="u4793_img" class="img " src="images/确认发货/u2248.png"/>
          <div id="u4793_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u4794" class="ax_default table_cell">
          <img id="u4794_img" class="img " src="images/确认出库/u3015.png"/>
          <div id="u4794_text" class="text ">
            <p><span>配送方式</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u4795" class="ax_default table_cell">
          <img id="u4795_img" class="img " src="images/确认出库/u3015.png"/>
          <div id="u4795_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u4796" class="ax_default table_cell">
          <img id="u4796_img" class="img " src="images/确认出库/u3015.png"/>
          <div id="u4796_text" class="text ">
            <p><span>车牌号</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u4797" class="ax_default table_cell">
          <img id="u4797_img" class="img " src="images/确认出库/u3018.png"/>
          <div id="u4797_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u4798" class="ax_default table_cell">
          <img id="u4798_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u4798_text" class="text ">
            <p><span>入库仓库</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u4799" class="ax_default table_cell">
          <img id="u4799_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u4799_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u4800" class="ax_default table_cell">
          <img id="u4800_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u4800_text" class="text ">
            <p><span>仓库地址</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u4801" class="ax_default table_cell">
          <img id="u4801_img" class="img " src="images/确认发货/u2248.png"/>
          <div id="u4801_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u4802" class="ax_default table_cell">
          <img id="u4802_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u4802_text" class="text ">
            <p><span>计划入库数</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u4803" class="ax_default table_cell">
          <img id="u4803_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u4803_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u4804" class="ax_default table_cell">
          <img id="u4804_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u4804_text" class="text ">
            <p><span>实际入库数</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u4805" class="ax_default table_cell">
          <img id="u4805_img" class="img " src="images/确认发货/u2248.png"/>
          <div id="u4805_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u4806" class="ax_default table_cell">
          <img id="u4806_img" class="img " src="images/确认发货/u2265.png"/>
          <div id="u4806_text" class="text ">
            <p><span>待入库数</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u4807" class="ax_default table_cell">
          <img id="u4807_img" class="img " src="images/确认发货/u2265.png"/>
          <div id="u4807_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u4808" class="ax_default table_cell">
          <img id="u4808_img" class="img " src="images/确认发货/u2265.png"/>
          <div id="u4808_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u4809" class="ax_default table_cell">
          <img id="u4809_img" class="img " src="images/确认发货/u2268.png"/>
          <div id="u4809_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4810" class="ax_default label">
        <div id="u4810_div" class=""></div>
        <div id="u4810_text" class="text ">
          <p><span style="font-family:'Arial Normal', 'Arial', sans-serif;">*产品类型：</span><span style="font-family:'Nunito Sans', sans-serif;">循环托盘 1311-H</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4811" class="ax_default label">
        <div id="u4811_div" class=""></div>
        <div id="u4811_text" class="text ">
          <p><span>入库数量</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u4812" class="ax_default text_field">
        <div id="u4812_div" class=""></div>
        <input id="u4812_input" type="text" value="" class="u4812_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4813" class="ax_default primary_button">
        <div id="u4813_div" class=""></div>
        <div id="u4813_text" class="text ">
          <p><span>确认</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4814" class="ax_default label">
        <div id="u4814_div" class=""></div>
        <div id="u4814_text" class="text ">
          <p><span>*产品等级：A类</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4815" class="ax_default label">
        <div id="u4815_div" class=""></div>
        <div id="u4815_text" class="text ">
          <p><span>待入库数：30</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4816" class="ax_default label">
        <div id="u4816_div" class=""></div>
        <div id="u4816_text" class="text ">
          <p><span style="font-family:'Arial Normal', 'Arial', sans-serif;">*产品类型：</span><span style="font-family:'Nunito Sans', sans-serif;">循环托盘 1311-H</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4817" class="ax_default label">
        <div id="u4817_div" class=""></div>
        <div id="u4817_text" class="text ">
          <p><span>入库数量</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u4818" class="ax_default text_field">
        <div id="u4818_div" class=""></div>
        <input id="u4818_input" type="text" value="" class="u4818_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4819" class="ax_default label">
        <div id="u4819_div" class=""></div>
        <div id="u4819_text" class="text ">
          <p><span>*产品等级：B类</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4820" class="ax_default label">
        <div id="u4820_div" class=""></div>
        <div id="u4820_text" class="text ">
          <p><span>待入库数：30</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4821" class="ax_default label">
        <div id="u4821_div" class=""></div>
        <div id="u4821_text" class="text ">
          <p><span style="font-family:'Arial Normal', 'Arial', sans-serif;">*产品类型：</span><span style="font-family:'Nunito Sans', sans-serif;">循环托盘 1311-H</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4822" class="ax_default label">
        <div id="u4822_div" class=""></div>
        <div id="u4822_text" class="text ">
          <p><span>入库数量</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u4823" class="ax_default text_field">
        <div id="u4823_div" class=""></div>
        <input id="u4823_input" type="text" value="" class="u4823_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4824" class="ax_default label">
        <div id="u4824_div" class=""></div>
        <div id="u4824_text" class="text ">
          <p><span>*产品等级：C类</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4825" class="ax_default label">
        <div id="u4825_div" class=""></div>
        <div id="u4825_text" class="text ">
          <p><span>待入库数：30</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4826" class="ax_default label">
        <div id="u4826_div" class=""></div>
        <div id="u4826_text" class="text ">
          <p><span>*实际入库时间</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u4827" class="ax_default text_field">
        <div id="u4827_div" class=""></div>
        <input id="u4827_input" type="text" value="" class="u4827_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4828" class="ax_default label">
        <div id="u4828_div" class=""></div>
        <div id="u4828_text" class="text ">
          <p><span>*入库凭证</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4829" class="ax_default box_1">
        <div id="u4829_div" class=""></div>
        <div id="u4829_text" class="text ">
          <p><span>+</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4830" class="ax_default label">
        <div id="u4830_div" class=""></div>
        <div id="u4830_text" class="text ">
          <p><span>备注</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u4831" class="ax_default text_field">
        <div id="u4831_div" class=""></div>
        <input id="u4831_input" type="text" value="" class="u4831_input"/>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
