package com.yi.enums;

/**
 * 出库状态枚举
 */
public enum OutboundStatusEnum {
    
    PENDING(1, "待出库"),
    IN_TRANSIT(2, "运输中"),
    COMPLETED(3, "已出库");

    private final Integer code;
    private final String desc;

    OutboundStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据状态码获取描述
     *
     * @param code 状态码
     * @return 状态描述
     */
    public static String getDescByCode(Integer code) {
        if (code == null) {
            return "";
        }
        for (OutboundStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status.getDesc();
            }
        }
        return "";
    }

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 枚举对象
     */
    public static OutboundStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (OutboundStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 检查状态是否有效
     *
     * @param code 状态码
     * @return 是否有效
     */
    public static boolean isValid(Integer code) {
        return getByCode(code) != null;
    }
}
