package com.yi.mapper.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 供应商仓库分页查询VO
 */
@Data
public class SupplierWarehousePageVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 区县名称
     */
    private String areaName;

    /**
     * 地址详情
     */
    private String detailedAddress;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 联系方式
     */
    private String contactPhone;

    /**
     * 启用状态：1-启用，0-禁用
     */
    private Integer enabled;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 最后修改人
     */
    private String lastModifiedBy;

    /**
     * 最后修改时间
     */
    private LocalDateTime lastModifiedTime;

    /**
     * 有效性：1-有效，0-无效
     */
    private Integer valid;
}
