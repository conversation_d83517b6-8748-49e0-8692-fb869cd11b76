<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yi.mapper.TCloudWarehouseMapper">

    <!-- 分页查询云仓列表 -->
    <select id="selectCloudWarehousePage" resultType="com.yi.mapper.vo.CloudWarehousePageVO">
        SELECT 
            cw.id,
            cw.enabled,
            cw.warehouse_name AS warehouseName,
            cw.province_name AS provinceName,
            cw.city_name AS cityName,
            cw.area_name AS areaName,
            cw.detailed_address AS detailedAddress,
            cw.contact_person AS contactPerson,
            cw.contact_phone AS contactPhone,
            cw.warehouse_type AS warehouseType,
            cw.warehouse_attribute AS warehouseAttribute,
            cw.working_hours AS workingHours,
            cw.remark,
            cw.created_by AS createdBy,
            cw.created_time AS createdTime
        FROM t_cloud_warehouse cw
        WHERE cw.valid = 1
        <if test="request.enabled != null">
            AND cw.enabled = #{request.enabled}
        </if>
        <if test="request.warehouseName != null and request.warehouseName != ''">
            AND cw.warehouse_name LIKE CONCAT('%', #{request.warehouseName}, '%')
        </if>
        <if test="request.warehouseType != null">
            AND cw.warehouse_type = #{request.warehouseType}
        </if>
        <if test="request.warehouseAttribute != null">
            AND cw.warehouse_attribute = #{request.warehouseAttribute}
        </if>
        <if test="request.address != null and request.address != ''">
            AND (
                cw.province_name LIKE CONCAT('%', #{request.address}, '%')
                OR cw.city_name LIKE CONCAT('%', #{request.address}, '%')
                OR cw.area_name LIKE CONCAT('%', #{request.address}, '%')
                OR cw.detailed_address LIKE CONCAT('%', #{request.address}, '%')
            )
        </if>
        ORDER BY cw.created_time DESC
    </select>

</mapper>
