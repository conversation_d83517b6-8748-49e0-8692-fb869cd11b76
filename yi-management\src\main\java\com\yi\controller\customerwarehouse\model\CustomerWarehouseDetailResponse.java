package com.yi.controller.customerwarehouse.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 仓库详情响应
 */
@Data
@ApiModel(value = "CustomerWarehouseDetailResponse", description = "仓库详情响应")
public class CustomerWarehouseDetailResponse {

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "公司ID")
    private String companyId;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "客户类型")
    private String customerType;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "省份ID")
    private String provinceId;

    @ApiModelProperty(value = "省份名称")
    private String provinceName;

    @ApiModelProperty(value = "城市ID")
    private String cityId;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "区县ID")
    private String areaId;

    @ApiModelProperty(value = "区县名称")
    private String areaName;

    @ApiModelProperty(value = "详细地址")
    private String detailedAddress;

    @ApiModelProperty(value = "收货人")
    private String contactPerson;

    @ApiModelProperty(value = "手机号")
    private String mobilePhone;

    @ApiModelProperty(value = "座机号")
    private String landlinePhone;

    @ApiModelProperty(value = "SKU类型列表")
    private List<CustomerWarehouseSkuTypeRequest> skuTypes;

    @ApiModelProperty(value = "启用状态：1-启用，0-禁用")
    private String enabled;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private String createdTime;

    @ApiModelProperty(value = "最后修改人")
    private String lastModifiedBy;

    @ApiModelProperty(value = "最后修改时间")
    private String lastModifiedTime;

    @ApiModelProperty(value = "有效性：1-有效，0-无效")
    private String valid;

    @ApiModelProperty(value = "备注")
    private String remark;
}
