package com.yi.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yi.controller.customerwarehouse.model.CustomerWarehouseSkuTypeRequest;
import com.yi.entity.TWarehouseSku;
import com.yi.mapper.TWarehouseSkuMapper;
import com.yi.utils.FormatUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 仓库SKU关联表 服务实现类
 */
@Service
public class TWarehouseSkuService extends ServiceImpl<TWarehouseSkuMapper, TWarehouseSku> {

    /**
     * 根据仓库ID查询SKU类型列表
     *
     * @param warehouseId 仓库ID
     * @return SKU类型列表
     */
    public List<CustomerWarehouseSkuTypeRequest> getSkuTypesByWarehouseId(Long warehouseId) {
        if (warehouseId == null) {
            return List.of();
        }
        
        LambdaQueryWrapper<TWarehouseSku> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TWarehouseSku::getValid, 1)
                .eq(TWarehouseSku::getWarehouseId, warehouseId)
                .orderBy(true, true, TWarehouseSku::getFirstCategory, TWarehouseSku::getSecondCategory);
        
        List<TWarehouseSku> warehouseSkuList = this.list(wrapper);
        
        return warehouseSkuList.stream()
                .map(this::convertToSkuTypeRequest)
                .collect(Collectors.toList());
    }

    /**
     * 保存仓库SKU类型关联
     *
     * @param warehouseId 仓库ID
     * @param skuTypes SKU类型列表
     */
    public void saveWarehouseSkuTypes(Long warehouseId, List<CustomerWarehouseSkuTypeRequest> skuTypes) {
        if (warehouseId == null) {
            return;
        }
        
        // 先删除原有的关联关系
        deleteByWarehouseId(warehouseId);
        
        // 如果没有SKU类型，直接返回
        if (skuTypes == null || skuTypes.isEmpty()) {
            return;
        }
        
        // 批量插入新的关联关系
        List<TWarehouseSku> warehouseSkuList = skuTypes.stream()
                .map(skuType -> convertToWarehouseSku(warehouseId, skuType))
                .collect(Collectors.toList());
        
        this.saveBatch(warehouseSkuList);
    }

    /**
     * 根据仓库ID删除SKU类型关联（软删除）
     *
     * @param warehouseId 仓库ID
     */
    public void deleteByWarehouseId(Long warehouseId) {
        if (warehouseId == null) {
            return;
        }
        
        LambdaQueryWrapper<TWarehouseSku> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TWarehouseSku::getValid, 1)
                .eq(TWarehouseSku::getWarehouseId, warehouseId);
        
        List<TWarehouseSku> existingList = this.list(wrapper);
        if (!existingList.isEmpty()) {
            // 软删除
            existingList.forEach(item -> {
                item.setValid(0);
                item.setLastModifiedTime(LocalDateTime.now());
            });
            this.updateBatchById(existingList);
        }
    }


    /**
     * 转换为SKU类型请求对象
     *
     * @param warehouseSku 仓库SKU实体
     * @return SKU类型请求对象
     */
    private CustomerWarehouseSkuTypeRequest convertToSkuTypeRequest(TWarehouseSku warehouseSku) {
        CustomerWarehouseSkuTypeRequest request = new CustomerWarehouseSkuTypeRequest();
        request.setFirstCategory(FormatUtils.safeToString(warehouseSku.getFirstCategory()));
        request.setSecondCategory(FormatUtils.safeString(warehouseSku.getSecondCategory()));
        return request;
    }

    /**
     * 转换为仓库SKU实体
     *
     * @param warehouseId 仓库ID
     * @param skuType SKU类型请求
     * @return 仓库SKU实体
     */
    private TWarehouseSku convertToWarehouseSku(Long warehouseId, CustomerWarehouseSkuTypeRequest skuType) {
        TWarehouseSku warehouseSku = new TWarehouseSku();
        warehouseSku.setWarehouseId(warehouseId);
        warehouseSku.setFirstCategory(FormatUtils.safeToInteger(skuType.getFirstCategory()));
        warehouseSku.setSecondCategory(skuType.getSecondCategory());
        warehouseSku.setEnabled(1); // 默认启用
        warehouseSku.setValid(1);
        warehouseSku.setCreatedTime(LocalDateTime.now());
        warehouseSku.setLastModifiedTime(LocalDateTime.now());
        return warehouseSku;
    }

}
