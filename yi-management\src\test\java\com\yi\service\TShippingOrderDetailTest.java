package com.yi.service;

import com.yi.controller.shippingorder.model.ShippingOrderDetailResponse;
import com.yi.entity.TCustomerCompany;
import com.yi.entity.TCustomerWarehouse;
import com.yi.entity.TGeneralFile;
import com.yi.entity.TShippingOrder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 发运订单详情查询测试
 */
@ExtendWith(MockitoExtension.class)
@SpringBootTest
public class TShippingOrderDetailTest {

    @InjectMocks
    private TShippingOrderService shippingOrderService;

    @Mock
    private TGeneralFileService generalFileService;

    @Mock
    private TCustomerWarehouseService customerWarehouseService;

    @Mock
    private TCustomerCompanyService customerCompanyService;

    private TShippingOrder mockOrder;
    private TCustomerWarehouse mockWarehouse;
    private TCustomerCompany mockCustomerCompany;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        mockOrder = new TShippingOrder();
        mockOrder.setId(1L);
        mockOrder.setOrderNo("XSD202412230001");
        mockOrder.setContractCode("CT001");
        mockOrder.setCustomerCompanyId(100L);
        mockOrder.setWarehouseId(200L);

        // 准备仓库测试数据
        mockWarehouse = new TCustomerWarehouse();
        mockWarehouse.setId(200L);
        mockWarehouse.setWarehouseName("深圳仓库");
        mockWarehouse.setContactPerson("张三");
        mockWarehouse.setMobilePhone("13800138000");
        mockWarehouse.setProvinceName("广东省");
        mockWarehouse.setCityName("深圳市");
        mockWarehouse.setAreaName("南山区");
        mockWarehouse.setDetailedAddress("科技园南区高新南一道999号");

        // 准备客户公司测试数据
        mockCustomerCompany = new TCustomerCompany();
        mockCustomerCompany.setId(100L);
        mockCustomerCompany.setCompanyName("深圳科技有限公司");
        
        // 产品信息
        mockOrder.setFirstCategory(1);
        mockOrder.setSecondCategory("标准托盘");
        mockOrder.setCount(100);
        mockOrder.setShippedQuantity(0);
        mockOrder.setReceivedQuantity(0);
        mockOrder.setDemandTime(LocalDate.of(2024, 12, 31));
        
        // 状态信息
        mockOrder.setStatus(1000); // PENDING - 待发货
        mockOrder.setCancelReason(null);
        mockOrder.setRemark("测试订单备注");
        
        // 创建信息
        mockOrder.setCreatedBy("testUser");
        mockOrder.setCreatedTime(LocalDateTime.of(2024, 12, 23, 10, 30, 0));
        mockOrder.setLastModifiedBy("testUser");
        mockOrder.setLastModifiedTime(LocalDateTime.of(2024, 12, 23, 10, 30, 0));
        mockOrder.setValid(1);
    }

    @Test
    void testGetShippingOrderDetailById_Success() {
        // Mock getById方法
        when(shippingOrderService.getById(1L)).thenReturn(mockOrder);

        // Mock 仓库查询
        when(customerWarehouseService.getById(200L)).thenReturn(mockWarehouse);

        // Mock 客户公司查询
        when(customerCompanyService.getById(100L)).thenReturn(mockCustomerCompany);

        // Mock 附件查询
        List<TGeneralFile> mockFiles = Arrays.asList(
            createMockFile(1L, "contract.pdf"),
            createMockFile(2L, "photo.jpg")
        );
        when(generalFileService.getShippingOrderAttachments(1L)).thenReturn(mockFiles);

        // 执行查询
        ShippingOrderDetailResponse response = shippingOrderService.getShippingOrderDetailById(1L);

        // 验证结果
        assertNotNull(response);
        assertEquals("1", response.getId());
        assertEquals("XSD202412230001", response.getOrderNo());
        assertEquals("CT001", response.getContractCode());
        assertEquals("100", response.getCustomerCompanyId());
        assertEquals("深圳科技有限公司", response.getCustomerCompanyName());
        assertEquals("200", response.getWarehouseId());
        assertEquals("深圳仓库", response.getWarehouseName());

        // 验证收货人信息（从仓库中获取）
        assertEquals("张三", response.getReceiverName());
        assertEquals("13800138000", response.getReceiverPhone());

        // 验证地址信息（从仓库中获取）
        assertEquals("广东省", response.getProvince());
        assertEquals("深圳市", response.getCity());
        assertEquals("南山区", response.getDistrict());
        assertEquals("科技园南区高新南一道999号", response.getDetailAddress());
        assertEquals("广东省深圳市南山区科技园南区高新南一道999号", response.getFullAddress());

        // 验证产品信息
        assertEquals("1", response.getFirstCategory());
        assertEquals("循环托盘", response.getFirstCategoryName());
        assertEquals("标准托盘", response.getSecondCategory());
        assertEquals("100", response.getCount());
        assertEquals("0", response.getShippedQuantity());
        assertEquals("0", response.getReceivedQuantity());
        assertEquals("2024-12-31", response.getDemandTime());

        // 验证状态信息
        assertEquals("1000", response.getStatus());
        assertEquals("待发货", response.getStatusName());
        assertEquals("测试订单备注", response.getRemark());

        // 验证附件信息
        assertNotNull(response.getAttachmentUrls());
        assertEquals(2, response.getAttachmentUrls().size());
        assertTrue(response.getAttachmentUrls().contains("contract.pdf"));
        assertTrue(response.getAttachmentUrls().contains("photo.jpg"));

        // 验证创建信息
        assertEquals("testUser", response.getCreatedBy());
        assertEquals("2024-12-23 10:30:00", response.getCreatedTime());
        assertEquals("testUser", response.getLastModifiedBy());
        assertEquals("2024-12-23 10:30:00", response.getLastModifiedTime());

        // 验证服务调用
        verify(customerWarehouseService, times(1)).getById(200L);
        verify(customerCompanyService, times(1)).getById(100L);
    }

    @Test
    void testGetShippingOrderDetailById_OrderNotExists() {
        // Mock getById方法返回null
        when(shippingOrderService.getById(1L)).thenReturn(null);

        // 执行查询
        ShippingOrderDetailResponse response = shippingOrderService.getShippingOrderDetailById(1L);

        // 验证结果
        assertNull(response);
        
        // 验证没有调用附件查询
        verify(generalFileService, never()).getShippingOrderAttachments(any());
    }

    @Test
    void testGetShippingOrderDetailById_InvalidOrder() {
        // 设置订单为无效
        mockOrder.setValid(0);
        
        // Mock getById方法
        when(shippingOrderService.getById(1L)).thenReturn(mockOrder);

        // 执行查询
        ShippingOrderDetailResponse response = shippingOrderService.getShippingOrderDetailById(1L);

        // 验证结果
        assertNull(response);
        
        // 验证没有调用附件查询
        verify(generalFileService, never()).getShippingOrderAttachments(any());
    }

    @Test
    void testGetShippingOrderDetailById_NoAttachments() {
        // Mock getById方法
        when(shippingOrderService.getById(1L)).thenReturn(mockOrder);
        when(customerWarehouseService.getById(200L)).thenReturn(mockWarehouse);
        when(customerCompanyService.getById(100L)).thenReturn(mockCustomerCompany);

        // Mock 附件查询返回空列表
        when(generalFileService.getShippingOrderAttachments(1L)).thenReturn(Arrays.asList());

        // 执行查询
        ShippingOrderDetailResponse response = shippingOrderService.getShippingOrderDetailById(1L);

        // 验证结果
        assertNotNull(response);
        assertNotNull(response.getAttachmentUrls());
        assertTrue(response.getAttachmentUrls().isEmpty());
    }

    @Test
    void testGetShippingOrderDetailById_AttachmentQueryException() {
        // Mock getById方法
        when(shippingOrderService.getById(1L)).thenReturn(mockOrder);
        when(customerWarehouseService.getById(200L)).thenReturn(mockWarehouse);
        when(customerCompanyService.getById(100L)).thenReturn(mockCustomerCompany);

        // Mock 附件查询抛出异常
        when(generalFileService.getShippingOrderAttachments(1L))
            .thenThrow(new RuntimeException("附件查询失败"));

        // 执行查询
        ShippingOrderDetailResponse response = shippingOrderService.getShippingOrderDetailById(1L);

        // 验证结果
        assertNotNull(response);
        assertNotNull(response.getAttachmentUrls());
        assertTrue(response.getAttachmentUrls().isEmpty()); // 异常时返回空列表
    }

    @Test
    void testBuildFullAddress() {
        // 测试完整地址构建
        when(shippingOrderService.getById(1L)).thenReturn(mockOrder);
        when(customerWarehouseService.getById(200L)).thenReturn(mockWarehouse);
        when(customerCompanyService.getById(100L)).thenReturn(mockCustomerCompany);
        when(generalFileService.getShippingOrderAttachments(1L)).thenReturn(Arrays.asList());

        ShippingOrderDetailResponse response = shippingOrderService.getShippingOrderDetailById(1L);

        assertEquals("广东省深圳市南山区科技园南区高新南一道999号", response.getFullAddress());
    }

    @Test
    void testBuildFullAddress_PartialAddress() {
        // 测试部分地址信息
        TCustomerWarehouse partialWarehouse = new TCustomerWarehouse();
        partialWarehouse.setId(200L);
        partialWarehouse.setWarehouseName("北京仓库");
        partialWarehouse.setContactPerson("李四");
        partialWarehouse.setMobilePhone("13900139000");
        partialWarehouse.setProvinceName("北京市");
        partialWarehouse.setCityName("");
        partialWarehouse.setAreaName("朝阳区");
        partialWarehouse.setDetailedAddress("建国路88号");

        when(shippingOrderService.getById(1L)).thenReturn(mockOrder);
        when(customerWarehouseService.getById(200L)).thenReturn(partialWarehouse);
        when(customerCompanyService.getById(100L)).thenReturn(mockCustomerCompany);
        when(generalFileService.getShippingOrderAttachments(1L)).thenReturn(Arrays.asList());

        ShippingOrderDetailResponse response = shippingOrderService.getShippingOrderDetailById(1L);

        assertEquals("北京市朝阳区建国路88号", response.getFullAddress());
    }

    @Test
    void testGetFirstCategoryName() {
        when(shippingOrderService.getById(1L)).thenReturn(mockOrder);
        when(customerWarehouseService.getById(200L)).thenReturn(mockWarehouse);
        when(customerCompanyService.getById(100L)).thenReturn(mockCustomerCompany);
        when(generalFileService.getShippingOrderAttachments(1L)).thenReturn(Arrays.asList());

        ShippingOrderDetailResponse response = shippingOrderService.getShippingOrderDetailById(1L);

        assertEquals("循环托盘", response.getFirstCategoryName());
    }

    @Test
    void testGetStatusName() {
        when(shippingOrderService.getById(1L)).thenReturn(mockOrder);
        when(customerWarehouseService.getById(200L)).thenReturn(mockWarehouse);
        when(customerCompanyService.getById(100L)).thenReturn(mockCustomerCompany);
        when(generalFileService.getShippingOrderAttachments(1L)).thenReturn(Arrays.asList());

        ShippingOrderDetailResponse response = shippingOrderService.getShippingOrderDetailById(1L);

        assertEquals("待发货", response.getStatusName());
    }

    /**
     * 创建模拟文件对象
     */
    private TGeneralFile createMockFile(Long id, String filePath) {
        TGeneralFile file = new TGeneralFile();
        file.setId(id);
        file.setFilePath(filePath);
        file.setType(2); // SHIPPING_ORDER类型
        file.setValid(1);
        return file;
    }
}
