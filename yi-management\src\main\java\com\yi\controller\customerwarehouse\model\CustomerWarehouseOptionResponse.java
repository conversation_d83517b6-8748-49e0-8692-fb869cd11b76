package com.yi.controller.customerwarehouse.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 仓库选项响应
 */
@Data
@ApiModel(value = "CustomerWarehouseOptionResponse", description = "仓库选项响应")
public class CustomerWarehouseOptionResponse {

    @ApiModelProperty(value = "仓库ID")
    private String id;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "仓库地址")
    private String warehouseAddress;

    @ApiModelProperty(value = "联系人")
    private String contactPerson;

    @ApiModelProperty(value = "手机")
    private String mobilePhone;

    @ApiModelProperty(value = "座机")
    private String landlinePhone;
}
