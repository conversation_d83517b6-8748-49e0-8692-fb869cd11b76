package com.yi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yi.entity.TContractLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 合同操作日志表 Mapper 接口
 */
@Mapper
public interface TContractLogMapper extends BaseMapper<TContractLog> {

    /**
     * 根据合同ID查询操作日志列表
     *
     * @param contractId 合同ID
     * @return 操作日志列表
     */
    List<TContractLog> selectByContractId(@Param("contractId") Long contractId);
}
