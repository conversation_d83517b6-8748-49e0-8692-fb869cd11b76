package com.yi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 云仓表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_cloud_warehouse")
public class TCloudWarehouse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 仓库类型：1-中心仓，2-卫星仓，3-虚拟仓
     */
    private Integer warehouseType;

    /**
     * 仓库属性：1-自有，2-第三方
     */
    private Integer warehouseAttribute;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 省份ID
     */
    private Long provinceId;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市ID
     */
    private Long cityId;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 区县ID
     */
    private Long areaId;

    /**
     * 区县名称
     */
    private String areaName;

    /**
     * 地址详情
     */
    private String detailedAddress;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 联系方式
     */
    private String contactPhone;

    /**
     * 作业时间
     */
    private String workingHours;

    /**
     * 启用状态：1-启用，0-禁用
     */
    private Integer enabled;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效性：1-有效，0-无效
     */
    private Integer valid;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 最后修改人
     */
    private String lastModifiedBy;

    /**
     * 最后修改时间
     */
    private LocalDateTime lastModifiedTime;
}
