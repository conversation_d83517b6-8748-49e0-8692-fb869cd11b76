package com.yi.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 入库单表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_inbound_order")
@ApiModel(value = "InboundOrder对象", description = "入库单表")
public class InboundOrder extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 入库单号（与出库单号一致）
     */
    @ApiModelProperty(value = "入库单号（与出库单号一致）")
    @TableField("order_no")
    private String orderNo;

    /**
     * 入库状态：1-待入库，2-部分入库，3-已入库
     */
    @ApiModelProperty(value = "入库状态：1-待入库，2-部分入库，3-已入库")
    @TableField("status")
    private Integer status;

    /**
     * 入库类型：1-采购入库，2-回收入库，3-调拨入库，4-销售入库
     */
    @ApiModelProperty(value = "入库类型：1-采购入库，2-回收入库，3-调拨入库，4-销售入库")
    @TableField("inbound_type")
    private Integer inboundType;

    /**
     * 入库仓库ID
     */
    @ApiModelProperty(value = "入库仓库ID")
    @TableField("inbound_warehouse_id")
    private Long inboundWarehouseId;

    /**
     * 入库仓库名称
     */
    @ApiModelProperty(value = "入库仓库名称")
    @TableField("inbound_warehouse_name")
    private String inboundWarehouseName;

    /**
     * 配送方式
     */
    @ApiModelProperty(value = "配送方式")
    @TableField("delivery_method")
    private String deliveryMethod;

    /**
     * 车牌号
     */
    @ApiModelProperty(value = "车牌号")
    @TableField("vehicle_number")
    private String vehicleNumber;

    /**
     * 司机姓名
     */
    @ApiModelProperty(value = "司机姓名")
    @TableField("driver_name")
    private String driverName;

    /**
     * 司机联系方式
     */
    @ApiModelProperty(value = "司机联系方式")
    @TableField("driver_phone")
    private String driverPhone;

    /**
     * 发货仓库ID
     */
    @ApiModelProperty(value = "发货仓库ID")
    @TableField("sender_warehouse_id")
    private Long senderWarehouseId;

    /**
     * 发货仓库名称
     */
    @ApiModelProperty(value = "发货仓库名称")
    @TableField("sender_warehouse_name")
    private String senderWarehouseName;

    /**
     * 发货地址
     */
    @ApiModelProperty(value = "发货地址")
    @TableField("sender_address")
    private String senderAddress;

    /**
     * 一级类目：1-共享托盘
     */
    @ApiModelProperty(value = "一级类目：1-共享托盘")
    @TableField("first_category")
    private Integer firstCategory;

    /**
     * 二级类目
     */
    @ApiModelProperty(value = "二级类目")
    @TableField("second_category")
    private String secondCategory;

    /**
     * 计划入库数
     */
    @ApiModelProperty(value = "计划入库数")
    @TableField("planned_quantity")
    private Integer plannedQuantity;

    /**
     * 实际入库数
     */
    @ApiModelProperty(value = "实际入库数")
    @TableField("actual_quantity")
    private Integer actualQuantity;

    /**
     * 入库时间
     */
    @ApiModelProperty(value = "入库时间")
    @TableField("inbound_time")
    private LocalDateTime inboundTime;

    /**
     * 关联出库单ID
     */
    @ApiModelProperty(value = "关联出库单ID")
    @TableField("outbound_order_id")
    private Long outboundOrderId;
}
