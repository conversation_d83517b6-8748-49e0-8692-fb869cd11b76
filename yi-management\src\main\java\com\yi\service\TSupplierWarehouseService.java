package com.yi.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yi.controller.supplierwarehouse.model.*;
import com.yi.entity.TSupplierWarehouse;
import com.yi.mapper.vo.SupplierWarehouseDetailVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yi.mapper.TSupplierWarehouseMapper;
import com.yi.mapper.vo.SupplierWarehousePageVO;
import com.yi.utils.FormatUtils;
import com.yi.utils.ExcelUtils;
import com.yi.utils.UserContextUtils;
import com.yi.enums.EnabledStatusEnum;
import com.yi.enums.ValidStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 供应商仓库表 服务实现类
 */
@Slf4j
@Service
public class TSupplierWarehouseService extends ServiceImpl<TSupplierWarehouseMapper, TSupplierWarehouse> {

    /**
     * 分页查询供应商仓库列表
     *
     * @param request 查询条件
     * @return 分页结果
     */
    public IPage<SupplierWarehousePageResponse> getSupplierWarehousePageResponse(SupplierWarehouseQueryRequest request) {
        Page<SupplierWarehousePageVO> page = new Page<>(request.getPageNum(), request.getPageSize());
        IPage<SupplierWarehousePageVO> warehousePage = this.baseMapper.selectSupplierWarehousePage(page, request);

        // 转换为响应对象
        IPage<SupplierWarehousePageResponse> responsePage = new Page<>(request.getPageNum(), request.getPageSize());
        responsePage.setTotal(warehousePage.getTotal());
        responsePage.setPages(warehousePage.getPages());

        List<SupplierWarehousePageResponse> responseList = warehousePage.getRecords().stream()
                .map(this::convertToPageResponse)
                .collect(Collectors.toList());
        responsePage.setRecords(responseList);

        return responsePage;
    }

    /**
     * 导出供应商仓库列表
     *
     * @param request 查询条件
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    public void exportSupplierWarehouseList(SupplierWarehouseQueryRequest request, HttpServletResponse response) throws IOException {
        // 使用分页查询方法获取所有数据（设置大页码）
        SupplierWarehouseQueryRequest exportRequest = new SupplierWarehouseQueryRequest();
        exportRequest.setPageNum(1);
        exportRequest.setPageSize(Integer.MAX_VALUE);
        exportRequest.setEnabled(request.getEnabled());
        exportRequest.setSupplierName(request.getSupplierName());
        exportRequest.setWarehouseName(request.getWarehouseName());
        exportRequest.setAddress(request.getAddress());

        Page<SupplierWarehousePageVO> page = new Page<>(1, Integer.MAX_VALUE);
        IPage<SupplierWarehousePageVO> warehousePage = this.baseMapper.selectSupplierWarehousePage(page, exportRequest);

        // 转换为导出VO
        List<SupplierWarehouseExportVO> exportList = warehousePage.getRecords().stream()
                .map(this::convertPageVOToExportVO)
                .collect(Collectors.toList());

        // 使用EasyExcel导出（文件名已包含时间戳）
        ExcelUtils.exportExcelWithTimestamp(response, "供应商仓库列表", "供应商仓库列表",
                SupplierWarehouseExportVO.class, exportList);
    }

    /**
     * 转换为分页响应对象
     *
     * @param vo 分页查询VO
     * @return 分页响应对象
     */
    private SupplierWarehousePageResponse convertToPageResponse(SupplierWarehousePageVO vo) {
        SupplierWarehousePageResponse response = new SupplierWarehousePageResponse();
        response.setId(vo.getId().toString());
        response.setEnabled(vo.getEnabled());
        response.setSupplierName(vo.getSupplierName());
        response.setWarehouseName(vo.getWarehouseName());
        
        // 拼接完整地址
        StringBuilder addressBuilder = new StringBuilder();
        if (StringUtils.hasText(vo.getProvinceName())) {
            addressBuilder.append(vo.getProvinceName());
        }
        if (StringUtils.hasText(vo.getCityName())) {
            addressBuilder.append(vo.getCityName());
        }
        if (StringUtils.hasText(vo.getAreaName())) {
            addressBuilder.append(vo.getAreaName());
        }
        if (StringUtils.hasText(vo.getDetailedAddress())) {
            if (addressBuilder.length() > 0) {
                addressBuilder.append(" ");
            }
            addressBuilder.append(vo.getDetailedAddress());
        }
        response.setAddress(addressBuilder.toString());
        
        response.setContactPerson(vo.getContactPerson());
        response.setContactPhone(vo.getContactPhone());
        response.setCreatedBy(vo.getCreatedBy());
        response.setCreatedTime(FormatUtils.formatDateTime(vo.getCreatedTime()));
        return response;
    }

    /**
     * 转换PageVO为导出VO
     *
     * @param vo 分页查询VO
     * @return 导出VO
     */
    private SupplierWarehouseExportVO convertPageVOToExportVO(SupplierWarehousePageVO vo) {
        SupplierWarehouseExportVO exportVO = new SupplierWarehouseExportVO();
        exportVO.setId(vo.getId().toString());
        exportVO.setEnabledText(EnabledStatusEnum.getValueByKey(vo.getEnabled()));
        exportVO.setSupplierName(vo.getSupplierName());
        exportVO.setWarehouseName(vo.getWarehouseName());

        // 拼接完整地址
        StringBuilder addressBuilder = new StringBuilder();
        if (StringUtils.hasText(vo.getProvinceName())) {
            addressBuilder.append(vo.getProvinceName());
        }
        if (StringUtils.hasText(vo.getCityName())) {
            addressBuilder.append(vo.getCityName());
        }
        if (StringUtils.hasText(vo.getAreaName())) {
            addressBuilder.append(vo.getAreaName());
        }
        if (StringUtils.hasText(vo.getDetailedAddress())) {
            if (addressBuilder.length() > 0) {
                addressBuilder.append(" ");
            }
            addressBuilder.append(vo.getDetailedAddress());
        }
        exportVO.setAddress(addressBuilder.toString());

        exportVO.setContactPerson(vo.getContactPerson());
        exportVO.setContactPhone(vo.getContactPhone());
        exportVO.setCreatedBy(vo.getCreatedBy());
        exportVO.setCreatedTime(FormatUtils.formatDateTime(vo.getCreatedTime()));
        return exportVO;
    }

    /**
     * 根据ID获取供应商仓库详情
     *
     * @param id 供应商仓库ID
     * @return 供应商仓库详情
     */
    public SupplierWarehouseDetailResponse getSupplierWarehouseDetailById(Long id) {
        // 使用Mapper联查获取详情
        SupplierWarehouseDetailVO detailVO = this.baseMapper.selectSupplierWarehouseDetailById(id);
        if (detailVO == null) {
            return null;
        }
        return convertDetailVOToResponse(detailVO);
    }

    /**
     * 新增供应商仓库
     *
     * @param request 新增请求
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean addSupplierWarehouse(SupplierWarehouseSaveRequest request) {
        // 检查仓库名称是否已存在（同一供应商下）
        if (isWarehouseNameExists(request.getSupplierId(), request.getWarehouseName(), null)) {
            throw new RuntimeException("该供应商下仓库名称已存在");
        }

        TSupplierWarehouse warehouse = new TSupplierWarehouse();
        warehouse.setSupplierId(request.getSupplierId());
        warehouse.setWarehouseName(request.getWarehouseName());
        warehouse.setProvinceId(request.getProvinceId());
        warehouse.setProvinceName(request.getProvinceName());
        warehouse.setCityId(request.getCityId());
        warehouse.setCityName(request.getCityName());
        warehouse.setAreaId(request.getAreaId());
        warehouse.setAreaName(request.getAreaName());
        warehouse.setDetailedAddress(request.getDetailedAddress());
        warehouse.setContactPerson(request.getContactPerson());
        warehouse.setContactPhone(request.getContactPhone());
        warehouse.setEnabled(EnabledStatusEnum.ENABLED.getKey());
        warehouse.setValid(ValidStatusEnum.VALID.getKey());
        warehouse.setCreatedBy(UserContextUtils.getCurrentUser());
        warehouse.setCreatedTime(java.time.LocalDateTime.now());
        warehouse.setLastModifiedBy(UserContextUtils.getCurrentUser());
        warehouse.setLastModifiedTime(java.time.LocalDateTime.now());

        boolean success = this.save(warehouse);
        if (success) {
            log.info("新增供应商仓库成功，ID: {}, 仓库名称: {}", warehouse.getId(), warehouse.getWarehouseName());
        }
        return success;
    }

    /**
     * 更新供应商仓库
     *
     * @param request 更新请求
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSupplierWarehouse(SupplierWarehouseSaveRequest request) {
        Long id = FormatUtils.safeParseLong(request.getId());
        if (id == null) {
            throw new RuntimeException("供应商仓库ID不能为空");
        }

        TSupplierWarehouse existingWarehouse = this.getById(id);
        if (existingWarehouse == null || !ValidStatusEnum.VALID.getKey().equals(existingWarehouse.getValid())) {
            throw new RuntimeException("供应商仓库不存在");
        }

        // 检查仓库名称是否已存在（同一供应商下，排除当前记录）
        if (isWarehouseNameExists(request.getSupplierId(), request.getWarehouseName(), id)) {
            throw new RuntimeException("该供应商下仓库名称已存在");
        }

        existingWarehouse.setSupplierId(request.getSupplierId());
        existingWarehouse.setWarehouseName(request.getWarehouseName());
        existingWarehouse.setProvinceId(request.getProvinceId());
        existingWarehouse.setProvinceName(request.getProvinceName());
        existingWarehouse.setCityId(request.getCityId());
        existingWarehouse.setCityName(request.getCityName());
        existingWarehouse.setAreaId(request.getAreaId());
        existingWarehouse.setAreaName(request.getAreaName());
        existingWarehouse.setDetailedAddress(request.getDetailedAddress());
        existingWarehouse.setContactPerson(request.getContactPerson());
        existingWarehouse.setContactPhone(request.getContactPhone());
        existingWarehouse.setLastModifiedBy(UserContextUtils.getCurrentUser());
        existingWarehouse.setLastModifiedTime(java.time.LocalDateTime.now());

        boolean success = this.updateById(existingWarehouse);
        if (success) {
            log.info("更新供应商仓库成功，ID: {}, 仓库名称: {}", existingWarehouse.getId(), existingWarehouse.getWarehouseName());
        }
        return success;
    }

    /**
     * 启用/禁用供应商仓库
     *
     * @param id 供应商仓库ID
     * @param enabled 启用状态
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSupplierWarehouseStatus(Long id, Integer enabled) {
        TSupplierWarehouse warehouse = this.getById(id);
        if (warehouse == null || !ValidStatusEnum.VALID.getKey().equals(warehouse.getValid())) {
            throw new RuntimeException("供应商仓库不存在");
        }

        warehouse.setEnabled(enabled);
        warehouse.setLastModifiedBy(UserContextUtils.getCurrentUser());
        warehouse.setLastModifiedTime(java.time.LocalDateTime.now());

        boolean success = this.updateById(warehouse);
        if (success) {
            String status = EnabledStatusEnum.ENABLED.getKey().equals(enabled) ? "启用" : "禁用";
            log.info("{}供应商仓库成功，ID: {}, 仓库名称: {}", status, warehouse.getId(), warehouse.getWarehouseName());
        }
        return success;
    }

    /**
     * 检查仓库名称是否已存在
     *
     * @param supplierId 供应商ID
     * @param warehouseName 仓库名称
     * @param excludeId 排除的ID（更新时使用）
     * @return 是否存在
     */
    private boolean isWarehouseNameExists(Long supplierId, String warehouseName, Long excludeId) {
        LambdaQueryWrapper<TSupplierWarehouse> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TSupplierWarehouse::getSupplierId, supplierId)
                .eq(TSupplierWarehouse::getWarehouseName, warehouseName)
                .eq(TSupplierWarehouse::getValid, ValidStatusEnum.VALID.getKey());

        if (excludeId != null) {
            wrapper.ne(TSupplierWarehouse::getId, excludeId);
        }

        return this.count(wrapper) > 0;
    }


    /**
     * 转换DetailVO为详情响应对象
     *
     * @param detailVO 详情VO
     * @return 详情响应对象
     */
    private SupplierWarehouseDetailResponse convertDetailVOToResponse(SupplierWarehouseDetailVO detailVO) {
        SupplierWarehouseDetailResponse response = new SupplierWarehouseDetailResponse();
        response.setId(detailVO.getId().toString());
        response.setSupplierName(detailVO.getSupplierName());
        response.setWarehouseName(detailVO.getWarehouseName());
        response.setProvinceName(detailVO.getProvinceName());
        response.setCityName(detailVO.getCityName());
        response.setAreaName(detailVO.getAreaName());
        response.setDetailedAddress(detailVO.getDetailedAddress());
        response.setContactPerson(detailVO.getContactPerson());
        response.setContactPhone(detailVO.getContactPhone());

        return response;
    }

}
