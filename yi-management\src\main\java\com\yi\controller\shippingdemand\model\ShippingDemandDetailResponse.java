package com.yi.controller.shippingdemand.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 发货需求详情响应
 */
@Data
@ApiModel(value = "ShippingDemandDetailResponse", description = "发货需求详情响应")
public class ShippingDemandDetailResponse {

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "发运订单号")
    private String orderNo;

    @ApiModelProperty(value = "发货状态")
    private String status;

    @ApiModelProperty(value = "发货状态名称")
    private String statusName;

    @ApiModelProperty(value = "客户公司ID")
    private String customerCompanyId;

    @ApiModelProperty(value = "客户公司名称")
    private String customerCompanyName;

    @ApiModelProperty(value = "收货仓库ID")
    private String warehouseId;

    @ApiModelProperty(value = "收货仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "收货地址")
    private String receivingAddress;

    @ApiModelProperty(value = "收货人")
    private String receiverName;

    @ApiModelProperty(value = "一级类目")
    private String firstCategory;

    @ApiModelProperty(value = "一级类目名称")
    private String firstCategoryName;

    @ApiModelProperty(value = "二级类目")
    private String secondCategory;

    @ApiModelProperty(value = "需求数量")
    private String demandQuantity;

    @ApiModelProperty(value = "待确认数量")
    private String pendingQuantity;

    @ApiModelProperty(value = "发货数量")
    private String shippedQuantity;

    @ApiModelProperty(value = "未执行数量")
    private String unexecutedQuantity;

    @ApiModelProperty(value = "需求时间")
    private String demandTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private String createdTime;

    @ApiModelProperty(value = "最后修改人")
    private String lastModifiedBy;

    @ApiModelProperty(value = "最后修改时间")
    private String lastModifiedTime;
}
