package com.yi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yi.entity.TSupplier;
import com.yi.mapper.vo.SupplierPageVO;
import com.yi.controller.supplier.model.SupplierQueryRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 供应商表 Mapper 接口
 */
@Mapper
public interface TSupplierMapper extends BaseMapper<TSupplier> {

    /**
     * 分页查询供应商列表
     *
     * @param page 分页参数
     * @param request 查询条件
     * @return 分页结果
     */
    IPage<SupplierPageVO> selectSupplierPage(Page<SupplierPageVO> page, 
                                            @Param("request") SupplierQueryRequest request);

    /**
     * 查询供应商列表（不分页）
     *
     * @param request 查询条件
     * @return 供应商列表
     */
    List<SupplierPageVO> selectSupplierList(@Param("request") SupplierQueryRequest request);

    /**
     * 根据供应商名称查询供应商
     *
     * @param supplierName 供应商名称
     * @return 供应商信息
     */
    TSupplier selectBySupplierName(@Param("supplierName") String supplierName);

    /**
     * 根据启用状态查询供应商列表
     *
     * @param enabled 启用状态
     * @return 供应商列表
     */
    List<TSupplier> selectByEnabled(@Param("enabled") Integer enabled);

    /**
     * 更新供应商启用状态
     *
     * @param id 供应商ID
     * @param enabled 启用状态
     * @param lastModifiedBy 修改人
     * @return 更新行数
     */
    int updateEnabled(@Param("id") Long id, 
                     @Param("enabled") Integer enabled, 
                     @Param("lastModifiedBy") String lastModifiedBy);

    /**
     * 检查供应商名称是否存在
     *
     * @param supplierName 供应商名称
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 存在的数量
     */
    int countBySupplierName(@Param("supplierName") String supplierName, 
                           @Param("excludeId") Long excludeId);
}
