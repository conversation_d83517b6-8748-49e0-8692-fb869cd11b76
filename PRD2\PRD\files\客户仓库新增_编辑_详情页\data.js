﻿$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,_(g,h,i,_(j,k,l,k)),m,[],n,_(h,o),p,[q],r,_(s,t,u,v,g,w,x,_(),y,[],z,_(A,B,C,D,E,_(F,G,H,I),J,null,K,L,L,M,N,O,null,P,Q,R,S,T,U,V,Q,W,X,Y,_(F,G,H,Z),ba,Q,bb,X,bc,_(bd,be,bf,bg,bh,bg,bi,bg,bj,k,H,_(bk,bl,bm,bl,bn,bl,bo,bp)),i,_(j,k,l,k)),bq,_(),br,_(),bs,_(bt,[_(bu,bv,bw,h,bx,by,u,bz,bA,bB,bC,bD,z,_(i,_(j,bE,l,bF),A,bG,bH,_(bI,bJ,bK,bL)),bq,_(),bM,_(),bN,_(bO,bP),bQ,be),_(bu,bR,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(i,_(j,bT,l,bU),A,bV,bH,_(bI,bJ,bK,bW),V,bX,Y,_(F,G,H,bY)),bq,_(),bM,_(),bQ,be),_(bu,bZ,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(i,_(j,ca,l,cb),A,cc,bH,_(bI,cd,bK,ce)),bq,_(),bM,_(),bQ,be),_(bu,cf,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,ch,ci,bF),i,_(j,cj,l,ck),A,cl,bH,_(bI,cm,bK,cn)),bq,_(),bM,_(),bQ,be),_(bu,co,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,ch,ci,bF),i,_(j,cj,l,ck),A,cl,bH,_(bI,cm,bK,cp)),bq,_(),bM,_(),bQ,be),_(bu,cq,bw,h,bx,cr,u,cs,bA,cs,bC,bD,z,_(i,_(j,ct,l,cu),cv,_(cw,_(A,cx),cy,_(A,cz)),A,cA,bH,_(bI,cB,bK,cC)),cD,be,bq,_(),bM,_(),cE,h),_(bu,cF,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,ch,ci,bF),i,_(j,cj,l,ck),A,cl,bH,_(bI,cm,bK,cG)),bq,_(),bM,_(),bQ,be),_(bu,cH,bw,h,bx,cr,u,cs,bA,cs,bC,bD,z,_(i,_(j,cI,l,cJ),cv,_(cw,_(A,cx),cy,_(A,cz)),A,cA,bH,_(bI,cK,bK,cL)),cD,be,bq,_(),bM,_(),cE,h),_(bu,cM,bw,h,bx,cN,u,cO,bA,cO,bC,bD,z,_(i,_(j,cP,l,cJ),A,cQ,cv,_(cy,_(A,cz)),bH,_(bI,cB,bK,cL)),cD,be,bq,_(),bM,_()),_(bu,cR,bw,h,bx,cN,u,cO,bA,cO,bC,bD,z,_(i,_(j,cP,l,cJ),A,cQ,cv,_(cy,_(A,cz)),bH,_(bI,cS,bK,cL)),cD,be,bq,_(),bM,_()),_(bu,cT,bw,h,bx,cN,u,cO,bA,cO,bC,bD,z,_(i,_(j,cP,l,cJ),A,cQ,cv,_(cy,_(A,cz)),bH,_(bI,cU,bK,cL)),cD,be,bq,_(),bM,_()),_(bu,cV,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,bY,ci,bF),i,_(j,cW,l,cX),A,cl,bH,_(bI,cY,bK,cZ),da,db),bq,_(),bM,_(),bQ,be),_(bu,dc,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,bY,ci,bF),i,_(j,cW,l,cX),A,cl,bH,_(bI,dd,bK,cZ),da,db),bq,_(),bM,_(),bQ,be),_(bu,de,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,bY,ci,bF),i,_(j,cW,l,cX),A,cl,bH,_(bI,df,bK,cZ),da,db),bq,_(),bM,_(),bQ,be),_(bu,dg,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,ch,ci,bF),i,_(j,dh,l,ck),A,cl,bH,_(bI,di,bK,dj)),bq,_(),bM,_(),bQ,be),_(bu,dk,bw,h,bx,cr,u,cs,bA,cs,bC,bD,z,_(i,_(j,cC,l,cu),cv,_(cw,_(A,cx),cy,_(A,cz)),A,cA,bH,_(bI,cB,bK,dl)),cD,be,bq,_(),bM,_(),cE,h),_(bu,dm,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,ch,ci,bF),i,_(j,dh,l,ck),A,cl,bH,_(bI,dn,bK,dj)),bq,_(),bM,_(),bQ,be),_(bu,dp,bw,h,bx,cr,u,cs,bA,cs,bC,bD,z,_(i,_(j,cC,l,cu),cv,_(cw,_(A,cx),cy,_(A,cz)),A,cA,bH,_(bI,dq,bK,dl)),cD,be,bq,_(),bM,_(),cE,h),_(bu,dr,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(i,_(j,ds,l,dt),A,du,bH,_(bI,dv,bK,dw),da,dx),bq,_(),bM,_(),br,_(dy,_(dz,dA,dB,dC,dD,[_(dB,h,dE,h,dF,be,dG,dH,dI,[_(dJ,dK,dB,dL,dM,dN,dO,_(dP,_(h,dL)),dQ,_(dR,r,b,dS,dT,bD),dU,dV)])])),dW,bD,bQ,be),_(bu,dX,bw,h,bx,cN,u,cO,bA,cO,bC,bD,z,_(i,_(j,ct,l,cu),A,cQ,cv,_(cy,_(A,cz)),bH,_(bI,cB,bK,dY)),cD,be,bq,_(),bM,_()),_(bu,dZ,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(i,_(j,cj,l,ck),A,cl,bH,_(bI,cm,bK,ea)),bq,_(),bM,_(),bQ,be),_(bu,eb,bw,h,bx,cN,u,cO,bA,cO,bC,bD,z,_(i,_(j,dl,l,cu),A,cQ,cv,_(cy,_(A,cz)),bH,_(bI,cB,bK,ec)),cD,be,bq,_(),bM,_()),_(bu,ed,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(i,_(j,ee,l,ck),A,cl,bH,_(bI,ef,bK,eg)),bq,_(),bM,_(),bQ,be),_(bu,eh,bw,h,bx,cN,u,cO,bA,cO,bC,bD,z,_(i,_(j,dl,l,cu),A,cQ,cv,_(cy,_(A,cz)),bH,_(bI,cB,bK,ei)),cD,be,bq,_(),bM,_()),_(bu,ej,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(i,_(j,ek,l,cu),A,du,bH,_(bI,el,bK,ei),E,_(F,G,H,em)),bq,_(),bM,_(),bQ,be),_(bu,en,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,ch,ci,bF),i,_(j,ek,l,cu),A,du,bH,_(bI,eo,bK,ei),E,_(F,G,H,I),V,bX),bq,_(),bM,_(),bQ,be),_(bu,ep,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(i,_(j,eq,l,er),A,es,bH,_(bI,et,bK,eu)),bq,_(),bM,_(),bQ,be),_(bu,ev,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,ch,ci,bF),i,_(j,ew,l,ck),A,cl,bH,_(bI,ex,bK,ey)),bq,_(),bM,_(),bQ,be),_(bu,ez,bw,h,bx,cr,u,cs,bA,cs,bC,bD,z,_(i,_(j,cC,l,cu),cv,_(cw,_(A,cx),cy,_(A,cz)),A,cA,bH,_(bI,cB,bK,eA)),cD,be,bq,_(),bM,_(),cE,h)])),eB,_(),eC,_(eD,_(eE,eF),eG,_(eE,eH),eI,_(eE,eJ),eK,_(eE,eL),eM,_(eE,eN),eO,_(eE,eP),eQ,_(eE,eR),eS,_(eE,eT),eU,_(eE,eV),eW,_(eE,eX),eY,_(eE,eZ),fa,_(eE,fb),fc,_(eE,fd),fe,_(eE,ff),fg,_(eE,fh),fi,_(eE,fj),fk,_(eE,fl),fm,_(eE,fn),fo,_(eE,fp),fq,_(eE,fr),fs,_(eE,ft),fu,_(eE,fv),fw,_(eE,fx),fy,_(eE,fz),fA,_(eE,fB),fC,_(eE,fD),fE,_(eE,fF),fG,_(eE,fH),fI,_(eE,fJ)));}; 
var b="url",c="客户仓库新增_编辑_详情页.html",d="generationDate",e=new Date(1753855217549.78),f="defaultAdaptiveView",g="name",h="",i="size",j="width",k=0,l="height",m="adaptiveViews",n="sketchKeys",o="s0",p="variables",q="OnLoadVariable",r="page",s="packageId",t="0fa1ab8a52aa4d00aa3b45cbf757d927",u="type",v="Axure:Page",w="客户仓库新增/编辑/详情页",x="notes",y="annotations",z="style",A="baseStyle",B="627587b6038d43cca051c114ac41ad32",C="pageAlignment",D="center",E="fill",F="fillType",G="solid",H="color",I=0xFFFFFFFF,J="image",K="imageAlignment",L="near",M="imageRepeat",N="auto",O="favicon",P="sketchFactor",Q="0",R="colorStyle",S="appliedColor",T="fontName",U="Applied Font",V="borderWidth",W="borderVisibility",X="all",Y="borderFill",Z=0xFF797979,ba="cornerRadius",bb="cornerVisibility",bc="outerShadow",bd="on",be=false,bf="offsetX",bg=5,bh="offsetY",bi="blurRadius",bj="spread",bk="r",bl=0,bm="g",bn="b",bo="a",bp=0.349019607843137,bq="adaptiveStyles",br="interactionMap",bs="diagram",bt="objects",bu="id",bv="1bccb74ca07a4f16a7d6177fda67f3c9",bw="label",bx="friendlyType",by="线段",bz="vectorShape",bA="styleType",bB="horizontalLine",bC="visible",bD=true,bE=1300,bF=1,bG="0327e893a7994793993b54c636419b7c",bH="location",bI="x",bJ=20,bK="y",bL=50,bM="imageOverrides",bN="images",bO="normal~",bP="images/客户管理/u350.svg",bQ="generateCompound",bR="a54ed9b91b9d4f459d4c6b34dacba4c3",bS="矩形",bT=235,bU=30,bV="4701f00c92714d4e9eed94e9fe75cfe8",bW=21,bX="1",bY=0xFFAAAAAA,bZ="3eaccece7e3a493eb3168bf16d6e9381",ca=56,cb=19,cc="4b88aa200ad64025ad561857a6779b03",cd=1264,ce=32,cf="18a4b6a74c1646b2ac3de91f200f247c",cg="foreGroundFill",ch=0xFF000000,ci="opacity",cj=62,ck=16,cl="df3da3fd8cfa4c4a81f05df7784209fe",cm=278,cn=154,co="c662eb674dac4f2e9f04098a9a5b30a5",cp=205,cq="9a1a6c4f47a541238ba6708cc26cc960",cr="文本框",cs="textBox",ct=700,cu=26,cv="stateStyles",cw="hint",cx="4889d666e8ad4c5e81e59863039a5cc0",cy="disabled",cz="9bd0236217a94d89b0314c8c7fc75f16",cA="2170b7f9af5c48fba2adcd540f2ba1a0",cB=350,cC=200,cD="HideHintOnFocused",cE="placeholderText",cF="d7ac5e2471b74b50a91a6eaa878ab71f",cG=260,cH="7a26d50d9b4a4068b3981fc2093500dd",cI=220,cJ=24,cK=830,cL=256,cM="c7bc5865e437492295df4419b40deb39",cN="下拉列表",cO="comboBox",cP=150,cQ="********************************",cR="839126952b7b4682afa80eaa93d92c4e",cS=510,cT="7b7a0c59d50746b09d306aae7c105302",cU=670,cV="829f3a21e6294c95b1e2f03b631d269d",cW=12,cX=14,cY=466,cZ=261,da="fontSize",db="12px",dc="6ffdaf01ea54470b8a1b62a3a10a01c7",dd=630,de="caa678f52ef64d098b7b931a9922f651",df=790,dg="e96f6ccb1ad849a894f0b681bc102841",dh=48,di=292,dj=315,dk="62fb80bcb9fd4796875642ca997faff6",dl=310,dm="73c6bedca12144819a4a60bc222de117",dn=797,dp="ca37fad652f040eda97b40a32d4049cc",dq=850,dr="b03f9adbf34243c896fda01e2322f912",ds=140,dt=40,du="f9d2a29eec41403f99d04559928d6317",dv=600,dw=491,dx="16px",dy="onClick",dz="eventType",dA="Click时",dB="description",dC="单击时",dD="cases",dE="conditionString",dF="isNewIfGroup",dG="caseColorHex",dH="AB68FF",dI="actions",dJ="action",dK="linkWindow",dL="打开 客户仓库管理 在 当前窗口",dM="displayName",dN="打开链接",dO="actionInfoDescriptions",dP="客户仓库管理",dQ="target",dR="targetType",dS="客户仓库管理.html",dT="includeVariables",dU="linkType",dV="current",dW="tabbable",dX="fa8d7230b4cc4f699aa97a90fb2ba07a",dY=149,dZ="199e8f390d6344c0a92a0d9edec2649e",ea=103,eb="e80803ec05c64f2e91e00e68b135752c",ec=98,ed="e5054d744f0c4f18bb3788fb944dd300",ee=63,ef=277,eg=427,eh="a7b9117220534a8bb3f60d5764ff9a13",ei=422,ej="302baba354ec41a2a073be67f02c0a45",ek=80,el=690,em=0xFF68A603,en="40abd7f33a504fa48221a95fa57db314",eo=785,ep="4f5438f6aac34a3cb094d4f9fcf32b60",eq=853,er=65,es="3106573e48474c3281b6db181d1a931f",et=233,eu=573,ev="28806403a0e9468b989d8b06dcb76f35",ew=42,ex=298,ey=371,ez="603d5eab65f349bbb9cd5071e6c798e9",eA=366,eB="masters",eC="objectPaths",eD="1bccb74ca07a4f16a7d6177fda67f3c9",eE="scriptId",eF="u831",eG="a54ed9b91b9d4f459d4c6b34dacba4c3",eH="u832",eI="3eaccece7e3a493eb3168bf16d6e9381",eJ="u833",eK="18a4b6a74c1646b2ac3de91f200f247c",eL="u834",eM="c662eb674dac4f2e9f04098a9a5b30a5",eN="u835",eO="9a1a6c4f47a541238ba6708cc26cc960",eP="u836",eQ="d7ac5e2471b74b50a91a6eaa878ab71f",eR="u837",eS="7a26d50d9b4a4068b3981fc2093500dd",eT="u838",eU="c7bc5865e437492295df4419b40deb39",eV="u839",eW="839126952b7b4682afa80eaa93d92c4e",eX="u840",eY="7b7a0c59d50746b09d306aae7c105302",eZ="u841",fa="829f3a21e6294c95b1e2f03b631d269d",fb="u842",fc="6ffdaf01ea54470b8a1b62a3a10a01c7",fd="u843",fe="caa678f52ef64d098b7b931a9922f651",ff="u844",fg="e96f6ccb1ad849a894f0b681bc102841",fh="u845",fi="62fb80bcb9fd4796875642ca997faff6",fj="u846",fk="73c6bedca12144819a4a60bc222de117",fl="u847",fm="ca37fad652f040eda97b40a32d4049cc",fn="u848",fo="b03f9adbf34243c896fda01e2322f912",fp="u849",fq="fa8d7230b4cc4f699aa97a90fb2ba07a",fr="u850",fs="199e8f390d6344c0a92a0d9edec2649e",ft="u851",fu="e80803ec05c64f2e91e00e68b135752c",fv="u852",fw="e5054d744f0c4f18bb3788fb944dd300",fx="u853",fy="a7b9117220534a8bb3f60d5764ff9a13",fz="u854",fA="302baba354ec41a2a073be67f02c0a45",fB="u855",fC="40abd7f33a504fa48221a95fa57db314",fD="u856",fE="4f5438f6aac34a3cb094d4f9fcf32b60",fF="u857",fG="28806403a0e9468b989d8b06dcb76f35",fH="u858",fI="603d5eab65f349bbb9cd5071e6c798e9",fJ="u859";
return _creator();
})());