﻿.ax_default {
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  line-height:normal;
  text-transform:none;
}
.image {
}
.heading_1 {
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:32px;
  text-align:left;
}
.heading_2 {
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:24px;
  text-align:left;
}
.heading_3 {
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:18px;
  text-align:left;
}
.heading_4 {
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
.heading_5 {
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:bold;
  font-style:normal;
  text-align:left;
}
.heading_6 {
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:10px;
  text-align:left;
}
.paragraph {
  text-align:left;
}
.table_cell {
}
.form_hint {
  color:#999999;
}
.form_disabled {
}
.flow_shape {
}
.paragraph1 {
  text-align:left;
}
.line {
}
.heading_11 {
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:32px;
  text-align:left;
}
.horizontal_line {
}
.shape {
}
._一级标题 {
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:32px;
  text-align:left;
}
.box_2 {
}
.image1 {
  color:#000000;
}
.placeholder {
}
.vertical_line {
}
.iconfont {
  font-family:'iconfont ', 'iconfont', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#666666;
}
.line1 {
}
.primary_button {
  color:#FFFFFF;
}
.button {
}
.box_3 {
}
.label {
  font-size:14px;
  text-align:left;
}
.connector {
}
.sticky_2 {
  text-align:left;
}
.marker {
  color:#FFFFFF;
}
.sticky_1 {
  text-align:left;
}
.icon {
}
.droplist {
  color:#000000;
  text-align:left;
}
.radio_button {
  text-align:left;
}
.ellipse {
}
.sticky_4 {
  text-align:left;
}
._默认样式 {
}
.box_21 {
}
.refs-design-material {
  font-family:'Noto Sans CJK SC ', 'Noto Sans CJK SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
.shape1 {
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:normal;
  font-style:normal;
  font-size:13px;
  color:#333333;
  text-align:center;
  line-height:normal;
}
.box_1 {
}
._形状 {
  font-family:'Microsoft YaHei UI Bold', 'Microsoft YaHei UI', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:12px;
  color:#FF0066;
}
.text_area {
  color:#000000;
  text-align:left;
}
.label1 {
  font-size:14px;
  text-align:left;
}
.box_22 {
}
.image2 {
  color:#000000;
}
._形状1 {
}
.text_field {
  color:#000000;
  text-align:left;
}
.refs-webm-wechat {
  font-family:'PingFang SC ', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
._形状2 {
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:normal;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:center;
  line-height:normal;
}
._形状3 {
}
.link_button {
  color:#169BD5;
}
._连接 {
}
._图像_ {
}
._三级标题 {
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:18px;
  text-align:left;
}
._表单提示 {
  color:#999999;
}
._表单禁用 {
}
.arrow {
}
._图片 {
}
.checkbox {
  text-align:left;
}
.shape2 {
}
textarea, select, input, button { outline: none; }
