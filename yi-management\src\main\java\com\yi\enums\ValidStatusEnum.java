package com.yi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 有效性状态枚举
 */
@Getter
@AllArgsConstructor
public enum ValidStatusEnum {

    /**
     * 无效（已删除）
     */
    INVALID(0, "无效"),

    /**
     * 有效
     */
    VALID(1, "有效");

    /**
     * 状态键
     */
    private final Integer key;

    /**
     * 状态值
     */
    private final String value;

    /**
     * 根据键获取枚举
     *
     * @param key 键
     * @return 枚举
     */
    public static ValidStatusEnum getByKey(Integer key) {
        if (key == null) {
            return null;
        }
        for (ValidStatusEnum status : ValidStatusEnum.values()) {
            if (status.getKey().equals(key)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据键获取值
     *
     * @param key 键
     * @return 值
     */
    public static String getValueByKey(Integer key) {
        ValidStatusEnum status = getByKey(key);
        return status != null ? status.getValue() : null;
    }

    /**
     * 判断是否有效
     *
     * @param key 键
     * @return 是否有效
     */
    public static boolean isValid(Integer key) {
        return VALID.getKey().equals(key);
    }

    /**
     * 判断是否无效
     *
     * @param key 键
     * @return 是否无效
     */
    public static boolean isInvalid(Integer key) {
        return INVALID.getKey().equals(key);
    }
}
