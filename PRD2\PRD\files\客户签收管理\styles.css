﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-30px;
  width:2226px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u1433_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1301px;
  height:2px;
}
#u1433 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:37px;
  width:1300px;
  height:1px;
  display:flex;
}
#u1433 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1433_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1434_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:180px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1434 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:7px;
  width:180px;
  height:30px;
  display:flex;
}
#u1434 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1434_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1435_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1435 {
  border-width:0px;
  position:absolute;
  left:1274px;
  top:18px;
  width:56px;
  height:19px;
  display:flex;
}
#u1435 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1435_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1436_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1300px;
  height:100px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1436 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:43px;
  width:1300px;
  height:100px;
  display:flex;
}
#u1436 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1436_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1437_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1437 {
  border-width:0px;
  position:absolute;
  left:820px;
  top:59px;
  width:28px;
  height:16px;
  display:flex;
}
#u1437 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1437_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1438_input {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1438_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1438_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u1438 {
  border-width:0px;
  position:absolute;
  left:858px;
  top:55px;
  width:120px;
  height:24px;
  display:flex;
  text-align:center;
}
#u1438 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1438_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u1438.disabled {
}
#u1439_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1439 {
  border-width:0px;
  position:absolute;
  left:299px;
  top:59px;
  width:42px;
  height:16px;
  display:flex;
}
#u1439 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1439_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1440_input {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1440_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1440_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u1440 {
  border-width:0px;
  position:absolute;
  left:346px;
  top:55px;
  width:120px;
  height:24px;
  display:flex;
  text-align:center;
}
#u1440 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1440_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u1440.disabled {
}
#u1441_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1441 {
  border-width:0px;
  position:absolute;
  left:48px;
  top:59px;
  width:56px;
  height:16px;
  display:flex;
}
#u1441 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1441_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1442_input {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1442_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1442_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1442 {
  border-width:0px;
  position:absolute;
  left:109px;
  top:55px;
  width:120px;
  height:24px;
  display:flex;
}
#u1442 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1442_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1442.disabled {
}
.u1442_input_option {
}
#u1443_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1443 {
  border-width:0px;
  position:absolute;
  left:1059px;
  top:101px;
  width:80px;
  height:30px;
  display:flex;
}
#u1443 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1443_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1444_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1444 {
  border-width:0px;
  position:absolute;
  left:1149px;
  top:101px;
  width:80px;
  height:30px;
  display:flex;
}
#u1444 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1444_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1445 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:213px;
  width:2226px;
  height:334px;
}
#u1446_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:30px;
}
#u1446 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:30px;
  display:flex;
}
#u1446 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1446_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1447_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:30px;
}
#u1447 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:0px;
  width:168px;
  height:30px;
  display:flex;
}
#u1447 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1447_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1448_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:159px;
  height:30px;
}
#u1448 {
  border-width:0px;
  position:absolute;
  left:231px;
  top:0px;
  width:159px;
  height:30px;
  display:flex;
}
#u1448 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1448_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1449_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:148px;
  height:30px;
}
#u1449 {
  border-width:0px;
  position:absolute;
  left:390px;
  top:0px;
  width:148px;
  height:30px;
  display:flex;
}
#u1449 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1449_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1450_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:30px;
}
#u1450 {
  border-width:0px;
  position:absolute;
  left:538px;
  top:0px;
  width:106px;
  height:30px;
  display:flex;
}
#u1450 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1450_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1451_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u1451 {
  border-width:0px;
  position:absolute;
  left:644px;
  top:0px;
  width:116px;
  height:30px;
  display:flex;
}
#u1451 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1451_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1452_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:30px;
}
#u1452 {
  border-width:0px;
  position:absolute;
  left:760px;
  top:0px;
  width:125px;
  height:30px;
  display:flex;
}
#u1452 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1452_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1453_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:30px;
}
#u1453 {
  border-width:0px;
  position:absolute;
  left:885px;
  top:0px;
  width:193px;
  height:30px;
  display:flex;
}
#u1453 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1453_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1454_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u1454 {
  border-width:0px;
  position:absolute;
  left:1078px;
  top:0px;
  width:139px;
  height:30px;
  display:flex;
}
#u1454 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1454_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1455_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:124px;
  height:30px;
}
#u1455 {
  border-width:0px;
  position:absolute;
  left:1217px;
  top:0px;
  width:124px;
  height:30px;
  display:flex;
}
#u1455 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1455_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1456_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:30px;
}
#u1456 {
  border-width:0px;
  position:absolute;
  left:1341px;
  top:0px;
  width:122px;
  height:30px;
  display:flex;
}
#u1456 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1456_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1457_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:30px;
}
#u1457 {
  border-width:0px;
  position:absolute;
  left:1463px;
  top:0px;
  width:108px;
  height:30px;
  display:flex;
}
#u1457 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1457_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1458_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1458 {
  border-width:0px;
  position:absolute;
  left:1571px;
  top:0px;
  width:131px;
  height:30px;
  display:flex;
}
#u1458 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1458_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1459_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1459 {
  border-width:0px;
  position:absolute;
  left:1702px;
  top:0px;
  width:131px;
  height:30px;
  display:flex;
}
#u1459 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1459_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1460_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1460 {
  border-width:0px;
  position:absolute;
  left:1833px;
  top:0px;
  width:131px;
  height:30px;
  display:flex;
}
#u1460 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1460_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1461_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1461 {
  border-width:0px;
  position:absolute;
  left:1964px;
  top:0px;
  width:131px;
  height:30px;
  display:flex;
}
#u1461 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1461_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1462_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1462 {
  border-width:0px;
  position:absolute;
  left:2095px;
  top:0px;
  width:131px;
  height:30px;
  display:flex;
}
#u1462 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1462_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1463_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:34px;
}
#u1463 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:30px;
  width:63px;
  height:34px;
  display:flex;
}
#u1463 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1463_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1464_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:34px;
}
#u1464 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:30px;
  width:168px;
  height:34px;
  display:flex;
}
#u1464 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1464_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1465_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:159px;
  height:34px;
}
#u1465 {
  border-width:0px;
  position:absolute;
  left:231px;
  top:30px;
  width:159px;
  height:34px;
  display:flex;
}
#u1465 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1465_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1466_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:148px;
  height:34px;
}
#u1466 {
  border-width:0px;
  position:absolute;
  left:390px;
  top:30px;
  width:148px;
  height:34px;
  display:flex;
}
#u1466 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1466_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1467_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:34px;
}
#u1467 {
  border-width:0px;
  position:absolute;
  left:538px;
  top:30px;
  width:106px;
  height:34px;
  display:flex;
}
#u1467 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1467_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1468_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:34px;
}
#u1468 {
  border-width:0px;
  position:absolute;
  left:644px;
  top:30px;
  width:116px;
  height:34px;
  display:flex;
}
#u1468 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1468_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1469_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:34px;
}
#u1469 {
  border-width:0px;
  position:absolute;
  left:760px;
  top:30px;
  width:125px;
  height:34px;
  display:flex;
}
#u1469 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1469_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1470_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:34px;
}
#u1470 {
  border-width:0px;
  position:absolute;
  left:885px;
  top:30px;
  width:193px;
  height:34px;
  display:flex;
}
#u1470 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1470_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1471_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:34px;
}
#u1471 {
  border-width:0px;
  position:absolute;
  left:1078px;
  top:30px;
  width:139px;
  height:34px;
  display:flex;
}
#u1471 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1471_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1472_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:124px;
  height:34px;
}
#u1472 {
  border-width:0px;
  position:absolute;
  left:1217px;
  top:30px;
  width:124px;
  height:34px;
  display:flex;
}
#u1472 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1472_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1473_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:34px;
}
#u1473 {
  border-width:0px;
  position:absolute;
  left:1341px;
  top:30px;
  width:122px;
  height:34px;
  display:flex;
}
#u1473 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1473_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1474_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:34px;
}
#u1474 {
  border-width:0px;
  position:absolute;
  left:1463px;
  top:30px;
  width:108px;
  height:34px;
  display:flex;
}
#u1474 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1474_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1475_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:34px;
}
#u1475 {
  border-width:0px;
  position:absolute;
  left:1571px;
  top:30px;
  width:131px;
  height:34px;
  display:flex;
}
#u1475 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1475_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1476_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:34px;
}
#u1476 {
  border-width:0px;
  position:absolute;
  left:1702px;
  top:30px;
  width:131px;
  height:34px;
  display:flex;
}
#u1476 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1476_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1477_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:34px;
}
#u1477 {
  border-width:0px;
  position:absolute;
  left:1833px;
  top:30px;
  width:131px;
  height:34px;
  display:flex;
}
#u1477 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1477_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1478_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:34px;
}
#u1478 {
  border-width:0px;
  position:absolute;
  left:1964px;
  top:30px;
  width:131px;
  height:34px;
  display:flex;
}
#u1478 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1478_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1479_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:34px;
}
#u1479 {
  border-width:0px;
  position:absolute;
  left:2095px;
  top:30px;
  width:131px;
  height:34px;
  display:flex;
}
#u1479 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1479_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1480_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:30px;
}
#u1480 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:64px;
  width:63px;
  height:30px;
  display:flex;
}
#u1480 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1480_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1481_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:30px;
}
#u1481 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:64px;
  width:168px;
  height:30px;
  display:flex;
}
#u1481 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1481_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1482_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:159px;
  height:30px;
}
#u1482 {
  border-width:0px;
  position:absolute;
  left:231px;
  top:64px;
  width:159px;
  height:30px;
  display:flex;
}
#u1482 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1482_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1483_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:148px;
  height:30px;
}
#u1483 {
  border-width:0px;
  position:absolute;
  left:390px;
  top:64px;
  width:148px;
  height:30px;
  display:flex;
}
#u1483 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1483_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1484_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:30px;
}
#u1484 {
  border-width:0px;
  position:absolute;
  left:538px;
  top:64px;
  width:106px;
  height:30px;
  display:flex;
}
#u1484 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1484_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1485_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u1485 {
  border-width:0px;
  position:absolute;
  left:644px;
  top:64px;
  width:116px;
  height:30px;
  display:flex;
}
#u1485 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1485_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1486_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:30px;
}
#u1486 {
  border-width:0px;
  position:absolute;
  left:760px;
  top:64px;
  width:125px;
  height:30px;
  display:flex;
}
#u1486 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1486_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1487_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:30px;
}
#u1487 {
  border-width:0px;
  position:absolute;
  left:885px;
  top:64px;
  width:193px;
  height:30px;
  display:flex;
}
#u1487 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1487_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1488_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u1488 {
  border-width:0px;
  position:absolute;
  left:1078px;
  top:64px;
  width:139px;
  height:30px;
  display:flex;
}
#u1488 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1488_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1489_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:124px;
  height:30px;
}
#u1489 {
  border-width:0px;
  position:absolute;
  left:1217px;
  top:64px;
  width:124px;
  height:30px;
  display:flex;
}
#u1489 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1489_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1490_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:30px;
}
#u1490 {
  border-width:0px;
  position:absolute;
  left:1341px;
  top:64px;
  width:122px;
  height:30px;
  display:flex;
}
#u1490 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1490_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1491_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:30px;
}
#u1491 {
  border-width:0px;
  position:absolute;
  left:1463px;
  top:64px;
  width:108px;
  height:30px;
  display:flex;
}
#u1491 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1491_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1492_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1492 {
  border-width:0px;
  position:absolute;
  left:1571px;
  top:64px;
  width:131px;
  height:30px;
  display:flex;
}
#u1492 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1492_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1493_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1493 {
  border-width:0px;
  position:absolute;
  left:1702px;
  top:64px;
  width:131px;
  height:30px;
  display:flex;
}
#u1493 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1493_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1494_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1494 {
  border-width:0px;
  position:absolute;
  left:1833px;
  top:64px;
  width:131px;
  height:30px;
  display:flex;
}
#u1494 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1494_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1495_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1495 {
  border-width:0px;
  position:absolute;
  left:1964px;
  top:64px;
  width:131px;
  height:30px;
  display:flex;
}
#u1495 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1495_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1496_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1496 {
  border-width:0px;
  position:absolute;
  left:2095px;
  top:64px;
  width:131px;
  height:30px;
  display:flex;
}
#u1496 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1496_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1497_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:30px;
}
#u1497 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:94px;
  width:63px;
  height:30px;
  display:flex;
}
#u1497 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1497_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1498_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:30px;
}
#u1498 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:94px;
  width:168px;
  height:30px;
  display:flex;
}
#u1498 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1498_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1499_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:159px;
  height:30px;
}
#u1499 {
  border-width:0px;
  position:absolute;
  left:231px;
  top:94px;
  width:159px;
  height:30px;
  display:flex;
}
#u1499 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1499_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1500_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:148px;
  height:30px;
}
#u1500 {
  border-width:0px;
  position:absolute;
  left:390px;
  top:94px;
  width:148px;
  height:30px;
  display:flex;
}
#u1500 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1500_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1501_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:30px;
}
#u1501 {
  border-width:0px;
  position:absolute;
  left:538px;
  top:94px;
  width:106px;
  height:30px;
  display:flex;
}
#u1501 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1501_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1502_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u1502 {
  border-width:0px;
  position:absolute;
  left:644px;
  top:94px;
  width:116px;
  height:30px;
  display:flex;
}
#u1502 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1502_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1503_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:30px;
}
#u1503 {
  border-width:0px;
  position:absolute;
  left:760px;
  top:94px;
  width:125px;
  height:30px;
  display:flex;
}
#u1503 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1503_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1504_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:30px;
}
#u1504 {
  border-width:0px;
  position:absolute;
  left:885px;
  top:94px;
  width:193px;
  height:30px;
  display:flex;
}
#u1504 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1504_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1505_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u1505 {
  border-width:0px;
  position:absolute;
  left:1078px;
  top:94px;
  width:139px;
  height:30px;
  display:flex;
}
#u1505 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1505_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1506_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:124px;
  height:30px;
}
#u1506 {
  border-width:0px;
  position:absolute;
  left:1217px;
  top:94px;
  width:124px;
  height:30px;
  display:flex;
}
#u1506 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1506_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1507_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:30px;
}
#u1507 {
  border-width:0px;
  position:absolute;
  left:1341px;
  top:94px;
  width:122px;
  height:30px;
  display:flex;
}
#u1507 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1507_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1508_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:30px;
}
#u1508 {
  border-width:0px;
  position:absolute;
  left:1463px;
  top:94px;
  width:108px;
  height:30px;
  display:flex;
}
#u1508 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1508_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1509_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1509 {
  border-width:0px;
  position:absolute;
  left:1571px;
  top:94px;
  width:131px;
  height:30px;
  display:flex;
}
#u1509 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1509_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1510_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1510 {
  border-width:0px;
  position:absolute;
  left:1702px;
  top:94px;
  width:131px;
  height:30px;
  display:flex;
}
#u1510 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1510_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1511_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1511 {
  border-width:0px;
  position:absolute;
  left:1833px;
  top:94px;
  width:131px;
  height:30px;
  display:flex;
}
#u1511 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1511_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1512_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1512 {
  border-width:0px;
  position:absolute;
  left:1964px;
  top:94px;
  width:131px;
  height:30px;
  display:flex;
}
#u1512 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1512_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1513_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1513 {
  border-width:0px;
  position:absolute;
  left:2095px;
  top:94px;
  width:131px;
  height:30px;
  display:flex;
}
#u1513 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1513_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1514_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:30px;
}
#u1514 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:124px;
  width:63px;
  height:30px;
  display:flex;
}
#u1514 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1514_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1515_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:30px;
}
#u1515 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:124px;
  width:168px;
  height:30px;
  display:flex;
}
#u1515 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1515_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1516_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:159px;
  height:30px;
}
#u1516 {
  border-width:0px;
  position:absolute;
  left:231px;
  top:124px;
  width:159px;
  height:30px;
  display:flex;
}
#u1516 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1516_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1517_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:148px;
  height:30px;
}
#u1517 {
  border-width:0px;
  position:absolute;
  left:390px;
  top:124px;
  width:148px;
  height:30px;
  display:flex;
}
#u1517 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1517_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1518_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:30px;
}
#u1518 {
  border-width:0px;
  position:absolute;
  left:538px;
  top:124px;
  width:106px;
  height:30px;
  display:flex;
}
#u1518 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1518_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1519_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u1519 {
  border-width:0px;
  position:absolute;
  left:644px;
  top:124px;
  width:116px;
  height:30px;
  display:flex;
}
#u1519 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1519_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1520_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:30px;
}
#u1520 {
  border-width:0px;
  position:absolute;
  left:760px;
  top:124px;
  width:125px;
  height:30px;
  display:flex;
}
#u1520 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1520_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1521_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:30px;
}
#u1521 {
  border-width:0px;
  position:absolute;
  left:885px;
  top:124px;
  width:193px;
  height:30px;
  display:flex;
}
#u1521 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1521_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1522_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u1522 {
  border-width:0px;
  position:absolute;
  left:1078px;
  top:124px;
  width:139px;
  height:30px;
  display:flex;
}
#u1522 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1522_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1523_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:124px;
  height:30px;
}
#u1523 {
  border-width:0px;
  position:absolute;
  left:1217px;
  top:124px;
  width:124px;
  height:30px;
  display:flex;
}
#u1523 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1523_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1524_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:30px;
}
#u1524 {
  border-width:0px;
  position:absolute;
  left:1341px;
  top:124px;
  width:122px;
  height:30px;
  display:flex;
}
#u1524 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1524_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1525_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:30px;
}
#u1525 {
  border-width:0px;
  position:absolute;
  left:1463px;
  top:124px;
  width:108px;
  height:30px;
  display:flex;
}
#u1525 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1525_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1526_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1526 {
  border-width:0px;
  position:absolute;
  left:1571px;
  top:124px;
  width:131px;
  height:30px;
  display:flex;
}
#u1526 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1526_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1527_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1527 {
  border-width:0px;
  position:absolute;
  left:1702px;
  top:124px;
  width:131px;
  height:30px;
  display:flex;
}
#u1527 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1527_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1528_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1528 {
  border-width:0px;
  position:absolute;
  left:1833px;
  top:124px;
  width:131px;
  height:30px;
  display:flex;
}
#u1528 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1528_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1529_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1529 {
  border-width:0px;
  position:absolute;
  left:1964px;
  top:124px;
  width:131px;
  height:30px;
  display:flex;
}
#u1529 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1529_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1530_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1530 {
  border-width:0px;
  position:absolute;
  left:2095px;
  top:124px;
  width:131px;
  height:30px;
  display:flex;
}
#u1530 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1530_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1531_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:30px;
}
#u1531 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:154px;
  width:63px;
  height:30px;
  display:flex;
}
#u1531 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1531_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1532_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:30px;
}
#u1532 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:154px;
  width:168px;
  height:30px;
  display:flex;
}
#u1532 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1532_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1533_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:159px;
  height:30px;
}
#u1533 {
  border-width:0px;
  position:absolute;
  left:231px;
  top:154px;
  width:159px;
  height:30px;
  display:flex;
}
#u1533 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1533_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1534_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:148px;
  height:30px;
}
#u1534 {
  border-width:0px;
  position:absolute;
  left:390px;
  top:154px;
  width:148px;
  height:30px;
  display:flex;
}
#u1534 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1534_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1535_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:30px;
}
#u1535 {
  border-width:0px;
  position:absolute;
  left:538px;
  top:154px;
  width:106px;
  height:30px;
  display:flex;
}
#u1535 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1535_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1536_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u1536 {
  border-width:0px;
  position:absolute;
  left:644px;
  top:154px;
  width:116px;
  height:30px;
  display:flex;
}
#u1536 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1536_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1537_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:30px;
}
#u1537 {
  border-width:0px;
  position:absolute;
  left:760px;
  top:154px;
  width:125px;
  height:30px;
  display:flex;
}
#u1537 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1537_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1538_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:30px;
}
#u1538 {
  border-width:0px;
  position:absolute;
  left:885px;
  top:154px;
  width:193px;
  height:30px;
  display:flex;
}
#u1538 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1538_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1539_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u1539 {
  border-width:0px;
  position:absolute;
  left:1078px;
  top:154px;
  width:139px;
  height:30px;
  display:flex;
}
#u1539 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1539_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1540_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:124px;
  height:30px;
}
#u1540 {
  border-width:0px;
  position:absolute;
  left:1217px;
  top:154px;
  width:124px;
  height:30px;
  display:flex;
}
#u1540 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1540_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1541_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:30px;
}
#u1541 {
  border-width:0px;
  position:absolute;
  left:1341px;
  top:154px;
  width:122px;
  height:30px;
  display:flex;
}
#u1541 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1541_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1542_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:30px;
}
#u1542 {
  border-width:0px;
  position:absolute;
  left:1463px;
  top:154px;
  width:108px;
  height:30px;
  display:flex;
}
#u1542 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1542_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1543_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1543 {
  border-width:0px;
  position:absolute;
  left:1571px;
  top:154px;
  width:131px;
  height:30px;
  display:flex;
}
#u1543 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1543_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1544_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1544 {
  border-width:0px;
  position:absolute;
  left:1702px;
  top:154px;
  width:131px;
  height:30px;
  display:flex;
}
#u1544 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1544_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1545_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1545 {
  border-width:0px;
  position:absolute;
  left:1833px;
  top:154px;
  width:131px;
  height:30px;
  display:flex;
}
#u1545 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1545_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1546_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1546 {
  border-width:0px;
  position:absolute;
  left:1964px;
  top:154px;
  width:131px;
  height:30px;
  display:flex;
}
#u1546 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1546_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1547_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1547 {
  border-width:0px;
  position:absolute;
  left:2095px;
  top:154px;
  width:131px;
  height:30px;
  display:flex;
}
#u1547 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1547_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1548_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:30px;
}
#u1548 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:184px;
  width:63px;
  height:30px;
  display:flex;
}
#u1548 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1548_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1549_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:30px;
}
#u1549 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:184px;
  width:168px;
  height:30px;
  display:flex;
}
#u1549 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1549_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1550_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:159px;
  height:30px;
}
#u1550 {
  border-width:0px;
  position:absolute;
  left:231px;
  top:184px;
  width:159px;
  height:30px;
  display:flex;
}
#u1550 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1550_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1551_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:148px;
  height:30px;
}
#u1551 {
  border-width:0px;
  position:absolute;
  left:390px;
  top:184px;
  width:148px;
  height:30px;
  display:flex;
}
#u1551 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1551_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1552_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:30px;
}
#u1552 {
  border-width:0px;
  position:absolute;
  left:538px;
  top:184px;
  width:106px;
  height:30px;
  display:flex;
}
#u1552 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1552_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1553_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u1553 {
  border-width:0px;
  position:absolute;
  left:644px;
  top:184px;
  width:116px;
  height:30px;
  display:flex;
}
#u1553 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1553_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1554_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:30px;
}
#u1554 {
  border-width:0px;
  position:absolute;
  left:760px;
  top:184px;
  width:125px;
  height:30px;
  display:flex;
}
#u1554 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1554_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1555_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:30px;
}
#u1555 {
  border-width:0px;
  position:absolute;
  left:885px;
  top:184px;
  width:193px;
  height:30px;
  display:flex;
}
#u1555 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1555_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1556_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u1556 {
  border-width:0px;
  position:absolute;
  left:1078px;
  top:184px;
  width:139px;
  height:30px;
  display:flex;
}
#u1556 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1556_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1557_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:124px;
  height:30px;
}
#u1557 {
  border-width:0px;
  position:absolute;
  left:1217px;
  top:184px;
  width:124px;
  height:30px;
  display:flex;
}
#u1557 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1557_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1558_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:30px;
}
#u1558 {
  border-width:0px;
  position:absolute;
  left:1341px;
  top:184px;
  width:122px;
  height:30px;
  display:flex;
}
#u1558 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1558_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1559_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:30px;
}
#u1559 {
  border-width:0px;
  position:absolute;
  left:1463px;
  top:184px;
  width:108px;
  height:30px;
  display:flex;
}
#u1559 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1559_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1560_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1560 {
  border-width:0px;
  position:absolute;
  left:1571px;
  top:184px;
  width:131px;
  height:30px;
  display:flex;
}
#u1560 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1560_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1561_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1561 {
  border-width:0px;
  position:absolute;
  left:1702px;
  top:184px;
  width:131px;
  height:30px;
  display:flex;
}
#u1561 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1561_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1562_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1562 {
  border-width:0px;
  position:absolute;
  left:1833px;
  top:184px;
  width:131px;
  height:30px;
  display:flex;
}
#u1562 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1562_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1563_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1563 {
  border-width:0px;
  position:absolute;
  left:1964px;
  top:184px;
  width:131px;
  height:30px;
  display:flex;
}
#u1563 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1563_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1564_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1564 {
  border-width:0px;
  position:absolute;
  left:2095px;
  top:184px;
  width:131px;
  height:30px;
  display:flex;
}
#u1564 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1564_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1565_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:30px;
}
#u1565 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:214px;
  width:63px;
  height:30px;
  display:flex;
}
#u1565 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1565_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1566_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:30px;
}
#u1566 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:214px;
  width:168px;
  height:30px;
  display:flex;
}
#u1566 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1566_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1567_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:159px;
  height:30px;
}
#u1567 {
  border-width:0px;
  position:absolute;
  left:231px;
  top:214px;
  width:159px;
  height:30px;
  display:flex;
}
#u1567 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1567_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1568_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:148px;
  height:30px;
}
#u1568 {
  border-width:0px;
  position:absolute;
  left:390px;
  top:214px;
  width:148px;
  height:30px;
  display:flex;
}
#u1568 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1568_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1569_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:30px;
}
#u1569 {
  border-width:0px;
  position:absolute;
  left:538px;
  top:214px;
  width:106px;
  height:30px;
  display:flex;
}
#u1569 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1569_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1570_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u1570 {
  border-width:0px;
  position:absolute;
  left:644px;
  top:214px;
  width:116px;
  height:30px;
  display:flex;
}
#u1570 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1570_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1571_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:30px;
}
#u1571 {
  border-width:0px;
  position:absolute;
  left:760px;
  top:214px;
  width:125px;
  height:30px;
  display:flex;
}
#u1571 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1571_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1572_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:30px;
}
#u1572 {
  border-width:0px;
  position:absolute;
  left:885px;
  top:214px;
  width:193px;
  height:30px;
  display:flex;
}
#u1572 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1572_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1573_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u1573 {
  border-width:0px;
  position:absolute;
  left:1078px;
  top:214px;
  width:139px;
  height:30px;
  display:flex;
}
#u1573 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1573_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1574_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:124px;
  height:30px;
}
#u1574 {
  border-width:0px;
  position:absolute;
  left:1217px;
  top:214px;
  width:124px;
  height:30px;
  display:flex;
}
#u1574 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1574_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1575_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:30px;
}
#u1575 {
  border-width:0px;
  position:absolute;
  left:1341px;
  top:214px;
  width:122px;
  height:30px;
  display:flex;
}
#u1575 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1575_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1576_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:30px;
}
#u1576 {
  border-width:0px;
  position:absolute;
  left:1463px;
  top:214px;
  width:108px;
  height:30px;
  display:flex;
}
#u1576 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1576_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1577_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1577 {
  border-width:0px;
  position:absolute;
  left:1571px;
  top:214px;
  width:131px;
  height:30px;
  display:flex;
}
#u1577 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1577_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1578_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1578 {
  border-width:0px;
  position:absolute;
  left:1702px;
  top:214px;
  width:131px;
  height:30px;
  display:flex;
}
#u1578 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1578_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1579_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1579 {
  border-width:0px;
  position:absolute;
  left:1833px;
  top:214px;
  width:131px;
  height:30px;
  display:flex;
}
#u1579 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1579_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1580_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1580 {
  border-width:0px;
  position:absolute;
  left:1964px;
  top:214px;
  width:131px;
  height:30px;
  display:flex;
}
#u1580 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1580_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1581_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1581 {
  border-width:0px;
  position:absolute;
  left:2095px;
  top:214px;
  width:131px;
  height:30px;
  display:flex;
}
#u1581 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1581_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1582_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:30px;
}
#u1582 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:244px;
  width:63px;
  height:30px;
  display:flex;
}
#u1582 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1582_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1583_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:30px;
}
#u1583 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:244px;
  width:168px;
  height:30px;
  display:flex;
}
#u1583 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1583_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1584_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:159px;
  height:30px;
}
#u1584 {
  border-width:0px;
  position:absolute;
  left:231px;
  top:244px;
  width:159px;
  height:30px;
  display:flex;
}
#u1584 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1584_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1585_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:148px;
  height:30px;
}
#u1585 {
  border-width:0px;
  position:absolute;
  left:390px;
  top:244px;
  width:148px;
  height:30px;
  display:flex;
}
#u1585 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1585_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1586_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:30px;
}
#u1586 {
  border-width:0px;
  position:absolute;
  left:538px;
  top:244px;
  width:106px;
  height:30px;
  display:flex;
}
#u1586 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1586_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1587_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u1587 {
  border-width:0px;
  position:absolute;
  left:644px;
  top:244px;
  width:116px;
  height:30px;
  display:flex;
}
#u1587 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1587_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1588_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:30px;
}
#u1588 {
  border-width:0px;
  position:absolute;
  left:760px;
  top:244px;
  width:125px;
  height:30px;
  display:flex;
}
#u1588 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1588_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1589_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:30px;
}
#u1589 {
  border-width:0px;
  position:absolute;
  left:885px;
  top:244px;
  width:193px;
  height:30px;
  display:flex;
}
#u1589 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1589_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1590_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u1590 {
  border-width:0px;
  position:absolute;
  left:1078px;
  top:244px;
  width:139px;
  height:30px;
  display:flex;
}
#u1590 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1590_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1591_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:124px;
  height:30px;
}
#u1591 {
  border-width:0px;
  position:absolute;
  left:1217px;
  top:244px;
  width:124px;
  height:30px;
  display:flex;
}
#u1591 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1591_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1592_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:30px;
}
#u1592 {
  border-width:0px;
  position:absolute;
  left:1341px;
  top:244px;
  width:122px;
  height:30px;
  display:flex;
}
#u1592 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1592_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1593_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:30px;
}
#u1593 {
  border-width:0px;
  position:absolute;
  left:1463px;
  top:244px;
  width:108px;
  height:30px;
  display:flex;
}
#u1593 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1593_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1594_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1594 {
  border-width:0px;
  position:absolute;
  left:1571px;
  top:244px;
  width:131px;
  height:30px;
  display:flex;
}
#u1594 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1594_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1595_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1595 {
  border-width:0px;
  position:absolute;
  left:1702px;
  top:244px;
  width:131px;
  height:30px;
  display:flex;
}
#u1595 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1595_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1596_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1596 {
  border-width:0px;
  position:absolute;
  left:1833px;
  top:244px;
  width:131px;
  height:30px;
  display:flex;
}
#u1596 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1596_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1597_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1597 {
  border-width:0px;
  position:absolute;
  left:1964px;
  top:244px;
  width:131px;
  height:30px;
  display:flex;
}
#u1597 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1597_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1598_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1598 {
  border-width:0px;
  position:absolute;
  left:2095px;
  top:244px;
  width:131px;
  height:30px;
  display:flex;
}
#u1598 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1598_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1599_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:30px;
}
#u1599 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:274px;
  width:63px;
  height:30px;
  display:flex;
}
#u1599 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1599_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1600_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:30px;
}
#u1600 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:274px;
  width:168px;
  height:30px;
  display:flex;
}
#u1600 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1600_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1601_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:159px;
  height:30px;
}
#u1601 {
  border-width:0px;
  position:absolute;
  left:231px;
  top:274px;
  width:159px;
  height:30px;
  display:flex;
}
#u1601 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1601_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1602_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:148px;
  height:30px;
}
#u1602 {
  border-width:0px;
  position:absolute;
  left:390px;
  top:274px;
  width:148px;
  height:30px;
  display:flex;
}
#u1602 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1602_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1603_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:30px;
}
#u1603 {
  border-width:0px;
  position:absolute;
  left:538px;
  top:274px;
  width:106px;
  height:30px;
  display:flex;
}
#u1603 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1603_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1604_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u1604 {
  border-width:0px;
  position:absolute;
  left:644px;
  top:274px;
  width:116px;
  height:30px;
  display:flex;
}
#u1604 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1604_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1605_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:30px;
}
#u1605 {
  border-width:0px;
  position:absolute;
  left:760px;
  top:274px;
  width:125px;
  height:30px;
  display:flex;
}
#u1605 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1605_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1606_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:30px;
}
#u1606 {
  border-width:0px;
  position:absolute;
  left:885px;
  top:274px;
  width:193px;
  height:30px;
  display:flex;
}
#u1606 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1606_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1607_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u1607 {
  border-width:0px;
  position:absolute;
  left:1078px;
  top:274px;
  width:139px;
  height:30px;
  display:flex;
}
#u1607 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1607_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1608_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:124px;
  height:30px;
}
#u1608 {
  border-width:0px;
  position:absolute;
  left:1217px;
  top:274px;
  width:124px;
  height:30px;
  display:flex;
}
#u1608 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1608_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1609_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:30px;
}
#u1609 {
  border-width:0px;
  position:absolute;
  left:1341px;
  top:274px;
  width:122px;
  height:30px;
  display:flex;
}
#u1609 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1609_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1610_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:30px;
}
#u1610 {
  border-width:0px;
  position:absolute;
  left:1463px;
  top:274px;
  width:108px;
  height:30px;
  display:flex;
}
#u1610 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1610_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1611_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1611 {
  border-width:0px;
  position:absolute;
  left:1571px;
  top:274px;
  width:131px;
  height:30px;
  display:flex;
}
#u1611 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1611_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1612_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1612 {
  border-width:0px;
  position:absolute;
  left:1702px;
  top:274px;
  width:131px;
  height:30px;
  display:flex;
}
#u1612 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1612_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1613_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1613 {
  border-width:0px;
  position:absolute;
  left:1833px;
  top:274px;
  width:131px;
  height:30px;
  display:flex;
}
#u1613 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1613_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1614_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1614 {
  border-width:0px;
  position:absolute;
  left:1964px;
  top:274px;
  width:131px;
  height:30px;
  display:flex;
}
#u1614 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1614_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1615_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1615 {
  border-width:0px;
  position:absolute;
  left:2095px;
  top:274px;
  width:131px;
  height:30px;
  display:flex;
}
#u1615 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1615_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1616_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:30px;
}
#u1616 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:304px;
  width:63px;
  height:30px;
  display:flex;
}
#u1616 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1616_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1617_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:30px;
}
#u1617 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:304px;
  width:168px;
  height:30px;
  display:flex;
}
#u1617 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1617_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1618_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:159px;
  height:30px;
}
#u1618 {
  border-width:0px;
  position:absolute;
  left:231px;
  top:304px;
  width:159px;
  height:30px;
  display:flex;
}
#u1618 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1618_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1619_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:148px;
  height:30px;
}
#u1619 {
  border-width:0px;
  position:absolute;
  left:390px;
  top:304px;
  width:148px;
  height:30px;
  display:flex;
}
#u1619 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1619_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1620_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:30px;
}
#u1620 {
  border-width:0px;
  position:absolute;
  left:538px;
  top:304px;
  width:106px;
  height:30px;
  display:flex;
}
#u1620 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1620_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1621_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u1621 {
  border-width:0px;
  position:absolute;
  left:644px;
  top:304px;
  width:116px;
  height:30px;
  display:flex;
}
#u1621 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1621_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1622_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:30px;
}
#u1622 {
  border-width:0px;
  position:absolute;
  left:760px;
  top:304px;
  width:125px;
  height:30px;
  display:flex;
}
#u1622 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1622_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1623_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:30px;
}
#u1623 {
  border-width:0px;
  position:absolute;
  left:885px;
  top:304px;
  width:193px;
  height:30px;
  display:flex;
}
#u1623 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1623_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1624_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u1624 {
  border-width:0px;
  position:absolute;
  left:1078px;
  top:304px;
  width:139px;
  height:30px;
  display:flex;
}
#u1624 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1624_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1625_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:124px;
  height:30px;
}
#u1625 {
  border-width:0px;
  position:absolute;
  left:1217px;
  top:304px;
  width:124px;
  height:30px;
  display:flex;
}
#u1625 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1625_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1626_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:30px;
}
#u1626 {
  border-width:0px;
  position:absolute;
  left:1341px;
  top:304px;
  width:122px;
  height:30px;
  display:flex;
}
#u1626 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1626_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1627_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:30px;
}
#u1627 {
  border-width:0px;
  position:absolute;
  left:1463px;
  top:304px;
  width:108px;
  height:30px;
  display:flex;
}
#u1627 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1627_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1628_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1628 {
  border-width:0px;
  position:absolute;
  left:1571px;
  top:304px;
  width:131px;
  height:30px;
  display:flex;
}
#u1628 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1628_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1629_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1629 {
  border-width:0px;
  position:absolute;
  left:1702px;
  top:304px;
  width:131px;
  height:30px;
  display:flex;
}
#u1629 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1629_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1630_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1630 {
  border-width:0px;
  position:absolute;
  left:1833px;
  top:304px;
  width:131px;
  height:30px;
  display:flex;
}
#u1630 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1630_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1631_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1631 {
  border-width:0px;
  position:absolute;
  left:1964px;
  top:304px;
  width:131px;
  height:30px;
  display:flex;
}
#u1631 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1631_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1632_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u1632 {
  border-width:0px;
  position:absolute;
  left:2095px;
  top:304px;
  width:131px;
  height:30px;
  display:flex;
}
#u1632 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1632_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1633_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1633 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:569px;
  width:57px;
  height:16px;
  display:flex;
}
#u1633 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1633_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1634_input {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:22px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1634_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:22px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1634_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1634 {
  border-width:0px;
  position:absolute;
  left:97px;
  top:563px;
  width:80px;
  height:22px;
  display:flex;
}
#u1634 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1634_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:22px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1634.disabled {
}
.u1634_input_option {
}
#u1635_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1635 {
  border-width:0px;
  position:absolute;
  left:187px;
  top:569px;
  width:168px;
  height:16px;
  display:flex;
}
#u1635 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1635_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1636_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1636 {
  border-width:0px;
  position:absolute;
  left:365px;
  top:569px;
  width:28px;
  height:16px;
  display:flex;
}
#u1636 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1636_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1637_input {
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:22px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1637_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:22px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1637_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u1637 {
  border-width:0px;
  position:absolute;
  left:398px;
  top:563px;
  width:30px;
  height:22px;
  display:flex;
  text-align:center;
}
#u1637 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1637_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:22px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u1637.disabled {
}
#u1638_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1638 {
  border-width:0px;
  position:absolute;
  left:433px;
  top:569px;
  width:14px;
  height:16px;
  display:flex;
}
#u1638 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1638_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1639_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1639 {
  border-width:0px;
  position:absolute;
  left:2150px;
  top:256px;
  width:28px;
  height:16px;
  display:flex;
}
#u1639 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1639_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1640_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1640 {
  border-width:0px;
  position:absolute;
  left:2193px;
  top:256px;
  width:28px;
  height:16px;
  display:flex;
}
#u1640 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1640_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1641_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1641 {
  border-width:0px;
  position:absolute;
  left:541px;
  top:59px;
  width:56px;
  height:16px;
  display:flex;
}
#u1641 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1641_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1642_input {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1642_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1642_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u1642 {
  border-width:0px;
  position:absolute;
  left:602px;
  top:55px;
  width:120px;
  height:24px;
  display:flex;
  text-align:center;
}
#u1642 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1642_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u1642.disabled {
}
#u1643_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1643 {
  border-width:0px;
  position:absolute;
  left:1038px;
  top:59px;
  width:56px;
  height:16px;
  display:flex;
}
#u1643 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1643_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1644_input {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1644_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1644_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u1644 {
  border-width:0px;
  position:absolute;
  left:1104px;
  top:55px;
  width:120px;
  height:24px;
  display:flex;
  text-align:center;
}
#u1644 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1644_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u1644.disabled {
}
#u1645_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1645 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:178px;
  width:80px;
  height:30px;
  display:flex;
}
#u1645 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1645_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1646 {
  border-width:0px;
  position:absolute;
  left:330px;
  top:143px;
  width:700px;
  height:400px;
  visibility:hidden;
}
#u1646_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:700px;
  height:400px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1646_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1647_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:245px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1647 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:245px;
  display:flex;
}
#u1647 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1647_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1648_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1648 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:50px;
  display:flex;
}
#u1648 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1648_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1649_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1649 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:14px;
  width:90px;
  height:22px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1649 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1649_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1650_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:13px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1650 {
  border-width:0px;
  position:absolute;
  left:463px;
  top:14px;
  width:13px;
  height:22px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1650 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1650_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1651_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1651 {
  border-width:0px;
  position:absolute;
  left:297px;
  top:171px;
  width:80px;
  height:30px;
  display:flex;
}
#u1651 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1651_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1652_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1652 {
  border-width:0px;
  position:absolute;
  left:396px;
  top:171px;
  width:80px;
  height:30px;
  display:flex;
}
#u1652 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1652_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1653_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1653 {
  border-width:0px;
  position:absolute;
  left:58px;
  top:84px;
  width:140px;
  height:16px;
  display:flex;
}
#u1653 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1653_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1646_state1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:700px;
  height:400px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u1646_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1654_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:388px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1654 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:388px;
  display:flex;
}
#u1654 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1654_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1655_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1655 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:50px;
  display:flex;
}
#u1655 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1655_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1656_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1656 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:14px;
  width:36px;
  height:22px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1656 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1656_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1657_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:13px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1657 {
  border-width:0px;
  position:absolute;
  left:463px;
  top:14px;
  width:13px;
  height:22px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1657 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1657_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1658_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1658 {
  border-width:0px;
  position:absolute;
  left:276px;
  top:339px;
  width:80px;
  height:30px;
  display:flex;
}
#u1658 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1658_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1659_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1659 {
  border-width:0px;
  position:absolute;
  left:375px;
  top:339px;
  width:80px;
  height:30px;
  display:flex;
}
#u1659 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1659_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1660_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1660 {
  border-width:0px;
  position:absolute;
  left:66px;
  top:75px;
  width:62px;
  height:16px;
  display:flex;
}
#u1660 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1660_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1661_input {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1661_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1661_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1661 {
  border-width:0px;
  position:absolute;
  left:138px;
  top:70px;
  width:300px;
  height:26px;
  display:flex;
}
#u1661 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1661_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:26px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1661.disabled {
}
#u1662_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1662 {
  border-width:0px;
  position:absolute;
  left:408px;
  top:73px;
  width:20px;
  height:20px;
  display:flex;
}
#u1662 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1662_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1663_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1663 {
  border-width:0px;
  position:absolute;
  left:66px;
  top:116px;
  width:62px;
  height:16px;
  display:flex;
}
#u1663 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1663_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1664_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:170px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
}
#u1664 {
  border-width:0px;
  position:absolute;
  left:138px;
  top:116px;
  width:150px;
  height:170px;
  display:flex;
  font-size:20px;
}
#u1664 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1664_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1665_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1300px;
  height:134px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  font-size:14px;
  line-height:20px;
}
#u1665 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:615px;
  width:1300px;
  height:134px;
  display:flex;
  font-size:14px;
  line-height:20px;
}
#u1665 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u1665_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1666_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:726px;
  height:57px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#000000;
  line-height:19px;
}
#u1666 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:623px;
  width:726px;
  height:57px;
  display:flex;
  font-size:15px;
  color:#000000;
  line-height:19px;
}
#u1666 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1666_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
