package com.yi.controller.contract.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 合同列表查询VO（联合查询结果）
 */
@Data
@ApiModel(value = "ContractListVO", description = "合同列表查询VO")
public class ContractListVO {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    @ApiModelProperty(value = "我司主体ID")
    private Integer outCustomerCompanyId;

    @ApiModelProperty(value = "客户主体ID")
    private Long customerCompanyId;

    @ApiModelProperty(value = "客户主体名称（联查获取）")
    private String customerCompanyName;

    @ApiModelProperty(value = "合同状态")
    private Integer contractStatus;

    @ApiModelProperty(value = "归档状态")
    private Integer archiveStatus;

    @ApiModelProperty(value = "合同生效日期")
    private LocalDate effectiveDate;

    @ApiModelProperty(value = "合同失效日期")
    private LocalDate expiryDate;

    @ApiModelProperty(value = "作废原因")
    private String cancelReason;

    @ApiModelProperty(value = "备注信息")
    private String remark;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "最后修改人")
    private String lastModifiedBy;

    @ApiModelProperty(value = "最后修改时间")
    private LocalDateTime lastModifiedTime;

    @ApiModelProperty(value = "有效性")
    private Integer valid;
}
