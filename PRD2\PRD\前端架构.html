﻿<!DOCTYPE html>
<html>
  <head>
    <title>前端架构</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/前端架构/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/前端架构/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (图片 ) -->
      <div id="u152" class="ax_default _图像_">
        <img id="u152_img" class="img " src="images/前端架构/u152.png"/>
        <div id="u152_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u153" class="ax_default heading_3">
        <div id="u153_div" class=""></div>
        <div id="u153_text" class="text ">
          <p><span>运营平台</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u154" class="ax_default heading_3">
        <div id="u154_div" class=""></div>
        <div id="u154_text" class="text ">
          <p><span>仓储WMS</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u155" class="ax_default box_1">
        <div id="u155_div" class=""></div>
        <div id="u155_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u156" class="ax_default box_1">
        <div id="u156_div" class=""></div>
        <div id="u156_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u157" class="ax_default heading_3">
        <div id="u157_div" class=""></div>
        <div id="u157_text" class="text ">
          <p><span>物流TMS</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u158" class="ax_default heading_3">
        <div id="u158_div" class=""></div>
        <div id="u158_text" class="text ">
          <p><span>后台管理中心</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u159" class="ax_default box_21">
        <div id="u159_div" class=""></div>
        <div id="u159_text" class="text ">
          <p><span>菜单目录</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u160" class="ax_default box_21">
        <div id="u160_div" class=""></div>
        <div id="u160_text" class="text ">
          <p><span>菜单目录</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u161" class="ax_default box_21">
        <div id="u161_div" class=""></div>
        <div id="u161_text" class="text ">
          <p><span>菜单目录</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u162" class="ax_default box_21">
        <div id="u162_div" class=""></div>
        <div id="u162_text" class="text ">
          <p><span>菜单目录</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u163" class="ax_default box_21">
        <div id="u163_div" class=""></div>
        <div id="u163_text" class="text ">
          <p><span>菜单目录</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u164" class="ax_default box_21">
        <div id="u164_div" class=""></div>
        <div id="u164_text" class="text ">
          <p><span>菜单目录</span></p>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u165" class="ax_default _图像_">
        <img id="u165_img" class="img " src="images/前端架构/u165.png"/>
        <div id="u165_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u166" class="ax_default label">
        <div id="u166_div" class=""></div>
        <div id="u166_text" class="text ">
          <p><span>吴XXX</span></p>
        </div>
      </div>

      <!-- Unnamed (SVG) -->
      <div id="u167" class="ax_default image">
        <img id="u167_img" class="img " src="images/前端架构/u167.svg"/>
        <div id="u167_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 操作弹窗 (动态面板) -->
      <div id="u168" class="ax_default ax_default_hidden" data-label="操作弹窗" style="display:none; visibility: hidden">
        <div id="u168_state0" class="panel_state" data-label="密码修改" style="">
          <div id="u168_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u169" class="ax_default box_1">
              <div id="u169_div" class=""></div>
              <div id="u169_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u170" class="ax_default box_21">
              <div id="u170_div" class=""></div>
              <div id="u170_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u171" class="ax_default heading_3">
              <div id="u171_div" class=""></div>
              <div id="u171_text" class="text ">
                <p><span>密码修改</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u172" class="ax_default heading_3">
              <div id="u172_div" class=""></div>
              <div id="u172_text" class="text ">
                <p><span>X</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u173" class="ax_default primary_button">
              <div id="u173_div" class=""></div>
              <div id="u173_text" class="text ">
                <p><span>确认</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u174" class="ax_default label">
              <div id="u174_div" class=""></div>
              <div id="u174_text" class="text ">
                <p><span>*新密码</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u175" class="ax_default text_field">
              <div id="u175_div" class=""></div>
              <input id="u175_input" type="text" value="" class="u175_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u176" class="ax_default label">
              <div id="u176_div" class=""></div>
              <div id="u176_text" class="text ">
                <p><span>*再次确认新密码</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u177" class="ax_default text_field">
              <div id="u177_div" class=""></div>
              <input id="u177_input" type="text" value="" class="u177_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u178" class="ax_default label">
              <div id="u178_div" class=""></div>
              <div id="u178_text" class="text ">
                <p><span>*原密码</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u179" class="ax_default text_field">
              <div id="u179_div" class=""></div>
              <input id="u179_input" type="text" value="" class="u179_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u180" class="ax_default label">
              <div id="u180_div" class=""></div>
              <div id="u180_text" class="text ">
                <p><span>提示：新密码必须包含 大写字母，小写字母，数字，且位数在8-20位之间</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u181" class="ax_default label">
        <div id="u181_div" class=""></div>
        <div id="u181_text" class="text ">
          <p><span>修改密码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u182" class="ax_default heading_1">
        <div id="u182_div" class=""></div>
        <div id="u182_text" class="text ">
          <p><span>易炬科技</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u183" class="ax_default sticky_1">
        <div id="u183_div" class=""></div>
        <div id="u183_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u184" class="ax_default label">
        <div id="u184_div" class=""></div>
        <div id="u184_text" class="text ">
          <p><span>功能说明：<br>&nbsp; &nbsp; &nbsp; &nbsp; 1、科动赫后台管理系统的登录成功后的页面 ；</span></p><p><span>&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; 2、修改密码：右上角，点击【修改密码】按钮后，弹出修改密码弹窗；</span></p><p><span>&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; 1）修改密码共计三个字段需要填写，【原密码，新密码，请再次确认密码】，三个字段均为必填，文本框，不多于50字符；</span></p><p><span>&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; 2）点击【确认】按钮后，需对数据进行校验，原密码要正确，新密码两次要一致，且需要满足密码设定条件，必须包含 大写字母，小写字母，数字，且位数在8-20位之间；</span></p><p><span>&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; 3）校验通过后修改密码成功，关闭弹窗，给出吐司提示：修改成功。校验不通过时，需显示对应错误原因返回提示；（密码格式错误，</span></p>
        </div>
      </div>

      <!-- Unnamed (水滴形) -->
      <div id="u185" class="ax_default marker">
        <img id="u185_img" class="img " src="images/前端架构/u185.svg"/>
        <div id="u185_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
