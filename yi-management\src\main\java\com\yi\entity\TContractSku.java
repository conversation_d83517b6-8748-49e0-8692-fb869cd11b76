package com.yi.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合同SKU明细表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_contract_sku")
@ApiModel(value = "TContractSku对象", description = "合同SKU明细表")
public class TContractSku extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "合同ID")
    private Long contractId;

    @ApiModelProperty(value = "一级类目 1:循环托盘")
    private Integer firstCategory;

    @ApiModelProperty(value = "业务模式 1:静态租赁 2:租赁+流转 3:一口价")
    private Integer businessMode;
}
