package com.yi.mapper.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 云仓导出VO
 */
@Data
public class CloudWarehouseExportVO {

    @ExcelProperty("ID")
    private String id;

    @ExcelProperty("状态")
    private String enabledText;

    @ExcelProperty("仓库名称")
    private String warehouseName;

    @ExcelProperty("地址")
    private String address;

    @ExcelProperty("联系人")
    private String contactPerson;

    @ExcelProperty("联系方式")
    private String contactPhone;

    @ExcelProperty("类型")
    private String warehouseTypeText;

    @ExcelProperty("属性")
    private String warehouseAttributeText;

    @ExcelProperty("作业时间")
    private String workingHours;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("创建人")
    private String createdBy;

    @ExcelProperty("创建时间")
    private String createdTime;
}
