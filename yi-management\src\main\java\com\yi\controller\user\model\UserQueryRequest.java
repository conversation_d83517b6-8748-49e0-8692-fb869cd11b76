package com.yi.controller.user.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户查询请求
 */
@Data
@ApiModel(value = "UserQueryRequest", description = "用户查询请求")
public class UserQueryRequest {

    @ApiModelProperty(value = "当前页码", example = "1")
    private String current = "1";

    @ApiModelProperty(value = "每页大小", example = "10")
    private String size = "10";

    @ApiModelProperty(value = "用户名（模糊查询）")
    private String username;

    @ApiModelProperty(value = "真实姓名（模糊查询）")
    private String realName;

    @ApiModelProperty(value = "邮箱（模糊查询）")
    private String email;

    @ApiModelProperty(value = "手机号（模糊查询）")
    private String phone;

    @ApiModelProperty(value = "部门ID")
    private Long deptId;

    @ApiModelProperty(value = "状态：null-全部，1-启用，0-禁用")
    private Integer status;
}
