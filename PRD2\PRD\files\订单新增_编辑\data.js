﻿$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,_(g,h,i,_(j,k,l,k)),m,[],n,_(h,o),p,[q],r,_(s,t,u,v,g,w,x,_(),y,[],z,_(A,B,C,D,E,_(F,G,H,I),J,null,K,L,L,M,N,O,null,P,Q,R,S,T,U,V,Q,W,X,Y,_(F,G,H,Z),ba,Q,bb,X,bc,_(bd,be,bf,bg,bh,bg,bi,bg,bj,k,H,_(bk,bl,bm,bl,bn,bl,bo,bp)),i,_(j,k,l,k)),bq,_(),br,_(),bs,_(bt,[_(bu,bv,bw,h,bx,by,u,bz,bA,bB,bC,bD,z,_(i,_(j,bE,l,bF),A,bG,bH,_(bI,bJ,bK,bL)),bq,_(),bM,_(),bN,_(bO,bP),bQ,be),_(bu,bR,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(i,_(j,bT,l,bJ),A,bU,bH,_(bI,bJ,bK,bV),Y,_(F,G,H,bW)),bq,_(),bM,_(),bQ,be),_(bu,bX,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(i,_(j,bY,l,bZ),A,ca,bH,_(bI,cb,bK,cc)),bq,_(),bM,_(),bQ,be),_(bu,cd,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(i,_(j,bY,l,ce),A,cf,bH,_(bI,cg,bK,ch)),bq,_(),bM,_(),bQ,be),_(bu,ci,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(i,_(j,cj,l,ck),A,bU,bH,_(bI,cl,bK,ch),Y,_(F,G,H,cm)),bq,_(),bM,_(),bQ,be),_(bu,cn,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(i,_(j,co,l,bJ),A,cp,bH,_(bI,cq,bK,cr)),bq,_(),bM,_(),br,_(cs,_(ct,cu,cv,cw,cx,[_(cv,h,cy,h,cz,be,cA,cB,cC,[_(cD,cE,cv,cF,cG,cH,cI,_(h,_(h,cJ)),cK,_(cL,r,cM,bD),cN,cO)])])),cP,bD,bQ,be),_(bu,cQ,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(i,_(j,cR,l,ce),A,cf,bH,_(bI,cS,bK,cT)),bq,_(),bM,_(),bQ,be),_(bu,cU,bw,h,bx,cV,u,cW,bA,cW,bC,bD,z,_(i,_(j,cX,l,cY),A,cZ,da,_(db,_(A,dc)),bH,_(bI,dd,bK,de),Y,_(F,G,H,cm)),df,be,bq,_(),bM,_()),_(bu,dg,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(i,_(j,dh,l,ce),A,cf,bH,_(bI,di,bK,cT)),bq,_(),bM,_(),bQ,be),_(bu,dj,bw,h,bx,cV,u,cW,bA,cW,bC,bD,z,_(i,_(j,cX,l,cY),A,cZ,da,_(db,_(A,dc)),bH,_(bI,dk,bK,de),Y,_(F,G,H,cm)),df,be,bq,_(),bM,_()),_(bu,dl,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(i,_(j,dh,l,ce),A,cf,bH,_(bI,cg,bK,dm)),bq,_(),bM,_(),bQ,be),_(bu,dn,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(i,_(j,bT,l,dp),A,bU,bH,_(bI,dd,bK,dm),ba,dq,dr,ds,Y,_(F,G,H,cm)),bq,_(),bM,_(),bQ,be),_(bu,dt,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(du,_(F,G,H,dv,dw,bF),i,_(j,dh,l,ce),A,cf,bH,_(bI,cg,bK,dx)),bq,_(),bM,_(),bQ,be),_(bu,dy,bw,h,bx,dz,u,dA,bA,dA,bC,bD,z,_(i,_(j,dB,l,cY),da,_(dC,_(A,dD),db,_(A,dc)),A,dE,bH,_(bI,dF,bK,dG),E,_(F,G,H,dH)),df,be,bq,_(),bM,_(),dI,h),_(bu,dJ,bw,h,bx,cV,u,cW,bA,cW,bC,bD,z,_(i,_(j,bT,l,cY),A,cZ,da,_(db,_(A,dc)),bH,_(bI,dd,bK,dG),E,_(F,G,H,dH)),df,be,bq,_(),bM,_()),_(bu,dK,bw,h,bx,cV,u,cW,bA,cW,bC,bD,z,_(i,_(j,bT,l,cY),A,cZ,da,_(db,_(A,dc)),bH,_(bI,dL,bK,dG),E,_(F,G,H,dH)),df,be,bq,_(),bM,_()),_(bu,dM,bw,h,bx,cV,u,cW,bA,cW,bC,bD,z,_(i,_(j,bT,l,cY),A,cZ,da,_(db,_(A,dc)),bH,_(bI,dN,bK,dG),E,_(F,G,H,dH)),df,be,bq,_(),bM,_()),_(bu,dO,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(du,_(F,G,H,cm,dw,bF),i,_(j,dP,l,dQ),A,cf,bH,_(bI,dR,bK,dS),dr,dT),bq,_(),bM,_(),bQ,be),_(bu,dU,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(du,_(F,G,H,cm,dw,bF),i,_(j,dP,l,dQ),A,cf,bH,_(bI,dV,bK,dS),dr,dT),bq,_(),bM,_(),bQ,be),_(bu,dW,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(du,_(F,G,H,cm,dw,bF),i,_(j,dP,l,dQ),A,cf,bH,_(bI,dX,bK,dS),dr,dT),bq,_(),bM,_(),bQ,be),_(bu,dY,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(du,_(F,G,H,dv,dw,bF),i,_(j,dZ,l,ce),A,cf,bH,_(bI,ea,bK,eb)),bq,_(),bM,_(),bQ,be),_(bu,ec,bw,h,bx,dz,u,dA,bA,dA,bC,bD,z,_(du,_(F,G,H,I,dw,bF),i,_(j,cX,l,ed),da,_(dC,_(A,dD),db,_(A,dc)),A,dE,bH,_(bI,dd,bK,ee),E,_(F,G,H,dH)),df,be,bq,_(),bM,_(),dI,h),_(bu,ef,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(du,_(F,G,H,dv,dw,bF),i,_(j,dZ,l,ce),A,cf,bH,_(bI,eg,bK,eb)),bq,_(),bM,_(),bQ,be),_(bu,eh,bw,h,bx,ei,u,bz,bA,bz,bC,bD,z,_(i,_(j,bT,l,dp),A,ej,bH,_(bI,ek,bK,dm)),bq,_(),bM,_(),bN,_(bO,el),bQ,be),_(bu,em,bw,h,bx,en,u,eo,bA,eo,bC,bD,z,_(A,ep,i,_(j,eq,l,eq),bH,_(bI,er,bK,es),J,null),bq,_(),bM,_(),bN,_(bO,et)),_(bu,eu,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(i,_(j,dh,l,ce),A,cf,bH,_(bI,cg,bK,ev)),bq,_(),bM,_(),bQ,be),_(bu,ew,bw,h,bx,dz,u,dA,bA,dA,bC,bD,z,_(i,_(j,ex,l,cY),da,_(dC,_(A,dD),db,_(A,dc)),A,dE,bH,_(bI,ev,bK,cl),Y,_(F,G,H,cm)),df,be,bq,_(),bM,_(),dI,h),_(bu,ey,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(i,_(j,dh,l,ce),A,cf,bH,_(bI,cg,bK,ez)),bq,_(),bM,_(),bQ,be),_(bu,eA,bw,h,bx,cV,u,cW,bA,cW,bC,bD,z,_(i,_(j,cj,l,cY),A,cZ,da,_(db,_(A,dc)),bH,_(bI,dd,bK,eB),Y,_(F,G,H,cm)),df,be,bq,_(),bM,_()),_(bu,eC,bw,h,bx,dz,u,dA,bA,dA,bC,bD,z,_(i,_(j,cX,l,ed),da,_(dC,_(A,dD),db,_(A,dc)),A,dE,bH,_(bI,dk,bK,ee),E,_(F,G,H,dH)),df,be,bq,_(),bM,_(),dI,h),_(bu,eD,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(i,_(j,eE,l,ce),A,cf,bH,_(bI,cg,bK,eF)),bq,_(),bM,_(),bQ,be),_(bu,eG,bw,h,bx,cV,u,cW,bA,cW,bC,bD,z,_(i,_(j,cX,l,cY),A,cZ,da,_(db,_(A,dc)),bH,_(bI,dd,bK,eH),Y,_(F,G,H,cm)),df,be,bq,_(),bM,_()),_(bu,eI,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(i,_(j,dh,l,ce),A,cf,bH,_(bI,di,bK,eF)),bq,_(),bM,_(),bQ,be),_(bu,eJ,bw,h,bx,dz,u,dA,bA,dA,bC,bD,z,_(i,_(j,ex,l,cY),da,_(dC,_(A,dD),db,_(A,dc)),A,dE,bH,_(bI,eK,bK,eH),Y,_(F,G,H,cm)),df,be,bq,_(),bM,_(),dI,h),_(bu,eL,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(i,_(j,bE,l,eM),A,eN,bH,_(bI,eO,bK,eP),dr,eQ,eR,eS),bq,_(),bM,_(),bQ,be),_(bu,eT,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(du,_(F,G,H,dv,dw,bF),i,_(j,eU,l,eV),A,cf,bH,_(bI,eW,bK,eX),dr,eY,eR,eZ),bq,_(),bM,_(),bQ,be)])),fa,_(),fb,_(fc,_(fd,fe),ff,_(fd,fg),fh,_(fd,fi),fj,_(fd,fk),fl,_(fd,fm),fn,_(fd,fo),fp,_(fd,fq),fr,_(fd,fs),ft,_(fd,fu),fv,_(fd,fw),fx,_(fd,fy),fz,_(fd,fA),fB,_(fd,fC),fD,_(fd,fE),fF,_(fd,fG),fH,_(fd,fI),fJ,_(fd,fK),fL,_(fd,fM),fN,_(fd,fO),fP,_(fd,fQ),fR,_(fd,fS),fT,_(fd,fU),fV,_(fd,fW),fX,_(fd,fY),fZ,_(fd,ga),gb,_(fd,gc),gd,_(fd,ge),gf,_(fd,gg),gh,_(fd,gi),gj,_(fd,gk),gl,_(fd,gm),gn,_(fd,go),gp,_(fd,gq),gr,_(fd,gs),gt,_(fd,gu),gv,_(fd,gw)));}; 
var b="url",c="订单新增_编辑.html",d="generationDate",e=new Date(1753855218712.2),f="defaultAdaptiveView",g="name",h="",i="size",j="width",k=0,l="height",m="adaptiveViews",n="sketchKeys",o="s0",p="variables",q="OnLoadVariable",r="page",s="packageId",t="b03dddd3f1a5436180f29be66a55eb93",u="type",v="Axure:Page",w="订单新增/编辑",x="notes",y="annotations",z="style",A="baseStyle",B="627587b6038d43cca051c114ac41ad32",C="pageAlignment",D="center",E="fill",F="fillType",G="solid",H="color",I=0xFFFFFFFF,J="image",K="imageAlignment",L="near",M="imageRepeat",N="auto",O="favicon",P="sketchFactor",Q="0",R="colorStyle",S="appliedColor",T="fontName",U="Applied Font",V="borderWidth",W="borderVisibility",X="all",Y="borderFill",Z=0xFF797979,ba="cornerRadius",bb="cornerVisibility",bc="outerShadow",bd="on",be=false,bf="offsetX",bg=5,bh="offsetY",bi="blurRadius",bj="spread",bk="r",bl=0,bm="g",bn="b",bo="a",bp=0.349019607843137,bq="adaptiveStyles",br="interactionMap",bs="diagram",bt="objects",bu="id",bv="1628e1303a924a78bc0ff61eb15acb9f",bw="label",bx="friendlyType",by="线段",bz="vectorShape",bA="styleType",bB="horizontalLine",bC="visible",bD=true,bE=1300,bF=1,bG="0327e893a7994793993b54c636419b7c",bH="location",bI="x",bJ=30,bK="y",bL=37,bM="imageOverrides",bN="images",bO="normal~",bP="images/客户管理/u350.svg",bQ="generateCompound",bR="560b2d4f45724a0d87fed1563c1254aa",bS="矩形",bT=150,bU="005450b8c9ab4e72bffa6c0bac80828f",bV=7,bW=0xFFD7D7D7,bX="773c6ba88a7540f5bd303eb43d9d318f",bY=56,bZ=19,ca="4b88aa200ad64025ad561857a6779b03",cb=1274,cc=18,cd="bc1910c57245480da642e79e703d0689",ce=16,cf="df3da3fd8cfa4c4a81f05df7784209fe",cg=265,ch=380,ci="fedb7506e6f64b288ce31446071dbe8f",cj=739,ck=84,cl=326,cm=0xFFAAAAAA,cn="8dfa34995a1a475ca63286d348cb003a",co=200,cp="f9d2a29eec41403f99d04559928d6317",cq=602,cr=700,cs="onClick",ct="eventType",cu="Click时",cv="description",cw="单击时",cx="cases",cy="conditionString",cz="isNewIfGroup",cA="caseColorHex",cB="AB68FF",cC="actions",cD="action",cE="linkWindow",cF="打开&nbsp; 在 当前窗口",cG="displayName",cH="打开链接",cI="actionInfoDescriptions",cJ="打开  在 当前窗口",cK="target",cL="targetType",cM="includeVariables",cN="linkType",cO="current",cP="tabbable",cQ="253051792eda416c8a8d98d2d6412c60",cR=34,cS=293,cT=77,cU="0e4871a807254ba6aa15d0a2e5f6acc9",cV="下拉列表",cW="comboBox",cX=300,cY=24,cZ="********************************",da="stateStyles",db="disabled",dc="9bd0236217a94d89b0314c8c7fc75f16",dd=332,de=73,df="HideHintOnFocused",dg="4b3e2011aef9466ebb8f2c5923721c7a",dh=62,di=704,dj="039c61defd8e437b8e44434f1e9e4c97",dk=771,dl="3cca9fb1ed92495ebafd17de5d4b0862",dm=494,dn="6e0d66b00093432691f0ea42b16be4b4",dp=180,dq="10",dr="fontSize",ds="24px",dt="bb7454ca9ed042a386da74a9b747b904",du="foreGroundFill",dv=0xFF000000,dw="opacity",dx=226,dy="87522cf561b140719632e8ce997f288b",dz="文本框",dA="textBox",dB=259,dC="hint",dD="********************************",dE="2170b7f9af5c48fba2adcd540f2ba1a0",dF=812,dG=222,dH=0xFFF2F2F2,dI="placeholderText",dJ="f135ee5f785448c7bdf24ecc5386c27e",dK="782bb2bdc4bb46038f8cf0ff84ad0a0e",dL=492,dM="2fbc9c35e4a24bf0ae2d604f0698c853",dN=652,dO="f1221bb400ca40e89970ae30ce9fd6b6",dP=12,dQ=14,dR=448,dS=227,dT="12px",dU="e57cd2ddef0647599505a75f0a850c03",dV=612,dW="591ebbd557284ec5bc8e329fa7b21205",dX=772,dY="ebc4550ab47c44399e074f919706491e",dZ=48,ea=279,eb=176,ec="d0d3fdddac6b442392b38520281b752f",ed=26,ee=171,ef="446cb3e58e1d446e91959739e880437e",eg=718,eh="fdecf68357af426084c54acf82b228da",ei="占位符",ej="c50e74f669b24b37bd9c18da7326bccd",ek=512,el="images/订单新增_编辑/u1345.svg",em="9cd81e3c59824ef4899e3d9e3a79f66a",en="SVG",eo="imageBox",ep="********************************",eq=32,er=624,es=501,et="images/合同新增_编辑/u1019.svg",eu="2310b2edc62d429385e71b4ccf333332",ev=330,ew="c8716cfbd00a4796a25b999937eb5478",ex=302,ey="f9d495c2639b4162b7fec2f64381caee",ez=126,eA="e7d781a8d38b42a6afe98efeba4c331b",eB=122,eC="2d99236ffc2c40a6b8d11fd40c230fc7",eD="0482d81b0ea44ee3aed9cb443017c2f3",eE=63,eF=280,eG="4773a6ed8f5b41ea88ac1c84650a296c",eH=276,eI="665011e932bd4f278ad43e4e71da5db3",eJ="52f1f2b9a0c143ddaf432d6ab1b463ea",eK=769,eL="a4f60597a5a04604b65befde8da7ec76",eM=81,eN="3106573e48474c3281b6db181d1a931f",eO=46,eP=811,eQ="14px",eR="lineSpacing",eS="20px",eT="967cc1eeac684011b2f0590e18421fc0",eU=527,eV=38,eW=65,eX=819,eY="15px",eZ="19px",fa="masters",fb="objectPaths",fc="1628e1303a924a78bc0ff61eb15acb9f",fd="scriptId",fe="u1322",ff="560b2d4f45724a0d87fed1563c1254aa",fg="u1323",fh="773c6ba88a7540f5bd303eb43d9d318f",fi="u1324",fj="bc1910c57245480da642e79e703d0689",fk="u1325",fl="fedb7506e6f64b288ce31446071dbe8f",fm="u1326",fn="8dfa34995a1a475ca63286d348cb003a",fo="u1327",fp="253051792eda416c8a8d98d2d6412c60",fq="u1328",fr="0e4871a807254ba6aa15d0a2e5f6acc9",fs="u1329",ft="4b3e2011aef9466ebb8f2c5923721c7a",fu="u1330",fv="039c61defd8e437b8e44434f1e9e4c97",fw="u1331",fx="3cca9fb1ed92495ebafd17de5d4b0862",fy="u1332",fz="6e0d66b00093432691f0ea42b16be4b4",fA="u1333",fB="bb7454ca9ed042a386da74a9b747b904",fC="u1334",fD="87522cf561b140719632e8ce997f288b",fE="u1335",fF="f135ee5f785448c7bdf24ecc5386c27e",fG="u1336",fH="782bb2bdc4bb46038f8cf0ff84ad0a0e",fI="u1337",fJ="2fbc9c35e4a24bf0ae2d604f0698c853",fK="u1338",fL="f1221bb400ca40e89970ae30ce9fd6b6",fM="u1339",fN="e57cd2ddef0647599505a75f0a850c03",fO="u1340",fP="591ebbd557284ec5bc8e329fa7b21205",fQ="u1341",fR="ebc4550ab47c44399e074f919706491e",fS="u1342",fT="d0d3fdddac6b442392b38520281b752f",fU="u1343",fV="446cb3e58e1d446e91959739e880437e",fW="u1344",fX="fdecf68357af426084c54acf82b228da",fY="u1345",fZ="9cd81e3c59824ef4899e3d9e3a79f66a",ga="u1346",gb="2310b2edc62d429385e71b4ccf333332",gc="u1347",gd="c8716cfbd00a4796a25b999937eb5478",ge="u1348",gf="f9d495c2639b4162b7fec2f64381caee",gg="u1349",gh="e7d781a8d38b42a6afe98efeba4c331b",gi="u1350",gj="2d99236ffc2c40a6b8d11fd40c230fc7",gk="u1351",gl="0482d81b0ea44ee3aed9cb443017c2f3",gm="u1352",gn="4773a6ed8f5b41ea88ac1c84650a296c",go="u1353",gp="665011e932bd4f278ad43e4e71da5db3",gq="u1354",gr="52f1f2b9a0c143ddaf432d6ab1b463ea",gs="u1355",gt="a4f60597a5a04604b65befde8da7ec76",gu="u1356",gv="967cc1eeac684011b2f0590e18421fc0",gw="u1357";
return _creator();
})());