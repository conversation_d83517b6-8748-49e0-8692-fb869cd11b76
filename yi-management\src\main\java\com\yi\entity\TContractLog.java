package com.yi.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 合同操作日志表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_contract_log")
@ApiModel(value = "TContractLog对象", description = "合同操作日志表")
public class TContractLog extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "合同ID")
    private Long contractId;

    @ApiModelProperty(value = "操作动作：1-编辑合同，2-作废合同")
    private Integer action;

    @ApiModelProperty(value = "操作人")
    private String operator;

    @ApiModelProperty(value = "操作时间")
    private LocalDateTime operationTime;
}
