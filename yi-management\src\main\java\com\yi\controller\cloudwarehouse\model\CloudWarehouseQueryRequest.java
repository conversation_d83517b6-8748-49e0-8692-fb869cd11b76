package com.yi.controller.cloudwarehouse.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 云仓查询请求
 */
@Data
@ApiModel("云仓查询请求")
public class CloudWarehouseQueryRequest {

    @ApiModelProperty("页码")
    private Integer pageNum = 1;

    @ApiModelProperty("每页大小")
    private Integer pageSize = 10;

    @ApiModelProperty("启用状态：1-启用，0-禁用")
    private Integer enabled;

    @ApiModelProperty("仓库名称")
    private String warehouseName;

    @ApiModelProperty("地址（省市区或详细地址）")
    private String address;

    @ApiModelProperty("仓库类型：1-中心仓，2-卫星仓，3-虚拟仓")
    private Integer warehouseType;

    @ApiModelProperty("仓库属性：1-自有，2-第三方")
    private Integer warehouseAttribute;
}
