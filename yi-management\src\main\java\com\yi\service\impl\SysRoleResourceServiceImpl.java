package com.yi.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yi.entity.SysRoleResource;
import com.yi.mapper.SysRoleResourceMapper;
import com.yi.service.SysRoleResourceService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 角色资源关联表 服务实现类
 */
@Service
public class SysRoleResourceServiceImpl extends ServiceImpl<SysRoleResourceMapper, SysRoleResource> implements SysRoleResourceService {

    @Override
    public int deleteByRoleId(Long roleId) {
        return baseMapper.deleteByRoleId(roleId);
    }

    @Override
    public int deleteByResourceId(Long resourceId) {
        return baseMapper.deleteByResourceId(resourceId);
    }

    @Override
    public int batchInsert(List<SysRoleResource> roleResources) {
        return baseMapper.batchInsert(roleResources);
    }

    @Override
    public List<Long> selectResourceIdsByRoleId(Long roleId) {
        return baseMapper.selectResourceIdsByRoleId(roleId);
    }

    @Override
    public List<Long> selectRoleIdsByResourceId(Long resourceId) {
        return baseMapper.selectRoleIdsByResourceId(resourceId);
    }

    @Override
    public boolean existsByRoleIdAndResourceId(Long roleId, Long resourceId) {
        return baseMapper.existsByRoleIdAndResourceId(roleId, resourceId);
    }
}
