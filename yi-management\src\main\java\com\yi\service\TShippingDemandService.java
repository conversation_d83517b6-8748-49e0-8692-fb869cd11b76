package com.yi.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yi.controller.shippingdemand.model.*;
import com.yi.entity.TShippingOrder;
import com.yi.enums.SkuFirstCategoryEnum;
import com.yi.enums.ShippingDemandStatusEnum;
import com.yi.mapper.vo.ShippingDemandPageVO;
import com.yi.mapper.vo.ShippingDemandDetailVO;
import com.yi.entity.TShippingDemand;
import com.yi.mapper.TShippingDemandMapper;
import com.yi.utils.ExcelUtils;
import com.yi.utils.FormatUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 发货需求表 服务类
 */
@Slf4j
@Service
public class TShippingDemandService extends ServiceImpl<TShippingDemandMapper, TShippingDemand> {

    /**
     * 分页查询发货需求列表
     *
     * @param request 查询条件
     * @return 分页结果
     */
    public IPage<ShippingDemandPageResponse> getShippingDemandPageResponse(ShippingDemandQueryRequest request) {
        // 转换分页参数
        Integer current = FormatUtils.safeToInteger(request.getCurrent(), 1);
        Integer size = FormatUtils.safeToInteger(request.getSize(), 10);
        Page<ShippingDemandPageVO> page = new Page<>(current, size);

        IPage<ShippingDemandPageVO> pageResult = this.baseMapper.selectShippingDemandPage(page, request);

        // 转换为响应对象
        IPage<ShippingDemandPageResponse> responsePage = new Page<>();
        responsePage.setCurrent(pageResult.getCurrent());
        responsePage.setSize(pageResult.getSize());
        responsePage.setTotal(pageResult.getTotal());
        responsePage.setPages(pageResult.getPages());

        List<ShippingDemandPageResponse> responseList = pageResult.getRecords().stream()
                .map(this::convertToPageResponse)
                .collect(Collectors.toList());
        responsePage.setRecords(responseList);

        return responsePage;
    }

    /**
     * 根据ID查询发货需求详情
     *
     * @param id 需求ID
     * @return 需求详情
     */
    public ShippingDemandDetailResponse getShippingDemandDetailById(Long id) {
        // 使用联查获取详情信息
        ShippingDemandDetailVO detailVO = this.baseMapper.selectShippingDemandDetailById(id);
        if (detailVO == null || detailVO.getValid() == 0) {
            return null;
        }

        return convertToDetailResponse(detailVO);
    }

    /**
     * 根据发运订单更新发货需求单
     *
     * @param order 发运订单
     */
    public void updateShippingDemandByOrder(TShippingOrder order) {
        try {
            // 根据订单号查询发货需求单
            LambdaQueryWrapper<TShippingDemand> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TShippingDemand::getOrderNo, order.getOrderNo())
                    .eq(TShippingDemand::getValid, 1);
            TShippingDemand existingDemand = this.getOne(wrapper);

            if (existingDemand == null) {
                log.warn("未找到对应的发货需求单，订单号: {}", order.getOrderNo());
                return;
            }

            // 创建更新对象
            TShippingDemand updateDemand = new TShippingDemand();
            updateDemand.setId(existingDemand.getId());

            // 更新可变字段（与订单同步的字段）
            updateDemand.setCustomerCompanyId(order.getCustomerCompanyId());
            updateDemand.setWarehouseId(order.getWarehouseId());
            updateDemand.setFirstCategory(order.getFirstCategory());
            updateDemand.setSecondCategory(order.getSecondCategory());
            updateDemand.setDemandTime(order.getDemandTime());
            updateDemand.setDemandQuantity(order.getCount());
            updateDemand.setPendingQuantity(order.getCount());
            updateDemand.setRemark(order.getRemark());

            // 设置修改人和修改时间
            updateDemand.setLastModifiedBy(getCurrentUser());
            updateDemand.setLastModifiedTime(LocalDateTime.now());

            // 执行更新
            boolean success = this.updateById(updateDemand);

            if (success) {
                log.info("成功更新发货需求单，订单号: {}", order.getOrderNo());
            } else {
                log.error("更新发货需求单失败，订单号: {}", order.getOrderNo());
            }

        } catch (Exception e) {
            log.error("更新发货需求单失败，订单号: " + order.getOrderNo() + ", 错误: " + e.getMessage(), e);
            // 这里可以选择抛出异常或者记录错误日志
            // 根据业务需求决定是否影响订单更新
            throw new RuntimeException("更新发货需求单失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据发运订单取消发货需求单
     *
     * @param orderNo 订单号
     * @param reason 取消原因
     */
    public void cancelShippingDemandByOrderNo(String orderNo, String reason) {
        try {
            // 根据订单号查询发货需求单
            LambdaQueryWrapper<TShippingDemand> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TShippingDemand::getOrderNo, orderNo)
                    .eq(TShippingDemand::getValid, 1);
            TShippingDemand existingDemand = this.getOne(wrapper);

            if (existingDemand == null) {
                log.warn("未找到对应的发货需求单，订单号: {}", orderNo);
                return;
            }

            // 校验发货需求单状态：只有待发货状态才能取消
            if (!ShippingDemandStatusEnum.canCancel(existingDemand.getStatus())) {
                log.warn("发货需求单状态不允许取消，订单号: {}, 当前状态: {}", orderNo, existingDemand.getStatus());
                return;
            }

            // 创建更新对象
            TShippingDemand updateDemand = new TShippingDemand();
            updateDemand.setId(existingDemand.getId());

            // 更新状态为已取消
            updateDemand.setStatus(ShippingDemandStatusEnum.CANCELLED.getCode());

            // 设置修改人和修改时间
            updateDemand.setLastModifiedBy(getCurrentUser());
            updateDemand.setLastModifiedTime(LocalDateTime.now());

            // 执行更新
            boolean success = this.updateById(updateDemand);

            if (success) {
                log.info("成功取消发货需求单，订单号: {}", orderNo);
            } else {
                log.error("取消发货需求单失败，订单号: {}", orderNo);
            }

        } catch (Exception e) {
            log.error("取消发货需求单失败，订单号: " + orderNo + ", 错误: " + e.getMessage(), e);
            // 这里可以选择抛出异常或者记录错误日志
            // 根据业务需求决定是否影响订单取消
            throw new RuntimeException("取消发货需求单失败: " + e.getMessage(), e);
        }
    }






    /**
     * 转换为分页响应对象
     *
     * @param demandVO 需求VO
     * @return 分页响应对象
     */
    private ShippingDemandPageResponse convertToPageResponse(ShippingDemandPageVO demandVO) {
        ShippingDemandPageResponse response = new ShippingDemandPageResponse();

        response.setId(FormatUtils.safeToString(demandVO.getId()));
        response.setOrderNo(FormatUtils.safeString(demandVO.getOrderNo()));
        response.setStatus(FormatUtils.safeToString(demandVO.getStatus()));
        response.setStatusName(getStatusName(demandVO.getStatus()));
        response.setCustomerCompanyName(FormatUtils.safeString(demandVO.getCustomerCompanyName()));
        response.setWarehouseName(FormatUtils.safeString(demandVO.getWarehouseName()));
        response.setReceivingAddress(FormatUtils.safeString(demandVO.getReceivingAddress()));
        response.setReceiverName(FormatUtils.safeString(demandVO.getReceiverName()));

        // 产品名称：一级类目 + 二级类目
        String productName = getProductName(demandVO.getFirstCategory(), demandVO.getSecondCategory());
        response.setProductName(productName);

        response.setDemandQuantity(FormatUtils.safeToString(demandVO.getDemandQuantity()));
        response.setPendingQuantity(FormatUtils.safeToString(demandVO.getPendingQuantity()));
        response.setShippedQuantity(FormatUtils.safeToString(demandVO.getShippedQuantity()));
        response.setUnexecutedQuantity(FormatUtils.safeToString(demandVO.getUnexecutedQuantity()));
        response.setDemandTime(FormatUtils.formatDate(demandVO.getDemandTime()));
        response.setCreatedBy(FormatUtils.safeString(demandVO.getCreatedBy()));
        response.setCreatedTime(FormatUtils.formatDateTime(demandVO.getCreatedTime()));

        return response;
    }


    /**
     * 转换为详情响应对象
     *
     * @param detailVO 需求详情VO
     * @return 详情响应对象
     */
    private ShippingDemandDetailResponse convertToDetailResponse(ShippingDemandDetailVO detailVO) {
        ShippingDemandDetailResponse response = new ShippingDemandDetailResponse();

        response.setId(FormatUtils.safeToString(detailVO.getId()));
        response.setOrderNo(FormatUtils.safeString(detailVO.getOrderNo()));
        response.setStatus(FormatUtils.safeToString(detailVO.getStatus()));
        response.setStatusName(getStatusName(detailVO.getStatus()));
        response.setCustomerCompanyId(FormatUtils.safeToString(detailVO.getCustomerCompanyId()));
        response.setCustomerCompanyName(FormatUtils.safeString(detailVO.getCustomerCompanyName()));
        response.setWarehouseId(FormatUtils.safeToString(detailVO.getWarehouseId()));
        response.setWarehouseName(FormatUtils.safeString(detailVO.getWarehouseName()));
        response.setReceivingAddress(FormatUtils.safeString(detailVO.getReceivingAddress()));
        response.setReceiverName(FormatUtils.safeString(detailVO.getReceiverName()));
        response.setFirstCategory(FormatUtils.safeToString(detailVO.getFirstCategory()));
        response.setFirstCategoryName(SkuFirstCategoryEnum.getDescriptionByCode(detailVO.getFirstCategory()));
        response.setSecondCategory(FormatUtils.safeString(detailVO.getSecondCategory()));
        response.setDemandQuantity(FormatUtils.safeToString(detailVO.getDemandQuantity()));
        response.setPendingQuantity(FormatUtils.safeToString(detailVO.getPendingQuantity()));
        response.setShippedQuantity(FormatUtils.safeToString(detailVO.getShippedQuantity()));
        response.setUnexecutedQuantity(FormatUtils.safeToString(detailVO.getUnexecutedQuantity()));
        response.setDemandTime(FormatUtils.formatDate(detailVO.getDemandTime()));
        response.setRemark(FormatUtils.safeString(detailVO.getRemark()));
        response.setCreatedBy(FormatUtils.safeString(detailVO.getCreatedBy()));
        response.setCreatedTime(FormatUtils.formatDateTime(detailVO.getCreatedTime()));
        response.setLastModifiedBy(FormatUtils.safeString(detailVO.getLastModifiedBy()));
        response.setLastModifiedTime(FormatUtils.formatDateTime(detailVO.getLastModifiedTime()));

        return response;
    }


    /**
     * 获取产品名称
     */
    private String getProductName(Integer firstCategory, String secondCategory) {
        StringBuilder product = new StringBuilder();

        // 一级类目
        if (firstCategory != null) {
            product.append(SkuFirstCategoryEnum.getDescriptionByCode(firstCategory));
        }

        // 二级类目
        if (secondCategory != null && !secondCategory.trim().isEmpty()) {
            if (product.length() > 0) {
                product.append("-");
            }
            product.append(secondCategory);
        }

        return product.toString();
    }

    /**
     * 获取状态名称
     */
    private String getStatusName(Integer status) {
        return ShippingDemandStatusEnum.getDescriptionByCode(status);
    }

    /**
     * 导出发货需求列表
     *
     * @param request 查询条件
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    public void exportShippingDemandList(ShippingDemandQueryRequest request, HttpServletResponse response) throws IOException {
        // 使用统一的SQL联查直接获取所有符合条件的数据（不分页模式）
        List<ShippingDemandPageVO> dataList = this.baseMapper.selectShippingDemandList(request);

        // 转换为导出VO
        List<ShippingDemandExportVO> exportList = dataList.stream()
                .map(this::convertToExportVO)
                .collect(Collectors.toList());

        // 使用EasyExcel导出（文件名已包含时间戳）
        ExcelUtils.exportExcelWithTimestamp(response, "发货需求列表", "发货需求列表",
                ShippingDemandExportVO.class, exportList);
    }

    /**
     * 转换为导出VO
     *
     * @param demandVO 需求VO
     * @return 导出VO
     */
    private ShippingDemandExportVO convertToExportVO(ShippingDemandPageVO demandVO) {
        ShippingDemandExportVO exportVO = new ShippingDemandExportVO();

        exportVO.setOrderNo(FormatUtils.safeString(demandVO.getOrderNo()));
        exportVO.setStatus(getStatusName(demandVO.getStatus()));
        exportVO.setCustomerCompanyName(FormatUtils.safeString(demandVO.getCustomerCompanyName()));
        exportVO.setWarehouseName(FormatUtils.safeString(demandVO.getWarehouseName()));
        exportVO.setReceivingAddress(FormatUtils.safeString(demandVO.getReceivingAddress()));
        exportVO.setReceiverName(FormatUtils.safeString(demandVO.getReceiverName()));
        exportVO.setProduct(getProductName(demandVO.getFirstCategory(), demandVO.getSecondCategory()));
        exportVO.setDemandQuantity(FormatUtils.safeToString(demandVO.getDemandQuantity()));
        exportVO.setPendingQuantity(FormatUtils.safeToString(demandVO.getPendingQuantity()));
        exportVO.setShippedQuantity(FormatUtils.safeToString(demandVO.getShippedQuantity()));
        exportVO.setUnexecutedQuantity(FormatUtils.safeToString(demandVO.getUnexecutedQuantity()));
        exportVO.setDemandTime(FormatUtils.formatDate(demandVO.getDemandTime()));
        exportVO.setCreatedBy(FormatUtils.safeString(demandVO.getCreatedBy()));
        exportVO.setCreatedTime(FormatUtils.formatDateTime(demandVO.getCreatedTime()));

        return exportVO;
    }

    /**
     * 获取当前用户
     */
    private String getCurrentUser() {
        // TODO: 从安全上下文获取当前用户
        return "system";
    }
}
