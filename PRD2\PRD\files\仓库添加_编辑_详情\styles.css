﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-20px;
  width:1300px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u3980_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1301px;
  height:2px;
}
#u3980 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:50px;
  width:1300px;
  height:1px;
  display:flex;
}
#u3980 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3980_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3981_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:235px;
  height:30px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3981 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:21px;
  width:235px;
  height:30px;
  display:flex;
}
#u3981 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3981_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3982_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3982 {
  border-width:0px;
  position:absolute;
  left:1264px;
  top:32px;
  width:56px;
  height:19px;
  display:flex;
}
#u3982 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3982_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3983_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u3983 {
  border-width:0px;
  position:absolute;
  left:278px;
  top:108px;
  width:62px;
  height:16px;
  display:flex;
  color:#000000;
}
#u3983 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3983_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3984_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u3984 {
  border-width:0px;
  position:absolute;
  left:278px;
  top:159px;
  width:62px;
  height:16px;
  display:flex;
  color:#000000;
}
#u3984 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3984_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3985_input {
  position:absolute;
  left:0px;
  top:0px;
  width:700px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3985_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:700px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3985_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:700px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3985 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:154px;
  width:700px;
  height:26px;
  display:flex;
}
#u3985 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3985_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:700px;
  height:26px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3985.disabled {
}
#u3986_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u3986 {
  border-width:0px;
  position:absolute;
  left:278px;
  top:214px;
  width:62px;
  height:16px;
  display:flex;
  color:#000000;
}
#u3986 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3986_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3987_input {
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3987_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3987_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3987 {
  border-width:0px;
  position:absolute;
  left:830px;
  top:210px;
  width:220px;
  height:24px;
  display:flex;
}
#u3987 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3987_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3987.disabled {
}
#u3988_input {
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3988_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3988_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3988 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:210px;
  width:150px;
  height:24px;
  display:flex;
}
#u3988 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3988_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3988.disabled {
}
.u3988_input_option {
}
#u3989_input {
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3989_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3989_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3989 {
  border-width:0px;
  position:absolute;
  left:510px;
  top:210px;
  width:150px;
  height:24px;
  display:flex;
}
#u3989 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3989_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3989.disabled {
}
.u3989_input_option {
}
#u3990_input {
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3990_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3990_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3990 {
  border-width:0px;
  position:absolute;
  left:670px;
  top:210px;
  width:150px;
  height:24px;
  display:flex;
}
#u3990 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3990_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3990.disabled {
}
.u3990_input_option {
}
#u3991_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#AAAAAA;
}
#u3991 {
  border-width:0px;
  position:absolute;
  left:466px;
  top:215px;
  width:12px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#AAAAAA;
}
#u3991 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3991_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3992_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#AAAAAA;
}
#u3992 {
  border-width:0px;
  position:absolute;
  left:630px;
  top:215px;
  width:12px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#AAAAAA;
}
#u3992 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3992_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3993_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#AAAAAA;
}
#u3993 {
  border-width:0px;
  position:absolute;
  left:790px;
  top:215px;
  width:12px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#AAAAAA;
}
#u3993 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3993_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3994_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u3994 {
  border-width:0px;
  position:absolute;
  left:292px;
  top:269px;
  width:48px;
  height:16px;
  display:flex;
  color:#000000;
}
#u3994 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3994_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3995_input {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3995_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3995_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3995 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:264px;
  width:200px;
  height:26px;
  display:flex;
}
#u3995 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3995_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:26px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3995.disabled {
}
#u3996_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u3996 {
  border-width:0px;
  position:absolute;
  left:792px;
  top:269px;
  width:48px;
  height:16px;
  display:flex;
  color:#000000;
}
#u3996 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3996_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3997_input {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3997_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3997_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3997 {
  border-width:0px;
  position:absolute;
  left:850px;
  top:264px;
  width:200px;
  height:26px;
  display:flex;
}
#u3997 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3997_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:26px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3997.disabled {
}
#u3998_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u3998 {
  border-width:0px;
  position:absolute;
  left:600px;
  top:486px;
  width:140px;
  height:40px;
  display:flex;
  font-size:16px;
}
#u3998 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3998_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3999_input {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3999_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3999_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3999 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:103px;
  width:200px;
  height:26px;
  display:flex;
}
#u3999 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3999_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:26px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3999.disabled {
}
.u3999_input_option {
}
#u4000_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u4000 {
  border-width:0px;
  position:absolute;
  left:778px;
  top:108px;
  width:62px;
  height:16px;
  display:flex;
  color:#000000;
}
#u4000 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4000_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4001_input {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4001_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4001_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4001 {
  border-width:0px;
  position:absolute;
  left:850px;
  top:103px;
  width:200px;
  height:26px;
  display:flex;
}
#u4001 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4001_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:26px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4001.disabled {
}
.u4001_input_option {
}
#u4002_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u4002 {
  border-width:0px;
  position:absolute;
  left:278px;
  top:320px;
  width:62px;
  height:16px;
  display:flex;
  color:#000000;
}
#u4002 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4002_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4003_input {
  position:absolute;
  left:0px;
  top:0px;
  width:700px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4003_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:700px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4003_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:700px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4003 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:315px;
  width:700px;
  height:26px;
  display:flex;
}
#u4003 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4003_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:700px;
  height:26px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4003.disabled {
}
#u4004_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u4004 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:366px;
  width:28px;
  height:16px;
  display:flex;
  color:#000000;
}
#u4004 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4004_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4005_input {
  position:absolute;
  left:0px;
  top:0px;
  width:700px;
  height:79px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4005_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:700px;
  height:79px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4005_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:700px;
  height:79px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4005 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:366px;
  width:700px;
  height:79px;
  display:flex;
}
#u4005 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4005_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:700px;
  height:79px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4005.disabled {
}
