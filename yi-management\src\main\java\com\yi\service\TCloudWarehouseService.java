package com.yi.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yi.controller.cloudwarehouse.model.*;
import com.yi.entity.TCloudWarehouse;
import com.yi.enums.EnabledStatusEnum;
import com.yi.enums.ValidStatusEnum;
import com.yi.enums.WarehouseAttributeEnum;
import com.yi.enums.WarehouseTypeEnum;
import com.yi.mapper.TCloudWarehouseMapper;
import com.yi.mapper.vo.CloudWarehouseExportVO;
import com.yi.mapper.vo.CloudWarehousePageVO;
import com.yi.utils.ExcelUtils;
import com.yi.utils.FormatUtils;
import com.yi.utils.UserContextUtils;
import com.yi.configuration.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 云仓表 服务实现类
 */
@Slf4j
@Service
public class TCloudWarehouseService extends ServiceImpl<TCloudWarehouseMapper, TCloudWarehouse> {

    /**
     * 分页查询云仓列表
     *
     * @param request 查询条件
     * @return 分页结果
     */
    public IPage<CloudWarehousePageResponse> getCloudWarehousePageResponse(CloudWarehouseQueryRequest request) {
        Page<CloudWarehousePageVO> page = new Page<>(request.getPageNum(), request.getPageSize());
        IPage<CloudWarehousePageVO> pageResult = this.baseMapper.selectCloudWarehousePage(page, request);
        
        // 转换为响应对象
        List<CloudWarehousePageResponse> responseList = pageResult.getRecords().stream()
                .map(this::convertToPageResponse)
                .collect(Collectors.toList());
        
        Page<CloudWarehousePageResponse> responsePage = new Page<>(request.getPageNum(), request.getPageSize());
        responsePage.setRecords(responseList);
        responsePage.setTotal(pageResult.getTotal());
        responsePage.setPages(pageResult.getPages());
        
        return responsePage;
    }

    /**
     * 导出云仓列表
     *
     * @param request 查询条件
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    public void exportCloudWarehouseList(CloudWarehouseQueryRequest request, HttpServletResponse response) throws IOException {
        // 查询所有数据（不分页）
        Page<CloudWarehousePageVO> page = new Page<>(1, Integer.MAX_VALUE);
        IPage<CloudWarehousePageVO> pageResult = this.baseMapper.selectCloudWarehousePage(page, request);
        
        // 转换为导出对象
        List<CloudWarehouseExportVO> exportList = pageResult.getRecords().stream()
                .map(this::convertToExportVO)
                .collect(Collectors.toList());
        
        // 导出Excel
        ExcelUtils.exportExcel(response, "云仓列表", "云仓列表", CloudWarehouseExportVO.class, exportList);
    }

    /**
     * 新增云仓
     *
     * @param request 新增请求
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean addCloudWarehouse(CloudWarehouseSaveRequest request) {
        // 校验仓库名称是否重复
        if (isWarehouseNameExists(request.getWarehouseName(), null)) {
            throw new BizException(400, "仓库名称已存在");
        }
        
        TCloudWarehouse cloudWarehouse = new TCloudWarehouse();
        convertRequestToEntity(request, cloudWarehouse);
        cloudWarehouse.setEnabled(EnabledStatusEnum.ENABLED.getKey());
        cloudWarehouse.setValid(ValidStatusEnum.VALID.getKey());
        cloudWarehouse.setCreatedBy(UserContextUtils.getCurrentUser());
        cloudWarehouse.setCreatedTime(LocalDateTime.now());
        cloudWarehouse.setLastModifiedBy(UserContextUtils.getCurrentUser());
        cloudWarehouse.setLastModifiedTime(LocalDateTime.now());
        
        return this.save(cloudWarehouse);
    }

    /**
     * 更新云仓
     *
     * @param request 更新请求
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCloudWarehouse(CloudWarehouseSaveRequest request) {
        if (!StringUtils.hasText(request.getId())) {
            throw new BizException(400, "云仓ID不能为空");
        }
        
        Long id = Long.valueOf(request.getId());
        TCloudWarehouse existingWarehouse = this.getById(id);
        if (existingWarehouse == null || !ValidStatusEnum.VALID.getKey().equals(existingWarehouse.getValid())) {
            throw new BizException(400, "云仓不存在");
        }

        // 校验仓库名称是否重复（排除自己）
        if (isWarehouseNameExists(request.getWarehouseName(), id)) {
            throw new BizException(400, "仓库名称已存在");
        }
        
        convertRequestToEntity(request, existingWarehouse);
        existingWarehouse.setLastModifiedBy(UserContextUtils.getCurrentUser());
        existingWarehouse.setLastModifiedTime(LocalDateTime.now());
        
        return this.updateById(existingWarehouse);
    }

    /**
     * 更新云仓状态
     *
     * @param id 云仓ID
     * @param enabled 启用状态
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCloudWarehouseStatus(Long id, Integer enabled) {
        TCloudWarehouse cloudWarehouse = this.getById(id);
        if (cloudWarehouse == null || !ValidStatusEnum.VALID.getKey().equals(cloudWarehouse.getValid())) {
            throw new BizException(400, "云仓不存在");
        }
        
        cloudWarehouse.setEnabled(enabled);
        cloudWarehouse.setLastModifiedBy(UserContextUtils.getCurrentUser());
        cloudWarehouse.setLastModifiedTime(LocalDateTime.now());
        
        return this.updateById(cloudWarehouse);
    }

    /**
     * 根据ID获取云仓详情
     *
     * @param id 云仓ID
     * @return 云仓详情
     */
    public CloudWarehouseDetailResponse getCloudWarehouseDetail(Long id) {
        TCloudWarehouse cloudWarehouse = this.getById(id);
        if (cloudWarehouse == null || !ValidStatusEnum.VALID.getKey().equals(cloudWarehouse.getValid())) {
            throw new BizException(400, "云仓不存在");
        }

        CloudWarehouseDetailResponse response = new CloudWarehouseDetailResponse();
        response.setId(cloudWarehouse.getId().toString());
        response.setWarehouseTypeText(WarehouseTypeEnum.getValueByKey(cloudWarehouse.getWarehouseType()));
        response.setWarehouseAttributeText(WarehouseAttributeEnum.getValueByKey(cloudWarehouse.getWarehouseAttribute()));
        response.setWarehouseName(cloudWarehouse.getWarehouseName());
        response.setProvinceName(cloudWarehouse.getProvinceName());
        response.setCityName(cloudWarehouse.getCityName());
        response.setAreaName(cloudWarehouse.getAreaName());
        response.setDetailedAddress(cloudWarehouse.getDetailedAddress());
        response.setContactPerson(cloudWarehouse.getContactPerson());
        response.setContactPhone(cloudWarehouse.getContactPhone());
        response.setWorkingHours(cloudWarehouse.getWorkingHours());
        response.setRemark(cloudWarehouse.getRemark());

        return response;
    }

    /**
     * 转换为分页响应对象
     *
     * @param vo 分页VO
     * @return 分页响应对象
     */
    private CloudWarehousePageResponse convertToPageResponse(CloudWarehousePageVO vo) {
        CloudWarehousePageResponse response = new CloudWarehousePageResponse();
        response.setId(vo.getId().toString());
        response.setEnabledText(EnabledStatusEnum.getValueByKey(vo.getEnabled()));
        response.setWarehouseName(vo.getWarehouseName());
        
        // 拼接地址，遵循地址格式规范：省市区和详细地址之间加空格
        StringBuilder addressBuilder = new StringBuilder();
        if (StringUtils.hasText(vo.getProvinceName())) {
            addressBuilder.append(vo.getProvinceName());
        }
        if (StringUtils.hasText(vo.getCityName())) {
            addressBuilder.append(vo.getCityName());
        }
        if (StringUtils.hasText(vo.getAreaName())) {
            addressBuilder.append(vo.getAreaName());
        }
        if (StringUtils.hasText(vo.getDetailedAddress())) {
            if (addressBuilder.length() > 0) {
                addressBuilder.append(" ");
            }
            addressBuilder.append(vo.getDetailedAddress());
        }
        response.setAddress(addressBuilder.toString());
        
        response.setContactPerson(vo.getContactPerson());
        response.setContactPhone(vo.getContactPhone());
        response.setWarehouseTypeText(WarehouseTypeEnum.getValueByKey(vo.getWarehouseType()));
        response.setWarehouseAttributeText(WarehouseAttributeEnum.getValueByKey(vo.getWarehouseAttribute()));
        response.setWorkingHours(vo.getWorkingHours());
        response.setRemark(vo.getRemark());
        response.setCreatedBy(vo.getCreatedBy());
        response.setCreatedTime(FormatUtils.formatDateTime(vo.getCreatedTime()));
        
        return response;
    }

    /**
     * 转换为导出对象
     *
     * @param vo 分页VO
     * @return 导出对象
     */
    private CloudWarehouseExportVO convertToExportVO(CloudWarehousePageVO vo) {
        CloudWarehouseExportVO exportVO = new CloudWarehouseExportVO();
        exportVO.setId(vo.getId().toString());
        exportVO.setEnabledText(EnabledStatusEnum.getValueByKey(vo.getEnabled()));
        exportVO.setWarehouseName(vo.getWarehouseName());
        
        // 拼接地址，遵循地址格式规范：省市区和详细地址之间加空格
        StringBuilder addressBuilder = new StringBuilder();
        if (StringUtils.hasText(vo.getProvinceName())) {
            addressBuilder.append(vo.getProvinceName());
        }
        if (StringUtils.hasText(vo.getCityName())) {
            addressBuilder.append(vo.getCityName());
        }
        if (StringUtils.hasText(vo.getAreaName())) {
            addressBuilder.append(vo.getAreaName());
        }
        if (StringUtils.hasText(vo.getDetailedAddress())) {
            if (addressBuilder.length() > 0) {
                addressBuilder.append(" ");
            }
            addressBuilder.append(vo.getDetailedAddress());
        }
        exportVO.setAddress(addressBuilder.toString());
        
        exportVO.setContactPerson(vo.getContactPerson());
        exportVO.setContactPhone(vo.getContactPhone());
        exportVO.setWarehouseTypeText(WarehouseTypeEnum.getValueByKey(vo.getWarehouseType()));
        exportVO.setWarehouseAttributeText(WarehouseAttributeEnum.getValueByKey(vo.getWarehouseAttribute()));
        exportVO.setWorkingHours(vo.getWorkingHours());
        exportVO.setRemark(vo.getRemark());
        exportVO.setCreatedBy(vo.getCreatedBy());
        exportVO.setCreatedTime(FormatUtils.formatDateTime(vo.getCreatedTime()));
        
        return exportVO;
    }

    /**
     * 请求转实体
     *
     * @param request 请求对象
     * @param entity 实体对象
     */
    private void convertRequestToEntity(CloudWarehouseSaveRequest request, TCloudWarehouse entity) {
        entity.setWarehouseType(request.getWarehouseType());
        entity.setWarehouseAttribute(request.getWarehouseAttribute());
        entity.setWarehouseName(request.getWarehouseName());
        entity.setProvinceId(request.getProvinceId());
        entity.setProvinceName(request.getProvinceName());
        entity.setCityId(request.getCityId());
        entity.setCityName(request.getCityName());
        entity.setAreaId(request.getAreaId());
        entity.setAreaName(request.getAreaName());
        entity.setDetailedAddress(request.getDetailedAddress());
        entity.setContactPerson(request.getContactPerson());
        entity.setContactPhone(request.getContactPhone());
        entity.setWorkingHours(request.getWorkingHours());
        entity.setRemark(request.getRemark());
    }

    /**
     * 检查仓库名称是否存在
     *
     * @param warehouseName 仓库名称
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    private boolean isWarehouseNameExists(String warehouseName, Long excludeId) {
        LambdaQueryWrapper<TCloudWarehouse> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TCloudWarehouse::getWarehouseName, warehouseName)
               .eq(TCloudWarehouse::getValid, ValidStatusEnum.VALID.getKey());
        if (excludeId != null) {
            wrapper.ne(TCloudWarehouse::getId, excludeId);
        }
        return this.count(wrapper) > 0;
    }
}
