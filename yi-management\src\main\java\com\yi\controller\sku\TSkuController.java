package com.yi.controller.sku;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yi.common.Result;
import com.yi.controller.sku.model.*;
import com.yi.enums.SkuFirstCategoryEnum;
import com.yi.service.TSkuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * SKU表 前端控制器
 */
@RestController
@RequestMapping("/api/sku")
@Api(tags = "SKU管理")
public class TSkuController {

    @Autowired
    private TSkuService skuService;

    @ApiOperation("分页查询SKU列表")
    @PostMapping("/page")
    public Result<IPage<SkuPageResponse>> getSkuPage(@RequestBody SkuQueryRequest request) {
        IPage<SkuPageResponse> page = skuService.getSkuPageResponse(request);
        return Result.success(page);
    }

    @ApiOperation("根据ID获取SKU详情")
    @GetMapping("/{id}")
    public Result<SkuDetailResponse> getSkuById(@ApiParam("SKU ID") @PathVariable Long id) {
        SkuDetailResponse response = skuService.getSkuDetailById(id);
        if (response == null) {
            return Result.failed("SKU不存在");
        }
        return Result.success(response);
    }

    @ApiOperation("新增SKU")
    @PostMapping("/add")
    public Result<Boolean> addSku(@Valid @RequestBody SkuRequest request) {
        try {
            boolean success = skuService.addSku(request);
            if (success) {
                return Result.success("新增成功", true);
            }
            return Result.failed("新增失败");
        } catch (RuntimeException e) {
            return Result.failed(e.getMessage());
        }
    }

    @ApiOperation("更新SKU")
    @PutMapping("/update")
    public Result<Boolean> updateSku(@Valid @RequestBody SkuRequest request) {
        if (request.getId() == null || request.getId().trim().isEmpty()) {
            return Result.validateFailed("SKU ID不能为空");
        }
        try {
            boolean success = skuService.updateSku(request);
            if (success) {
                return Result.success("更新成功", true);
            }
            return Result.failed("更新失败");
        } catch (RuntimeException e) {
            return Result.failed(e.getMessage());
        }
    }


    @ApiOperation("启用/禁用SKU")
    @PutMapping("/{id}/status")
    public Result<Boolean> updateSkuStatus(@ApiParam("SKU ID") @PathVariable Long id,
                                           @ApiParam("启用状态") @RequestParam Integer enabled) {
        boolean success = skuService.updateSkuStatus(id, enabled);
        if (success) {
            String message = enabled == 1 ? "启用成功" : "禁用成功";
            return Result.success(message, true);
        }
        return Result.failed("状态更新失败");
    }

    @ApiOperation("导出SKU列表")
    @PostMapping("/export")
    public void exportSkuList(@RequestBody SkuQueryRequest request,
                              HttpServletResponse response) throws IOException {
        skuService.exportSkuList(request, response);
    }

    @ApiOperation("获取一级类目枚举列表")
    @GetMapping("/first-category-enum")
    public Result<List<FirstCategoryEnumVO>> getFirstCategoryEnum() {
        List<FirstCategoryEnumVO> enumList = Arrays.stream(SkuFirstCategoryEnum.values())
                .map(category -> {
                    FirstCategoryEnumVO vo = new FirstCategoryEnumVO();
                    vo.setCode(category.getCode().toString());
                    vo.setName(category.getDescription());
                    return vo;
                })
                .collect(Collectors.toList());
        return Result.success(enumList);
    }

    @ApiOperation("查询SKU类目信息（用于仓库SKU类型下拉框）")
    @GetMapping("/category-list")
    public Result<List<SkuCategoryResponse>> getSkuCategoryList() {
        List<SkuCategoryResponse> categoryList = skuService.getSkuCategoryList();
        return Result.success(categoryList);
    }
}
