package com.yi.utils;

import java.util.ArrayList;
import java.util.List;

public class ListUtils {
    private ListUtils() {

    }

    public static Boolean isNotEmpty(List o) {
        return o != null && o.size() > 0;
    }

    public static Boolean isEmpty(List o) {
        return !isNotEmpty(o).booleanValue();
    }

    public static <T> List<List<T>> splitList(List<T> list, int blockSize) {
        List<List<T>> lists = new ArrayList<List<T>>();
        if(blockSize == 1){
            lists.add(list);
            return lists;
        }
        if (list != null && blockSize > 0) {
            int listSize = list.size();
            if(listSize<=blockSize){
                lists.add(list);
                return lists;
            }
            int batchSize = listSize / blockSize;
            int remain = listSize % blockSize;
            for (int i = 0; i < batchSize; i++) {
                int fromIndex = i * blockSize;
                int toIndex = fromIndex + blockSize;
                lists.add(list.subList(fromIndex, toIndex));
            }
            if(remain>0){
                lists.add(list.subList(listSize-remain, listSize));
            }
        }
        return lists;
    }


}
