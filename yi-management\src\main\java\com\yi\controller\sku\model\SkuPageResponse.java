package com.yi.controller.sku.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * SKU分页返回响应
 */
@Data
@ApiModel(value = "SkuPageResponse", description = "SKU分页返回响应")
public class SkuPageResponse {

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "一级类目")
    private String firstCategory;

    @ApiModelProperty(value = "二级类目")
    private String secondCategory;

    @ApiModelProperty(value = "三级类目")
    private String thirdCategory;

    @ApiModelProperty(value = "规格")
    private String specification;

    @ApiModelProperty(value = "重量(Kg)")
    private String weight;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private String createdTime;
}
