package com.yi.enums;

/**
 * 合同状态枚举
 */
public enum ContractStatusEnum {
    
    PENDING(1, "待生效"),
    ACTIVE(2, "生效中"),
    EXPIRED(3, "已到期"),
    CANCELLED(4, "已作废");
    
    private final Integer code;
    private final String name;
    
    ContractStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    /**
     * 根据code获取枚举
     */
    public static ContractStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ContractStatusEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
    
    /**
     * 根据code获取名称
     */
    public static String getNameByCode(Integer code) {
        ContractStatusEnum item = getByCode(code);
        return item != null ? item.getName() : "";
    }
}
