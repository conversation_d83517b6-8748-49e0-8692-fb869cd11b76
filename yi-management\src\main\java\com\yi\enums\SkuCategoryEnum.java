package com.yi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * SKU类目枚举
 */
@Getter
@AllArgsConstructor
public enum SkuCategoryEnum {

    /**
     * 共享托盘
     */
    SHARED_PALLET(1, "共享托盘");

    /**
     * 类目键
     */
    private final Integer key;

    /**
     * 类目值
     */
    private final String value;

    /**
     * 根据键获取枚举
     *
     * @param key 键
     * @return 枚举
     */
    public static SkuCategoryEnum getByKey(Integer key) {
        if (key == null) {
            return null;
        }
        for (SkuCategoryEnum category : SkuCategoryEnum.values()) {
            if (category.getKey().equals(key)) {
                return category;
            }
        }
        return null;
    }

    /**
     * 根据键获取值
     *
     * @param key 键
     * @return 值
     */
    public static String getValueByKey(Integer key) {
        SkuCategoryEnum category = getByKey(key);
        return category != null ? category.getValue() : null;
    }

    /**
     * 判断是否为共享托盘
     *
     * @param key 键
     * @return 是否为共享托盘
     */
    public static boolean isSharedPallet(Integer key) {
        return SHARED_PALLET.getKey().equals(key);
    }
}
