﻿<!DOCTYPE html>
<html>
  <head>
    <title>仓库添加/编辑/详情</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/仓库添加_编辑_详情/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/仓库添加_编辑_详情/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (线段) -->
      <div id="u3980" class="ax_default line1">
        <img id="u3980_img" class="img " src="images/客户管理/u350.svg"/>
        <div id="u3980_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3981" class="ax_default box_21">
        <div id="u3981_div" class=""></div>
        <div id="u3981_text" class="text ">
          <p><span>仓库新增/编辑/详情&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; X</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3982" class="ax_default link_button">
        <div id="u3982_div" class=""></div>
        <div id="u3982_text" class="text ">
          <p><span>刷新本页</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3983" class="ax_default label">
        <div id="u3983_div" class=""></div>
        <div id="u3983_text" class="text ">
          <p><span>*仓库类型</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3984" class="ax_default label">
        <div id="u3984_div" class=""></div>
        <div id="u3984_text" class="text ">
          <p><span>*仓库名称</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u3985" class="ax_default text_field">
        <div id="u3985_div" class=""></div>
        <input id="u3985_input" type="text" value="" class="u3985_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3986" class="ax_default label">
        <div id="u3986_div" class=""></div>
        <div id="u3986_text" class="text ">
          <p><span>*地址详情</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u3987" class="ax_default text_field">
        <div id="u3987_div" class=""></div>
        <input id="u3987_input" type="text" value="" class="u3987_input"/>
      </div>

      <!-- Unnamed (下拉列表) -->
      <div id="u3988" class="ax_default droplist">
        <div id="u3988_div" class=""></div>
        <select id="u3988_input" class="u3988_input">
        </select>
      </div>

      <!-- Unnamed (下拉列表) -->
      <div id="u3989" class="ax_default droplist">
        <div id="u3989_div" class=""></div>
        <select id="u3989_input" class="u3989_input">
        </select>
      </div>

      <!-- Unnamed (下拉列表) -->
      <div id="u3990" class="ax_default droplist">
        <div id="u3990_div" class=""></div>
        <select id="u3990_input" class="u3990_input">
        </select>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3991" class="ax_default label">
        <div id="u3991_div" class=""></div>
        <div id="u3991_text" class="text ">
          <p><span>省</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3992" class="ax_default label">
        <div id="u3992_div" class=""></div>
        <div id="u3992_text" class="text ">
          <p><span>市</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3993" class="ax_default label">
        <div id="u3993_div" class=""></div>
        <div id="u3993_text" class="text ">
          <p><span>区</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3994" class="ax_default label">
        <div id="u3994_div" class=""></div>
        <div id="u3994_text" class="text ">
          <p><span>*联系人</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u3995" class="ax_default text_field">
        <div id="u3995_div" class=""></div>
        <input id="u3995_input" type="text" value="" class="u3995_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3996" class="ax_default label">
        <div id="u3996_div" class=""></div>
        <div id="u3996_text" class="text ">
          <p><span>*手机号</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u3997" class="ax_default text_field">
        <div id="u3997_div" class=""></div>
        <input id="u3997_input" type="text" value="" class="u3997_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3998" class="ax_default primary_button">
        <div id="u3998_div" class=""></div>
        <div id="u3998_text" class="text ">
          <p><span>保存</span></p>
        </div>
      </div>

      <!-- Unnamed (下拉列表) -->
      <div id="u3999" class="ax_default droplist">
        <div id="u3999_div" class=""></div>
        <select id="u3999_input" class="u3999_input">
          <option class="u3999_input_option" value=" ">&nbsp;</option>
          <option class="u3999_input_option" value="中心仓">中心仓</option>
          <option class="u3999_input_option" value="卫星仓">卫星仓</option>
          <option class="u3999_input_option" value="虚拟仓">虚拟仓</option>
        </select>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4000" class="ax_default label">
        <div id="u4000_div" class=""></div>
        <div id="u4000_text" class="text ">
          <p><span>*仓库属性</span></p>
        </div>
      </div>

      <!-- Unnamed (下拉列表) -->
      <div id="u4001" class="ax_default droplist">
        <div id="u4001_div" class=""></div>
        <select id="u4001_input" class="u4001_input">
          <option class="u4001_input_option" value=" ">&nbsp;</option>
          <option class="u4001_input_option" value="自有">自有</option>
          <option class="u4001_input_option" value="第三方">第三方</option>
        </select>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4002" class="ax_default label">
        <div id="u4002_div" class=""></div>
        <div id="u4002_text" class="text ">
          <p><span>*作业时间</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u4003" class="ax_default text_field">
        <div id="u4003_div" class=""></div>
        <input id="u4003_input" type="text" value="" class="u4003_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4004" class="ax_default label">
        <div id="u4004_div" class=""></div>
        <div id="u4004_text" class="text ">
          <p><span>备注</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u4005" class="ax_default text_field">
        <div id="u4005_div" class=""></div>
        <input id="u4005_input" type="text" value="" class="u4005_input"/>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
