package com.yi.controller.user.io;

import lombok.Data;

import java.util.List;

@Data
public class WebUiLoginUserInfoModel {

	private Long id;

	private String userAccount;

	private String password;

	private String userName;

	private String userMobilePhone;

	private String remark;

	TokenModel tokenModel;

	private Integer enabled;

	private String userCode;

	private List<Long> roles;

	private Long organizationId;

	private Integer isAdmin;

	/** 是否是管理员 1:是，0：否  */
	private Integer isTheAdmin;

	private Long orgId;

	private String orgCode;

	private String userSalt;

	private Long supplierId;

	private String appId;
}
