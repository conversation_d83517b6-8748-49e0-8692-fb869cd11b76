# 发运订单更新接口限制说明

## 概述

为了保证发运订单数据的业务完整性和操作安全性，对发运订单更新接口(`updateShippingOrder`)添加了状态校验和字段编辑限制。

## 修改内容

### 1. 状态校验限制

**限制规则**：
- **只有待发货状态的订单才能编辑**
- 其他状态（发货中、已完结、已取消）的订单不允许编辑

**状态枚举**：
- `PENDING` - 待发货（✅ 可编辑）
- `SHIPPING` - 发货中（❌ 不可编辑）
- `COMPLETED` - 已完结（❌ 不可编辑）
- `CANCELLED` - 已取消（❌ 不可编辑）

### 2. 可编辑字段限制

**允许编辑的字段**（与新增时相同）：
- `contractCode` - 合同编号
- `customerCompanyId` - 客户公司ID
- `warehouseId` - 收货仓库ID
- `firstCategory` - 一级类目
- `secondCategory` - 二级类目
- `count` - 需求数量
- `demandTime` - 需求时间
- `remark` - 备注
- `attachmentUrls` - 附件列表

**不允许编辑的字段**：
- `orderNo` - 订单号（系统生成，不可修改）
- `status` - 订单状态（通过专门的状态更新接口修改）
- `shippedQuantity` - 发货数量（业务流程中自动更新）
- `receivedQuantity` - 签收数量（业务流程中自动更新）
- `cancelReason` - 取消原因（取消时设置）

## 实现逻辑

### 修改前的问题
```java
// 原有实现存在的问题：
// 1. 没有状态校验，任何状态都可以编辑
// 2. 允许编辑所有字段，包括不应该编辑的字段
// 3. 引用了不存在的completeReason字段
public boolean updateShippingOrder(ShippingOrderRequest orderRequest) {
    // 直接更新所有字段，没有状态和字段限制
    order.setStatus(FormatUtils.safeString(orderRequest.getStatus()));
    order.setShippedQuantity(FormatUtils.safeToInteger(orderRequest.getShippedQuantity()));
    order.setCompleteReason(FormatUtils.safeString(orderRequest.getCompleteReason())); // 字段不存在
    // ...
}
```

### 修改后的实现
```java
public boolean updateShippingOrder(ShippingOrderRequest orderRequest) {
    // 1. 获取现有订单信息
    TShippingOrder existingOrder = this.getById(orderId);
    if (existingOrder == null) {
        throw new RuntimeException("发运订单不存在");
    }

    // 2. 校验订单状态：只有待发货状态才能编辑
    if (!"PENDING".equals(existingOrder.getStatus())) {
        throw new RuntimeException("只有待发货状态的订单才能编辑");
    }

    // 3. 只更新允许编辑的字段（与新增时相同的字段）
    TShippingOrder order = new TShippingOrder();
    order.setId(orderId);
    order.setContractCode(FormatUtils.safeString(orderRequest.getContractCode()));
    order.setCustomerCompanyId(FormatUtils.safeToLong(orderRequest.getCustomerCompanyId()));
    // ... 其他允许编辑的字段

    // 4. 处理附件更新
    if (success && orderRequest.getAttachmentUrls() != null) {
        generalFileService.saveShippingOrderAttachments(orderId, orderRequest.getAttachmentUrls());
    }
}
```

## 接口行为

### 成功场景
**请求**：
```json
{
  "id": "1",
  "contractCode": "CT002",
  "customerCompanyId": "101",
  "warehouseId": "201",
  "firstCategory": "1",
  "secondCategory": "标准托盘",
  "count": "150",
  "demandTime": "2024-12-31",
  "remark": "更新后的备注",
  "attachmentUrls": ["file1.pdf", "file2.jpg"]
}
```

**响应**：
```json
{
  "code": 200,
  "message": "更新成功",
  "data": true
}
```

### 失败场景

#### 1. 订单不存在
```json
{
  "code": 500,
  "message": "发运订单不存在",
  "data": null
}
```

#### 2. 订单状态不允许编辑
```json
{
  "code": 500,
  "message": "只有待发货状态的订单才能编辑",
  "data": null
}
```

#### 3. 订单ID为空
```json
{
  "code": 500,
  "message": "订单ID不能为空",
  "data": null
}
```

## 业务流程

### 订单状态流转
```
待发货(PENDING) ──编辑──> 待发货(PENDING)  ✅ 允许编辑
     │
     ├──发货──> 发货中(SHIPPING)  ❌ 不允许编辑
     │
     ├──完结──> 已完结(COMPLETED)  ❌ 不允许编辑
     │
     └──取消──> 已取消(CANCELLED)  ❌ 不允许编辑
```

### 字段编辑权限
```
新增时可填写的字段 = 编辑时可修改的字段
- 合同编号 ✅
- 客户公司 ✅
- 收货仓库 ✅
- 产品类目 ✅
- 需求数量 ✅
- 需求时间 ✅
- 备注信息 ✅
- 附件文件 ✅

系统字段（不可编辑）：
- 订单号 ❌
- 订单状态 ❌
- 发货数量 ❌
- 签收数量 ❌
- 取消原因 ❌
```

## 测试验证

**测试文件**：`yi-management/src/test/java/com/yi/service/TShippingOrderUpdateTest.java`

**测试覆盖**：
- ✅ 正常更新（待发货状态）
- ❌ 订单不存在
- ❌ 非待发货状态编辑限制
- ❌ 订单ID为空
- ✅ 无附件更新
- ✅ 只更新允许编辑的字段
- ❌ 各种状态的编辑限制

## 影响评估

### 正面影响
1. **业务安全性**：防止已发货订单被意外修改
2. **数据完整性**：保护系统字段不被误操作
3. **操作规范性**：明确的编辑权限和状态限制
4. **代码健壮性**：完善的异常处理和校验逻辑

### 潜在影响
1. **API行为变更**：原本可以编辑的订单现在可能被限制
2. **前端适配**：需要根据订单状态控制编辑按钮的显示
3. **业务流程**：需要确保业务人员了解新的编辑限制

## 建议

### 前端适配
1. **状态判断**：根据订单状态显示/隐藏编辑按钮
2. **字段控制**：只显示允许编辑的字段
3. **错误处理**：妥善处理状态限制的错误信息

### 业务培训
1. **操作规范**：培训业务人员新的编辑限制规则
2. **状态理解**：明确各个状态下的操作权限
3. **异常处理**：指导如何处理编辑限制的情况

## 相关文件

- 服务实现：`yi-management/src/main/java/com/yi/service/TShippingOrderService.java`
- 控制器：`yi-management/src/main/java/com/yi/controller/shippingorder/TShippingOrderController.java`
- 请求模型：`yi-management/src/main/java/com/yi/controller/shippingorder/model/ShippingOrderRequest.java`
- 测试文件：`yi-management/src/test/java/com/yi/service/TShippingOrderUpdateTest.java`
- 实体类：`yi-management/src/main/java/com/yi/entity/TShippingOrder.java`
