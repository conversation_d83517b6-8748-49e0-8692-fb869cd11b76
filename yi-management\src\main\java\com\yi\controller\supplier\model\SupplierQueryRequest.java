package com.yi.controller.supplier.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 供应商查询请求
 */
@Data
@ApiModel(value = "SupplierQueryRequest", description = "供应商查询请求")
public class SupplierQueryRequest {

    @ApiModelProperty(value = "页码", example = "1")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "页大小", example = "10")
    private Integer pageSize = 10;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;
}
