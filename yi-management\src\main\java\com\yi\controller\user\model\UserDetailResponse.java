package com.yi.controller.user.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户详情响应
 */
@Data
@ApiModel(value = "UserDetailResponse", description = "用户详情响应")
public class UserDetailResponse {

    @ApiModelProperty(value = "用户ID")
    private Long id;

    @ApiModelProperty(value = "用户名")
    private String username;

    @ApiModelProperty(value = "真实姓名")
    private String realName;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "头像地址")
    private String avatar;

    @ApiModelProperty(value = "性别：0-女，1-男")
    private Integer gender;

    @ApiModelProperty(value = "性别描述")
    private String genderDesc;

    @ApiModelProperty(value = "生日")
    private LocalDate birthday;

    @ApiModelProperty(value = "部门ID")
    private Long deptId;

    @ApiModelProperty(value = "部门名称")
    private String deptName;

    @ApiModelProperty(value = "状态：0-禁用，1-启用")
    private Integer status;

    @ApiModelProperty(value = "状态描述")
    private String statusDesc;

    @ApiModelProperty(value = "最后登录IP")
    private String loginIp;

    @ApiModelProperty(value = "最后登录时间")
    private LocalDateTime loginTime;

    @ApiModelProperty(value = "角色ID列表")
    private List<Long> roleIds;

    @ApiModelProperty(value = "角色列表")
    private List<UserRoleInfo> roles;

    @ApiModelProperty(value = "权限列表")
    private List<String> permissions;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "最后修改人")
    private String lastModifiedBy;

    @ApiModelProperty(value = "最后修改时间")
    private LocalDateTime lastModifiedTime;

    /**
     * 用户角色信息
     */
    @Data
    @ApiModel(value = "UserRoleInfo", description = "用户角色信息")
    public static class UserRoleInfo {
        @ApiModelProperty(value = "角色ID")
        private Long roleId;

        @ApiModelProperty(value = "角色编码")
        private String roleCode;

        @ApiModelProperty(value = "角色名称")
        private String roleName;

        @ApiModelProperty(value = "角色描述")
        private String roleDesc;
    }
}
