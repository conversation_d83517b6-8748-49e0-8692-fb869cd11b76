﻿<!DOCTYPE html>
<html>
  <head>
    <title>确认出库</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/确认出库_1/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/确认出库_1/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (线段) -->
      <div id="u5191" class="ax_default line1">
        <img id="u5191_img" class="img " src="images/客户管理/u350.svg"/>
        <div id="u5191_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5192" class="ax_default box_1">
        <div id="u5192_div" class=""></div>
        <div id="u5192_text" class="text ">
          <p><span>确认出库&nbsp; &nbsp; X</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5193" class="ax_default link_button">
        <div id="u5193_div" class=""></div>
        <div id="u5193_text" class="text ">
          <p><span>刷新本页</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5194" class="ax_default box_21">
        <div id="u5194_div" class=""></div>
        <div id="u5194_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5195" class="ax_default heading_3">
        <div id="u5195_div" class=""></div>
        <div id="u5195_text" class="text ">
          <p><span>基本信息</span></p>
        </div>
      </div>

      <!-- Unnamed (表格) -->
      <div id="u5196" class="ax_default">

        <!-- Unnamed (单元格) -->
        <div id="u5197" class="ax_default table_cell">
          <img id="u5197_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u5197_text" class="text ">
            <p><span>状态</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u5198" class="ax_default table_cell">
          <img id="u5198_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u5198_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u5199" class="ax_default table_cell">
          <img id="u5199_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u5199_text" class="text ">
            <p><span>出库单号</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u5200" class="ax_default table_cell">
          <img id="u5200_img" class="img " src="images/确认发货/u2248.png"/>
          <div id="u5200_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u5201" class="ax_default table_cell">
          <img id="u5201_img" class="img " src="images/确认出库/u3015.png"/>
          <div id="u5201_text" class="text ">
            <p><span>配送方式</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u5202" class="ax_default table_cell">
          <img id="u5202_img" class="img " src="images/确认出库/u3015.png"/>
          <div id="u5202_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u5203" class="ax_default table_cell">
          <img id="u5203_img" class="img " src="images/确认出库/u3015.png"/>
          <div id="u5203_text" class="text ">
            <p><span>车牌号</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u5204" class="ax_default table_cell">
          <img id="u5204_img" class="img " src="images/确认出库/u3018.png"/>
          <div id="u5204_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u5205" class="ax_default table_cell">
          <img id="u5205_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u5205_text" class="text ">
            <p><span>出库仓库</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u5206" class="ax_default table_cell">
          <img id="u5206_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u5206_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u5207" class="ax_default table_cell">
          <img id="u5207_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u5207_text" class="text ">
            <p><span>仓库地址</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u5208" class="ax_default table_cell">
          <img id="u5208_img" class="img " src="images/确认发货/u2248.png"/>
          <div id="u5208_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u5209" class="ax_default table_cell">
          <img id="u5209_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u5209_text" class="text ">
            <p><span>产品类型</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u5210" class="ax_default table_cell">
          <img id="u5210_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u5210_text" class="text ">
            <p><span>循环托盘 1311-H</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u5211" class="ax_default table_cell">
          <img id="u5211_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u5211_text" class="text ">
            <p><span>计划出库数</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u5212" class="ax_default table_cell">
          <img id="u5212_img" class="img " src="images/确认发货/u2248.png"/>
          <div id="u5212_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u5213" class="ax_default table_cell">
          <img id="u5213_img" class="img " src="images/确认发货/u2265.png"/>
          <div id="u5213_text" class="text ">
            <p><span>待出库数</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u5214" class="ax_default table_cell">
          <img id="u5214_img" class="img " src="images/确认发货/u2265.png"/>
          <div id="u5214_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u5215" class="ax_default table_cell">
          <img id="u5215_img" class="img " src="images/确认发货/u2265.png"/>
          <div id="u5215_text" class="text ">
            <p><span>实际出库数</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u5216" class="ax_default table_cell">
          <img id="u5216_img" class="img " src="images/确认发货/u2268.png"/>
          <div id="u5216_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5217" class="ax_default label">
        <div id="u5217_div" class=""></div>
        <div id="u5217_text" class="text ">
          <p><span>*出库数量</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u5218" class="ax_default text_field">
        <div id="u5218_div" class=""></div>
        <input id="u5218_input" type="text" value="" class="u5218_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5219" class="ax_default primary_button">
        <div id="u5219_div" class=""></div>
        <div id="u5219_text" class="text ">
          <p><span>确认</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5220" class="ax_default label">
        <div id="u5220_div" class=""></div>
        <div id="u5220_text" class="text ">
          <p><span>*实际出库时间</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u5221" class="ax_default text_field">
        <div id="u5221_div" class=""></div>
        <input id="u5221_input" type="text" value="" class="u5221_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5222" class="ax_default label">
        <div id="u5222_div" class=""></div>
        <div id="u5222_text" class="text ">
          <p><span>*出库凭证</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5223" class="ax_default box_1">
        <div id="u5223_div" class=""></div>
        <div id="u5223_text" class="text ">
          <p><span>+</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5224" class="ax_default label">
        <div id="u5224_div" class=""></div>
        <div id="u5224_text" class="text ">
          <p><span>备注</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u5225" class="ax_default text_field">
        <div id="u5225_div" class=""></div>
        <input id="u5225_input" type="text" value="" class="u5225_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5226" class="ax_default label">
        <div id="u5226_div" class=""></div>
        <div id="u5226_text" class="text ">
          <p><span>*产品等级</span></p>
        </div>
      </div>

      <!-- Unnamed (下拉列表) -->
      <div id="u5227" class="ax_default droplist">
        <div id="u5227_div" class=""></div>
        <select id="u5227_input" class="u5227_input">
          <option class="u5227_input_option" value="A类">A类</option>
          <option class="u5227_input_option" value="B类">B类</option>
          <option class="u5227_input_option" value="C类">C类</option>
          <option class="u5227_input_option" value="新品">新品</option>
        </select>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
