package com.yi.controller.shippingdemand.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 发货需求查询请求
 */
@Data
@ApiModel(value = "ShippingDemandQueryRequest", description = "发货需求查询请求")
public class ShippingDemandQueryRequest {

    @ApiModelProperty(value = "当前页码", example = "1")
    private String current = "1";

    @ApiModelProperty(value = "每页大小", example = "10")
    private String size = "10";

    @ApiModelProperty(value = "发货状态：1000-待发货，2000-已发货，3000-已完结，4000-已取消")
    private String status;

    @ApiModelProperty(value = "发运订单号（模糊查询）")
    private String orderNo;

    @ApiModelProperty(value = "客户公司名称（模糊查询）")
    private String customerCompanyName;

    @ApiModelProperty(value = "收货仓库名称（模糊查询）")
    private String warehouseName;
}
