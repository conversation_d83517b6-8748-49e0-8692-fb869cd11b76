package com.yi.configuration.threadlocal;

import com.yi.constant.CoreConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ThreadContext {
    private static final Logger log = LoggerFactory.getLogger(ThreadContext.class);
    private static final ThreadLocal<Map<String, Object>> threadLocal = new InheritableThreadLocal<>();

    private ThreadContext() {
        super();
    }

    public static void set(String key, Object value) {
        Map<String, Object> map = threadLocal.get();
        if (map == null) {
            map = new HashMap<>();
            threadLocal.set(map);
        }
        map.put(key, value);
    }

    public static Object get(String key) {
        Map<String, Object> map = threadLocal.get();
        if (map == null) {
            map = new HashMap<>();
            threadLocal.set(map);
        }
        return map.get(key);
    }


    public static Long getUserId() {
        Object value = get(CoreConstants.CONTEXT_KEY_USER_ID);
        if (value != null && !"".equals(value)) {
            try {
                return Long.valueOf(value.toString());
            } catch (NumberFormatException e) {
                log.error("get userId error:", e);
                return -1L;
            }
        }
        return -1L;
    }

    public static List<Long> getUserRoles() {
        Object value = get(CoreConstants.JWT_KEY_USER_ROLES);
        if (value != null && !"".equals(value)) {
            try {
                return castList(value, Long.class);
            } catch (NumberFormatException e) {
                log.error("get userId error:", e);
                return new ArrayList<>();
            }
        }
        return new ArrayList<>();
    }

    public static <T> List<T> castList(Object obj, Class<T> clazz) {
        List<T> result = new ArrayList<T>();
        if (obj instanceof List<?>) {
            for (Object o : (List<?>) obj) {
                result.add(clazz.cast(o));
            }
            return result;
        }
        return null;
    }

    public static String getUserName() {
        Object value = get(CoreConstants.CONTEXT_KEY_USERNAME);
        return returnObjectValue(value);
    }


    public static String getToken() {
        Object value = get(CoreConstants.CONTEXT_KEY_APP_TOKEN);
        return null == value ? "" : value.toString();
    }

    public static void setUserRoles(List<Long> userRoles) {

        set(CoreConstants.JWT_KEY_USER_ROLES, userRoles);
    }

    public static void setToken(String token) {
        set(CoreConstants.CONTEXT_KEY_APP_TOKEN, token);
    }


    public static void setUserId(Long userId) {
        set(CoreConstants.CONTEXT_KEY_USER_ID, userId);
    }

    public static void setUserName(String userName) {
        set(CoreConstants.CONTEXT_KEY_USERNAME, userName);
    }

    private static String returnObjectValue(Object value) {
        return value == null ? null : value.toString();
    }

    public static void remove() {
        threadLocal.remove();
    }
}
