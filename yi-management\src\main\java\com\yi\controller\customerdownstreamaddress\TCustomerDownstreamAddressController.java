package com.yi.controller.customerdownstreamaddress;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yi.common.Result;
import com.yi.controller.customerdownstreamaddress.model.*;
import com.yi.service.TCustomerDownstreamAddressService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;

/**
 * 客户下游地址关联表 前端控制器
 */
@RestController
@RequestMapping("/api/customer-downstream-address")
@Api(tags = "客户下游地址关联管理")
public class TCustomerDownstreamAddressController {

    @Autowired
    private TCustomerDownstreamAddressService customerDownstreamAddressService;

    @ApiOperation("新增客户下游地址关联")
    @PostMapping("/add")
    public Result<Boolean> addCustomerDownstreamAddress(@Valid @RequestBody CustomerDownstreamAddressRequest request) {
        try {
            boolean success = customerDownstreamAddressService.addCustomerDownstreamAddress(request);
            if (success) {
                return Result.success("新增成功", true);
            }
            return Result.failed("新增失败");
        } catch (RuntimeException e) {
            return Result.failed(e.getMessage());
        }
    }


    @ApiOperation("启用/禁用客户下游地址关联")
    @PutMapping("/{id}/status")
    public Result<Boolean> updateCustomerDownstreamAddressStatus(@ApiParam("关联ID") @PathVariable Long id,
                                                                 @ApiParam("启用状态") @RequestParam Integer enabled) {
        boolean success = customerDownstreamAddressService.updateCustomerDownstreamAddressStatus(id, enabled);
        if (success) {
            String message = enabled == 1 ? "启用成功" : "禁用成功";
            return Result.success(message, true);
        }
        return Result.failed("状态更新失败");
    }

    @ApiOperation("分页查询客户下游地址关联列表")
    @PostMapping("/page")
    public Result<IPage<CustomerDownstreamAddressPageResponse>> getCustomerDownstreamAddressPage(@RequestBody CustomerDownstreamAddressQueryRequest request) {
        IPage<CustomerDownstreamAddressPageResponse> page = customerDownstreamAddressService.getCustomerDownstreamAddressPageResponse(request);
        return Result.success(page);
    }

    @ApiOperation("根据ID查看客户下游地址关联详情")
    @GetMapping("/{id}")
    public Result<CustomerDownstreamAddressDetailResponse> getCustomerDownstreamAddressDetail(@ApiParam("关联ID") @PathVariable Long id) {
        CustomerDownstreamAddressDetailResponse detail = customerDownstreamAddressService.getCustomerDownstreamAddressDetailById(id);
        if (detail != null) {
            return Result.success(detail);
        }
        return Result.failed("关联信息不存在");
    }

    @ApiOperation("导出客户下游地址关联列表")
    @PostMapping("/export")
    public void exportCustomerDownstreamAddressList(@RequestBody CustomerDownstreamAddressQueryRequest request,
                                                    HttpServletResponse response) throws IOException {
        customerDownstreamAddressService.exportCustomerDownstreamAddressList(request, response);
    }

    @ApiOperation("根据ID删除客户下游地址关联")
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteCustomerDownstreamAddress(@ApiParam("关联ID") @PathVariable Long id) {
        // 软删除：将valid字段设置为0
        boolean success = customerDownstreamAddressService.deleteCustomerDownstreamAddress(id);
        if (success) {
            return Result.success("删除成功", true);
        }
        return Result.failed("删除失败");
    }
}
