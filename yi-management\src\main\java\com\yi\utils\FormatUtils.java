package com.yi.utils;

import com.yi.enums.CustomerTypeEnum;
import com.yi.enums.EnabledStatusEnum;
import com.yi.enums.InvoiceTypeEnum;
import com.yi.enums.ValidStatusEnum;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 格式化工具类
 */
public class FormatUtils {

    /**
     * 默认日期时间格式化器（精确到秒）
     */
    private static final DateTimeFormatter DEFAULT_DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 日期格式化器
     */
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 时间格式化器
     */
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");

    /**
     * 格式化日期时间为字符串（精确到秒）
     *
     * @param dateTime 时间
     * @return 格式化后的时间字符串，格式：yyyy-MM-dd HH:mm:ss
     */
    public static String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }
        return dateTime.format(DEFAULT_DATETIME_FORMATTER);
    }

    /**
     * 格式化日期时间为字符串（自定义格式）
     *
     * @param dateTime 时间
     * @param pattern 格式模式
     * @return 格式化后的时间字符串
     */
    public static String formatDateTime(LocalDateTime dateTime, String pattern) {
        if (dateTime == null || pattern == null || pattern.trim().isEmpty()) {
            return "";
        }
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
            return dateTime.format(formatter);
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 格式化日期为字符串
     *
     * @param dateTime 时间
     * @return 格式化后的日期字符串，格式：yyyy-MM-dd
     */
    public static String formatDate(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }
        return dateTime.format(DATE_FORMATTER);
    }

    /**
     * 格式化日期为字符串
     *
     * @param date 日期
     * @return 格式化后的日期字符串，格式：yyyy-MM-dd
     */
    public static String formatDate(LocalDate date) {
        if (date == null) {
            return "";
        }
        return date.format(DATE_FORMATTER);
    }

    /**
     * 解析日期字符串为LocalDate
     *
     * @param dateStr 日期字符串，格式：yyyy-MM-dd
     * @return LocalDate对象
     */
    public static LocalDate parseDate(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        try {
            return LocalDate.parse(dateStr.trim(), DATE_FORMATTER);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 格式化时间为字符串
     *
     * @param dateTime 时间
     * @return 格式化后的时间字符串，格式：HH:mm:ss
     */
    public static String formatTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }
        return dateTime.format(TIME_FORMATTER);
    }

    /**
     * 获取启用状态中文描述
     *
     * @param enabled 启用状态
     * @return 状态中文描述
     */
    public static String getEnabledStatusDescription(Integer enabled) {
        String description = EnabledStatusEnum.getValueByKey(enabled);
        return description != null ? description : "";
    }

    /**
     * 获取有效性状态中文描述
     *
     * @param valid 有效性状态
     * @return 状态中文描述
     */
    public static String getValidStatusDescription(Integer valid) {
        String description = ValidStatusEnum.getValueByKey(valid);
        return description != null ? description : "";
    }

    /**
     * 获取客户类型中文描述
     *
     * @param customerType 客户类型
     * @return 客户类型中文描述
     */
    public static String getCustomerTypeDescription(String customerType) {
        if (customerType == null || customerType.trim().isEmpty()) {
            return "";
        }
        try {
            Integer typeValue = Integer.valueOf(customerType);
            String description = CustomerTypeEnum.getValueByKey(typeValue);
            return description != null ? description : "";
        } catch (NumberFormatException e) {
            return "";
        }
    }

    /**
     * 获取客户类型中文描述
     *
     * @param customerType 客户类型
     * @return 客户类型中文描述
     */
    public static String getCustomerTypeDescription(Integer customerType) {
        String description = CustomerTypeEnum.getValueByKey(customerType);
        return description != null ? description : "";
    }

    /**
     * 获取开票类型中文描述
     *
     * @param invoiceType 开票类型
     * @return 开票类型中文描述
     */
    public static String getInvoiceTypeDescription(Integer invoiceType) {
        String description = InvoiceTypeEnum.getValueByKey(invoiceType);
        return description != null ? description : "";
    }

    /**
     * 构建完整地址
     *
     * @param provinceName 省份名称
     * @param cityName 城市名称
     * @param areaName 区县名称
     * @param detailedAddress 详细地址
     * @return 完整地址
     */
    public static String buildFullAddress(String provinceName, String cityName, String areaName, String detailedAddress) {
        StringBuilder address = new StringBuilder();

        // 拼接省市区
        if (provinceName != null && !provinceName.trim().isEmpty()) {
            address.append(provinceName);
        }
        if (cityName != null && !cityName.trim().isEmpty()) {
            address.append(cityName);
        }
        if (areaName != null && !areaName.trim().isEmpty()) {
            address.append(areaName);
        }

        // 如果有省市区信息且有详细地址，则在省市区后加空格
        if (address.length() > 0 && detailedAddress != null && !detailedAddress.trim().isEmpty()) {
            address.append(" ");
        }

        // 拼接详细地址
        if (detailedAddress != null && !detailedAddress.trim().isEmpty()) {
            address.append(detailedAddress);
        }

        return address.toString();
    }

    /**
     * 安全转换Long为String
     *
     * @param value Long值
     * @return String值
     */
    public static String safeToString(Long value) {
        return value != null ? value.toString() : "";
    }

    /**
     * 安全转换Integer为String
     *
     * @param value Integer值
     * @return String值
     */
    public static String safeToString(Integer value) {
        return value != null ? value.toString() : "";
    }

    /**
     * 安全转换Object为String
     *
     * @param value Object值
     * @return String值
     */
    public static String safeToString(Object value) {
        return value != null ? value.toString() : "";
    }

    /**
     * 安全获取字符串，null转为空字符串
     *
     * @param value 字符串值
     * @return 安全的字符串
     */
    public static String safeString(String value) {
        return value != null ? value : "";
    }

    /**
     * 安全获取字符串，null转为默认值
     *
     * @param value 字符串值
     * @param defaultValue 默认值
     * @return 安全的字符串
     */
    public static String safeString(String value, String defaultValue) {
        return value != null ? value : (defaultValue != null ? defaultValue : "");
    }

    /**
     * 手机号脱敏处理，第4-7位用*代替
     *
     * @param phone 手机号
     * @return 脱敏后的手机号
     */
    public static String maskPhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return "";
        }

        phone = phone.trim();
        if (phone.length() < 7) {
            return phone; // 长度不足7位，不进行脱敏
        }

        StringBuilder masked = new StringBuilder();
        for (int i = 0; i < phone.length(); i++) {
            if (i >= 3 && i <= 6) { // 第4-7位（索引3-6）
                masked.append("*");
            } else {
                masked.append(phone.charAt(i));
            }
        }

        return masked.toString();
    }

    /**
     * 组合收货人信息（姓名 + 脱敏手机号）
     *
     * @param name 收货人姓名
     * @param phone 收货人手机号
     * @return 组合后的收货人信息
     */
    public static String combineReceiverInfo(String name, String phone) {
        StringBuilder result = new StringBuilder();

        // 添加姓名
        if (name != null && !name.trim().isEmpty()) {
            result.append(name.trim());
        }

        // 添加脱敏手机号
        String maskedPhone = maskPhone(phone);
        if (!maskedPhone.isEmpty()) {
            if (result.length() > 0) {
                result.append(" ");
            }
            result.append(maskedPhone);
        }

        return result.toString();
    }

    /**
     * 安全转换String为Integer
     *
     * @param value String值
     * @return Integer值，转换失败返回null
     */
    public static Integer safeToInteger(String value) {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        try {
            return Integer.valueOf(value.trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 安全转换String为Long
     *
     * @param value String值
     * @return Long值，转换失败返回null
     */
    public static Long safeParseLong(String value) {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        try {
            return Long.valueOf(value.trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 安全转换String为Long
     *
     * @param value String值
     * @return Long值，转换失败返回null
     */
    public static Long safeToLong(String value) {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        try {
            return Long.valueOf(value.trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 安全转换String为Integer，带默认值
     *
     * @param value String值
     * @param defaultValue 默认值
     * @return Integer值，转换失败返回默认值
     */
    public static Integer safeToInteger(String value, Integer defaultValue) {
        Integer result = safeToInteger(value);
        return result != null ? result : defaultValue;
    }

    /**
     * 安全转换String为Long，带默认值
     *
     * @param value String值
     * @param defaultValue 默认值
     * @return Long值，转换失败返回默认值
     */
    public static Long safeToLong(String value, Long defaultValue) {
        Long result = safeToLong(value);
        return result != null ? result : defaultValue;
    }

    /**
     * 获取一级类目中文描述
     *
     * @param firstCategory 一级类目
     * @return 一级类目中文描述
     */
    public static String getFirstCategoryDescription(Integer firstCategory) {
        return com.yi.enums.SkuFirstCategoryEnum.getDescriptionByCode(firstCategory);
    }

    /**
     * 获取一级类目中文描述（String类型）
     *
     * @param firstCategory 一级类目
     * @return 一级类目中文描述
     */
    public static String getFirstCategoryDescription(String firstCategory) {
        if (firstCategory == null || firstCategory.trim().isEmpty()) {
            return "";
        }
        try {
            Integer categoryValue = Integer.valueOf(firstCategory);
            return getFirstCategoryDescription(categoryValue);
        } catch (NumberFormatException e) {
            return "";
        }
    }

    /**
     * 格式化规格（长*宽*高）
     *
     * @param length 长度
     * @param width 宽度
     * @param height 高度
     * @return 格式化的规格，如：1200*800*150
     */
    public static String formatSpecification(Integer length, Integer width, Integer height) {
        if (length == null || width == null || height == null) {
            return "";
        }
        return length + "*" + width + "*" + height;
    }

    /**
     * 格式化尺寸规格（保留原方法兼容性）
     *
     * @param length 长度
     * @param width 宽度
     * @param height 高度
     * @return 格式化的尺寸规格，如：1200×800×150mm
     */
    public static String formatDimensions(Integer length, Integer width, Integer height) {
        if (length == null || width == null || height == null) {
            return "";
        }
        return length + "×" + width + "×" + height + "mm";
    }

    /**
     * 安全转换String为BigDecimal
     *
     * @param value String值
     * @return BigDecimal值，转换失败返回null
     */
    public static java.math.BigDecimal safeToBigDecimal(String value) {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        try {
            return new java.math.BigDecimal(value.trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }
}
