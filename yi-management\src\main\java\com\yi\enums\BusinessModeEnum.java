package com.yi.enums;

/**
 * 业务模式枚举
 */
public enum BusinessModeEnum {
    
    STATIC_RENTAL(1, "静态租赁"),
    RENTAL_CIRCULATION(2, "租赁+流转"),
    FIXED_PRICE(3, "一口价");
    
    private final Integer code;
    private final String name;
    
    BusinessModeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    /**
     * 根据code获取枚举
     */
    public static BusinessModeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (BusinessModeEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
    
    /**
     * 根据code获取名称
     */
    public static String getNameByCode(Integer code) {
        BusinessModeEnum item = getByCode(code);
        return item != null ? item.getName() : "";
    }
}
