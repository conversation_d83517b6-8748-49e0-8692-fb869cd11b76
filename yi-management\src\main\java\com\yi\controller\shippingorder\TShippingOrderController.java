package com.yi.controller.shippingorder;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yi.common.Result;
import com.yi.controller.shippingorder.model.*;
import com.yi.service.TShippingOrderLogService;
import com.yi.service.TShippingOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 发运订单表 前端控制器
 */
@RestController
@RequestMapping("/api/shipping-order")
@Api(tags = "发运订单管理")
public class TShippingOrderController {

    @Autowired
    private TShippingOrderService shippingOrderService;

    @Autowired
    private TShippingOrderLogService shippingOrderLogService;

    @ApiOperation("分页查询发运订单列表")
    @PostMapping("/page")
    public Result<IPage<ShippingOrderPageResponse>> getShippingOrderPage(@RequestBody ShippingOrderQueryRequest request) {
        IPage<ShippingOrderPageResponse> page = shippingOrderService.getShippingOrderPageResponse(request);
        return Result.success(page);
    }

    @ApiOperation("根据ID获取发运订单详情")
    @GetMapping("/{id}")
    public Result<ShippingOrderDetailResponse> getShippingOrderById(@ApiParam("发运订单ID") @PathVariable String id) {
        ShippingOrderDetailResponse response = shippingOrderService.getShippingOrderDetailById(Long.valueOf(id));
        if (response == null) {
            return Result.failed("发运订单不存在");
        }
        return Result.success(response);
    }

    @ApiOperation("新增发运订单")
    @PostMapping("/add")
    public Result<Boolean> addShippingOrder(@Valid @RequestBody ShippingOrderRequest request) {
        try {
            boolean success = shippingOrderService.addShippingOrder(request);
            if (success) {
                return Result.success("新增成功", true);
            }
            return Result.failed("新增失败");
        } catch (RuntimeException e) {
            return Result.failed(e.getMessage());
        }
    }

    @ApiOperation("更新发运订单")
    @PutMapping("/update")
    public Result<Boolean> updateShippingOrder(@Valid @RequestBody ShippingOrderRequest request) {
        if (request.getId() == null || request.getId().trim().isEmpty()) {
            return Result.validateFailed("发运订单ID不能为空");
        }
        try {
            boolean success = shippingOrderService.updateShippingOrder(request);
            if (success) {
                return Result.success("更新成功", true);
            }
            return Result.failed("更新失败");
        } catch (RuntimeException e) {
            return Result.failed(e.getMessage());
        }
    }

    @ApiOperation("取消发运订单")
    @PutMapping("/{id}/cancel")
    public Result<Boolean> cancelShippingOrder(@ApiParam("发运订单ID") @PathVariable String id,
                                               @ApiParam("取消原因") @RequestParam String reason) {
        boolean success = shippingOrderService.cancelShippingOrder(Long.valueOf(id), reason);
        if (success) {
            return Result.success("取消成功", true);
        }
        return Result.failed("取消失败");
    }

    @ApiOperation("导出发运订单列表")
    @PostMapping("/export")
    public void exportShippingOrderList(@RequestBody ShippingOrderQueryRequest request, HttpServletResponse response) {
        try {
            shippingOrderService.exportShippingOrderList(request, response);
        } catch (Exception e) {
            throw new RuntimeException("导出失败：" + e.getMessage());
        }
    }

    @ApiOperation("根据合同编号和仓库ID获取SKU类型下拉框数据")
    @GetMapping("/sku-types")
    public Result<List<ShippingOrderSkuTypeResponse>> getSkuTypesByContractAndWarehouse(
            @ApiParam("合同编号") @RequestParam String contractCode,
            @ApiParam("仓库ID") @RequestParam String warehouseId) {
        List<ShippingOrderSkuTypeResponse> skuTypes = shippingOrderService.getSkuTypesByContractAndWarehouse(contractCode, Long.valueOf(warehouseId));
        return Result.success(skuTypes);
    }

    @ApiOperation("根据订单ID查询操作日志")
    @GetMapping("/{id}/logs")
    public Result<List<ShippingOrderLogResponse>> getShippingOrderLogs(@ApiParam("发运订单ID") @PathVariable String id) {
        List<ShippingOrderLogResponse> logs = shippingOrderLogService.getLogsByOrderId(Long.valueOf(id));
        return Result.success(logs);
    }
}
