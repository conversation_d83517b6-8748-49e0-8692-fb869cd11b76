package com.yi.service;

import com.yi.controller.shippingorder.model.ShippingOrderRequest;
import com.yi.entity.TShippingOrder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 发运订单更新功能测试
 */
@ExtendWith(MockitoExtension.class)
@SpringBootTest
public class TShippingOrderUpdateTest {

    @InjectMocks
    private TShippingOrderService shippingOrderService;

    @Mock
    private TGeneralFileService generalFileService;

    private TShippingOrder existingOrder;
    private ShippingOrderRequest updateRequest;

    @BeforeEach
    void setUp() {
        // 准备现有订单数据
        existingOrder = new TShippingOrder();
        existingOrder.setId(1L);
        existingOrder.setOrderNo("SO20240101000001");
        existingOrder.setStatus(1000); // PENDING - 待发货状态
        existingOrder.setContractCode("CT001");
        existingOrder.setCustomerCompanyId(100L);
        existingOrder.setWarehouseId(200L);
        existingOrder.setFirstCategory(1);
        existingOrder.setSecondCategory("标准托盘");
        existingOrder.setCount(100);
        existingOrder.setDemandTime(LocalDate.now());
        existingOrder.setRemark("原始备注");
        existingOrder.setCreatedBy("testUser");
        existingOrder.setCreatedTime(LocalDateTime.now());
        existingOrder.setValid(1);

        // 准备更新请求
        updateRequest = new ShippingOrderRequest();
        updateRequest.setId("1");
        updateRequest.setContractCode("CT002");
        updateRequest.setCustomerCompanyId("101");
        updateRequest.setWarehouseId("201");
        updateRequest.setFirstCategory("1");
        updateRequest.setSecondCategory("非标准托盘");
        updateRequest.setCount("150");
        updateRequest.setDemandTime("2024-12-31");
        updateRequest.setRemark("更新后的备注");
        updateRequest.setAttachmentUrls(Arrays.asList("file1.pdf", "file2.jpg"));
    }

    @Test
    void testUpdateShippingOrder_Success() {
        // Mock getById方法返回现有订单
        when(shippingOrderService.getById(1L)).thenReturn(existingOrder);
        
        // Mock updateById方法返回true
        when(shippingOrderService.updateById(any(TShippingOrder.class))).thenReturn(true);
        
        // Mock 附件保存方法
        when(generalFileService.saveShippingOrderAttachments(any(), any())).thenReturn(true);

        // 执行更新操作
        boolean result = shippingOrderService.updateShippingOrder(updateRequest);

        // 验证结果
        assertTrue(result);
        verify(shippingOrderService, times(1)).getById(1L);
        verify(shippingOrderService, times(1)).updateById(any(TShippingOrder.class));
        verify(generalFileService, times(1)).saveShippingOrderAttachments(1L, updateRequest.getAttachmentUrls());
    }

    @Test
    void testUpdateShippingOrder_OrderNotExists_ShouldThrowException() {
        // Mock getById方法返回null
        when(shippingOrderService.getById(1L)).thenReturn(null);

        // 执行更新操作并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            shippingOrderService.updateShippingOrder(updateRequest);
        });

        assertEquals("发运订单不存在", exception.getMessage());
        verify(shippingOrderService, times(1)).getById(1L);
        verify(shippingOrderService, never()).updateById(any(TShippingOrder.class));
    }

    @Test
    void testUpdateShippingOrder_NotPendingStatus_ShouldThrowException() {
        // 设置订单状态为非待发货状态
        existingOrder.setStatus(2000); // SHIPPING - 发货中状态
        
        // Mock getById方法返回现有订单
        when(shippingOrderService.getById(1L)).thenReturn(existingOrder);

        // 执行更新操作并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            shippingOrderService.updateShippingOrder(updateRequest);
        });

        assertEquals("只有待发货状态的订单才能编辑", exception.getMessage());
        verify(shippingOrderService, times(1)).getById(1L);
        verify(shippingOrderService, never()).updateById(any(TShippingOrder.class));
    }

    @Test
    void testUpdateShippingOrder_EmptyId_ShouldThrowException() {
        // 设置空的ID
        updateRequest.setId("");

        // 执行更新操作并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            shippingOrderService.updateShippingOrder(updateRequest);
        });

        assertEquals("订单ID不能为空", exception.getMessage());
        verify(shippingOrderService, never()).getById(any());
        verify(shippingOrderService, never()).updateById(any(TShippingOrder.class));
    }

    @Test
    void testUpdateShippingOrder_NullId_ShouldThrowException() {
        // 设置null的ID
        updateRequest.setId(null);

        // 执行更新操作并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            shippingOrderService.updateShippingOrder(updateRequest);
        });

        assertEquals("订单ID不能为空", exception.getMessage());
        verify(shippingOrderService, never()).getById(any());
        verify(shippingOrderService, never()).updateById(any(TShippingOrder.class));
    }

    @Test
    void testUpdateShippingOrder_WithoutAttachments() {
        // 设置无附件的更新请求
        updateRequest.setAttachmentUrls(null);
        
        // Mock getById方法返回现有订单
        when(shippingOrderService.getById(1L)).thenReturn(existingOrder);
        
        // Mock updateById方法返回true
        when(shippingOrderService.updateById(any(TShippingOrder.class))).thenReturn(true);

        // 执行更新操作
        boolean result = shippingOrderService.updateShippingOrder(updateRequest);

        // 验证结果
        assertTrue(result);
        verify(shippingOrderService, times(1)).getById(1L);
        verify(shippingOrderService, times(1)).updateById(any(TShippingOrder.class));
        // 验证没有调用附件保存方法
        verify(generalFileService, never()).saveShippingOrderAttachments(any(), any());
    }

    @Test
    void testUpdateShippingOrder_OnlyEditableFieldsUpdated() {
        // Mock getById方法返回现有订单
        when(shippingOrderService.getById(1L)).thenReturn(existingOrder);
        
        // Mock updateById方法返回true
        when(shippingOrderService.updateById(any(TShippingOrder.class))).thenReturn(true);

        // 执行更新操作
        shippingOrderService.updateShippingOrder(updateRequest);

        // 验证只有允许编辑的字段被更新
        verify(shippingOrderService, times(1)).updateById(argThat(order -> {
            TShippingOrder updatedOrder = (TShippingOrder) order;
            return updatedOrder.getId().equals(1L) &&
                   updatedOrder.getContractCode().equals("CT002") &&
                   updatedOrder.getCustomerCompanyId().equals(101L) &&
                   updatedOrder.getWarehouseId().equals(201L) &&
                   updatedOrder.getFirstCategory().equals(1) &&
                   updatedOrder.getSecondCategory().equals("非标准托盘") &&
                   updatedOrder.getCount().equals(150) &&
                   updatedOrder.getRemark().equals("更新后的备注");
        }));
    }

    @Test
    void testUpdateShippingOrder_StatusValidation() {
        // 测试各种状态下的编辑限制
        Integer[] nonEditableStatuses = {2000, 3000, 4000}; // SHIPPING, COMPLETED, CANCELLED

        for (Integer status : nonEditableStatuses) {
            existingOrder.setStatus(status);
            when(shippingOrderService.getById(1L)).thenReturn(existingOrder);

            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                shippingOrderService.updateShippingOrder(updateRequest);
            });

            assertEquals("只有待发货状态的订单才能编辑", exception.getMessage());
        }
    }
}
