package com.yi.controller.customerwarehouse.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

/**
 * 仓库导出VO
 */
@Data
@HeadRowHeight(20)
@ContentRowHeight(18)
public class CustomerWarehouseExportVO {

    @ExcelProperty(value = "状态")
    @ColumnWidth(10)
    private String status;

    @ExcelProperty(value = "公司名称")
    @ColumnWidth(25)
    private String companyName;

    @ExcelProperty(value = "仓库名称")
    @ColumnWidth(25)
    private String warehouseName;

    @ExcelProperty(value = "地址")
    @ColumnWidth(40)
    private String address;

    @ExcelProperty(value = "SKU类型")
    @ColumnWidth(15)
    private String skuType;

    @ExcelProperty(value = "联系人")
    @ColumnWidth(12)
    private String contactPerson;

    @ExcelProperty(value = "手机号")
    @ColumnWidth(15)
    private String mobilePhone;

    @ExcelProperty(value = "座机号")
    @ColumnWidth(15)
    private String landlinePhone;

    @ExcelProperty(value = "创建人")
    @ColumnWidth(12)
    private String createdBy;

    @ExcelProperty(value = "创建时间")
    @ColumnWidth(20)
    private String createdTime;
}
