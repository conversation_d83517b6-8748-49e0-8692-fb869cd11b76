package com.yi.controller.supplierwarehouse.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 供应商仓库查询请求
 */
@Data
@ApiModel("供应商仓库查询请求")
public class SupplierWarehouseQueryRequest {

    @ApiModelProperty("页码")
    private Integer pageNum = 1;

    @ApiModelProperty("每页大小")
    private Integer pageSize = 10;

    @ApiModelProperty("状态：1-启用，0-禁用")
    private Integer enabled;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("仓库名称")
    private String warehouseName;

    @ApiModelProperty("地址")
    private String address;
}
