package com.yi.controller.common.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 文件上传响应
 */
@Data
@ApiModel(value = "FileUploadResponse", description = "文件上传响应")
public class FileUploadResponse {

    @ApiModelProperty(value = "文件名")
    private String fileName;

    @ApiModelProperty(value = "原始文件名")
    private String originalFileName;

    @ApiModelProperty(value = "文件大小（字节）")
    private Long fileSize;

    @ApiModelProperty(value = "文件类型")
    private String fileType;

    @ApiModelProperty(value = "文件相对路径")
    private String filePath;

    @ApiModelProperty(value = "文件访问URL")
    private String fileUrl;

    @ApiModelProperty(value = "上传时间")
    private String uploadTime;

    @ApiModelProperty(value = "是否为临时文件")
    private Boolean isTemp;
}
