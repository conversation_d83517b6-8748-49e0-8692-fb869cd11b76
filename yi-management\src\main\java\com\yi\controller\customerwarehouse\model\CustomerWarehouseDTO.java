package com.yi.controller.customerwarehouse.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 仓库DTO
 */
@Data
@ApiModel(value = "CustomerWarehouseDTO", description = "仓库数据传输对象")
public class CustomerWarehouseDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @NotNull(message = "公司ID不能为空")
    @ApiModelProperty(value = "公司ID", required = true)
    private Long companyId;

    @NotBlank(message = "仓库名称不能为空")
    @ApiModelProperty(value = "仓库名称", required = true)
    private String warehouseName;

    @ApiModelProperty(value = "省份ID")
    private Long provinceId;

    @ApiModelProperty(value = "省份名称")
    private String provinceName;

    @ApiModelProperty(value = "城市ID")
    private Long cityId;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "区县ID")
    private Long areaId;

    @ApiModelProperty(value = "区县名称")
    private String areaName;

    @ApiModelProperty(value = "详细地址")
    private String detailedAddress;

    @ApiModelProperty(value = "收货人")
    private String contactPerson;

    @ApiModelProperty(value = "手机号")
    private String mobilePhone;

    @ApiModelProperty(value = "座机号")
    private String landlinePhone;

    @NotNull(message = "启用状态不能为空")
    @ApiModelProperty(value = "启用状态：1-启用，0-禁用", required = true)
    private Integer enabled;

    @ApiModelProperty(value = "备注")
    private String remark;
}
