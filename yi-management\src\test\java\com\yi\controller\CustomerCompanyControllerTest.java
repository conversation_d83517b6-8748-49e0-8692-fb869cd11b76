package com.yi.controller;

import com.yi.entity.TCustomerCompany;
import com.yi.service.TCustomerCompanyService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 客户公司控制器测试
 */
@SpringBootTest
public class CustomerCompanyControllerTest {

    @MockBean
    private TCustomerCompanyService customerCompanyService;

    @Test
    public void testGetCustomerCompanyByType_WithType() {
        // 准备测试数据
        TCustomerCompany company1 = new TCustomerCompany();
        company1.setId(1L);
        company1.setCompanyName("测试公司1");
        company1.setCustomerCompanyType("1");

        TCustomerCompany company2 = new TCustomerCompany();
        company2.setId(2L);
        company2.setCompanyName("测试公司2");
        company2.setCustomerCompanyType("1");

        List<TCustomerCompany> expectedList = Arrays.asList(company1, company2);

        // 模拟Service调用
        when(customerCompanyService.getCustomerCompanyByType("1")).thenReturn(expectedList);

        // 调用Service方法
        List<TCustomerCompany> result = customerCompanyService.getCustomerCompanyByType("1");

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("测试公司1", result.get(0).getCompanyName());
        assertEquals("测试公司2", result.get(1).getCompanyName());

        // 验证Service方法被调用
        verify(customerCompanyService, times(1)).getCustomerCompanyByType("1");
    }

    @Test
    public void testGetCustomerCompanyByType_WithoutType() {
        // 准备测试数据 - 包含不同类型的公司
        TCustomerCompany company1 = new TCustomerCompany();
        company1.setId(1L);
        company1.setCompanyName("测试公司1");
        company1.setCustomerCompanyType("1");

        TCustomerCompany company2 = new TCustomerCompany();
        company2.setId(2L);
        company2.setCompanyName("测试公司2");
        company2.setCustomerCompanyType("2");

        TCustomerCompany company3 = new TCustomerCompany();
        company3.setId(3L);
        company3.setCompanyName("测试公司3");
        company3.setCustomerCompanyType("3");

        List<TCustomerCompany> expectedList = Arrays.asList(company1, company2, company3);

        // 模拟Service调用 - 传入null参数
        when(customerCompanyService.getCustomerCompanyByType(null)).thenReturn(expectedList);

        // 调用Service方法
        List<TCustomerCompany> result = customerCompanyService.getCustomerCompanyByType(null);

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("测试公司1", result.get(0).getCompanyName());
        assertEquals("测试公司2", result.get(1).getCompanyName());
        assertEquals("测试公司3", result.get(2).getCompanyName());

        // 验证Service方法被调用
        verify(customerCompanyService, times(1)).getCustomerCompanyByType(null);
    }

    @Test
    public void testGetCustomerCompanyByType_WithEmptyType() {
        // 准备测试数据
        TCustomerCompany company1 = new TCustomerCompany();
        company1.setId(1L);
        company1.setCompanyName("测试公司1");
        company1.setCustomerCompanyType("1");

        TCustomerCompany company2 = new TCustomerCompany();
        company2.setId(2L);
        company2.setCompanyName("测试公司2");
        company2.setCustomerCompanyType("2");

        List<TCustomerCompany> expectedList = Arrays.asList(company1, company2);

        // 模拟Service调用 - 传入空字符串
        when(customerCompanyService.getCustomerCompanyByType("")).thenReturn(expectedList);

        // 调用Service方法
        List<TCustomerCompany> result = customerCompanyService.getCustomerCompanyByType("");

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证Service方法被调用
        verify(customerCompanyService, times(1)).getCustomerCompanyByType("");
    }
}
