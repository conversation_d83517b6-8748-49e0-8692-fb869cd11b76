package com.yi.controller.warehouse.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 入库单响应
 */
@Data
@ApiModel(value = "InboundOrderResponse", description = "入库单响应")
public class InboundOrderResponse {

    @ApiModelProperty(value = "入库单ID")
    private Long id;

    @ApiModelProperty(value = "入库单号")
    private String orderNo;

    @ApiModelProperty(value = "入库状态：1-待入库，2-部分入库，3-已入库")
    private Integer status;

    @ApiModelProperty(value = "入库状态描述")
    private String statusDesc;

    @ApiModelProperty(value = "入库类型：1-采购入库，2-回收入库，3-调拨入库，4-销售入库")
    private Integer inboundType;

    @ApiModelProperty(value = "入库类型描述")
    private String inboundTypeDesc;

    @ApiModelProperty(value = "入库仓库ID")
    private Long inboundWarehouseId;

    @ApiModelProperty(value = "入库仓库名称")
    private String inboundWarehouseName;

    @ApiModelProperty(value = "配送方式")
    private String deliveryMethod;

    @ApiModelProperty(value = "车牌号")
    private String vehicleNumber;

    @ApiModelProperty(value = "司机姓名")
    private String driverName;

    @ApiModelProperty(value = "司机联系方式")
    private String driverPhone;

    @ApiModelProperty(value = "发货仓库ID")
    private Long senderWarehouseId;

    @ApiModelProperty(value = "发货仓库名称")
    private String senderWarehouseName;

    @ApiModelProperty(value = "发货地址")
    private String senderAddress;

    @ApiModelProperty(value = "一级类目：1-共享托盘")
    private Integer firstCategory;

    @ApiModelProperty(value = "一级类目描述")
    private String firstCategoryDesc;

    @ApiModelProperty(value = "二级类目")
    private String secondCategory;

    @ApiModelProperty(value = "计划入库数")
    private Integer plannedQuantity;

    @ApiModelProperty(value = "实际入库数")
    private Integer actualQuantity;

    @ApiModelProperty(value = "入库时间")
    private LocalDateTime inboundTime;

    @ApiModelProperty(value = "关联出库单ID")
    private Long outboundOrderId;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "最后修改人")
    private String lastModifiedBy;

    @ApiModelProperty(value = "最后修改时间")
    private LocalDateTime lastModifiedTime;

    @ApiModelProperty(value = "备注")
    private String remark;
}
