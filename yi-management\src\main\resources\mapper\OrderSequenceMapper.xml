<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yi.mapper.OrderSequenceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yi.entity.OrderSequence">
        <id column="id" property="id" />
        <result column="date_key" property="dateKey" />
        <result column="sequence_value" property="sequenceValue" />
        <result column="created_time" property="createdTime" />
        <result column="last_modified_time" property="lastModifiedTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, date_key, sequence_value, created_time, last_modified_time
    </sql>

    <!-- 根据日期键查询序列记录 -->
    <select id="selectByDateKey" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM t_order_sequence
        WHERE date_key = #{dateKey}
    </select>

    <!-- 获取并递增序列号（原子操作） -->
    <select id="getAndIncrementSequence" resultType="java.lang.Integer">
        SELECT 
            CASE 
                WHEN COUNT(*) = 0 THEN 
                    (SELECT @new_seq := 1)
                ELSE 
                    (SELECT @new_seq := sequence_value + 1 FROM t_order_sequence WHERE date_key = #{dateKey})
            END as new_sequence
        FROM t_order_sequence 
        WHERE date_key = #{dateKey}
    </select>

    <!-- 初始化当日序列记录 -->
    <insert id="initTodaySequence">
        INSERT INTO t_order_sequence (date_key, sequence_value, created_time, last_modified_time)
        VALUES (#{dateKey}, 0, NOW(), NOW())
        ON DUPLICATE KEY UPDATE last_modified_time = NOW()
    </insert>

    <!-- 更新序列号 -->
    <update id="updateSequence">
        UPDATE t_order_sequence 
        SET sequence_value = #{sequenceValue}, 
            last_modified_time = NOW()
        WHERE date_key = #{dateKey}
    </update>

</mapper>
