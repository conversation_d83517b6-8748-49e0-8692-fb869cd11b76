package com.yi.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yi.controller.customerwarehouse.model.CustomerWarehouseRequest;
import com.yi.controller.customerwarehouse.model.CustomerWarehouseQueryRequest;
import com.yi.controller.customerwarehouse.model.CustomerWarehousePageResponse;
import com.yi.controller.customerwarehouse.model.CustomerWarehouseDetailResponse;
import com.yi.controller.customerwarehouse.model.CustomerWarehouseExportVO;
import com.yi.controller.customerwarehouse.model.CustomerWarehouseSkuTypeRequest;
import com.yi.controller.customerwarehouse.model.CustomerWarehouseOptionResponse;
import com.yi.mapper.vo.WarehousePageVO;
import com.yi.entity.TCustomerWarehouse;
import com.yi.entity.TCustomerCompany;
import com.yi.mapper.TCustomerWarehouseMapper;
import com.yi.utils.ExcelUtils;
import com.yi.utils.FormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 仓库表 服务类
 */
@Service
public class TCustomerWarehouseService extends ServiceImpl<TCustomerWarehouseMapper, TCustomerWarehouse> {

    @Autowired
    private TCustomerCompanyService customerCompanyService;

    @Autowired
    private TWarehouseSkuService warehouseSkuService;


    /**
     * 分页查询仓库列表（返回Response格式）
     *
     * @param request 查询条件
     * @return 分页结果
     */
    public IPage<CustomerWarehousePageResponse> getWarehousePageResponse(CustomerWarehouseQueryRequest request) {
        // 转换分页参数
        Integer current = FormatUtils.safeToInteger(request.getCurrent(), 1);
        Integer size = FormatUtils.safeToInteger(request.getSize(), 10);
        Page<WarehousePageVO> page = new Page<>(current, size);

        // 使用统一的SQL联查直接获取结果（分页模式）
        IPage<WarehousePageVO> originalPage = baseMapper.selectWarehouseWithSku(page, request);

        // 转换为Response对象
        List<CustomerWarehousePageResponse> responseList = originalPage.getRecords().stream()
                .map(this::convertVOToPageResponse)
                .collect(Collectors.toList());

        // 构建返回的分页对象
        Page<CustomerWarehousePageResponse> responsePage = new Page<>(originalPage.getCurrent(), originalPage.getSize(), originalPage.getTotal());
        responsePage.setRecords(responseList);

        return responsePage;
    }

    /**
     * 根据ID获取仓库详情
     *
     * @param id 主键ID
     * @return 仓库详情
     */
    public TCustomerWarehouse getWarehouseById(Long id) {
        LambdaQueryWrapper<TCustomerWarehouse> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TCustomerWarehouse::getId, id)
                .eq(TCustomerWarehouse::getValid, 1);
        return this.getOne(wrapper);
    }

    /**
     * 新增仓库
     *
     * @param customerWarehouseRequest 仓库信息
     * @return 是否成功
     */
    public boolean addWarehouse(CustomerWarehouseRequest customerWarehouseRequest) {
        // 校验仓库名称重复
        validateWarehouseNameUnique(FormatUtils.safeToLong(customerWarehouseRequest.getCompanyId()),
                                   customerWarehouseRequest.getWarehouseName(), null);

        // 校验SKU类型
        validateSkuTypes(customerWarehouseRequest.getCompanyId(), customerWarehouseRequest.getSkuTypes());

        TCustomerWarehouse warehouse = new TCustomerWarehouse();

        // 手动转换字段类型
        warehouse.setCompanyId(FormatUtils.safeToLong(customerWarehouseRequest.getCompanyId()));
        warehouse.setWarehouseName(customerWarehouseRequest.getWarehouseName());
        warehouse.setProvinceId(FormatUtils.safeToLong(customerWarehouseRequest.getProvinceId()));
        warehouse.setProvinceName(customerWarehouseRequest.getProvinceName());
        warehouse.setCityId(FormatUtils.safeToLong(customerWarehouseRequest.getCityId()));
        warehouse.setCityName(customerWarehouseRequest.getCityName());
        warehouse.setAreaId(FormatUtils.safeToLong(customerWarehouseRequest.getAreaId()));
        warehouse.setAreaName(customerWarehouseRequest.getAreaName());
        warehouse.setDetailedAddress(customerWarehouseRequest.getDetailedAddress());
        warehouse.setContactPerson(customerWarehouseRequest.getContactPerson());
        warehouse.setMobilePhone(customerWarehouseRequest.getMobilePhone());
        warehouse.setLandlinePhone(customerWarehouseRequest.getLandlinePhone());
        warehouse.setSkuTypes(null); // 不再在仓库表中存储SKU类型
        warehouse.setRemark(customerWarehouseRequest.getRemark());

        // 设置默认值
        warehouse.setEnabled(0); // 默认禁用状态
        warehouse.setValid(1);
        warehouse.setCreatedTime(LocalDateTime.now());
        warehouse.setLastModifiedTime(LocalDateTime.now());

        // 保存仓库信息
        boolean success = this.save(warehouse);

        if (success) {
            // 保存仓库SKU类型关联
            warehouseSkuService.saveWarehouseSkuTypes(warehouse.getId(), customerWarehouseRequest.getSkuTypes());
        }

        return success;
    }

    /**
     * 更新仓库
     *
     * @param customerWarehouseRequest 仓库信息
     * @return 是否成功
     */
    public boolean updateWarehouse(CustomerWarehouseRequest customerWarehouseRequest) {
        Long warehouseId = FormatUtils.safeToLong(customerWarehouseRequest.getId());

        // 校验仓库名称重复
        validateWarehouseNameUnique(FormatUtils.safeToLong(customerWarehouseRequest.getCompanyId()),
                                   customerWarehouseRequest.getWarehouseName(), warehouseId);

        // 校验SKU类型
        validateSkuTypes(customerWarehouseRequest.getCompanyId(), customerWarehouseRequest.getSkuTypes());

        TCustomerWarehouse warehouse = new TCustomerWarehouse();

        // 手动转换字段类型
        warehouse.setId(warehouseId);
        warehouse.setCompanyId(FormatUtils.safeToLong(customerWarehouseRequest.getCompanyId()));
        warehouse.setWarehouseName(customerWarehouseRequest.getWarehouseName());
        warehouse.setProvinceId(FormatUtils.safeToLong(customerWarehouseRequest.getProvinceId()));
        warehouse.setProvinceName(customerWarehouseRequest.getProvinceName());
        warehouse.setCityId(FormatUtils.safeToLong(customerWarehouseRequest.getCityId()));
        warehouse.setCityName(customerWarehouseRequest.getCityName());
        warehouse.setAreaId(FormatUtils.safeToLong(customerWarehouseRequest.getAreaId()));
        warehouse.setAreaName(customerWarehouseRequest.getAreaName());
        warehouse.setDetailedAddress(customerWarehouseRequest.getDetailedAddress());
        warehouse.setContactPerson(customerWarehouseRequest.getContactPerson());
        warehouse.setMobilePhone(customerWarehouseRequest.getMobilePhone());
        warehouse.setLandlinePhone(customerWarehouseRequest.getLandlinePhone());
        warehouse.setSkuTypes(null); // 不再在仓库表中存储SKU类型
        warehouse.setRemark(customerWarehouseRequest.getRemark());

        warehouse.setLastModifiedTime(LocalDateTime.now());

        // 更新仓库信息
        boolean success = this.updateById(warehouse);

        if (success) {
            // 更新仓库SKU类型关联
            warehouseSkuService.saveWarehouseSkuTypes(warehouseId, customerWarehouseRequest.getSkuTypes());
        }

        return success;
    }


    /**
     * 启用/禁用仓库
     *
     * @param id 主键ID
     * @param enabled 启用状态
     * @return 是否成功
     */
    public boolean updateWarehouseStatus(Long id, Integer enabled) {
        TCustomerWarehouse warehouse = new TCustomerWarehouse();
        warehouse.setId(id);
        warehouse.setEnabled(enabled);
        warehouse.setLastModifiedTime(LocalDateTime.now());

        return this.updateById(warehouse);
    }

    /**
     * 根据公司ID查询仓库列表
     *
     * @param companyId 公司ID
     * @return 仓库列表
     */
    public List<TCustomerWarehouse> getWarehouseByCompanyId(Long companyId) {
        return baseMapper.selectByCompanyId(companyId);
    }


    /**
     * 根据仓库名称查询仓库
     *
     * @param warehouseName 仓库名称
     * @return 仓库列表
     */
    public List<TCustomerWarehouse> getWarehouseByName(String warehouseName) {
        return baseMapper.selectByWarehouseName(warehouseName);
    }


    /**
     * 根据ID获取仓库详情（返回Response格式）
     *
     * @param id 主键ID
     * @return 仓库详情Response
     */
    public CustomerWarehouseDetailResponse getWarehouseDetailById(Long id) {
        TCustomerWarehouse warehouse = getWarehouseById(id);
        if (warehouse == null) {
            return null;
        }
        return convertToDetailResponse(warehouse);
    }



    /**
     * 转换VO为分页Response对象
     *
     * @param vo 仓库分页查询VO
     * @return 分页Response对象
     */
    private CustomerWarehousePageResponse convertVOToPageResponse(WarehousePageVO vo) {
        CustomerWarehousePageResponse response = new CustomerWarehousePageResponse();

        // 基础字段转换
        response.setId(FormatUtils.safeToString(vo.getWarehouseId()));
        response.setWarehouseName(FormatUtils.safeString(vo.getWarehouseName()));
        response.setCompanyName(FormatUtils.safeString(vo.getCompanyName()));
        response.setContactPerson(FormatUtils.safeString(vo.getContactPerson()));
        response.setCreatedBy(FormatUtils.safeString(vo.getCreatedBy()));

        // 状态转换为中文
        response.setStatus(FormatUtils.getEnabledStatusDescription(vo.getEnabled()));

        // SKU类型显示
        if (vo.getFirstCategory() != null) {
            String firstCategoryName = FormatUtils.getFirstCategoryDescription(vo.getFirstCategory());
            String secondCategory = FormatUtils.safeString(vo.getSecondCategory());

            if (secondCategory.isEmpty()) {
                response.setSkuType(firstCategoryName);
            } else {
                response.setSkuType(firstCategoryName + " " + secondCategory);
            }
        } else {
            response.setSkuType("");
        }

        // 联系方式（优先手机号，其次座机号）
        String contactPhone = FormatUtils.safeString(vo.getMobilePhone());
        if (contactPhone.isEmpty()) {
            contactPhone = FormatUtils.safeString(vo.getLandlinePhone());
        }
        response.setContactPhone(contactPhone);

        // 地址拼接
        response.setAddress(FormatUtils.buildFullAddress(
                vo.getProvinceName(),
                vo.getCityName(),
                vo.getAreaName(),
                vo.getDetailedAddress()
        ));

        // 创建时间格式化
        response.setCreatedTime(FormatUtils.formatDateTime(vo.getCreatedTime()));

        return response;
    }

    /**
     * 转换为详情Response对象
     *
     * @param warehouse 仓库实体
     * @return 详情Response对象
     */
    private CustomerWarehouseDetailResponse convertToDetailResponse(TCustomerWarehouse warehouse) {
        CustomerWarehouseDetailResponse response = new CustomerWarehouseDetailResponse();

        // 基础字段转换（保持与数据库字段一致）
        response.setId(FormatUtils.safeToString(warehouse.getId()));
        response.setCompanyId(FormatUtils.safeToString(warehouse.getCompanyId()));
        response.setWarehouseName(FormatUtils.safeString(warehouse.getWarehouseName()));

        // 查询公司信息
        TCustomerCompany company = getCompanyById(warehouse.getCompanyId());
        if (company != null) {
            response.setCompanyName(FormatUtils.safeString(company.getCompanyName()));
            response.setCustomerType(FormatUtils.getCustomerTypeDescription(company.getCustomerCompanyType()));
        } else {
            response.setCompanyName("");
            response.setCustomerType("");
        }
        response.setProvinceId(FormatUtils.safeToString(warehouse.getProvinceId()));
        response.setProvinceName(FormatUtils.safeString(warehouse.getProvinceName()));
        response.setCityId(FormatUtils.safeToString(warehouse.getCityId()));
        response.setCityName(FormatUtils.safeString(warehouse.getCityName()));
        response.setAreaId(FormatUtils.safeToString(warehouse.getAreaId()));
        response.setAreaName(FormatUtils.safeString(warehouse.getAreaName()));
        response.setDetailedAddress(FormatUtils.safeString(warehouse.getDetailedAddress()));
        response.setContactPerson(FormatUtils.safeString(warehouse.getContactPerson()));
        response.setMobilePhone(FormatUtils.safeString(warehouse.getMobilePhone()));
        response.setLandlinePhone(FormatUtils.safeString(warehouse.getLandlinePhone()));
        response.setSkuTypes(warehouseSkuService.getSkuTypesByWarehouseId(warehouse.getId()));
        response.setEnabled(FormatUtils.safeToString(warehouse.getEnabled()));
        response.setCreatedBy(FormatUtils.safeString(warehouse.getCreatedBy()));
        response.setCreatedTime(FormatUtils.formatDateTime(warehouse.getCreatedTime()));
        response.setLastModifiedBy(FormatUtils.safeString(warehouse.getLastModifiedBy()));
        response.setLastModifiedTime(FormatUtils.formatDateTime(warehouse.getLastModifiedTime()));
        response.setValid(FormatUtils.safeToString(warehouse.getValid()));
        response.setRemark(FormatUtils.safeString(warehouse.getRemark()));

        return response;
    }

    /**
     * 导出仓库列表
     *
     * @param request 查询条件
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    public void exportWarehouseList(CustomerWarehouseQueryRequest request, HttpServletResponse response) throws IOException {
        // 使用统一的SQL联查直接获取所有符合条件的数据（不分页模式）
        List<WarehousePageVO> dataList = baseMapper.selectWarehouseWithSku(request);

        // 转换为导出VO
        List<CustomerWarehouseExportVO> exportList = dataList.stream()
                .map(this::convertVOToExportVO)
                .collect(Collectors.toList());

        // 使用EasyExcel导出（文件名已包含时间戳）
        ExcelUtils.exportExcelWithTimestamp(response, "仓库列表", "仓库列表",
                CustomerWarehouseExportVO.class, exportList);
    }

    /**
     * 转换为导出VO对象
     *
     * @param warehouse 仓库实体
     * @return 导出VO对象
     */
    /**
     * 转换VO为导出VO对象
     *
     * @param vo 仓库分页查询VO
     * @return 导出VO对象
     */
    private CustomerWarehouseExportVO convertVOToExportVO(WarehousePageVO vo) {
        CustomerWarehouseExportVO exportVO = new CustomerWarehouseExportVO();

        // 基础字段转换
        exportVO.setCompanyName(FormatUtils.safeString(vo.getCompanyName()));
        exportVO.setWarehouseName(FormatUtils.safeString(vo.getWarehouseName()));
        exportVO.setContactPerson(FormatUtils.safeString(vo.getContactPerson()));
        exportVO.setMobilePhone(FormatUtils.safeString(vo.getMobilePhone()));
        exportVO.setLandlinePhone(FormatUtils.safeString(vo.getLandlinePhone()));
        exportVO.setCreatedBy(FormatUtils.safeString(vo.getCreatedBy()));

        // 状态转换为中文
        exportVO.setStatus(FormatUtils.getEnabledStatusDescription(vo.getEnabled()));

        // SKU类型显示
        if (vo.getFirstCategory() != null) {
            String firstCategoryName = FormatUtils.getFirstCategoryDescription(vo.getFirstCategory());
            String secondCategory = FormatUtils.safeString(vo.getSecondCategory());

            if (secondCategory.isEmpty()) {
                exportVO.setSkuType(firstCategoryName);
            } else {
                exportVO.setSkuType(firstCategoryName + " " + secondCategory);
            }
        } else {
            exportVO.setSkuType("");
        }

        // 地址拼接
        exportVO.setAddress(FormatUtils.buildFullAddress(
                vo.getProvinceName(),
                vo.getCityName(),
                vo.getAreaName(),
                vo.getDetailedAddress()
        ));

        // 创建时间格式化
        exportVO.setCreatedTime(FormatUtils.formatDateTime(vo.getCreatedTime()));

        return exportVO;
    }


    /**
     * 根据公司ID获取公司信息
     *
     * @param companyId 公司ID
     * @return 公司信息
     */
    private TCustomerCompany getCompanyById(Long companyId) {
        if (companyId == null) {
            return null;
        }
        return customerCompanyService.getCustomerCompanyById(companyId);
    }

    /**
     * 校验SKU类型
     *
     * @param companyId 公司ID
     * @param skuTypes SKU类型列表
     */
    private void validateSkuTypes(String companyId, List<CustomerWarehouseSkuTypeRequest> skuTypes) {
        // 获取公司信息
        Long companyIdLong = FormatUtils.safeToLong(companyId);
        if (companyIdLong == null) {
            throw new RuntimeException("公司ID不能为空");
        }

        TCustomerCompany company = customerCompanyService.getCustomerCompanyById(companyIdLong);
        if (company == null) {
            throw new RuntimeException("公司不存在");
        }

        // 判断客户类型
        String customerCompanyType = company.getCustomerCompanyType();
        if ("1".equals(customerCompanyType)) { // 合约客户
            if (skuTypes == null || skuTypes.isEmpty()) {
                throw new RuntimeException("合约客户的SKU类型为必填字段，至少填1个");
            }
            if (skuTypes.size() > 9) {
                throw new RuntimeException("SKU类型最多只能选择9个");
            }
        }
        // 非合约客户不需要校验SKU类型
    }

    /**
     * 根据公司ID查询仓库选项列表
     *
     * @param companyId 公司ID
     * @return 仓库选项列表
     */
    public List<CustomerWarehouseOptionResponse> getWarehouseOptionsByCompanyId(String companyId) {
        // 转换参数
        Long companyIdLong = FormatUtils.safeToLong(companyId);
        if (companyIdLong == null) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<TCustomerWarehouse> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TCustomerWarehouse::getValid, 1)
                .eq(TCustomerWarehouse::getEnabled, 1) // 只查询启用的仓库
                .eq(TCustomerWarehouse::getCompanyId, companyIdLong)
                .orderBy(true, true, TCustomerWarehouse::getWarehouseName);

        List<TCustomerWarehouse> warehouseList = this.list(wrapper);

        return warehouseList.stream()
                .map(this::convertToWarehouseOptionResponse)
                .collect(Collectors.toList());
    }

    /**
     * 转换为仓库选项Response对象
     *
     * @param warehouse 仓库实体
     * @return 仓库选项Response对象
     */
    private CustomerWarehouseOptionResponse convertToWarehouseOptionResponse(TCustomerWarehouse warehouse) {
        CustomerWarehouseOptionResponse response = new CustomerWarehouseOptionResponse();

        // 基础字段转换
        response.setId(FormatUtils.safeToString(warehouse.getId()));
        response.setWarehouseName(FormatUtils.safeString(warehouse.getWarehouseName()));
        response.setContactPerson(FormatUtils.safeString(warehouse.getContactPerson()));
        response.setMobilePhone(FormatUtils.safeString(warehouse.getMobilePhone()));
        response.setLandlinePhone(FormatUtils.safeString(warehouse.getLandlinePhone()));

        // 地址拼接
        response.setWarehouseAddress(FormatUtils.buildFullAddress(
                warehouse.getProvinceName(),
                warehouse.getCityName(),
                warehouse.getAreaName(),
                warehouse.getDetailedAddress()
        ));

        return response;
    }

    /**
     * 校验仓库名称在同一客户下是否唯一
     *
     * @param companyId 公司ID
     * @param warehouseName 仓库名称
     * @param excludeId 排除的仓库ID（编辑时使用）
     */
    private void validateWarehouseNameUnique(Long companyId, String warehouseName, Long excludeId) {
        if (companyId == null || warehouseName == null || warehouseName.trim().isEmpty()) {
            return;
        }

        int count = this.baseMapper.countByCompanyIdAndWarehouseName(companyId, warehouseName.trim(), excludeId);
        if (count > 0) {
            throw new RuntimeException("同一客户下仓库名称不能重复");
        }
    }
}
