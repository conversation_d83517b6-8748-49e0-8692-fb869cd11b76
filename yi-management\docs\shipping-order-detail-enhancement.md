# 发运订单详情接口增强说明

## 概述

根据前端界面需求，对`getShippingOrderById`接口进行了全面增强，添加了收货人信息、地址信息和附件管理功能，使接口返回的数据与界面字段完全一致。

## 🎯 **界面字段对应**

### **原有字段**
- ✅ 客户（下拉选择） → `customerCompanyId` + `customerCompanyName`
- ✅ 合同编号（下拉选择） → `contractCode`
- ✅ 收货仓库（下拉选择） → `warehouseId` + `warehouseName`
- ✅ SKU类型（下拉选择） → `firstCategory` + `firstCategoryName` + `secondCategory`
- ✅ 需求数量 → `count`
- ✅ 需求时间 → `demandTime`
- ✅ 备注信息 → `remark`

### **新增字段**
- ✅ 收货人 → `receiverName`
- ✅ 手机号 → `receiverPhone`
- ✅ 地址详情 → `province` + `city` + `district` + `detailAddress` + `fullAddress`
- ✅ 附件上传 → `attachmentUrls`

## 📋 **数据库表结构修改**

### **新增字段**
```sql
ALTER TABLE `t_shipping_order` 
ADD COLUMN `receiver_name` varchar(100) DEFAULT NULL COMMENT '收货人' AFTER `warehouse_id`,
ADD COLUMN `receiver_phone` varchar(20) DEFAULT NULL COMMENT '收货人手机号' AFTER `receiver_name`,
ADD COLUMN `province` varchar(50) DEFAULT NULL COMMENT '省份' AFTER `receiver_phone`,
ADD COLUMN `city` varchar(50) DEFAULT NULL COMMENT '城市' AFTER `province`,
ADD COLUMN `district` varchar(50) DEFAULT NULL COMMENT '区县' AFTER `city`,
ADD COLUMN `detail_address` varchar(500) DEFAULT NULL COMMENT '详细地址' AFTER `district`;
```

## 🔧 **代码修改详情**

### **1. 实体类修改**
**文件**: `yi-management/src/main/java/com/yi/entity/TShippingOrder.java`

**新增字段**:
```java
/**
 * 收货人
 */
private String receiverName;

/**
 * 收货人手机号
 */
private String receiverPhone;

/**
 * 省份
 */
private String province;

/**
 * 城市
 */
private String city;

/**
 * 区县
 */
private String district;

/**
 * 详细地址
 */
private String detailAddress;
```

### **2. 响应类修改**
**文件**: `yi-management/src/main/java/com/yi/controller/shippingorder/model/ShippingOrderDetailResponse.java`

**新增字段**:
```java
@ApiModelProperty(value = "收货人")
private String receiverName;

@ApiModelProperty(value = "手机号")
private String receiverPhone;

@ApiModelProperty(value = "省份")
private String province;

@ApiModelProperty(value = "城市")
private String city;

@ApiModelProperty(value = "区县")
private String district;

@ApiModelProperty(value = "详细地址")
private String detailAddress;

@ApiModelProperty(value = "完整地址")
private String fullAddress;

@ApiModelProperty(value = "附件列表")
private List<String> attachmentUrls;
```

### **3. 请求类修改**
**文件**: `yi-management/src/main/java/com/yi/controller/shippingorder/model/ShippingOrderRequest.java`

**新增字段**:
```java
@ApiModelProperty(value = "收货人", required = true)
@NotBlank(message = "收货人不能为空")
private String receiverName;

@ApiModelProperty(value = "手机号", required = true)
@NotBlank(message = "手机号不能为空")
private String receiverPhone;

@ApiModelProperty(value = "省份", required = true)
@NotBlank(message = "省份不能为空")
private String province;

@ApiModelProperty(value = "城市", required = true)
@NotBlank(message = "城市不能为空")
private String city;

@ApiModelProperty(value = "区县", required = true)
@NotBlank(message = "区县不能为空")
private String district;

@ApiModelProperty(value = "详细地址", required = true)
@NotBlank(message = "详细地址不能为空")
private String detailAddress;
```

### **4. 服务层修改**
**文件**: `yi-management/src/main/java/com/yi/service/TShippingOrderService.java`

**convertToDetailResponse方法增强**:
```java
private ShippingOrderDetailResponse convertToDetailResponse(TShippingOrder order) {
    ShippingOrderDetailResponse response = new ShippingOrderDetailResponse();

    // 基本信息
    response.setId(FormatUtils.safeToString(order.getId()));
    response.setOrderNo(FormatUtils.safeString(order.getOrderNo()));
    response.setCustomerCompanyId(FormatUtils.safeToString(order.getCustomerCompanyId()));
    response.setContractCode(FormatUtils.safeString(order.getContractCode()));
    response.setWarehouseId(FormatUtils.safeToString(order.getWarehouseId()));
    
    // 收货人信息
    response.setReceiverName(FormatUtils.safeString(order.getReceiverName()));
    response.setReceiverPhone(FormatUtils.safeString(order.getReceiverPhone()));
    
    // 地址信息
    response.setProvince(FormatUtils.safeString(order.getProvince()));
    response.setCity(FormatUtils.safeString(order.getCity()));
    response.setDistrict(FormatUtils.safeString(order.getDistrict()));
    response.setDetailAddress(FormatUtils.safeString(order.getDetailAddress()));
    response.setFullAddress(buildFullAddress(order));
    
    // 产品信息
    response.setFirstCategory(FormatUtils.safeToString(order.getFirstCategory()));
    response.setFirstCategoryName(getFirstCategoryName(order.getFirstCategory()));
    response.setSecondCategory(FormatUtils.safeString(order.getSecondCategory()));
    response.setCount(FormatUtils.safeToString(order.getCount()));
    response.setShippedQuantity(FormatUtils.safeToString(order.getShippedQuantity()));
    response.setReceivedQuantity(FormatUtils.safeToString(order.getReceivedQuantity()));
    response.setDemandTime(FormatUtils.formatDate(order.getDemandTime()));
    
    // 状态信息
    response.setStatus(FormatUtils.safeString(order.getStatus()));
    response.setStatusName(getStatusName(order.getStatus()));
    response.setCancelReason(FormatUtils.safeString(order.getCancelReason()));
    response.setRemark(FormatUtils.safeString(order.getRemark()));
    
    // 创建信息
    response.setCreatedBy(FormatUtils.safeString(order.getCreatedBy()));
    response.setCreatedTime(FormatUtils.formatDateTime(order.getCreatedTime()));
    response.setLastModifiedBy(FormatUtils.safeString(order.getLastModifiedBy()));
    response.setLastModifiedTime(FormatUtils.formatDateTime(order.getLastModifiedTime()));

    // 查询关联信息
    populateRelatedInfo(response, order);
    
    // 查询附件信息
    response.setAttachmentUrls(getOrderAttachments(order.getId()));

    return response;
}
```

**新增辅助方法**:
```java
/**
 * 构建完整地址
 */
private String buildFullAddress(TShippingOrder order) {
    StringBuilder fullAddress = new StringBuilder();
    
    if (order.getProvince() != null && !order.getProvince().trim().isEmpty()) {
        fullAddress.append(order.getProvince());
    }
    if (order.getCity() != null && !order.getCity().trim().isEmpty()) {
        fullAddress.append(order.getCity());
    }
    if (order.getDistrict() != null && !order.getDistrict().trim().isEmpty()) {
        fullAddress.append(order.getDistrict());
    }
    if (order.getDetailAddress() != null && !order.getDetailAddress().trim().isEmpty()) {
        fullAddress.append(order.getDetailAddress());
    }
    
    return fullAddress.toString();
}

/**
 * 获取订单附件列表
 */
private List<String> getOrderAttachments(Long orderId) {
    try {
        return generalFileService.getShippingOrderAttachments(orderId)
                .stream()
                .map(file -> file.getFilePath())
                .collect(Collectors.toList());
    } catch (Exception e) {
        log.warn("获取订单附件失败，订单ID: " + orderId + ", 错误: " + e.getMessage());
        return new ArrayList<>();
    }
}
```

## 📊 **接口响应示例**

### **请求**
```http
GET /api/shipping-order/1
```

### **响应**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": "1",
    "orderNo": "XSD202412230001",
    "customerCompanyId": "100",
    "customerCompanyName": "深圳科技有限公司",
    "contractCode": "CT001",
    "warehouseId": "200",
    "warehouseName": "深圳仓库",
    "receiverName": "张三",
    "receiverPhone": "13800138000",
    "province": "广东省",
    "city": "深圳市",
    "district": "南山区",
    "detailAddress": "科技园南区高新南一道999号",
    "fullAddress": "广东省深圳市南山区科技园南区高新南一道999号",
    "firstCategory": "1",
    "firstCategoryName": "共享托盘",
    "secondCategory": "标准托盘",
    "count": "100",
    "shippedQuantity": "0",
    "receivedQuantity": "0",
    "demandTime": "2024-12-31",
    "status": "PENDING",
    "statusName": "待发货",
    "cancelReason": null,
    "remark": "测试订单备注",
    "attachmentUrls": [
      "contract.pdf",
      "photo.jpg"
    ],
    "createdBy": "testUser",
    "createdTime": "2024-12-23 10:30:00",
    "lastModifiedBy": "testUser",
    "lastModifiedTime": "2024-12-23 10:30:00"
  }
}
```

## 🧪 **测试覆盖**

### **单元测试**
**文件**: `yi-management/src/test/java/com/yi/service/TShippingOrderDetailTest.java`

**测试用例**:
- ✅ 正常查询订单详情
- ✅ 订单不存在场景
- ✅ 无效订单场景
- ✅ 无附件场景
- ✅ 附件查询异常场景
- ✅ 完整地址构建测试
- ✅ 部分地址信息测试
- ✅ 一级类目名称测试
- ✅ 状态名称测试

## 🔄 **向后兼容性**

### **兼容性保证**
1. **原有字段保持不变** - 所有原有字段的名称、类型和含义保持一致
2. **新增字段可选** - 新增字段在数据库中允许为NULL，不影响现有数据
3. **接口结构扩展** - 只增加字段，不删除或修改现有字段
4. **默认值处理** - 新字段在没有数据时返回空字符串或空列表

### **迁移建议**
1. **数据库迁移** - 先执行DDL脚本添加新字段
2. **代码部署** - 部署新版本代码
3. **数据补全** - 根据需要补全历史订单的收货人和地址信息
4. **前端适配** - 前端根据新字段调整界面显示

## 📝 **注意事项**

### **1. 数据完整性**
- 新增字段在请求时为必填，确保数据完整性
- 历史数据可能存在新字段为空的情况，需要妥善处理

### **2. 性能考虑**
- 附件查询可能影响性能，已添加异常处理
- 关联信息查询（客户名称、仓库名称）待后续实现

### **3. 扩展性**
- 地址信息支持省市区三级联动
- 附件支持多文件上传
- 预留了关联信息查询的扩展点

## 🚀 **后续优化**

### **待实现功能**
1. **关联信息查询** - 实现客户公司名称和仓库名称的查询
2. **地址验证** - 添加省市区数据的有效性验证
3. **附件管理** - 完善附件的上传、下载、预览功能
4. **缓存优化** - 对频繁查询的关联信息添加缓存

### **性能优化**
1. **批量查询** - 优化关联信息的批量查询
2. **索引优化** - 为新增字段添加合适的数据库索引
3. **分页优化** - 优化大量附件的分页加载

通过这次增强，`getShippingOrderById`接口现在能够完全满足前端界面的数据需求，提供了完整的订单详情信息！🎉
