package com.yi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yi.entity.OrderSequence;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 单号序列表 Mapper 接口
 */
@Mapper
public interface OrderSequenceMapper extends BaseMapper<OrderSequence> {

    /**
     * 根据日期键查询序列记录
     *
     * @param dateKey 日期键（YYYYMMDD）
     * @return 序列记录
     */
    OrderSequence selectByDateKey(@Param("dateKey") String dateKey);

    /**
     * 获取并递增序列号（原子操作）
     *
     * @param dateKey 日期键（YYYYMMDD）
     * @return 新的序列号
     */
    Integer getAndIncrementSequence(@Param("dateKey") String dateKey);

    /**
     * 初始化当日序列记录
     *
     * @param dateKey 日期键（YYYYMMDD）
     * @return 插入行数
     */
    int initTodaySequence(@Param("dateKey") String dateKey);

    /**
     * 更新序列号
     *
     * @param dateKey 日期键（YYYYMMDD）
     * @param sequenceValue 新的序列号
     * @return 更新行数
     */
    int updateSequence(@Param("dateKey") String dateKey, @Param("sequenceValue") Integer sequenceValue);
}
