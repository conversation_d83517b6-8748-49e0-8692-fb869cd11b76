package com.yi.service;

import com.yi.configuration.exception.BizException;
import com.yi.enums.BasicDataExceptionEnum;
import com.yi.utils.AuthSignUtils;
import com.yi.utils.Base64Utils;
import com.yi.utils.RSAUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.text.NumberFormat;
import java.util.Date;

@Slf4j
@Component
public class CommonService {
    public static final String MES_CODE = "MES_CODE";

    public static final String MES_CODE_LOCK_PREFIX = "MES_CODE_LOCK_PREFIX";

    @Value("${login.privateKey}")
    public String privateKey;
    @Value("${login.publicKey}")
    public String publicKey;

    /**
     * 解密
     *
     * @param info 加密信息
     * @return 明文
     */
    public String decodeInfo(String info) {
        try {
            byte[] bytes = RSAUtils.decryptByPrivateKey(Base64Utils.decode(info), privateKey);
            return new String(bytes);
        } catch (Exception e) {
            log.error("解密失败---", e);
            throw new BizException(BasicDataExceptionEnum.DECRYPTION_FAILURE);
        }
    }

    /**
     * 加密
     *
     * @param plainText         明文
     * @return                  密文
     */
    public String encrypt(String plainText) {
        try {
            return AuthSignUtils.encryptByRSA(publicKey, plainText);
        } catch (Exception e) {
            log.error("加密失败---", e);
            throw new BizException(BasicDataExceptionEnum.ENCRYPTION_FAILURE);
        }
    }

//    public void setBaseEntityAdd(BaseEntity baseEntity) {
//        this.setBaseEntityAdd(baseEntity, BaseContextHandler.getUserName());
//    }
//    public void setBaseEntityModify(BaseEntity baseEntity) {
//        this.setBaseEntityModify(baseEntity, BaseContextHandler.getUserName());
//    }

//    public void setBaseEntityAdd(BaseEntity baseEntity, String operationName) {
//        Date now = new Date();
//        baseEntity.setValid(1);
//        baseEntity.setCreatedBy(operationName);
//        baseEntity.setCreatedTime(now);
//        baseEntity.setLastModifiedBy(operationName);
//        baseEntity.setLastModifiedTime(now);
//    }
//    public void setBaseEntityModify(BaseEntity baseEntity, String operationName) {
//        Date now = new Date();
//        baseEntity.setLastModifiedBy(operationName);
//        baseEntity.setLastModifiedTime(now);
//    }


//    /**
//     * 生成编号
//     */
//    public String getBusinessTypeCode(BusinessCodeTypeEnum type,String businessCode) {
//
//        //code 前缀
//        String prefix = type.getValue();
//        //code 日期部分
//        String todayTimeFormat = DateUtils.dateToString(new Date(), CommonConstant.YYYYMMDD);
//        //redis的key
//        String key = MES_CODE + CommonConstant.COLON + todayTimeFormat + CommonConstant.COLON + type.getKey() + CommonConstant.COLON + businessCode;
//
//        //加锁
//        Integer exc = DLockHelper.exc(MES_CODE_LOCK_PREFIX + type.getKey(), () -> {
//            //获取自增值
//            Integer increment = (Integer) redisUtil.get(key);
//            if (increment == null) {
//                increment = 1;
//                redisUtil.set(key, 1, 86400L);
//            } else {
//                increment++;
//                redisUtil.set(key, increment, 86400L);
//            }
//            return increment;
//        });
//
//        //code 自增部分
//        NumberFormat numberFormat = NumberFormat.getInstance();
//        numberFormat.setGroupingUsed(false);//没有逗号分隔数字0,001
//        numberFormat.setMinimumIntegerDigits(type.getCodeLength());//整数部分最小四位数，补0
//        String formatIncrement = numberFormat.format(exc);
//
//        //返回
//        return prefix + todayTimeFormat + businessCode + formatIncrement;
//    }
//
//    /**
//     * 根据id和类型获取自增值
//     *
//     * @param keyName
//     * @param type
//     * @return
//     */
//    public Long incrementIndexByEnum(String keyName, IncrementIndexEnum type) {
//        String key = type.getKey() + keyName;
//        Long increment = redisUtil.increment(key, CommonConstant.LONG_ONE);
//        Long timeout = type.getTimeout();
//        if (timeout != null) {
//            redisUtil.expir(key, timeout, type.getUnit());
//        }
//        return increment;
//    }
}
