package com.yi.controller.contract.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 合同操作日志响应
 */
@Data
@ApiModel(value = "ContractOperationLogResponse", description = "合同操作日志响应")
public class ContractOperationLogResponse {

    @ApiModelProperty(value = "序号")
    private String id;

    @ApiModelProperty(value = "动作")
    private String operationType;

    @ApiModelProperty(value = "动作名称")
    private String operationTypeName;

    @ApiModelProperty(value = "操作人")
    private String operator;

    @ApiModelProperty(value = "操作时间")
    private String operationTime;

    @ApiModelProperty(value = "备注")
    private String remark;
}
