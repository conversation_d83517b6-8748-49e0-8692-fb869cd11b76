package com.yi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yi.entity.SysResource;

import java.util.List;

/**
 * 系统资源表 服务类
 */
public interface SysResourceService extends IService<SysResource> {

    /**
     * 查询所有资源（树形结构）
     *
     * @param resourceType 资源类型
     * @param status 状态
     * @return 资源列表
     */
    List<SysResource> selectResourceTree(Integer resourceType, Integer status);

    /**
     * 根据父ID查询子资源
     *
     * @param parentId 父资源ID
     * @return 资源列表
     */
    List<SysResource> selectByParentId(Long parentId);

    /**
     * 根据资源编码查询资源
     *
     * @param resourceCode 资源编码
     * @return 资源信息
     */
    SysResource selectByResourceCode(String resourceCode);

    /**
     * 根据用户ID查询用户拥有的资源
     *
     * @param userId 用户ID
     * @param resourceType 资源类型
     * @return 资源列表
     */
    List<SysResource> selectByUserId(Long userId, Integer resourceType);

    /**
     * 根据角色ID查询角色拥有的资源
     *
     * @param roleId 角色ID
     * @return 资源列表
     */
    List<SysResource> selectByRoleId(Long roleId);

    /**
     * 查询用户的菜单权限
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
    List<SysResource> selectMenusByUserId(Long userId);

    /**
     * 查询用户的按钮权限
     *
     * @param userId 用户ID
     * @return 按钮权限列表
     */
    List<SysResource> selectButtonsByUserId(Long userId);

    /**
     * 查询所有启用的资源
     *
     * @return 资源列表
     */
    List<SysResource> selectEnabledResources();

    /**
     * 创建资源
     *
     * @param resource 资源信息
     * @return 是否成功
     */
    boolean createResource(SysResource resource);

    /**
     * 更新资源
     *
     * @param resource 资源信息
     * @return 是否成功
     */
    boolean updateResource(SysResource resource);

    /**
     * 删除资源
     *
     * @param resourceId 资源ID
     * @return 是否成功
     */
    boolean deleteResource(Long resourceId);

    /**
     * 检查资源编码是否存在
     *
     * @param resourceCode 资源编码
     * @param excludeId 排除的资源ID
     * @return 是否存在
     */
    boolean checkResourceCodeExists(String resourceCode, Long excludeId);
}
