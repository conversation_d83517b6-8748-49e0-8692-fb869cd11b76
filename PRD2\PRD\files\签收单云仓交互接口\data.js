﻿$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,_(g,h,i,_(j,k,l,k)),m,[],n,_(h,o),p,[q],r,_(s,t,u,v,g,w,x,_(),y,[],z,_(A,B,C,D,E,_(F,G,H,I),J,null,K,L,L,M,N,O,null,P,Q,R,S,T,U,V,Q,W,X,Y,_(F,G,H,Z),ba,Q,bb,X,bc,_(bd,be,bf,bg,bh,bg,bi,bg,bj,k,H,_(bk,bl,bm,bl,bn,bl,bo,bp)),i,_(j,k,l,k)),bq,_(),br,_(),bs,_(bt,[_(bu,bv,bw,h,bx,by,u,bz,bA,bz,bB,bC,z,_(bD,bE,i,_(j,bF,l,bG),A,bH,bI,_(bJ,bK,bL,bM)),bq,_(),bN,_(),bO,be),_(bu,bP,bw,h,bx,by,u,bz,bA,bz,bB,bC,z,_(bD,bE,i,_(j,bQ,l,bG),A,bH,bI,_(bJ,bK,bL,bR)),bq,_(),bN,_(),bO,be)])),bS,_(),bT,_(bU,_(bV,bW),bX,_(bV,bY)));}; 
var b="url",c="签收单云仓交互接口.html",d="generationDate",e=new Date(1753855219399.88),f="defaultAdaptiveView",g="name",h="",i="size",j="width",k=0,l="height",m="adaptiveViews",n="sketchKeys",o="s0",p="variables",q="OnLoadVariable",r="page",s="packageId",t="dc7a4235c09b46c7ae83787c4c67ec6f",u="type",v="Axure:Page",w="签收单云仓交互接口",x="notes",y="annotations",z="style",A="baseStyle",B="627587b6038d43cca051c114ac41ad32",C="pageAlignment",D="center",E="fill",F="fillType",G="solid",H="color",I=0xFFFFFFFF,J="image",K="imageAlignment",L="near",M="imageRepeat",N="auto",O="favicon",P="sketchFactor",Q="0",R="colorStyle",S="appliedColor",T="fontName",U="Applied Font",V="borderWidth",W="borderVisibility",X="all",Y="borderFill",Z=0xFF797979,ba="cornerRadius",bb="cornerVisibility",bc="outerShadow",bd="on",be=false,bf="offsetX",bg=5,bh="offsetY",bi="blurRadius",bj="spread",bk="r",bl=0,bm="g",bn="b",bo="a",bp=0.349019607843137,bq="adaptiveStyles",br="interactionMap",bs="diagram",bt="objects",bu="id",bv="e43c66372c0b4794ad6dcc0faa9526d9",bw="label",bx="friendlyType",by="矩形",bz="vectorShape",bA="styleType",bB="visible",bC=true,bD="fontWeight",bE="700",bF=972,bG=21,bH="8c7a4c5ad69a4369a5f7788171ac0b32",bI="location",bJ="x",bK=59,bL="y",bM=39,bN="imageOverrides",bO="generateCompound",bP="27beab1b0020472f99da7d3d8093c578",bQ=684,bR=90,bS="masters",bT="objectPaths",bU="e43c66372c0b4794ad6dcc0faa9526d9",bV="scriptId",bW="u1667",bX="27beab1b0020472f99da7d3d8093c578",bY="u1668";
return _creator();
})());