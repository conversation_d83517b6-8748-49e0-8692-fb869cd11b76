package com.yi.controller.contract.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

import java.util.List;

/**
 * 合同详情响应
 */
@Data
@ApiModel(value = "ContractDetailResponse", description = "合同详情响应")
public class ContractDetailResponse {

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "我司主体ID")
    private String outCustomerCompanyId;

    @ApiModelProperty(value = "我司主体名称")
    private String outCustomerCompanyName;

    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    @ApiModelProperty(value = "客户主体ID")
    private String customerCompanyId;

    @ApiModelProperty(value = "客户主体名称")
    private String customerCompanyName;

    @ApiModelProperty(value = "合同生效日期")
    private String effectiveDate;

    @ApiModelProperty(value = "合同失效日期")
    private String expiryDate;

    @ApiModelProperty(value = "作废原因")
    private String cancelReason;

    @ApiModelProperty(value = "备注信息")
    private String remark;

    @ApiModelProperty(value = "合同状态")
    private String contractStatus;

    @ApiModelProperty(value = "合同状态名称")
    private String contractStatusName;

    @ApiModelProperty(value = "归档状态")
    private String archiveStatus;

    @ApiModelProperty(value = "归档状态名称")
    private String archiveStatusName;

    @ApiModelProperty(value = "合同附件列表")
    private List<ContractAttachmentResponse> attachments;

    @ApiModelProperty(value = "SKU明细列表")
    private List<ContractSkuResponse> skuDetails;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private String createdTime;

    @ApiModelProperty(value = "最后修改人")
    private String lastModifiedBy;

    @ApiModelProperty(value = "最后修改时间")
    private String lastModifiedTime;

    @ApiModelProperty(value = "有效性")
    private String valid;

    @ApiModelProperty(value = "合同附件列表")
    private List<String> attachmentPaths;
}
