package com.yi.controller.contract.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 合同分页响应
 */
@Data
@ApiModel(value = "ContractPageResponse", description = "合同分页响应")
public class ContractPageResponse {

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "合同状态")
    private String contractStatus;

    @ApiModelProperty(value = "合同状态名称")
    private String contractStatusName;

    @ApiModelProperty(value = "归档状态")
    private String archiveStatus;

    @ApiModelProperty(value = "归档状态名称")
    private String archiveStatusName;

    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    @ApiModelProperty(value = "我司主体名称")
    private String outCustomerCompanyName;

    @ApiModelProperty(value = "客户主体名称")
    private String customerCompanyName;

    @ApiModelProperty(value = "生效日期")
    private String effectiveDate;

    @ApiModelProperty(value = "失效日期")
    private String expiryDate;

    @ApiModelProperty(value = "作废原因")
    private String cancelReason;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private String createdTime;
}
