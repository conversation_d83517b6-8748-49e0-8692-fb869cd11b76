package com.yi.utils;


import com.alibaba.fastjson.JSONObject;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * RSA公钥/私钥/签名工具包
 * 字符串格式的密钥在未在特殊说明情况下都为BASE64编码格式<br/>
 * 由于非对称加密速度极其缓慢，一般文件不使用它来加密而是使用对称加密，<br/>
 * 非对称加密算法可以用来对对称加密的密钥加密，这样保证密钥的安全也就保证了数据的安全
 *
 * <AUTHOR>
 */
public class RSAUtils {
       /**
     * 加密算法RSA
     */
    private static final String KEY_ALGORITHM = "RSA";
    /**
     * 获取公钥的key
     */
    private static final String PUBLIC_KEY = "RSAPublicKey";
    /**
     * 获取私钥的key
     */
    private static final String PRIVATE_KEY = "RSAPrivateKey";
    /**
     * 签名算法
     */
    private static final String SIGNATURE_ALGORITHM = "MD5withRSA";
    /**
     * 常量0
     */
    private static final int ZERO = 0;
    /**
     * RSA最大加密明文最大大小
     */
    private static final int MAX_ENCRYPT_BLOCK = 117;
    /**
     * RSA最大解密密文最大大小
     * 当密钥位数为1024时,解密密文最大是 128
     * 当密钥位数为2048时需要改为 256 不然会报错（Decryption error）
     */
    private static final int MAX_DECRYPT_BLOCK = 128;
    /**
     * 默认key大小
     */
    private static final int DEFAULT_KEY_SIZE = 1024;


    private static final String UTF8CHARSET = "UTF-8";

    /**
     * 生成密钥对(公钥和私钥)
     *
     * @return
     * @throws Exception
     */
    public static Map<String, Object> initKey() throws Exception {
        return initKey(DEFAULT_KEY_SIZE);
    }

    /**
     * 生成密钥对(公钥和私钥)
     *
     * @return
     * @throws Exception
     */
    public static Map<String, Object> initKey(int keySize) throws Exception {
        KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance(KEY_ALGORITHM);
        keyPairGen.initialize(keySize);
        KeyPair keyPair = keyPairGen.generateKeyPair();
        RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
        RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();
        Map<String, Object> keyMap = new HashMap<String, Object>(2);
        keyMap.put(PUBLIC_KEY, publicKey);
        keyMap.put(PRIVATE_KEY, privateKey);
        return keyMap;
    }


    public static String calcRsaSign(String appKey, String secret, String privateKey, Map paramMap) {
        Set<String> keySet = paramMap.keySet();
        JSONObject requestParams = new JSONObject();
        for (String key : keySet) {
            requestParams.put(key, paramMap.get(key));
        }

        // 组装请求参数，作为requestbody
        JSONObject jsonObject = new JSONObject();
        //appId
        jsonObject.put("appKey", appKey);
        //appSecret
        jsonObject.put("appSecret", secret);
        //时间戳
        jsonObject.put("timestamp", getRtick());
        //签名方式
        jsonObject.put("signType", "rsa");
        //入参转mde5
        //对请求参数进行排序,保证两边一致性
        if (requestParams != null && !"".equals(requestParams)) {
            jsonObject.put("requestParams", md5(requestParams.toString().getBytes()));
        }

        String signStr = jsonObject.toJSONString();
        byte[] rsaSign = null;
        try {
            rsaSign = RSAUtils.encryptByPrivateKey(signStr.getBytes(), privateKey);
        } catch (Exception e) {
            e.printStackTrace();
        }
        //rsa算出来的sign，需要urlencode
        return Base64Utils.encode(rsaSign, false);

    }

    public static String sortString(String str) {
        if (str != null) {
            byte[] bytes = str.getBytes();
            Arrays.sort(bytes);
            return new String(bytes);
        }
        return null;
    }

    /**
     * md5
     *
     * @param data
     * @return
     */
    public static String md5(byte[] data) {
        char hexDigits[] = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
        byte[] btInput = data;
        // 获得MD5摘要算法的 MessageDigest 对象
        MessageDigest mdInst;
        try {
            mdInst = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
        // 使用指定的字节更新摘要
        mdInst.update(btInput);
        // 获得密文
        byte[] md = mdInst.digest();
        // 把密文转换成十六进制的字符串形式
        int j = md.length;
        char str[] = new char[j * 2];
        int k = 0;
        for (int i = 0; i < j; i++) {
            byte byte0 = md[i];
            str[k++] = hexDigits[byte0 >>> 4 & 0xf];
            str[k++] = hexDigits[byte0 & 0xf];
        }
        return new String(str);
    }

    /**
     * 获取当前的时间戳参数
     *
     * @return
     */
    public static String getRtick() {
        Long timestamp = new Date().getTime();
        return timestamp.toString();
    }

    /**
     * 公钥加密
     *
     * @param data      源数据
     * @param publicKey 公钥(BASE64编码)
     * @return
     * @throws Exception
     */
    public static byte[] encryptByPublicKey(byte[] data, String publicKey)
            throws Exception {
        byte[] keyBytes = Base64Utils.decode(publicKey);
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        return encrypt(data, KeyFactory.getInstance(KEY_ALGORITHM), keyFactory.generatePublic(x509KeySpec));
    }


    /**
     * 私钥加密
     *
     * @param data       源数据
     * @param privateKey 私钥(BASE64编码)
     * @return
     * @throws Exception
     */
    public static byte[] encryptByPrivateKey(byte[] data, String privateKey)
            throws Exception {
        byte[] keyBytes = Base64Utils.decode(privateKey);
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        Key privateK = keyFactory.generatePrivate(pkcs8KeySpec);
        return encrypt(data, keyFactory, privateK);
    }


    /**
     * 私钥解密
     *
     * @param encryptedData 已加密数据
     * @param privateKey    私钥(BASE64编码)
     * @return
     * @throws Exception
     */
    public static byte[] decryptByPrivateKey(byte[] encryptedData, String privateKey)
            throws Exception {
        byte[] keyBytes = Base64Utils.decode(privateKey);
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        return decrypt(encryptedData, keyFactory, keyFactory.generatePrivate(pkcs8KeySpec));
    }


    /**
     * 公钥解密
     *
     * @param encryptedData 已加密数据
     * @param publicKey     公钥(BASE64编码)
     * @return
     * @throws Exception
     */
    public static byte[] decryptByPublicKey(byte[] encryptedData, String publicKey)
            throws Exception {
        byte[] keyBytes = Base64Utils.decode(publicKey);
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        Key publicK = keyFactory.generatePublic(x509KeySpec);
        return decrypt(encryptedData, keyFactory, publicK);

    }


    /**
     * 用私钥对信息生成数字签名
     *
     * @param data       已加密数据
     * @param privateKey 私钥(BASE64编码)
     * @return
     * @throws Exception
     */
    public static String sign(byte[] data, String privateKey) throws Exception {
        byte[] keyBytes = Base64Utils.decode(privateKey);
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        PrivateKey privateK = keyFactory.generatePrivate(pkcs8KeySpec);
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initSign(privateK);
        signature.update(data);
        return Base64Utils.encode(signature.sign(), false);
    }


    /**
     * 校验数字签名
     *
     * @param data      已加密数据
     * @param publicKey 公钥(BASE64编码)
     * @param sign      数字签名
     * @return
     * @throws Exception
     */
    public static boolean verify(byte[] data, String publicKey, String sign)
            throws Exception {
        byte[] keyBytes = Base64Utils.decode(publicKey);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        PublicKey publicK = keyFactory.generatePublic(keySpec);
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initVerify(publicK);
        signature.update(data);
        return signature.verify(Base64Utils.decode(sign));
    }


    /**
     * 获取私钥
     *
     * @param keyMap 密钥对
     * @return
     * @throws Exception
     */
    public static String getPrivateKey(Map<String, Object> keyMap)
            throws Exception {
        Key key = (Key) keyMap.get(PRIVATE_KEY);
        return Base64Utils.encode(key.getEncoded(), false);
    }


    /**
     * 获取公钥
     *
     * @param keyMap 密钥对
     * @return
     * @throws Exception
     */
    public static String getPublicKey(Map<String, Object> keyMap)
            throws Exception {
        Key key = (Key) keyMap.get(PUBLIC_KEY);
        return Base64Utils.encode(key.getEncoded(), false);
    }

    /**
     * 解密公共方法
     */
    private static byte[] decrypt(byte[] data, KeyFactory keyFactory, Key key) throws Exception {

        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(Cipher.DECRYPT_MODE, key);
        return encryptAndDecrypt(data, cipher, MAX_DECRYPT_BLOCK);
    }

    /**
     * 加密公共方法
     */
    private static byte[] encrypt(byte[] data, KeyFactory keyFactory, Key key) throws Exception {
        // 对数据加密
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(Cipher.ENCRYPT_MODE, key);
        return encryptAndDecrypt(data, cipher, MAX_ENCRYPT_BLOCK);
    }


    /**
     * 加密解密分段处理公共方法
     */
    private static byte[] encryptAndDecrypt(byte[] data, Cipher cipher, int maxSize) throws Exception {
        int inputLen = data.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = ZERO;
        byte[] cache;
        int i = ZERO;
        // 对数据分段加密
        while (inputLen - offSet > ZERO) {
            if (inputLen - offSet > maxSize) {
                cache = cipher.doFinal(data, offSet, maxSize);
            } else {
                cache = cipher.doFinal(data, offSet, inputLen - offSet);
            }
            out.write(cache, ZERO, cache.length);
            i++;
            offSet = i * maxSize;
        }
        byte[] encryptedData = out.toByteArray();
        out.close();
        return encryptedData;
    }

    public static void main(String[] args) throws Exception {

        //生成公钥与私钥
        Map<String, Object> initKeyMap = RSAUtils.initKey(1024);
        //公钥
        String publicKey = RSAUtils.getPublicKey(initKeyMap);
        //私钥
        String privateKey = RSAUtils.getPrivateKey(initKeyMap);
        System.out.println("公钥 长度: " + publicKey.length() + " 值: " + publicKey);
        System.out.println("私钥 长度: " + privateKey.length() + " 值: " + privateKey);
        Map<String, String> paramMap = new HashMap<>();

        paramMap.put("memberName", "11111111");
        paramMap.put("orderCode", "Hd1111111");
        paramMap.put("sapDeliveryCode", "11111111");
        paramMap.put("productName", "产品");
        paramMap.put("palletNum", "11");
        paramMap.put("receiverCompany", "长安区");
        paramMap.put("receiverAddress", "二月二十八前台注册上游取货地址一的详细地址");
        paramMap.put("receiverPhone", "山西省");
        paramMap.put("deliveryDate", "2019-01-01 12:00:00");
        paramMap.put("arrivalDate", "2019-06-01 12:00:00");
        paramMap.put("carNo", "皖b11111");
        paramMap.put("warehouseName", "仓库");
        paramMap.put("dispatchStatus", "取消");
        paramMap.put("type", "C");


        Set<String> keySet = paramMap.keySet();
        JSONObject requestParams = new JSONObject();
        for (String key : keySet) {
            requestParams.put(key, paramMap.get(key));
        }


        // 组装请求参数，作为requestbody
        JSONObject jsonObject = new JSONObject();
        //appId
        jsonObject.put("appKey", "app_key");
        //appSecret
        jsonObject.put("appSecret", "app_secret");
        //时间戳
        jsonObject.put("timestamp", getRtick());
        //签名方式
        jsonObject.put("signType", "rsa");
        //入参转mde5
        //对请求参数进行排序,保证两边一致性
        if (requestParams != null && !"".equals(requestParams)) {
            jsonObject.put("requestParams", md5(requestParams.toString().getBytes()));
        }

        String signStr = jsonObject.toJSONString();
        byte[] rsaSign = null;
        try {
            rsaSign = RSAUtils.encryptByPrivateKey(signStr.getBytes(), privateKey);
        } catch (Exception e) {
            e.printStackTrace();
        }


        System.out.println("加密后 ：" + Base64Utils.encode(rsaSign, false));
        byte[] bytes2 = RSAUtils.decryptByPublicKey(rsaSign, publicKey);
        System.out.println("解密后 ：" + new String(bytes2));
//        //生成公钥与私钥
//        Map<String, Object> initKeyMap = RSAUtils.initKey(1024);
//        //公钥
//        String publicKey = RSAUtils.getPublicKey(initKeyMap);
//        //私钥
//        String privateKey = RSAUtils.getPrivateKey(initKeyMap);
//        System.out.println("公钥 长度: " + publicKey.length() + " 值: " + publicKey);
//        System.out.println("私钥 长度: " + privateKey.length() + " 值: " + privateKey);
//
//        StringBuilder str = new StringBuilder();
//        for (int i = 0; i < 2000; i++) {
//            str.append("我爱祖国|");
//        }
//        byte[] bytes1 = RSAUtils.encryptByPublicKey(str.toString().getBytes(), publicKey);
//        byte[] bytes2 = RSAUtils.decryptByPrivateKey(bytes1, privateKey);
//
//        System.out.println();
//        System.out.println("****** 公钥加密 私钥解密 start ******");
//        System.out.println("加密前长度 ：" + str.toString().length());
//        System.out.println("加密后 ：" + Base64Utils.encode(bytes1, false));
//        System.out.println("解密后 ：" + new String(bytes2));
//        System.out.println("解密后长度 ：" + new String(bytes2).length());
//        System.out.println("****** 公钥加密 私钥解密 end ******");
//
//        System.out.println();
//        byte[] bytes3 = RSAUtils.encryptByPrivateKey(str.toString().getBytes(), privateKey);
//        byte[] bytes4 = RSAUtils.decryptByPublicKey(bytes3, publicKey);
//
//        System.out.println("****** 私钥加密 公钥解密 start ******");
//        System.out.println("加密前长度 ：" + str.toString().length());
//        System.out.println("加密后 ：" + Base64Utils.encode(bytes3, false));
//        System.out.println("解密后 ：" + new String(bytes4));
//        System.out.println("解密后长度 ：" + new String(bytes4).length());
//        System.out.println("****** 私钥加密 公钥解密 end ******");
//##-------------------------------------------------------------------------------------##
//        //生成公钥与私钥
//        Map<String, Object> initKeyMap = RSAUtils.initKey(1024);
//        //公钥
//        String publicKey = RSAUtils.getPublicKey(initKeyMap);
//        //私钥
//        String privateKey = RSAUtils.getPrivateKey(initKeyMap);
//        System.out.println("公钥 长度: " + publicKey.length() + " 值: " + publicKey);
//        System.out.println("私钥 长度: " + privateKey.length() + " 值: " + privateKey);
//
//        StringBuilder str = new StringBuilder();
//        for (int i = 0; i < 2000; i++) {
//            str.append("我爱祖国|");
//        }
//
//        //加签
//        String sign = RSAUtils.sign(str.toString().getBytes(), privateKey);
//        //验签
//        boolean verify = RSAUtils.verify(str.toString().getBytes(), publicKey, sign);
//
//        System.out.println();
//        System.out.println("****** 加签 start ******");
//        System.out.println("签名 sign: " + sign);
//        System.out.println("验签结果: " + verify);
//        System.out.println("****** 加签 end ******");
    }




    /**
     * 计算参数签名
     * @param developerId 开发者ID
     * @param privateKey 用户私钥
     * @param host 请求的HOST地址（http://ip:port/context）
     * @param methodName 请求的接口方法名
     * @param rtick 时间戳参数
     * @param urlParams url参数（param1=value1&param2=value2&param3=value3）
     * @param requestBody request body 参数（JSON字符串）
     * @return
     */
    public static String calcRsaSign(String developerId, String privateKey, String host, String methodName, String rtick, String urlParams, String requestBody) {
        String url = host+methodName;

        Map<String, String> mySignedURLParams = new TreeMap<>();
        mySignedURLParams.put("developerId", developerId);
        mySignedURLParams.put("rtick", rtick);
        mySignedURLParams.put("signType", "rsa");

        if(urlParams != null && !"".equals(urlParams)){
            String[] params = urlParams.split("&");
            for(String p1 : params){
                String[] p2 = p1.split("=");
                String key = p2[0];
                String value = "";
                if(p2.length == 2){
                    value = p2[1];
                }
                mySignedURLParams.put(key, value);
            }
        }

        String requestPath;
        try {
            requestPath = new URL(url).getPath();
        } catch (MalformedURLException e) {
            throw new RuntimeException(e.getMessage(), e);
        }

        StringBuilder signStringBuilder = new StringBuilder();
        for (Map.Entry<String,String> params : mySignedURLParams.entrySet()) {
            signStringBuilder.append(params.getKey());
            signStringBuilder.append("=");
            signStringBuilder.append(params.getValue());
        }
        signStringBuilder.append(requestPath);

        if (requestBody != null && !"".equals(requestBody) ) {
            String requestMd5 = getRequestMd5(requestBody);
            signStringBuilder.append(requestMd5);
        }

        String signString = signStringBuilder.toString();
        String rsaSign =  calcRsaSign(privateKey, signString);
        //rsa算出来的sign，需要urlencode
        try {
            rsaSign = URLEncoder.encode(rsaSign,UTF8CHARSET);
        } catch (UnsupportedEncodingException e) {
            rsaSign = null;
        }
        return rsaSign;
    }

    /**
     * 获取request body 的MD5
     * @param requestBody
     * @return
     */
    private static String getRequestMd5(final String requestBody) {
        byte[] data = null;

        String newRequestBody = convertToUtf8(requestBody);
        if (newRequestBody != null) {
            try {
                data = newRequestBody.getBytes(UTF8CHARSET);
            }catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e.getMessage(), e);
            }
        }
        return md5(data);
    }

    /**
     * 计算参数RSA签名
     * @param privateKey
     * @param signData
     * @return
     */
    private static String calcRsaSign(String privateKey, final String signData) {
        byte[] data;
        try {
            data = signData.getBytes(UTF8CHARSET);
        }catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
        byte[] sign = null;
        // 解密由base64编码的私钥
        byte[] privateKeyBytes = base64decode(privateKey.getBytes());

        // 构造PKCS8EncodedKeySpec对象
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(privateKeyBytes);

        // KEY_ALGORITHM 指定的加密算法
        KeyFactory keyFactory;
        try {
            keyFactory = KeyFactory.getInstance("RSA");
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e.getMessage(), e);
        }

        // 取私钥匙对象
        PrivateKey priKey;
        try {
            priKey = keyFactory.generatePrivate(pkcs8KeySpec);
        } catch (InvalidKeySpecException e) {
            throw new RuntimeException(e.getMessage(), e);
        }

        // 用私钥对信息生成数字签名
        Signature signature;
        try {
            signature = Signature.getInstance("SHA1withRSA");
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
        try {
            signature.initSign(priKey);
        } catch (InvalidKeyException e) {
            throw new RuntimeException(e.getMessage(), e);
        }

        try {
            signature.update(data);
            sign = signature.sign();
        } catch (SignatureException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
        return new String(base64encode(sign));
    }

    /**
     * 转换字符集到utf8
     *
     * @param src
     * @return
     */
    private static String convertToUtf8(String src) {
        if (src == null || src.length() == 0) {
            return src;
        }
        if (UTF8CHARSET.equalsIgnoreCase(Charset.defaultCharset().name())) {
            return src;
        }

        byte[] srcData = src.getBytes();
        try {
            return new String(srcData, UTF8CHARSET);
        }
        catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }



    /**
     * base64编码
     * @param data
     * @return
     */
    public static byte[] base64encode(byte[] data) {
        return Base64.encodeBase64(data);
    }

    /**
     * base64编码字符串
     * @param data
     * @return
     */
    public static String base64encodeString(byte[] data) {
        String base64Str = Base64.encodeBase64String(data);
        base64Str = replaceBlank(base64Str);
        return base64Str;
    }

    /**
     * base64解码
     * @param data
     * @return
     */
    public static byte[] base64decode(byte[] data) {
        try {
            return Base64.decodeBase64(data);
        } catch (Exception e) {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            for (int i = 0; i < data.length; i++) {
                byte c = data[i];
                if (c == 13 || c == 10) {
                    continue;
                }
                outputStream.write(c);
            }
            try {
                outputStream.close();
            } catch (IOException e2) {
                //
            }
            data = outputStream.toByteArray();
            return Base64.decodeBase64(data);
        }
    }

    public static String replaceBlank(String str) {
        String dest = "";
        if (!isBlank(str)) {
            Pattern p = Pattern.compile("\\s*|\t|\r|\n");
            Matcher m = p.matcher(str);
            dest = m.replaceAll("");
        }
        return dest;
    }

    public static boolean isBlank(final String value) {
        return (value == null || value.trim().length() < 1);
    }

}