﻿<!DOCTYPE html>
<html>
  <head>
    <title>客户仓库新增/编辑/详情页</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/客户仓库新增_编辑_详情页/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/客户仓库新增_编辑_详情页/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (线段) -->
      <div id="u831" class="ax_default line1">
        <img id="u831_img" class="img " src="images/客户管理/u350.svg"/>
        <div id="u831_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u832" class="ax_default box_21">
        <div id="u832_div" class=""></div>
        <div id="u832_text" class="text ">
          <p><span>客户仓库新增/编辑/详情&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; X</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u833" class="ax_default link_button">
        <div id="u833_div" class=""></div>
        <div id="u833_text" class="text ">
          <p><span>刷新本页</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u834" class="ax_default label">
        <div id="u834_div" class=""></div>
        <div id="u834_text" class="text ">
          <p><span>*公司名称</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u835" class="ax_default label">
        <div id="u835_div" class=""></div>
        <div id="u835_text" class="text ">
          <p><span>*仓库名称</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u836" class="ax_default text_field">
        <div id="u836_div" class=""></div>
        <input id="u836_input" type="text" value="" class="u836_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u837" class="ax_default label">
        <div id="u837_div" class=""></div>
        <div id="u837_text" class="text ">
          <p><span>*地址详情</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u838" class="ax_default text_field">
        <div id="u838_div" class=""></div>
        <input id="u838_input" type="text" value="" class="u838_input"/>
      </div>

      <!-- Unnamed (下拉列表) -->
      <div id="u839" class="ax_default droplist">
        <div id="u839_div" class=""></div>
        <select id="u839_input" class="u839_input">
        </select>
      </div>

      <!-- Unnamed (下拉列表) -->
      <div id="u840" class="ax_default droplist">
        <div id="u840_div" class=""></div>
        <select id="u840_input" class="u840_input">
        </select>
      </div>

      <!-- Unnamed (下拉列表) -->
      <div id="u841" class="ax_default droplist">
        <div id="u841_div" class=""></div>
        <select id="u841_input" class="u841_input">
        </select>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u842" class="ax_default label">
        <div id="u842_div" class=""></div>
        <div id="u842_text" class="text ">
          <p><span>省</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u843" class="ax_default label">
        <div id="u843_div" class=""></div>
        <div id="u843_text" class="text ">
          <p><span>市</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u844" class="ax_default label">
        <div id="u844_div" class=""></div>
        <div id="u844_text" class="text ">
          <p><span>区</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u845" class="ax_default label">
        <div id="u845_div" class=""></div>
        <div id="u845_text" class="text ">
          <p><span>*收货人</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u846" class="ax_default text_field">
        <div id="u846_div" class=""></div>
        <input id="u846_input" type="text" value="" class="u846_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u847" class="ax_default label">
        <div id="u847_div" class=""></div>
        <div id="u847_text" class="text ">
          <p><span>*手机号</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u848" class="ax_default text_field">
        <div id="u848_div" class=""></div>
        <input id="u848_input" type="text" value="" class="u848_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u849" class="ax_default primary_button">
        <div id="u849_div" class=""></div>
        <div id="u849_text" class="text ">
          <p><span>保存</span></p>
        </div>
      </div>

      <!-- Unnamed (下拉列表) -->
      <div id="u850" class="ax_default droplist">
        <div id="u850_div" class=""></div>
        <select id="u850_input" class="u850_input">
        </select>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u851" class="ax_default label">
        <div id="u851_div" class=""></div>
        <div id="u851_text" class="text ">
          <p><span style="color:#D9001B;">*</span><span>客户类型</span></p>
        </div>
      </div>

      <!-- Unnamed (下拉列表) -->
      <div id="u852" class="ax_default droplist">
        <div id="u852_div" class=""></div>
        <select id="u852_input" class="u852_input">
          <option class="u852_input_option" selected value="合约客户">合约客户</option>
          <option class="u852_input_option" value="非合约客户">非合约客户</option>
        </select>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u853" class="ax_default label">
        <div id="u853_div" class=""></div>
        <div id="u853_text" class="text ">
          <p><span style="color:#D9001B;">*</span><span>SKU类型</span></p>
        </div>
      </div>

      <!-- Unnamed (下拉列表) -->
      <div id="u854" class="ax_default droplist">
        <div id="u854_div" class=""></div>
        <select id="u854_input" class="u854_input">
          <option class="u854_input_option" selected value="一级 二级">一级 二级</option>
          <option class="u854_input_option" value="循环托盘 1311">循环托盘 1311</option>
          <option class="u854_input_option" value="循环托盘 1140">循环托盘 1140</option>
        </select>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u855" class="ax_default primary_button">
        <div id="u855_div" class=""></div>
        <div id="u855_text" class="text ">
          <p><span>增加</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u856" class="ax_default primary_button">
        <div id="u856_div" class=""></div>
        <div id="u856_text" class="text ">
          <p><span>删除</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u857" class="ax_default sticky_1">
        <div id="u857_div" class=""></div>
        <div id="u857_text" class="text ">
          <p><span>【客户类型】选择 合约客户 时，【SKU类型】为必选字段，至少填1个，最多填9个，选择 非合约客户 时，【SKU类型】字段隐藏；</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u858" class="ax_default label">
        <div id="u858_div" class=""></div>
        <div id="u858_text" class="text ">
          <p><span>座机号</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u859" class="ax_default text_field">
        <div id="u859_div" class=""></div>
        <input id="u859_input" type="text" value="" class="u859_input"/>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
