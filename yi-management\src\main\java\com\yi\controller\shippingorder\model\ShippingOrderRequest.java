package com.yi.controller.shippingorder.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 发运订单请求
 */
@Data
@ApiModel(value = "ShippingOrderRequest", description = "发运订单请求")
public class ShippingOrderRequest {

    @ApiModelProperty(value = "主键ID（编辑时必填）")
    private String id;

    @ApiModelProperty(value = "合同编号", required = true)
    @NotBlank(message = "合同编号不能为空")
    private String contractCode;

    @ApiModelProperty(value = "客户公司ID", required = true)
    @NotBlank(message = "客户公司不能为空")
    private String customerCompanyId;

    @ApiModelProperty(value = "收货仓库ID", required = true)
    @NotBlank(message = "收货仓库不能为空")
    private String warehouseId;

    @ApiModelProperty(value = "一级类目：1-共享托盘", required = true)
    @NotBlank(message = "一级类目不能为空")
    private String firstCategory;

    @ApiModelProperty(value = "二级类目")
    private String secondCategory;

    @ApiModelProperty(value = "需求数量", required = true)
    @NotBlank(message = "需求数量不能为空")
    private String count;

    @ApiModelProperty(value = "需求时间（格式：yyyy-MM-dd）", required = true)
    @NotBlank(message = "需求时间不能为空")
    private String demandTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "附件链接列表")
    private List<String> attachmentUrls;
}
