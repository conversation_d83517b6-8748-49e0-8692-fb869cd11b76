package com.yi.configuration.auth;

import com.yi.configuration.jwt.*;
import com.yi.controller.user.io.LoginRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service("logisticsAuthService")
public class MesAuthService {

    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    public TokenModule loginCreateToken(LoginRequest requestModel) {
        TokenVo tokenVo = generateToken(new JWTInfo(requestModel.getUserId(), requestModel.getUserName()
                ));
        return new TokenModule(tokenVo.getToken(), tokenVo.getExpire());
    }

    // 生成token
    public TokenVo generateToken(IJWTInfo jwtInfo) {
        TokenVo tokenVo = jwtTokenUtil.generateToken(jwtInfo);
        Map<String, Object> tokenMap = new HashMap<>();
        tokenMap.put(JwtConstants.JWT_TOKEN_MAP_KEY_TOKEN, tokenVo.getToken());
        tokenMap.put(JwtConstants.JWT_TOKEN_MAP_KEY_TOKEN_EXPIRE_TIME, tokenVo.getExpireTime());
        // redisUtil.set(jwtInfo.getUserCode(), tokenMap, ConverterUtils.toLong(tokenVo.getExpire()));
        log.info(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>generateToken " + tokenVo.getToken() + " ;add redis with key [" + jwtInfo.getUserCode() + "], value " + tokenMap);
        return tokenVo;
    }

    // 刷新token
    public TokenModule refreshToken(String oldToken) {
        IJWTInfo infoFromToken = jwtTokenUtil.getInfoFromToken(oldToken);
        TokenVo tokenVo = generateToken(new JWTInfo(infoFromToken.getSupplierId(), infoFromToken.getFactoryId(),
                infoFromToken.getUserName(),
                infoFromToken.getUserId(),
                infoFromToken.getNickName(), infoFromToken.getCustomerCompanyId(), infoFromToken.getAppId(), infoFromToken.getUserCode(), infoFromToken.getUserRoles()));
        return new TokenModule(tokenVo.getToken(), tokenVo.getExpire());
    }

    // 清空token
    public void cleanUserToken(CleanUserTokenMessage model) {
        // 可能会影响多个系统的token，所以放list
        List<JwtApplicationTokenPrefixEnum> jwtApplicationTokenPrefixEnumList = new ArrayList<>();
        if (JwtApplicationTokenPrefixEnum.MES_MANAGEMENT_WEB.getKey().equals(model.getUserTokenType())) {
            jwtApplicationTokenPrefixEnumList.add(JwtApplicationTokenPrefixEnum.MES_MANAGEMENT_WEB);
        } else if (JwtApplicationTokenPrefixEnum.MES_CUSTOMER_WX.getKey().equals(model.getUserTokenType())) {
            jwtApplicationTokenPrefixEnumList.add(JwtApplicationTokenPrefixEnum.MES_CUSTOMER_WX);
        } else if (JwtApplicationTokenPrefixEnum.MES_CUSTOMER_APP.getKey().equals(model.getUserTokenType())) {
            jwtApplicationTokenPrefixEnumList.add(JwtApplicationTokenPrefixEnum.MES_CUSTOMER_APP);
        }
        // 遍历会影响系统的token，逐一删除
        if (null != jwtApplicationTokenPrefixEnumList && !jwtApplicationTokenPrefixEnumList.isEmpty()) {
            for (JwtApplicationTokenPrefixEnum item : jwtApplicationTokenPrefixEnumList) {
                // redisUtil.remove(item.getKey() + model.getUserCode());
            }
        }
    }

}
