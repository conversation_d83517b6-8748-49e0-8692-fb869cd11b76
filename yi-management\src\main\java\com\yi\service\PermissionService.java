package com.yi.service;

import com.yi.configuration.threadlocal.ThreadContext;
import com.yi.constant.CommonConstant;
import com.yi.controller.user.io.*;
import com.yi.utils.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class PermissionService {

    @Autowired
    private CommonService commonService;
//    @Autowired
//    private TPermissionMapper tPermissionMapper;
//    @Autowired
//    private TRolePermissionMapper rolePermissionMapper;
//    @Autowired
//    private TUserMapper tUserMapper;
//    @Autowired
//    private TUserRoleMapper tUserRoleMapper;
//
//    @Resource
//    private FileBiz fileBiz;
//
//    @Resource
//    private ConfigKeyConstant configKeyConstant;

    /**
     * 列表、角色新增页面使用
     *
     * @return
     */
    public List<RoleMenuListResponse> getAllMenu() {
        List<RoleMenuListResponse> source = new ArrayList<>();//tPermissionMapper.getAllMenu();
//        for (RoleMenuListResponse model : source) {
//            //处理图片的url
//            if (StringUtils.isNotBlank(model.getPermissionImg())) {
//                model.setPermissionImg(configKeyConstant.ossFileAccessAddress +
//                        fileBiz.getOSSFileURL(CommonConstant.INTEGER_ONE, model.getPermissionImg()));
//            }
//        }
        List<RoleMenuListResponse> result = new ArrayList<>();
        getAllMenuMap(source, result, 0L);
        return result;
    }

    //递归菜单
    public List<RoleMenuListResponse> getAllMenuMap(List<RoleMenuListResponse> source, List<RoleMenuListResponse> result, Long mapParentId) {
        for (RoleMenuListResponse tmp : source) {
            if (mapParentId.equals(tmp.getParentId())) {
                result.add(tmp);
                getAllMenuMap(source, tmp.getRoleMenuList(), tmp.getMenuId());
            }
        }
        return result;
    }

    /**
     * 菜单详情
     *
     * @param requestModel
     * @return
     */
    public PermissionDetailResponse getPermissionDetail(PermissionIdRequest requestModel) {
        PermissionDetailResponse model = null; // tPermissionMapper.getPermissionDetail(requestModel.getMenuId());
        //处理图片的url
//        if (StringUtils.isNotBlank(model.getPermissionImg())) {
//            model.setPermissionImg(configKeyConstant.ossFileAccessAddress +
//                    fileBiz.getOSSFileURL(CommonConstant.INTEGER_ONE, model.getPermissionImg()));
//        }
        return model;
    }

//    /**
//     * 添加/修改菜单
//     *
//     * @param requestModel
//     */
//    @Transactional
//    public void savePermission(SavePermissionRequestModel requestModel) {
//        TPermission permission = new TPermission();
//        permission.setPermissionName(requestModel.getPermissionName());
//        permission.setPermissionSort(requestModel.getPermissionSort());
//        permission.setMenuUrl(requestModel.getMenuUrl());
//        permission.setParentId(requestModel.getParentId());
//        //添加处理icon的逻辑 当url带有两个/时 表示已上传正式 不需要处理  如果是单个 表示前端修改了上传 需要重新同步到正式
//        if (StringUtils.isNotBlank(requestModel.getPermissionImg())) {
//            //大于1 需要copy文件到正式
//            if (FileBiz.getSeparatorCount(requestModel.getPermissionImg()) <= CommonConstant.INTEGER_ONE) {
//                String relPath = "/" + FileUploadMenuEnum.PERMISSION.getValue() + requestModel.getPermissionImg();
//                FileCopyRequestModel copyModel = new FileCopyRequestModel();
//                copyModel.setFileSrcPath(configKeyConstant.ossTempImageUploadCatalog + requestModel.getPermissionImg());
//                copyModel.setFileTargetPath(configKeyConstant.ossImageUploadCatalog + relPath);
//                fileBiz.copyFileOSS(copyModel);
//
//                permission.setPermissionImg(relPath);
//            }
//        }
//        permission.setRemark(requestModel.getRemark());
//        //校验功能点
//        TPermission tPermissionName = null, tPermissionCode = null;
//        if (CommonConstant.INTEGER_THREE.equals(requestModel.getPermissionType())) {
//            tPermissionName = tPermissionMapper.getByNameAndParentId(requestModel.getPermissionName(), requestModel.getParentId());
//        } else {
//            tPermissionCode = tPermissionMapper.selectByPermissionCode(requestModel.getPermissionName(),
//                    requestModel.getApplicationId());
//        }
//        if (requestModel.getMenuId() == null || requestModel.getMenuId() <= CommonConstant.LONG_ZERO) {
//            if (tPermissionCode != null) {
//                throw new BizException(BasicDataExceptionEnum.PERMISSION_NAME_EXIST);
//            }
//            if (tPermissionName != null) {
//                throw new BizException(BasicDataExceptionEnum.PERMISSION_NAME_EXIST);
//            }
//            permission.setPermissionType(requestModel.getPermissionType());
//            permission.setApplicationId(requestModel.getApplicationId());
//            permission.setFactoryId(BaseContextHandler.getFactoryId());
//            commonService.setBaseEntityAdd(permission, BaseContextHandler.getUserName());
//            tPermissionMapper.insertSelective(permission);
//        } else {
//            if (tPermissionCode != null && !tPermissionCode.getId().equals(requestModel.getMenuId())) {
//                throw new BizException(BasicDataExceptionEnum.PERMISSION_NAME_EXIST);
//            }
//            if (tPermissionName != null && !tPermissionName.getId().equals(requestModel.getMenuId())) {
//                throw new BizException(BasicDataExceptionEnum.PERMISSION_NAME_EXIST);
//            }
//            permission.setPermissionType(requestModel.getPermissionType());
//            permission.setId(requestModel.getMenuId());
//            commonService.setBaseEntityModify(permission, BaseContextHandler.getUserName());
//            tPermissionMapper.updateByPrimaryKeySelective(permission);
//        }
//    }
//
//    /**
//     * 删除菜单
//     *
//     * @param requestModel
//     */
//    @Transactional
//    public void deletePermission(PermissionIdRequestModel requestModel) {
//        List<TPermission> tPermissionLis = tPermissionMapper.getPermissionOrChildById(requestModel.getMenuId());
//        List<Long> deleteIds = new ArrayList<>();
//        if (requestModel.getDeleteType() == null || requestModel.getDeleteType().equals(CommonConstant.INTEGER_ZERO)) {
//            if (tPermissionLis.size() > CommonConstant.INTEGER_ONE) {
//                throw new BizException(BasicDataExceptionEnum.PERMISSION_EXIST_CHILD_PERMISSION);
//            }
//            deleteIds.add(requestModel.getMenuId());
//        } else {
//            //递归查询本级以及子集菜单的id
//            deleteIds = tPermissionMapper.getChildIdsByParentId(requestModel.getMenuId());
//        }
//        List<TPermission> updateList = new ArrayList<>();
//        for (Long deleteId : deleteIds) {
//            TPermission tPermission = new TPermission();
//            tPermission.setId(deleteId);
//            tPermission.setValid(CommonConstant.INTEGER_ZERO);
//            commonService.setBaseEntityModify(tPermission, BaseContextHandler.getUserName());
//            updateList.add(tPermission);
//        }
//        //批量更新
//        tPermissionMapper.batchUpdateSelective(updateList);
//        //批量更新关系表
//        rolePermissionMapper.batchDeleteByMenuId(deleteIds, BaseContextHandler.getUserName());
//    }

    /**
     * 后台加载菜单和权限
     *
     * @param requestModel
     * @return
     */
    public LoadMenuAndPermissionResponseModel loadMenuAndPermission(LoadMenuAndPermissionRequestModel requestModel) {
        LoadMenuAndPermissionResponseModel responseModel = new LoadMenuAndPermissionResponseModel();
        Long userId = ThreadContext.getUserId();

        List<LoadMenuAndPermissionModel> allMenuByAppId = new ArrayList<>();//tPermissionMapper.getAllMenuByAppId(requestModel.getApplicationId());
        List<LoadMenuAndPermissionModel> allMenu = new ArrayList<>();
        selectAllMap(allMenuByAppId, allMenu, CommonConstant.LONG_ZERO);
        for (LoadMenuAndPermissionModel loadMenuAndPermissionModel : allMenu) {
            operatorMenuName(loadMenuAndPermissionModel, null);
        }

        responseModel.setAllMenuInfo(allMenu);

        List<Long> menuIdList = new ArrayList<>();//tPermissionMapper.loadMenuAndPermissionId(userId, CommonConstant.INTEGER_ONE, requestModel.getApplicationId());

        if (ListUtils.isEmpty(menuIdList)) {
            return responseModel;
        }

        List<LoadMenuAndPermissionModel> result = new ArrayList<>();
        List<LoadMenuAndPermissionModel> loadMenuAndPermissionModels = new ArrayList<>();//tPermissionMapper.loadMenuAndPermission(StringUtils.listToString(menuIdList, ','));
//        for (LoadMenuAndPermissionModel loadMenuAndPermissionModel : loadMenuAndPermissionModels) {
//            if (StringUtils.isNotBlank(loadMenuAndPermissionModel.getPermissionImg())) {
//                loadMenuAndPermissionModel.setPermissionImg(configKeyConstant.ossFileAccessAddress +
//                        fileBiz.getOSSFileURL(CommonConstant.INTEGER_ONE, loadMenuAndPermissionModel.getPermissionImg()));
//            }
//        }
        selectAllMap(loadMenuAndPermissionModels, result, CommonConstant.LONG_ZERO);
        responseModel.setMenuInfo(result);

        List<Long> permissionIdList = new ArrayList<>(); //tPermissionMapper.loadMenuAndPermissionId(userId, CommonConstant.INTEGER_TWO, requestModel.getApplicationId());
        if (ListUtils.isNotEmpty(permissionIdList)) {
            // responseModel.setPermissionInfo(tPermissionMapper.loadMenuAndPermission(StringUtils.listToString(permissionIdList, ',')));
        }

        return responseModel;
    }

    /**
     * 对树层级的名称进行拼接
     *
     * @param model
     * @param parentName
     */
    private void operatorMenuName(LoadMenuAndPermissionModel model, String parentName) {
        if (StringUtils.isNotBlank(parentName)) {
            model.setPermissionName(parentName + "/" + model.getPermissionName());
        }
        if (ListUtils.isNotEmpty(model.getList())) {
            for (LoadMenuAndPermissionModel loadMenuAndPermissionModel : model.getList()) {
                operatorMenuName(loadMenuAndPermissionModel, model.getPermissionName());
            }
        }
    }

    //递归菜单角色
    public List<LoadMenuAndPermissionModel> selectAllMap(List<LoadMenuAndPermissionModel> source, List<LoadMenuAndPermissionModel> result, Long mapParentId) {
        for (LoadMenuAndPermissionModel tmp : source) {
            if (mapParentId.equals(tmp.getParentId())) {
                result.add(tmp);
                selectAllMap(source, tmp.getList(), tmp.getId());
            }
        }
        return result;
    }

//    public LoadApplicationPermissionResponseModel loadApplicationPermissionByAccount(
//            LoadApplicationPermissionRequestModel requestModel) {
//        TUser user = tUserMapper.getByUserAccount(requestModel.getUserAccount());
//        if (user == null) {
//            return new LoadApplicationPermissionResponseModel();
//        }
//        List<TUserRole> userRoleList = tUserRoleMapper.getByUserId(user.getId());
//        if (ListUtils.isEmpty(userRoleList)) {
//            return new LoadApplicationPermissionResponseModel();
//        }
//
//        List<Long> roleIds = userRoleList.stream().map(TUserRole::getRoleId).collect(Collectors.toList());
//
//        List<TPermission> tPermissions = tPermissionMapper.getPermissionByRoleIds(roleIds);
//        if (ListUtils.isEmpty(tPermissions)) {
//            return new LoadApplicationPermissionResponseModel();
//        }
//        // 主菜单
//        List<Long> mainMenuParentId = new ArrayList<>();
//        // 下级菜单
//        List<TPermission> nextMenuParentId = new ArrayList<>();
//        for (TPermission tPermission : tPermissions) {
//            if (CommonConstant.LONG_ZERO.equals(tPermission.getParentId())) {
//                mainMenuParentId.add(tPermission.getId());
//            } else {
//                nextMenuParentId.add(tPermission);
//            }
//        }
//        Map<Long, List<TPermission>> applicationMap = nextMenuParentId.stream()
//                .collect(Collectors.groupingBy(TPermission::getApplicationId));
//        // 需要判断子节点的父节点是否存在  如果父节点没有勾选则代表没有权限
//        List<Long> hasMainApplicationCode = new ArrayList<>();
//        for (Map.Entry<Long, List<TPermission>> entry : applicationMap.entrySet()) {
//            Long applicationId = entry.getKey();
//            List<TPermission> permissions = entry.getValue();
//            Map<Long, List<TPermission>> parentIdCodeMap = permissions.stream()
//                    .collect(Collectors.groupingBy(TPermission::getId));
//            if (ListUtils.isEmpty(permissions)) {
//                continue;
//            }
//            boolean permission = this.selectHasApplicationCodePermission(permissions, parentIdCodeMap, mainMenuParentId);
//            if (permission) {
//                hasMainApplicationCode.add(applicationId);
//            }
//        }
//        LoadApplicationPermissionResponseModel loadApplicationPermissionResponseModel = new LoadApplicationPermissionResponseModel();
//        loadApplicationPermissionResponseModel.setApplicationPermissionCode(hasMainApplicationCode);
//        return loadApplicationPermissionResponseModel;
//    }
//
//    private boolean selectHasApplicationCodePermission(List<TPermission> permissions, Map<Long, List<TPermission>> parentIdCodeMap, List<Long> mainMenuParentId) {
//        for (TPermission permission : permissions) {
//            if (ListUtils.isNotEmpty(parentIdCodeMap.get(permission.getParentId()))) {
//                selectHasApplicationCodePermission(parentIdCodeMap.get(permission.getParentId()), parentIdCodeMap, mainMenuParentId);
//            } else {
//                if (mainMenuParentId.contains(permission.getParentId())) {
//                    return true;
//                }
//            }
//        }
//        return false;
//    }
}
