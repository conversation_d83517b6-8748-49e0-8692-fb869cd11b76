﻿<!DOCTYPE html>
<html>
  <head>
    <title>确认发货</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/确认发货/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/确认发货/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (线段) -->
      <div id="u2239" class="ax_default line1">
        <img id="u2239_img" class="img " src="images/客户管理/u350.svg"/>
        <div id="u2239_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2240" class="ax_default box_1">
        <div id="u2240_div" class=""></div>
        <div id="u2240_text" class="text ">
          <p><span>确认发货&nbsp; &nbsp; X</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2241" class="ax_default link_button">
        <div id="u2241_div" class=""></div>
        <div id="u2241_text" class="text ">
          <p><span>刷新本页</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2242" class="ax_default box_21">
        <div id="u2242_div" class=""></div>
        <div id="u2242_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2243" class="ax_default heading_3">
        <div id="u2243_div" class=""></div>
        <div id="u2243_text" class="text ">
          <p><span>基本信息</span></p>
        </div>
      </div>

      <!-- Unnamed (表格) -->
      <div id="u2244" class="ax_default">

        <!-- Unnamed (单元格) -->
        <div id="u2245" class="ax_default table_cell">
          <img id="u2245_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u2245_text" class="text ">
            <p><span>订单号</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2246" class="ax_default table_cell">
          <img id="u2246_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u2246_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2247" class="ax_default table_cell">
          <img id="u2247_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u2247_text" class="text ">
            <p><span>客户</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2248" class="ax_default table_cell">
          <img id="u2248_img" class="img " src="images/确认发货/u2248.png"/>
          <div id="u2248_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2249" class="ax_default table_cell">
          <img id="u2249_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u2249_text" class="text ">
            <p><span>收货仓库</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2250" class="ax_default table_cell">
          <img id="u2250_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u2250_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2251" class="ax_default table_cell">
          <img id="u2251_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u2251_text" class="text ">
            <p><span>收货地址</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2252" class="ax_default table_cell">
          <img id="u2252_img" class="img " src="images/确认发货/u2248.png"/>
          <div id="u2252_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2253" class="ax_default table_cell">
          <img id="u2253_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u2253_text" class="text ">
            <p><span>收货人</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2254" class="ax_default table_cell">
          <img id="u2254_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u2254_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2255" class="ax_default table_cell">
          <img id="u2255_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u2255_text" class="text ">
            <p><span>产品</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2256" class="ax_default table_cell">
          <img id="u2256_img" class="img " src="images/确认发货/u2248.png"/>
          <div id="u2256_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2257" class="ax_default table_cell">
          <img id="u2257_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u2257_text" class="text ">
            <p><span>需求数量</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2258" class="ax_default table_cell">
          <img id="u2258_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u2258_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2259" class="ax_default table_cell">
          <img id="u2259_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u2259_text" class="text ">
            <p><span>需求时间</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2260" class="ax_default table_cell">
          <img id="u2260_img" class="img " src="images/确认发货/u2248.png"/>
          <div id="u2260_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2261" class="ax_default table_cell">
          <img id="u2261_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u2261_text" class="text ">
            <p><span>待确认数量</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2262" class="ax_default table_cell">
          <img id="u2262_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u2262_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2263" class="ax_default table_cell">
          <img id="u2263_img" class="img " src="images/确认发货/u2245.png"/>
          <div id="u2263_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2264" class="ax_default table_cell">
          <img id="u2264_img" class="img " src="images/确认发货/u2248.png"/>
          <div id="u2264_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2265" class="ax_default table_cell">
          <img id="u2265_img" class="img " src="images/确认发货/u2265.png"/>
          <div id="u2265_text" class="text ">
            <p><span>备注</span></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2266" class="ax_default table_cell">
          <img id="u2266_img" class="img " src="images/确认发货/u2265.png"/>
          <div id="u2266_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2267" class="ax_default table_cell">
          <img id="u2267_img" class="img " src="images/确认发货/u2265.png"/>
          <div id="u2267_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (单元格) -->
        <div id="u2268" class="ax_default table_cell">
          <img id="u2268_img" class="img " src="images/确认发货/u2268.png"/>
          <div id="u2268_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u2269" class="ax_default text_field">
        <div id="u2269_div" class=""></div>
        <input id="u2269_input" type="text" value="" class="u2269_input"/>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u2270" class="ax_default text_field">
        <div id="u2270_div" class=""></div>
        <input id="u2270_input" type="text" value="" class="u2270_input"/>
      </div>

      <!-- Unnamed (单选按钮) -->
      <div id="u2271" class="ax_default radio_button" selectiongroup="发货方式">
        <label id="u2271_input_label" for="u2271_input" style="position: absolute; left: 0px;">
          <img id="u2271_img" class="img " src="images/确认发货/u2271.svg"/>
          <div id="u2271_text" class="text ">
            <p><span>仓库发货</span></p>
          </div>
        </label>
        <input id="u2271_input" type="radio" value="radio" name="发货方式"/>
      </div>

      <!-- Unnamed (单选按钮) -->
      <div id="u2272" class="ax_default radio_button" selectiongroup="发货方式">
        <label id="u2272_input_label" for="u2272_input" style="position: absolute; left: 0px;">
          <img id="u2272_img" class="img " src="images/确认发货/u2272.svg"/>
          <div id="u2272_text" class="text ">
            <p><span>采购直发</span></p>
          </div>
        </label>
        <input id="u2272_input" type="radio" value="radio" name="发货方式"/>
      </div>

      <!-- Unnamed (动态面板) -->
      <div id="u2273" class="ax_default">
        <div id="u2273_state0" class="panel_state" data-label="仓库发货" style="">
          <div id="u2273_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u2274" class="ax_default label">
              <div id="u2274_div" class=""></div>
              <div id="u2274_text" class="text ">
                <p><span>*请选择发货仓库</span></p>
              </div>
            </div>

            <!-- Unnamed (下拉列表) -->
            <div id="u2275" class="ax_default droplist">
              <div id="u2275_div" class=""></div>
              <select id="u2275_input" class="u2275_input">
                <option class="u2275_input_option" value="仓库1">仓库1</option>
                <option class="u2275_input_option" value="仓库2">仓库2</option>
                <option class="u2275_input_option" value="仓库3">仓库3</option>
                <option class="u2275_input_option" value="仓库4">仓库4</option>
              </select>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2276" class="ax_default label">
              <div id="u2276_div" class=""></div>
              <div id="u2276_text" class="text ">
                <p><span>*明细类型</span></p>
              </div>
            </div>

            <!-- Unnamed (下拉列表) -->
            <div id="u2277" class="ax_default droplist">
              <div id="u2277_div" class=""></div>
              <select id="u2277_input" class="u2277_input">
                <option class="u2277_input_option" value="一级类目 二级类目 三级类目">一级类目 二级类目 三级类目</option>
              </select>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2278" class="ax_default label">
              <div id="u2278_div" class=""></div>
              <div id="u2278_text" class="text ">
                <p><span>*配送方式</span></p>
              </div>
            </div>

            <!-- Unnamed (单选按钮) -->
            <div id="u2279" class="ax_default radio_button selected">
              <label id="u2279_input_label" for="u2279_input" style="position: absolute; left: 0px;">
                <img id="u2279_img" class="img " src="images/确认发货/u2279_selected.svg"/>
                <div id="u2279_text" class="text ">
                  <p><span>线下找车</span></p>
                </div>
              </label>
              <input id="u2279_input" type="radio" value="radio" name="u2279" checked/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2280" class="ax_default label">
              <div id="u2280_div" class=""></div>
              <div id="u2280_text" class="text ">
                <p><span>*司机姓名</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u2281" class="ax_default text_field">
              <div id="u2281_div" class=""></div>
              <input id="u2281_input" type="text" value="" class="u2281_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2282" class="ax_default label">
              <div id="u2282_div" class=""></div>
              <div id="u2282_text" class="text ">
                <p><span>*司机联系方式</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u2283" class="ax_default text_field">
              <div id="u2283_div" class=""></div>
              <input id="u2283_input" type="text" value="" class="u2283_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2284" class="ax_default label">
              <div id="u2284_div" class=""></div>
              <div id="u2284_text" class="text ">
                <p><span>*司机身份证</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u2285" class="ax_default text_field">
              <div id="u2285_div" class=""></div>
              <input id="u2285_input" type="text" value="" class="u2285_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2286" class="ax_default label">
              <div id="u2286_div" class=""></div>
              <div id="u2286_text" class="text ">
                <p><span>*车牌号</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u2287" class="ax_default text_field">
              <div id="u2287_div" class=""></div>
              <input id="u2287_input" type="text" value="" class="u2287_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2288" class="ax_default primary_button">
              <div id="u2288_div" class=""></div>
              <div id="u2288_text" class="text ">
                <p><span>确认</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2289" class="ax_default label">
              <div id="u2289_div" class=""></div>
              <div id="u2289_text" class="text ">
                <p><span>*发货数量</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u2290" class="ax_default text_field">
              <div id="u2290_div" class=""></div>
              <input id="u2290_input" type="text" value="" class="u2290_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2291" class="ax_default label">
              <div id="u2291_div" class=""></div>
              <div id="u2291_text" class="text ">
                <p><span>仓库余量1000</span></p>
              </div>
            </div>

            <!-- Unnamed (圆形) -->
            <div id="u2292" class="ax_default ellipse">
              <img id="u2292_img" class="img " src="images/确认发货/u2292.svg"/>
              <div id="u2292_text" class="text ">
                <p><span>+</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2293" class="ax_default label">
              <div id="u2293_div" class=""></div>
              <div id="u2293_text" class="text ">
                <p><span>*明细类型</span></p>
              </div>
            </div>

            <!-- Unnamed (下拉列表) -->
            <div id="u2294" class="ax_default droplist">
              <div id="u2294_div" class=""></div>
              <select id="u2294_input" class="u2294_input">
                <option class="u2294_input_option" value="一级类目 二级类目 三级类目">一级类目 二级类目 三级类目</option>
              </select>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2295" class="ax_default label">
              <div id="u2295_div" class=""></div>
              <div id="u2295_text" class="text ">
                <p><span>*发货数量</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u2296" class="ax_default text_field">
              <div id="u2296_div" class=""></div>
              <input id="u2296_input" type="text" value="" class="u2296_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2297" class="ax_default label">
              <div id="u2297_div" class=""></div>
              <div id="u2297_text" class="text ">
                <p><span>仓库余量1000</span></p>
              </div>
            </div>

            <!-- Unnamed (圆形) -->
            <div id="u2298" class="ax_default ellipse">
              <img id="u2298_img" class="img " src="images/确认发货/u2292.svg"/>
              <div id="u2298_text" class="text ">
                <p><span>+</span></p>
              </div>
            </div>

            <!-- Unnamed (圆形) -->
            <div id="u2299" class="ax_default ellipse">
              <img id="u2299_img" class="img " src="images/确认发货/u2299.svg"/>
              <div id="u2299_text" class="text ">
                <p><span>-</span></p>
              </div>
            </div>

            <!-- Unnamed (圆形) -->
            <div id="u2300" class="ax_default ellipse">
              <img id="u2300_img" class="img " src="images/确认发货/u2299.svg"/>
              <div id="u2300_text" class="text ">
                <p><span>-</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2301" class="ax_default label">
              <div id="u2301_div" class=""></div>
              <div id="u2301_text" class="text ">
                <p><span>*期望送达时间</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u2302" class="ax_default text_field">
              <div id="u2302_div" class=""></div>
              <input id="u2302_input" type="text" value="" class="u2302_input"/>
            </div>
          </div>
        </div>
        <div id="u2273_state1" class="panel_state" data-label="采购发货" style="visibility: hidden;">
          <div id="u2273_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u2303" class="ax_default primary_button">
              <div id="u2303_div" class=""></div>
              <div id="u2303_text" class="text ">
                <p><span>确认</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2304" class="ax_default label">
              <div id="u2304_div" class=""></div>
              <div id="u2304_text" class="text ">
                <p><span>*期望送达时间</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u2305" class="ax_default text_field">
              <div id="u2305_div" class=""></div>
              <input id="u2305_input" type="text" value="" class="u2305_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2306" class="ax_default label">
              <div id="u2306_div" class=""></div>
              <div id="u2306_text" class="text ">
                <p><span>*明细类型</span></p>
              </div>
            </div>

            <!-- Unnamed (下拉列表) -->
            <div id="u2307" class="ax_default droplist">
              <div id="u2307_div" class=""></div>
              <select id="u2307_input" class="u2307_input">
                <option class="u2307_input_option" value="一级类目 二级类目 三级类目">一级类目 二级类目 三级类目</option>
              </select>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2308" class="ax_default label">
              <div id="u2308_div" class=""></div>
              <div id="u2308_text" class="text ">
                <p><span>*发货数量</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u2309" class="ax_default text_field">
              <div id="u2309_div" class=""></div>
              <input id="u2309_input" type="text" value="" class="u2309_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2310" class="ax_default label">
              <div id="u2310_div" class=""></div>
              <div id="u2310_text" class="text ">
                <p><span>仓库余量1000</span></p>
              </div>
            </div>

            <!-- Unnamed (圆形) -->
            <div id="u2311" class="ax_default ellipse">
              <img id="u2311_img" class="img " src="images/确认发货/u2292.svg"/>
              <div id="u2311_text" class="text ">
                <p><span>+</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2312" class="ax_default label">
              <div id="u2312_div" class=""></div>
              <div id="u2312_text" class="text ">
                <p><span>*明细类型</span></p>
              </div>
            </div>

            <!-- Unnamed (下拉列表) -->
            <div id="u2313" class="ax_default droplist">
              <div id="u2313_div" class=""></div>
              <select id="u2313_input" class="u2313_input">
                <option class="u2313_input_option" value="一级类目 二级类目 三级类目">一级类目 二级类目 三级类目</option>
              </select>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2314" class="ax_default label">
              <div id="u2314_div" class=""></div>
              <div id="u2314_text" class="text ">
                <p><span>*发货数量</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u2315" class="ax_default text_field">
              <div id="u2315_div" class=""></div>
              <input id="u2315_input" type="text" value="" class="u2315_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2316" class="ax_default label">
              <div id="u2316_div" class=""></div>
              <div id="u2316_text" class="text ">
                <p><span>仓库余量1000</span></p>
              </div>
            </div>

            <!-- Unnamed (圆形) -->
            <div id="u2317" class="ax_default ellipse">
              <img id="u2317_img" class="img " src="images/确认发货/u2292.svg"/>
              <div id="u2317_text" class="text ">
                <p><span>+</span></p>
              </div>
            </div>

            <!-- Unnamed (圆形) -->
            <div id="u2318" class="ax_default ellipse">
              <img id="u2318_img" class="img " src="images/确认发货/u2299.svg"/>
              <div id="u2318_text" class="text ">
                <p><span>-</span></p>
              </div>
            </div>

            <!-- Unnamed (圆形) -->
            <div id="u2319" class="ax_default ellipse">
              <img id="u2319_img" class="img " src="images/确认发货/u2299.svg"/>
              <div id="u2319_text" class="text ">
                <p><span>-</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2320" class="ax_default sticky_1">
        <div id="u2320_div" class=""></div>
        <div id="u2320_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2321" class="ax_default label">
        <div id="u2321_div" class=""></div>
        <div id="u2321_text" class="text ">
          <p><span>核心字段说明：<br>&nbsp; &nbsp; 1、【仓库发货】、【采购直发】按钮，前置条件：发货需求单 的 待确认数 大于0：</span></p><p><span>&nbsp;&nbsp;&nbsp; 2、【确认】成功后，生成对应的采购单或者仓库出库单，同时减少 发货需求单 待确认数；</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
