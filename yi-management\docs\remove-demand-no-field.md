# 去除发货需求表demand_no字段修改说明

## 概述

根据您的要求，从`t_shipping_demand`表中去除了`demand_no`字段，并修改了所有相关的代码实现。发货需求不再需要独立的需求单号，而是直接通过`order_no`字段关联发运订单。

## 🎯 **修改背景**

### **问题描述**：
- 原来的设计中，发货需求表有独立的`demand_no`字段
- 需求单号的生成增加了系统复杂性
- 发货需求与发运订单是一对一关系，不需要独立编号

### **解决方案**：
- 去除`demand_no`字段，简化表结构
- 直接使用`order_no`字段关联发运订单
- 删除所有需求单号生成相关的代码

## 📋 **修改内容**

### **1. 数据库表结构修改**

**发货需求表 (t_shipping_demand)**：
```sql
-- 修改前
CREATE TABLE `t_shipping_demand` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `demand_no` varchar(50) NOT NULL COMMENT '需求单号',  -- 删除此字段
    `order_no` varchar(50) NOT NULL COMMENT '发运订单号',
    -- ... 其他字段
);

-- 修改后
CREATE TABLE `t_shipping_demand` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_no` varchar(50) NOT NULL COMMENT '发运订单号',  -- 保留此字段作为关联
    -- ... 其他字段
);
```

### **2. 实体类修改**

**TShippingDemand实体类**：
```java
// 删除了以下字段
// /**
//  * 需求单号
//  */
// private String demandNo;

// 保留的字段
/**
 * 发运订单号
 */
private String orderNo;
```

### **3. 服务类修改**

**TShippingOrderService修改**：
```java
// 删除了需求单号生成相关的代码
private void createShippingDemand(TShippingOrder order) {
    TShippingDemand demand = new TShippingDemand();
    
    // 删除了需求单号生成
    // demand.setDemandNo(generateDemandNo());
    
    // 直接关联订单号
    demand.setOrderNo(order.getOrderNo());
    
    // ... 其他设置
}

// 删除了以下方法：
// - generateDemandNo()
// - getDemandMaxSequenceOfDay()
// - isDemandNoUnique()
```

**TShippingDemandService修改**：
```java
// 删除了需求单号相关的方法：
// - getMaxSequenceOfDay()
// - isDemandNoUnique()
// - validateDemandNoUnique()

// 修改了转换方法，去除需求单号设置
private ShippingDemandPageResponse convertToPageResponse(ShippingDemandPageVO demandVO) {
    ShippingDemandPageResponse response = new ShippingDemandPageResponse();
    
    response.setId(FormatUtils.safeToString(demandVO.getId()));
    // 删除了需求单号设置
    // response.setDemandNo(FormatUtils.safeString(demandVO.getDemandNo()));
    response.setOrderNo(FormatUtils.safeString(demandVO.getOrderNo()));
    
    // ... 其他设置
    return response;
}
```

### **4. 数据访问层修改**

**TShippingDemandMapper修改**：
```java
// 删除了以下方法：
// TShippingDemand selectByDemandNo(@Param("demandNo") String demandNo);
// int getMaxSequenceOfDay(@Param("prefix") String prefix);
```

**TShippingDemandMapper.xml修改**：
```xml
<!-- 删除了以下SQL：
<select id="selectByDemandNo" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List" />
    FROM t_shipping_demand
    WHERE demand_no = #{demandNo} AND valid = 1
</select>

<select id="getMaxSequenceOfDay" resultType="int">
    SELECT COALESCE(MAX(CAST(SUBSTRING(demand_no, LENGTH(#{prefix}) + 1) AS UNSIGNED)), 0)
    FROM t_shipping_demand
    WHERE demand_no LIKE CONCAT(#{prefix}, '%')
      AND valid = 1
      AND LENGTH(demand_no) = LENGTH(#{prefix}) + 4
</select>
-->
```

### **5. VO和响应类修改**

**ShippingDemandPageVO**：
```java
// 删除了需求单号字段
// /**
//  * 需求单号
//  */
// private String demandNo;

// 保留的字段
/**
 * 发运订单号
 */
private String orderNo;
```

**ShippingDemandPageResponse**：
```java
// 删除了需求单号字段
// @ApiModelProperty(value = "需求单号")
// private String demandNo;

// 保留的字段
@ApiModelProperty(value = "发运订单号")
private String orderNo;
```

**ShippingDemandDetailResponse**：
```java
// 删除了需求单号字段
// @ApiModelProperty(value = "需求单号")
// private String demandNo;

// 保留的字段
@ApiModelProperty(value = "发运订单号")
private String orderNo;
```

**ShippingDemandRequest**：
```java
// 删除了需求单号字段和验证
// @ApiModelProperty(value = "需求单号", required = true)
// @NotBlank(message = "需求单号不能为空")
// private String demandNo;

// 保留的字段
@ApiModelProperty(value = "发运订单号", required = true)
@NotBlank(message = "发运订单号不能为空")
private String orderNo;
```

### **6. 测试修改**

**TShippingOrderSyncTest修改**：
```java
// 删除了需求单号相关的测试
@Test
void testAddShippingOrder_ShouldCreateShippingDemand() {
    // 删除了需求单号生成的Mock
    // when(shippingDemandService.getMaxSequenceOfDay(anyString())).thenReturn(0);
    // when(shippingDemandService.isDemandNoUnique(anyString())).thenReturn(true);
    
    // 简化为只Mock保存操作
    when(shippingDemandService.save(any(TShippingDemand.class))).thenReturn(true);
    
    // 删除了需求单号的验证
    // assertNotNull(capturedDemand.getDemandNo());
    // assertTrue(capturedDemand.getDemandNo().startsWith("FHD"));
    
    // 保留订单号的验证
    assertNotNull(capturedDemand.getOrderNo());
    assertTrue(capturedDemand.getOrderNo().startsWith("XSD"));
}

// 删除了以下测试方法：
// - testGenerateDemandNo_ShouldGenerateCorrectFormat()
// - testDemandNoUniqueness_ShouldRetryOnDuplicate()
```

## 🔧 **数据关联关系**

### **修改前的关联关系**：
```
TShippingOrder (发运订单)
├── orderNo: "XSD202412230001"
└── 关联到 ↓

TShippingDemand (发货需求)
├── demandNo: "FHD202412230001"  // 独立的需求单号
└── orderNo: "XSD202412230001"   // 关联订单号
```

### **修改后的关联关系**：
```
TShippingOrder (发运订单)
├── orderNo: "XSD202412230001"
└── 关联到 ↓

TShippingDemand (发货需求)
└── orderNo: "XSD202412230001"   // 直接使用订单号关联
```

## 📊 **业务流程简化**

### **修改前的流程**：
```
1. 创建发运订单
   ├── 生成订单号: XSD202412230001
   └── 保存订单信息

2. 同步创建发货需求
   ├── 生成需求单号: FHD202412230001
   ├── 关联订单号: XSD202412230001
   ├── 验证需求单号唯一性
   ├── 重试机制处理冲突
   └── 保存需求信息

3. 后续查询和管理
   ├── 通过需求单号查询
   └── 通过订单号关联查询
```

### **修改后的流程**：
```
1. 创建发运订单
   ├── 生成订单号: XSD202412230001
   └── 保存订单信息

2. 同步创建发货需求
   ├── 直接使用订单号: XSD202412230001
   └── 保存需求信息

3. 后续查询和管理
   └── 通过订单号直接关联查询
```

## ✅ **修改优势**

### **1. 简化系统复杂性**
- **去除编号生成** - 不再需要复杂的需求单号生成逻辑
- **减少冲突处理** - 无需处理需求单号的唯一性冲突
- **简化重试机制** - 去除需求单号生成的重试逻辑

### **2. 提升性能**
- **减少数据库查询** - 不再需要查询最大序号
- **简化事务处理** - 减少事务中的操作步骤
- **降低并发冲突** - 避免需求单号生成时的并发问题

### **3. 数据一致性**
- **直接关联** - 通过订单号直接关联，关系更清晰
- **减少冗余** - 去除重复的编号字段
- **简化维护** - 减少需要维护的字段和索引

### **4. 代码可维护性**
- **代码简化** - 删除大量需求单号相关的代码
- **逻辑清晰** - 业务逻辑更加直观
- **测试简化** - 减少需要测试的场景

## ⚠️ **注意事项**

### **1. 数据迁移**
如果已有生产数据，需要考虑：
- **备份现有数据** - 删除字段前备份demand_no数据
- **数据清理** - 确认不再需要需求单号后再删除
- **索引调整** - 删除基于demand_no的索引

### **2. 接口兼容性**
- **API变更** - 前端调用的API响应结构发生变化
- **第三方集成** - 检查是否有第三方系统依赖需求单号
- **报表查询** - 更新相关的报表查询逻辑

### **3. 业务影响**
- **用户界面** - 前端界面需要去除需求单号的显示
- **业务流程** - 确认业务流程中不再需要需求单号
- **文档更新** - 更新相关的业务文档和技术文档

## 🚀 **后续优化建议**

### **1. 索引优化**
```sql
-- 基于订单号的索引优化
ALTER TABLE `t_shipping_demand` 
ADD INDEX `idx_order_no_status` (`order_no`, `status`) COMMENT '订单号和状态复合索引';
```

### **2. 查询优化**
```java
// 通过订单号查询发货需求
public TShippingDemand getByOrderNo(String orderNo) {
    LambdaQueryWrapper<TShippingDemand> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(TShippingDemand::getOrderNo, orderNo)
           .eq(TShippingDemand::getValid, 1);
    return this.getOne(wrapper);
}
```

### **3. 业务扩展**
```java
// 如果后续需要支持一个订单对应多个发货需求
public List<TShippingDemand> getByOrderNo(String orderNo) {
    LambdaQueryWrapper<TShippingDemand> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(TShippingDemand::getOrderNo, orderNo)
           .eq(TShippingDemand::getValid, 1)
           .orderByDesc(TShippingDemand::getCreatedTime);
    return this.list(wrapper);
}
```

## 📝 **总结**

通过去除`demand_no`字段：

1. **✅ 简化了系统架构** - 去除了复杂的需求单号生成逻辑
2. **✅ 提升了系统性能** - 减少了数据库查询和并发冲突
3. **✅ 增强了数据一致性** - 通过订单号直接关联，关系更清晰
4. **✅ 降低了维护成本** - 减少了需要维护的代码和测试用例

现在的设计更加简洁明了，发货需求直接通过订单号与发运订单关联，符合一对一的业务关系，为后续的功能扩展和维护奠定了良好的基础！🎉
