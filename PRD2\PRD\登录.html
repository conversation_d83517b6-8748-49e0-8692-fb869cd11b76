﻿<!DOCTYPE html>
<html>
  <head>
    <title>登录</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/登录/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/登录/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (图片 ) -->
      <div id="u138" class="ax_default image">
        <img id="u138_img" class="img " src="images/登录/u138.png"/>
        <div id="u138_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u139" class="ax_default image">
        <img id="u139_img" class="img " src="images/登录/u139.png"/>
        <div id="u139_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u140" class="ax_default box_1">
        <div id="u140_div" class=""></div>
        <div id="u140_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u141" class="ax_default box_1">
        <div id="u141_div" class=""></div>
        <div id="u141_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u142" class="ax_default box_21">
        <div id="u142_div" class=""></div>
        <div id="u142_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u143" class="ax_default heading_3">
        <div id="u143_div" class=""></div>
        <div id="u143_text" class="text ">
          <p><span>登录</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u144" class="ax_default label">
        <div id="u144_div" class=""></div>
        <div id="u144_text" class="text ">
          <p><span>*账号</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u145" class="ax_default text_field">
        <div id="u145_div" class=""></div>
        <input id="u145_input" type="text" value="" class="u145_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u146" class="ax_default primary_button">
        <div id="u146_div" class=""></div>
        <div id="u146_text" class="text ">
          <p><span>确认登录</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u147" class="ax_default label">
        <div id="u147_div" class=""></div>
        <div id="u147_text" class="text ">
          <p><span>*密码</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u148" class="ax_default text_field">
        <div id="u148_div" class=""></div>
        <input id="u148_input" type="text" value="" class="u148_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u149" class="ax_default heading_1">
        <div id="u149_div" class=""></div>
        <div id="u149_text" class="text ">
          <p><span>易炬科技</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u150" class="ax_default sticky_1">
        <div id="u150_div" class=""></div>
        <div id="u150_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u151" class="ax_default label">
        <div id="u151_div" class=""></div>
        <div id="u151_text" class="text ">
          <p><span>功能说明：<br>&nbsp; &nbsp; &nbsp; &nbsp; 1、科动赫后台管理系统的登录页面；</span></p><p><span>&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; 2、账号、密码：必填项，文本框，点击【确认登录】按钮后，后台校验账号密码的一致性，准确性；校验通过后登录成功，校验失败统一提示：账号密码错误，请重新输入；</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
