package com.yi.enums;

/**
 * 一级类目枚举
 */
public enum FirstCategoryEnum {
    
    SHARED_PALLET(1, "共享托盘");

    private final Integer code;
    private final String desc;

    FirstCategoryEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据类目码获取描述
     *
     * @param code 类目码
     * @return 类目描述
     */
    public static String getDescByCode(Integer code) {
        if (code == null) {
            return "";
        }
        for (FirstCategoryEnum category : values()) {
            if (category.getCode().equals(code)) {
                return category.getDesc();
            }
        }
        return "";
    }

    /**
     * 根据类目码获取枚举
     *
     * @param code 类目码
     * @return 枚举对象
     */
    public static FirstCategoryEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (FirstCategoryEnum category : values()) {
            if (category.getCode().equals(code)) {
                return category;
            }
        }
        return null;
    }

    /**
     * 检查类目是否有效
     *
     * @param code 类目码
     * @return 是否有效
     */
    public static boolean isValid(Integer code) {
        return getByCode(code) != null;
    }
}
