<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yi.mapper.TContractMapper">

    <!-- 联合查询结果映射 -->
    <resultMap id="ContractListVOMap" type="com.yi.controller.contract.model.ContractListVO">
        <id column="id" property="id"/>
        <result column="contract_no" property="contractNo"/>
        <result column="out_customer_company_id" property="outCustomerCompanyId"/>
        <result column="customer_company_id" property="customerCompanyId"/>
        <result column="customer_company_name" property="customerCompanyName"/>
        <result column="contract_status" property="contractStatus"/>
        <result column="archive_status" property="archiveStatus"/>
        <result column="effective_date" property="effectiveDate"/>
        <result column="expiry_date" property="expiryDate"/>
        <result column="cancel_reason" property="cancelReason"/>
        <result column="remark" property="remark"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_time" property="createdTime"/>
        <result column="last_modified_by" property="lastModifiedBy"/>
        <result column="last_modified_time" property="lastModifiedTime"/>
        <result column="valid" property="valid"/>
    </resultMap>

    <!-- 基础查询SQL -->
    <sql id="selectContractWithJoin">
        SELECT 
            c.id,
            c.contract_no,
            c.out_customer_company_id,
            c.customer_company_id,
            cc.company_name as customer_company_name,
            c.contract_status,
            c.archive_status,
            c.effective_date,
            c.expiry_date,
            c.cancel_reason,
            c.remark,
            c.created_by,
            c.created_time,
            c.last_modified_by,
            c.last_modified_time,
            c.valid
        FROM t_contract c
        LEFT JOIN t_customer_company cc ON c.customer_company_id = cc.id AND cc.valid = 1
        WHERE c.valid = 1
    </sql>

    <!-- 查询条件 -->
    <sql id="contractQueryConditions">
        <if test="contractNo != null and contractNo != ''">
            AND c.contract_no LIKE CONCAT('%', #{contractNo}, '%')
        </if>
        <if test="customerCompanyName != null and customerCompanyName != ''">
            AND cc.company_name LIKE CONCAT('%', #{customerCompanyName}, '%')
        </if>
        <if test="contractStatus != null">
            AND c.contract_status = #{contractStatus}
        </if>
    </sql>

    <!-- 分页查询合同列表（联合查询） -->
    <select id="selectContractPageWithJoin" resultMap="ContractListVOMap">
        <include refid="selectContractWithJoin"/>
        <include refid="contractQueryConditions"/>
        ORDER BY c.created_time DESC
    </select>

    <!-- 查询合同列表（联合查询，用于导出） -->
    <select id="selectContractListWithJoin" resultMap="ContractListVOMap">
        <include refid="selectContractWithJoin"/>
        <include refid="contractQueryConditions"/>
        ORDER BY c.created_time DESC
    </select>

    <!-- 根据ID查询合同详情（联合查询） -->
    <select id="selectContractDetailWithJoin" resultMap="ContractListVOMap">
        <include refid="selectContractWithJoin"/>
        AND c.id = #{id}
    </select>

    <!-- 根据合同编号查询合同 -->
    <select id="selectByContractNo" resultType="com.yi.entity.TContract">
        SELECT * FROM t_contract 
        WHERE contract_no = #{contractNo} AND valid = 1
    </select>

    <!-- 根据客户公司ID查询有效合同列表 -->
    <select id="selectValidContractsByCustomerId" resultType="com.yi.entity.TContract">
        SELECT * FROM t_contract 
        WHERE customer_company_id = #{customerCompanyId} 
        AND valid = 1 
        AND contract_status IN (1, 2)
        ORDER BY created_time DESC
    </select>

</mapper>
