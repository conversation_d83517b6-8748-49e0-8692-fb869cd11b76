package com.yi.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 仓库表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_warehouse")
@ApiModel(value = "TWarehouse对象", description = "仓库表")
public class TCustomerWarehouse extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 公司ID
     */
    @ApiModelProperty(value = "公司ID")
    private Long companyId;

    /**
     * 仓库名称
     */
    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    /**
     * 省份ID
     */
    @ApiModelProperty(value = "省份ID")
    private Long provinceId;

    /**
     * 省份名称
     */
    @ApiModelProperty(value = "省份名称")
    private String provinceName;

    /**
     * 城市ID
     */
    @ApiModelProperty(value = "城市ID")
    private Long cityId;

    /**
     * 城市名称
     */
    @ApiModelProperty(value = "城市名称")
    private String cityName;

    /**
     * 区县ID
     */
    @ApiModelProperty(value = "区县ID")
    private Long areaId;

    /**
     * 区县名称
     */
    @ApiModelProperty(value = "区县名称")
    private String areaName;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    private String detailedAddress;

    /**
     * 收货人
     */
    @ApiModelProperty(value = "收货人")
    private String contactPerson;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String mobilePhone;

    /**
     * 座机号
     */
    @ApiModelProperty(value = "座机号")
    private String landlinePhone;

    /**
     * SKU类型（JSON格式存储）
     */
    @ApiModelProperty(value = "SKU类型（JSON格式存储）")
    private String skuTypes;

    /**
     * 启用状态：1-启用，0-禁用
     */
    @ApiModelProperty(value = "启用状态：1-启用，0-禁用")
    private Integer enabled;
}
