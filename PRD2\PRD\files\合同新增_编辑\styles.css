﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-30px;
  width:1300px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u995_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1301px;
  height:2px;
}
#u995 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:37px;
  width:1300px;
  height:1px;
  display:flex;
}
#u995 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u995_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u996_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:216px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u996 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:7px;
  width:216px;
  height:30px;
  display:flex;
}
#u996 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u996_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u997_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u997 {
  border-width:0px;
  position:absolute;
  left:1274px;
  top:18px;
  width:56px;
  height:19px;
  display:flex;
}
#u997 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u997_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u998_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u998 {
  border-width:0px;
  position:absolute;
  left:71px;
  top:114px;
  width:62px;
  height:16px;
  display:flex;
}
#u998 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u998_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u999_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u999 {
  border-width:0px;
  position:absolute;
  left:71px;
  top:159px;
  width:62px;
  height:16px;
  display:flex;
}
#u999 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u999_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1000_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1000 {
  border-width:0px;
  position:absolute;
  left:77px;
  top:207px;
  width:56px;
  height:16px;
  display:flex;
}
#u1000 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1000_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1001_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1009px;
  height:84px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1001 {
  border-width:0px;
  position:absolute;
  left:138px;
  top:207px;
  width:1009px;
  height:84px;
  display:flex;
}
#u1001 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1001_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1002_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1300px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1002 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:40px;
  width:1300px;
  height:50px;
  display:flex;
}
#u1002 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1002_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1003_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1003 {
  border-width:0px;
  position:absolute;
  left:57px;
  top:55px;
  width:72px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1003 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1003_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1004_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1004 {
  border-width:0px;
  position:absolute;
  left:488px;
  top:114px;
  width:62px;
  height:16px;
  display:flex;
}
#u1004 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1004_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1005_input {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1005_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1005_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1005 {
  border-width:0px;
  position:absolute;
  left:138px;
  top:155px;
  width:200px;
  height:24px;
  display:flex;
}
#u1005 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1005_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1005.disabled {
}
#u1006_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1006 {
  border-width:0px;
  position:absolute;
  left:460px;
  top:159px;
  width:90px;
  height:16px;
  display:flex;
}
#u1006 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1006_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1007_input {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1007_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1007_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1007 {
  border-width:0px;
  position:absolute;
  left:555px;
  top:155px;
  width:200px;
  height:24px;
  display:flex;
}
#u1007 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1007_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1007.disabled {
}
#u1008_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1008 {
  border-width:0px;
  position:absolute;
  left:852px;
  top:159px;
  width:90px;
  height:16px;
  display:flex;
}
#u1008 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1008_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1009_input {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1009_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1009_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1009 {
  border-width:0px;
  position:absolute;
  left:947px;
  top:155px;
  width:200px;
  height:24px;
  display:flex;
}
#u1009 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1009_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1009.disabled {
}
#u1010_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1300px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1010 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:516px;
  width:1300px;
  height:50px;
  display:flex;
}
#u1010 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1010_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1011_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1011 {
  border-width:0px;
  position:absolute;
  left:57px;
  top:531px;
  width:72px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1011 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1011_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1012_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:180px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:24px;
}
#u1012 {
  border-width:0px;
  position:absolute;
  left:57px;
  top:592px;
  width:150px;
  height:180px;
  display:flex;
  font-size:24px;
}
#u1012 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1012_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1013_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1013 {
  border-width:0px;
  position:absolute;
  left:57px;
  top:792px;
  width:280px;
  height:16px;
  display:flex;
}
#u1013 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1013_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1014_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:252px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1014 {
  border-width:0px;
  position:absolute;
  left:57px;
  top:818px;
  width:252px;
  height:16px;
  display:flex;
}
#u1014 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1014_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1015_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:246px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1015 {
  border-width:0px;
  position:absolute;
  left:57px;
  top:844px;
  width:246px;
  height:16px;
  display:flex;
}
#u1015 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1015_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1016_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:180px;
}
#u1016 {
  border-width:0px;
  position:absolute;
  left:247px;
  top:592px;
  width:150px;
  height:180px;
  display:flex;
}
#u1016 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1016_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1017_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:180px;
}
#u1017 {
  border-width:0px;
  position:absolute;
  left:407px;
  top:592px;
  width:150px;
  height:180px;
  display:flex;
}
#u1017 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1017_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1018_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:34px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1018 {
  border-width:0px;
  position:absolute;
  left:605px;
  top:881px;
  width:200px;
  height:34px;
  display:flex;
}
#u1018 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1018_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1019_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:32px;
}
#u1019 {
  border-width:0px;
  position:absolute;
  left:359px;
  top:599px;
  width:32px;
  height:32px;
  display:flex;
}
#u1019 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1019_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1020_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:32px;
}
#u1020 {
  border-width:0px;
  position:absolute;
  left:519px;
  top:599px;
  width:32px;
  height:32px;
  display:flex;
}
#u1020 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1020_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1021_input {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1021_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1021_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1021 {
  border-width:0px;
  position:absolute;
  left:138px;
  top:110px;
  width:200px;
  height:24px;
  display:flex;
}
#u1021 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1021_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1021.disabled {
}
.u1021_input_option {
}
#u1022_input {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1022_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1022_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1022 {
  border-width:0px;
  position:absolute;
  left:555px;
  top:110px;
  width:200px;
  height:24px;
  display:flex;
}
#u1022 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1022_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1022.disabled {
}
.u1022_input_option {
}
#u1023_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1300px;
  height:75px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  font-size:14px;
  line-height:20px;
}
#u1023 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:1022px;
  width:1300px;
  height:75px;
  display:flex;
  font-size:14px;
  line-height:20px;
}
#u1023 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u1023_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1024_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:587px;
  height:38px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#000000;
  line-height:19px;
}
#u1024 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:1030px;
  width:587px;
  height:38px;
  display:flex;
  font-size:15px;
  color:#000000;
  line-height:19px;
}
#u1024 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1024_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1025 {
  border-width:0px;
  position:absolute;
  left:57px;
  top:382px;
  width:1090px;
  height:93px;
}
#u1026_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:34px;
}
#u1026 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:34px;
  display:flex;
}
#u1026 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1026_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1027_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:230px;
  height:34px;
}
#u1027 {
  border-width:0px;
  position:absolute;
  left:99px;
  top:0px;
  width:230px;
  height:34px;
  display:flex;
}
#u1027 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1027_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1028_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:370px;
  height:34px;
}
#u1028 {
  border-width:0px;
  position:absolute;
  left:329px;
  top:0px;
  width:370px;
  height:34px;
  display:flex;
}
#u1028 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1028_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1029_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:391px;
  height:34px;
}
#u1029 {
  border-width:0px;
  position:absolute;
  left:699px;
  top:0px;
  width:391px;
  height:34px;
  display:flex;
}
#u1029 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1029_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1030_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:29px;
}
#u1030 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:34px;
  width:99px;
  height:29px;
  display:flex;
}
#u1030 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1030_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1031_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:230px;
  height:29px;
}
#u1031 {
  border-width:0px;
  position:absolute;
  left:99px;
  top:34px;
  width:230px;
  height:29px;
  display:flex;
}
#u1031 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1031_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1032_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:370px;
  height:29px;
}
#u1032 {
  border-width:0px;
  position:absolute;
  left:329px;
  top:34px;
  width:370px;
  height:29px;
  display:flex;
}
#u1032 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1032_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1033_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:391px;
  height:29px;
}
#u1033 {
  border-width:0px;
  position:absolute;
  left:699px;
  top:34px;
  width:391px;
  height:29px;
  display:flex;
}
#u1033 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1033_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1034_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:30px;
}
#u1034 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:63px;
  width:99px;
  height:30px;
  display:flex;
}
#u1034 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1034_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1035_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:230px;
  height:30px;
}
#u1035 {
  border-width:0px;
  position:absolute;
  left:99px;
  top:63px;
  width:230px;
  height:30px;
  display:flex;
}
#u1035 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1035_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1036_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:370px;
  height:30px;
}
#u1036 {
  border-width:0px;
  position:absolute;
  left:329px;
  top:63px;
  width:370px;
  height:30px;
  display:flex;
}
#u1036 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1036_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1037_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:391px;
  height:30px;
}
#u1037 {
  border-width:0px;
  position:absolute;
  left:699px;
  top:63px;
  width:391px;
  height:30px;
  display:flex;
}
#u1037 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1037_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1038_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1300px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1038 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:315px;
  width:1300px;
  height:50px;
  display:flex;
}
#u1038 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1038_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1039_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1039 {
  border-width:0px;
  position:absolute;
  left:57px;
  top:330px;
  width:75px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1039 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1039_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1040_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:25px;
  background:inherit;
  background-color:rgba(2, 167, 240, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1040 {
  border-width:0px;
  position:absolute;
  left:162px;
  top:326px;
  width:70px;
  height:25px;
  display:flex;
}
#u1040 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1040_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1041_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1041 {
  border-width:0px;
  position:absolute;
  left:227px;
  top:421px;
  width:28px;
  height:16px;
  display:flex;
}
#u1041 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1041_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1042_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1042 {
  border-width:0px;
  position:absolute;
  left:275px;
  top:421px;
  width:28px;
  height:16px;
  display:flex;
}
#u1042 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1042_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1043_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:238px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#D9001B;
  text-align:center;
}
#u1043 {
  border-width:0px;
  position:absolute;
  left:236px;
  top:334px;
  width:238px;
  height:16px;
  display:flex;
  color:#D9001B;
  text-align:center;
}
#u1043 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1043_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1044 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1045_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:726px;
  height:247px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1045 {
  border-width:0px;
  position:absolute;
  left:267px;
  top:281px;
  width:726px;
  height:247px;
  display:flex;
}
#u1045 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1045_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1046_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:726px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1046 {
  border-width:0px;
  position:absolute;
  left:267px;
  top:281px;
  width:726px;
  height:50px;
  display:flex;
}
#u1046 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1046_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1047_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:111px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1047 {
  border-width:0px;
  position:absolute;
  left:295px;
  top:296px;
  width:111px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1047 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1047_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1048_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u1048 {
  border-width:0px;
  position:absolute;
  left:345px;
  top:366px;
  width:63px;
  height:16px;
  display:flex;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u1048 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1048_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1049_input {
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1049_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1049_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1049 {
  border-width:0px;
  position:absolute;
  left:418px;
  top:362px;
  width:150px;
  height:24px;
  display:flex;
}
#u1049 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1049_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1049.disabled {
}
.u1049_input_option {
}
#u1050_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1050 {
  border-width:0px;
  position:absolute;
  left:532px;
  top:442px;
  width:80px;
  height:30px;
  display:flex;
}
#u1050 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1050_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1051_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1051 {
  border-width:0px;
  position:absolute;
  left:632px;
  top:442px;
  width:80px;
  height:30px;
  display:flex;
}
#u1051 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1051_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1052_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:13px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1052 {
  border-width:0px;
  position:absolute;
  left:962px;
  top:296px;
  width:13px;
  height:21px;
  display:flex;
}
#u1052 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1052_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1053_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1053 {
  border-width:0px;
  position:absolute;
  left:613px;
  top:366px;
  width:62px;
  height:16px;
  display:flex;
}
#u1053 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1053_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1054_input {
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1054_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1054_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1054 {
  border-width:0px;
  position:absolute;
  left:685px;
  top:362px;
  width:150px;
  height:24px;
  display:flex;
}
#u1054 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1054_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1054.disabled {
}
.u1054_input_option {
}
