package com.yi.controller.contract.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 合同SKU明细请求
 */
@Data
@ApiModel(value = "ContractSkuRequest", description = "合同SKU明细请求")
public class ContractSkuRequest {

    @ApiModelProperty(value = "主键ID（编辑时可选）")
    private String id;

    @ApiModelProperty(value = "合同ID")
    private String contractId;

    @NotNull(message = "一级类目不能为空")
    @ApiModelProperty(value = "一级类目：1-循环托盘", required = true)
    private String firstCategory;

    @NotNull(message = "业务模式不能为空")
    @ApiModelProperty(value = "业务模式：1-静态租赁，2-租赁+流转，3-一口价", required = true)
    private String businessMode;
}
