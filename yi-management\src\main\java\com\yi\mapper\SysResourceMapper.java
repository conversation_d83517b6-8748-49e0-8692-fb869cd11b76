package com.yi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yi.controller.user.model.ResourceVO;
import com.yi.entity.SysResource;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统资源表 Mapper 接口
 */
@Mapper
public interface SysResourceMapper extends BaseMapper<SysResource> {

    /**
     * 查询所有资源（树形结构）
     *
     * @param resourceType 资源类型
     * @param status 状态
     * @return 资源列表
     */
    List<SysResource> selectResourceTree(@Param("resourceType") Integer resourceType,
                                         @Param("status") Integer status);

    /**
     * 根据父ID查询子资源
     *
     * @param parentId 父资源ID
     * @return 资源列表
     */
    List<SysResource> selectByParentId(@Param("parentId") Long parentId);

    /**
     * 根据资源编码查询资源
     *
     * @param resourceCode 资源编码
     * @return 资源信息
     */
    SysResource selectByResourceCode(@Param("resourceCode") String resourceCode);

    /**
     * 根据用户ID查询用户拥有的资源
     *
     * @param userId 用户ID
     * @param resourceType 资源类型
     * @return 资源列表
     */
    List<SysResource> selectByUserId(@Param("userId") Long userId,
                                     @Param("resourceType") Integer resourceType);

    /**
     * 根据角色ID查询角色拥有的资源
     *
     * @param roleId 角色ID
     * @return 资源列表
     */
    List<SysResource> selectByRoleId(@Param("roleId") Long roleId);

    /**
     * 查询用户的菜单权限
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
    List<SysResource> selectMenusByUserId(@Param("userId") Long userId);

    /**
     * 查询用户的按钮权限
     *
     * @param userId 用户ID
     * @return 按钮权限列表
     */
    List<SysResource> selectButtonsByUserId(@Param("userId") Long userId);

    /**
     * 查询所有启用的资源
     *
     * @return 资源列表
     */
    List<SysResource> selectEnabledResources();

    /**
     * 根据角色ID查询角色拥有的资源
     *
     * @param roleIdList 角色ID
     * @return 资源列表
     */
    List<ResourceVO> selectByRoleIdList(@Param("roleIdList") List<Long> roleIdList);
}
