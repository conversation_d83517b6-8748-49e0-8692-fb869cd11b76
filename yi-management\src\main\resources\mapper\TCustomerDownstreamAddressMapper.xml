<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yi.mapper.TCustomerDownstreamAddressMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yi.entity.TCustomerDownstreamAddress">
        <id column="id" property="id" />
        <result column="company_id" property="companyId" />
        <result column="downstream_customer_company_id" property="downstreamCustomerCompanyId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="enabled" property="enabled" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="last_modified_by" property="lastModifiedBy" />
        <result column="last_modified_time" property="lastModifiedTime" />
        <result column="valid" property="valid" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, company_id, downstream_customer_company_id, warehouse_id, enabled, created_by, created_time,
        last_modified_by, last_modified_time, valid, remark
    </sql>

    <!-- 分页查询客户下游地址关联列表 -->
    <select id="selectDownstreamAddressPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_customer_relate_downstream_address
        WHERE valid = 1
        <if test="companyId != null">
            AND company_id = #{companyId}
        </if>
        <if test="downstreamCustomerCompanyId != null">
            AND downstream_customer_company_id = #{downstreamCustomerCompanyId}
        </if>
        <if test="warehouseId != null">
            AND warehouse_id = #{warehouseId}
        </if>
        <if test="enabled != null">
            AND enabled = #{enabled}
        </if>
        ORDER BY created_time DESC
    </select>

    <!-- 根据下游客户ID查询关联地址列表 -->
    <select id="selectByDownstreamCustomerId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_customer_relate_downstream_address
        WHERE valid = 1 AND downstream_customer_company_id = #{downstreamCustomerCompanyId}
        ORDER BY created_time DESC
    </select>

    <!-- 根据仓库ID查询关联地址列表 -->
    <select id="selectByWarehouseId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_customer_relate_downstream_address
        WHERE valid = 1 AND warehouse_id = #{warehouseId}
        ORDER BY created_time DESC
    </select>

    <!-- 查询客户下游地址关联列表（左关联客户公司和仓库）- 支持分页和不分页 -->
    <select id="selectCustomerDownstreamAddressWithDetail" resultType="com.yi.mapper.vo.CustomerDownstreamAddressPageVO">
        SELECT
            cda.id AS relationId,
            cda.company_id AS companyId,
            cda.downstream_customer_company_id AS downstreamCustomerCompanyId,
            dc.company_name AS downstreamCompanyName,
            cda.warehouse_id AS warehouseId,
            w.warehouse_name AS warehouseName,
            w.province_name AS provinceName,
            w.city_name AS cityName,
            w.area_name AS areaName,
            w.detailed_address AS detailedAddress,
            w.contact_person AS contactPerson,
            w.mobile_phone AS mobilePhone,
            w.landline_phone AS landlinePhone,
            cda.enabled AS enabled,
            cda.last_modified_by AS lastModifiedBy,
            cda.last_modified_time AS lastModifiedTime,
            cda.remark AS remark
        FROM t_customer_relate_downstream_address cda
        LEFT JOIN t_customer_company dc ON cda.downstream_customer_company_id = dc.id AND dc.valid = 1
        LEFT JOIN t_warehouse w ON cda.warehouse_id = w.id AND w.valid = 1
        WHERE cda.valid = 1
        <if test="request.enabled != null and request.enabled != ''">
            AND cda.enabled = #{request.enabled}
        </if>
        <if test="request.companyId != null and request.companyId != ''">
            AND cda.company_id = #{request.companyId}
        </if>
        <if test="request.warehouseKeyword != null and request.warehouseKeyword != ''">
            AND (w.warehouse_name LIKE CONCAT('%', #{request.warehouseKeyword}, '%')
                OR w.province_name LIKE CONCAT('%', #{request.warehouseKeyword}, '%')
                OR w.city_name LIKE CONCAT('%', #{request.warehouseKeyword}, '%')
                OR w.area_name LIKE CONCAT('%', #{request.warehouseKeyword}, '%')
                OR w.detailed_address LIKE CONCAT('%', #{request.warehouseKeyword}, '%'))
        </if>
        ORDER BY cda.last_modified_time DESC, cda.id ASC
    </select>

    <!-- 根据ID查询客户下游地址关联详情（左关联客户公司和仓库） -->
    <select id="selectCustomerDownstreamAddressDetailById" resultType="com.yi.mapper.vo.CustomerDownstreamAddressPageVO">
        SELECT
            cda.id AS relationId,
            cda.company_id AS companyId,
            cda.downstream_customer_company_id AS downstreamCustomerCompanyId,
            dc.company_name AS downstreamCompanyName,
            cda.warehouse_id AS warehouseId,
            w.warehouse_name AS warehouseName,
            w.province_name AS provinceName,
            w.city_name AS cityName,
            w.area_name AS areaName,
            w.detailed_address AS detailedAddress,
            w.contact_person AS contactPerson,
            w.mobile_phone AS mobilePhone,
            w.landline_phone AS landlinePhone,
            cda.enabled AS enabled,
            cda.last_modified_by AS lastModifiedBy,
            cda.last_modified_time AS lastModifiedTime,
            cda.remark AS remark
        FROM t_customer_relate_downstream_address cda
        LEFT JOIN t_customer_company dc ON cda.downstream_customer_company_id = dc.id AND dc.valid = 1
        LEFT JOIN t_warehouse w ON cda.warehouse_id = w.id AND w.valid = 1
        WHERE cda.valid = 1 AND cda.id = #{id}
    </select>

</mapper>
