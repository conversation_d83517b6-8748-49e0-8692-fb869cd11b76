<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yi.mapper.TShippingOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yi.entity.TShippingOrder">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="contract_code" property="contractCode" />
        <result column="customer_company_id" property="customerCompanyId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="first_category" property="firstCategory" />
        <result column="second_category" property="secondCategory" />
        <result column="count" property="count" />
        <result column="shipped_quantity" property="shippedQuantity" />
        <result column="received_quantity" property="receivedQuantity" />
        <result column="demand_time" property="demandTime" />
        <result column="status" property="status" />
        <result column="cancel_reason" property="cancelReason" />
        <result column="remark" property="remark" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="last_modified_by" property="lastModifiedBy" />
        <result column="last_modified_time" property="lastModifiedTime" />
        <result column="valid" property="valid" />
    </resultMap>

    <!-- 分页查询结果映射 -->
    <resultMap id="PageResultMap" type="com.yi.mapper.vo.ShippingOrderPageVO">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="contract_code" property="contractCode" />
        <result column="customer_company_id" property="customerCompanyId" />
        <result column="customer_company_name" property="customerCompanyName" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="warehouse_name" property="warehouseName" />
        <result column="warehouse_address" property="warehouseAddress" />
        <result column="receiver_name" property="receiverName" />
        <result column="receiver_phone" property="receiverPhone" />
        <result column="first_category" property="firstCategory" />
        <result column="second_category" property="secondCategory" />
        <result column="count" property="count" />
        <result column="shipped_quantity" property="shippedQuantity" />
        <result column="received_quantity" property="receivedQuantity" />
        <result column="status" property="status" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="valid" property="valid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_no, contract_code, customer_company_id, warehouse_id, first_category,
        second_category, count, shipped_quantity, received_quantity, demand_time, status,
        cancel_reason, remark, created_by, created_time, last_modified_by,
        last_modified_time, valid
    </sql>

    <!-- 分页查询发运订单列表 -->
    <select id="selectShippingOrderPage" resultMap="PageResultMap">
        SELECT
            so.id,
            so.order_no,
            so.contract_code,
            so.customer_company_id,
            cc.company_name as customer_company_name,
            so.warehouse_id,
            w.warehouse_name,
            CONCAT(w.province_name, ' ', w.city_name, ' ', w.area_name, ' ', w.detailed_address) as warehouse_address,
            w.receiver_name,
            w.receiver_phone,
            so.first_category,
            so.second_category,
            so.count,
            so.shipped_quantity,
            so.received_quantity,
            so.status,
            so.created_by,
            so.created_time,
            so.valid
        FROM t_shipping_order so
        LEFT JOIN t_customer_company cc ON so.customer_company_id = cc.id AND cc.valid = 1
        LEFT JOIN t_customer_warehouse w ON so.warehouse_id = w.id AND w.valid = 1
        WHERE so.valid = 1
        <if test="request.orderNo != null and request.orderNo != ''">
            AND so.order_no LIKE CONCAT('%', #{request.orderNo}, '%')
        </if>
        <if test="request.contractCode != null and request.contractCode != ''">
            AND so.contract_code LIKE CONCAT('%', #{request.contractCode}, '%')
        </if>
        <if test="request.customerCompanyName != null and request.customerCompanyName != ''">
            AND cc.company_name LIKE CONCAT('%', #{request.customerCompanyName}, '%')
        </if>
        <if test="request.status != null and request.status != ''">
            AND so.status = #{request.status}
        </if>
        ORDER BY so.created_time DESC
    </select>

    <!-- 查询发运订单列表（不分页） -->
    <select id="selectShippingOrderList" resultMap="PageResultMap">
        SELECT
            so.id,
            so.order_no,
            so.contract_code,
            so.customer_company_id,
            cc.company_name as customer_company_name,
            so.warehouse_id,
            w.warehouse_name,
            CONCAT(w.province_name, ' ', w.city_name, ' ', w.area_name, ' ', w.detailed_address) as warehouse_address,
            w.contact_person as receiver_name,
            w.mobile_phone as receiver_phone,
            so.first_category,
            so.second_category,
            so.count,
            so.shipped_quantity,
            so.received_quantity,
            so.demand_time,
            so.status,
            so.cancel_reason,
            so.remark,
            so.created_by,
            so.created_time,
            so.last_modified_by,
            so.last_modified_time,
            so.valid
        FROM t_shipping_order so
        LEFT JOIN t_customer_company cc ON so.customer_company_id = cc.id AND cc.valid = 1
        LEFT JOIN t_customer_warehouse w ON so.warehouse_id = w.id AND w.valid = 1
        WHERE so.valid = 1
        <if test="request.orderNo != null and request.orderNo != ''">
            AND so.order_no LIKE CONCAT('%', #{request.orderNo}, '%')
        </if>
        <if test="request.contractCode != null and request.contractCode != ''">
            AND so.contract_code LIKE CONCAT('%', #{request.contractCode}, '%')
        </if>
        <if test="request.customerCompanyId != null and request.customerCompanyId != ''">
            AND so.customer_company_id = #{request.customerCompanyId}
        </if>
        <if test="request.customerCompanyName != null and request.customerCompanyName != ''">
            AND cc.company_name LIKE CONCAT('%', #{request.customerCompanyName}, '%')
        </if>
        <if test="request.warehouseId != null and request.warehouseId != ''">
            AND so.warehouse_id = #{request.warehouseId}
        </if>
        <if test="request.warehouseName != null and request.warehouseName != ''">
            AND w.warehouse_name LIKE CONCAT('%', #{request.warehouseName}, '%')
        </if>
        <if test="request.firstCategory != null and request.firstCategory != ''">
            AND so.first_category = #{request.firstCategory}
        </if>
        <if test="request.secondCategory != null and request.secondCategory != ''">
            AND so.second_category LIKE CONCAT('%', #{request.secondCategory}, '%')
        </if>
        <if test="request.status != null and request.status != ''">
            AND so.status = #{request.status}
        </if>
        <if test="request.demandTimeStart != null and request.demandTimeStart != ''">
            AND so.demand_time >= #{request.demandTimeStart}
        </if>
        <if test="request.demandTimeEnd != null and request.demandTimeEnd != ''">
            AND so.demand_time &lt;= #{request.demandTimeEnd}
        </if>
        <if test="request.createdTimeStart != null and request.createdTimeStart != ''">
            AND DATE(so.created_time) >= #{request.createdTimeStart}
        </if>
        <if test="request.createdTimeEnd != null and request.createdTimeEnd != ''">
            AND DATE(so.created_time) &lt;= #{request.createdTimeEnd}
        </if>
        <if test="request.createdBy != null and request.createdBy != ''">
            AND so.created_by LIKE CONCAT('%', #{request.createdBy}, '%')
        </if>
        ORDER BY so.created_time DESC
    </select>

    <!-- 根据订单号查询发运订单 -->
    <select id="selectByOrderNo" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_shipping_order
        WHERE order_no = #{orderNo} AND valid = 1
    </select>

    <!-- 根据客户公司ID查询发运订单数量 -->
    <select id="countByCustomerCompanyId" resultType="int">
        SELECT COUNT(*)
        FROM t_shipping_order
        WHERE customer_company_id = #{customerCompanyId} AND valid = 1
    </select>

    <!-- 根据仓库ID查询发运订单数量 -->
    <select id="countByWarehouseId" resultType="int">
        SELECT COUNT(*)
        FROM t_shipping_order
        WHERE warehouse_id = #{warehouseId} AND valid = 1
    </select>

    <!-- 根据状态查询发运订单列表 -->
    <select id="selectByStatus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_shipping_order
        WHERE status = #{status} AND valid = 1
        ORDER BY created_time DESC
    </select>

    <!-- 更新发运订单状态 -->
    <update id="updateStatus">
        UPDATE t_shipping_order
        SET status = #{status},
            last_modified_by = #{lastModifiedBy},
            last_modified_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 更新发货数量 -->
    <update id="updateShippedQuantity">
        UPDATE t_shipping_order
        SET shipped_quantity = #{shippedQuantity},
            last_modified_by = #{lastModifiedBy},
            last_modified_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 更新签收数量 -->
    <update id="updateReceivedQuantity">
        UPDATE t_shipping_order
        SET received_quantity = #{receivedQuantity},
            last_modified_by = #{lastModifiedBy},
            last_modified_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 根据前缀获取最大序号 -->
    <select id="getMaxSequenceByPrefix" resultType="int">
        SELECT COALESCE(MAX(CAST(SUBSTRING(order_no, LENGTH(#{prefix}) + 1) AS UNSIGNED)), 0)
        FROM t_shipping_order
        WHERE order_no LIKE CONCAT(#{prefix}, '%')
          AND LENGTH(order_no) = LENGTH(#{prefix}) + 6
          AND valid = 1
    </select>

</mapper>
