package com.yi.controller.shippingorder.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 发运订单分页响应
 */
@Data
@ApiModel(value = "ShippingOrderPageResponse", description = "发运订单分页响应")
public class ShippingOrderPageResponse {

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "客户名称")
    private String customerCompanyName;

    @ApiModelProperty(value = "收货仓库")
    private String warehouseName;

    @ApiModelProperty(value = "收货地址")
    private String warehouseAddress;

    @ApiModelProperty(value = "收货人")
    private String receiverName;

    @ApiModelProperty(value = "产品")
    private String product;

    @ApiModelProperty(value = "需求数量")
    private String count;

    @ApiModelProperty(value = "发货数量")
    private String shippedQuantity;

    @ApiModelProperty(value = "签收数量")
    private String receivedQuantity;

    @ApiModelProperty(value = "订单状态")
    private String status;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private String createdTime;
}
