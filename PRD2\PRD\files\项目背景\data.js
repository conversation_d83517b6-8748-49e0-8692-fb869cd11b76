﻿$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,_(g,h,i,_(j,k,l,k)),m,[],n,_(h,o),p,[q],r,_(s,t,u,v,g,w,x,_(),y,[],z,_(A,B,C,D,E,_(F,G,H,I),J,null,K,L,L,M,N,O,null,P,Q,R,S,T,U,V,Q,W,X,Y,_(F,G,H,Z),ba,Q,bb,X,bc,_(bd,be,bf,bg,bh,bg,bi,bg,bj,k,H,_(bk,bl,bm,bl,bn,bl,bo,bp)),i,_(j,k,l,k)),bq,_(),br,_(),bs,_(bt,[_(bu,bv,bw,h,bx,by,u,bz,bA,bz,bB,bC,z,_(bD,bE,i,_(j,bF,l,bG),A,bH,bI,_(bJ,bK,bL,bM)),bq,_(),bN,_(),bO,be),_(bu,bP,bw,h,bx,by,u,bz,bA,bz,bB,bC,z,_(i,_(j,bQ,l,bR),A,bS,bI,_(bJ,bT,bL,bU)),bq,_(),bN,_(),bO,be),_(bu,bV,bw,h,bx,by,u,bz,bA,bz,bB,bC,z,_(bD,bE,i,_(j,bF,l,bG),A,bH,bI,_(bJ,bK,bL,bW)),bq,_(),bN,_(),bO,be),_(bu,bX,bw,h,bx,by,u,bz,bA,bz,bB,bC,z,_(i,_(j,bY,l,bR),A,bS,bI,_(bJ,bZ,bL,ca)),bq,_(),bN,_(),bO,be)])),cb,_(),cc,_(cd,_(ce,cf),cg,_(ce,ch),ci,_(ce,cj),ck,_(ce,cl)));}; 
var b="url",c="项目背景.html",d="generationDate",e=new Date(1753855215376.94),f="defaultAdaptiveView",g="name",h="",i="size",j="width",k=0,l="height",m="adaptiveViews",n="sketchKeys",o="s0",p="variables",q="OnLoadVariable",r="page",s="packageId",t="19f96cb7b3ad44cbbed425229dd92323",u="type",v="Axure:Page",w="项目背景",x="notes",y="annotations",z="style",A="baseStyle",B="627587b6038d43cca051c114ac41ad32",C="pageAlignment",D="center",E="fill",F="fillType",G="solid",H="color",I=0xFFFFFFFF,J="image",K="imageAlignment",L="near",M="imageRepeat",N="auto",O="favicon",P="sketchFactor",Q="0",R="colorStyle",S="appliedColor",T="fontName",U="Applied Font",V="borderWidth",W="borderVisibility",X="all",Y="borderFill",Z=0xFF797979,ba="cornerRadius",bb="cornerVisibility",bc="outerShadow",bd="on",be=false,bf="offsetX",bg=5,bh="offsetY",bi="blurRadius",bj="spread",bk="r",bl=0,bm="g",bn="b",bo="a",bp=0.349019607843137,bq="adaptiveStyles",br="interactionMap",bs="diagram",bt="objects",bu="id",bv="cf6f914313204b419efb3698c009643b",bw="label",bx="friendlyType",by="矩形",bz="vectorShape",bA="styleType",bB="visible",bC=true,bD="fontWeight",bE="700",bF=72,bG=21,bH="8c7a4c5ad69a4369a5f7788171ac0b32",bI="location",bJ="x",bK=25,bL="y",bM=23,bN="imageOverrides",bO="generateCompound",bP="409dc92e6b734a8392221819dfb3a449",bQ=364,bR=16,bS="df3da3fd8cfa4c4a81f05df7784209fe",bT=65,bU=54,bV="c4a1f9ff6dee48cc88cb6355316a232d",bW=174,bX="632279d28cf349c1ae2aac7e4559f6c2",bY=221,bZ=67,ca=202,cb="masters",cc="objectPaths",cd="cf6f914313204b419efb3698c009643b",ce="scriptId",cf="u24",cg="409dc92e6b734a8392221819dfb3a449",ch="u25",ci="c4a1f9ff6dee48cc88cb6355316a232d",cj="u26",ck="632279d28cf349c1ae2aac7e4559f6c2",cl="u27";
return _creator();
})());