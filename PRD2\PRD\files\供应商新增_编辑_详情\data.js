﻿$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,_(g,h,i,_(j,k,l,k)),m,[],n,_(h,o),p,[q],r,_(s,t,u,v,g,w,x,_(),y,[],z,_(A,B,C,D,E,_(F,G,H,I),J,null,K,L,L,M,N,O,null,P,Q,R,S,T,U,V,Q,W,X,Y,_(F,G,H,Z),ba,Q,bb,X,bc,_(bd,be,bf,bg,bh,bg,bi,bg,bj,k,H,_(bk,bl,bm,bl,bn,bl,bo,bp)),i,_(j,k,l,k)),bq,_(),br,_(),bs,_(bt,[_(bu,bv,bw,h,bx,by,u,bz,bA,bB,bC,bD,z,_(i,_(j,bE,l,bF),A,bG,bH,_(bI,bJ,bK,bL)),bq,_(),bM,_(),bN,_(bO,bP),bQ,be),_(bu,bR,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(i,_(j,bT,l,bU),A,bV,bH,_(bI,bJ,bK,bW),V,bX,Y,_(F,G,H,bY)),bq,_(),bM,_(),bQ,be),_(bu,bZ,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(i,_(j,ca,l,cb),A,cc,bH,_(bI,cd,bK,ce)),bq,_(),bM,_(),bQ,be),_(bu,cf,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,ch,ci,bF),i,_(j,cj,l,ck),A,cl,bH,_(bI,cm,bK,cn)),bq,_(),bM,_(),bQ,be),_(bu,co,bw,h,bx,cp,u,cq,bA,cq,bC,bD,z,_(i,_(j,cr,l,cs),ct,_(cu,_(A,cv),cw,_(A,cx)),A,cy,bH,_(bI,cz,bK,cA)),cB,be,bq,_(),bM,_(),cC,h),_(bu,cD,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(i,_(j,cE,l,bL),A,bV,bH,_(bI,bU,bK,ca)),bq,_(),bM,_(),bQ,be),_(bu,cF,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cG,cH,i,_(j,cI,l,bW),A,cJ,bH,_(bI,cK,bK,cL)),bq,_(),bM,_(),bQ,be),_(bu,cM,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,ch,ci,bF),i,_(j,cK,l,ck),A,cl,bH,_(bI,cN,bK,cO)),bq,_(),bM,_(),bQ,be),_(bu,cP,bw,h,bx,cp,u,cq,bA,cq,bC,bD,z,_(i,_(j,cQ,l,cs),ct,_(cu,_(A,cv),cw,_(A,cx)),A,cy,bH,_(bI,cz,bK,cR)),cB,be,bq,_(),bM,_(),cC,h),_(bu,cS,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,ch,ci,bF),i,_(j,cj,l,ck),A,cl,bH,_(bI,cT,bK,cO)),bq,_(),bM,_(),bQ,be),_(bu,cU,bw,h,bx,cp,u,cq,bA,cq,bC,bD,z,_(i,_(j,cQ,l,cs),ct,_(cu,_(A,cv),cw,_(A,cx)),A,cy,bH,_(bI,cV,bK,cR)),cB,be,bq,_(),bM,_(),cC,h),_(bu,cW,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,bY,ci,bF),i,_(j,cX,l,ck),A,cl,bH,_(bI,cY,bK,cO)),bq,_(),bM,_(),bQ,be),_(bu,cZ,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(i,_(j,cn,l,da),A,db,bH,_(bI,dc,bK,dd),de,df),bq,_(),bM,_(),br,_(dg,_(dh,di,dj,dk,dl,[_(dj,h,dm,h,dn,be,dp,dq,dr,[_(ds,dt,dj,du,dv,dw,dx,_(dy,_(h,du)),dz,_(dA,r,b,dB,dC,bD),dD,dE)])])),dF,bD,bQ,be),_(bu,dG,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,ch,ci,bF),i,_(j,ca,l,ck),A,cl,bH,_(bI,dH,bK,dI)),bq,_(),bM,_(),bQ,be),_(bu,dJ,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(i,_(j,dK,l,dL),A,dM,bH,_(bI,cz,bK,dI),de,dN),bq,_(),bM,_(),bQ,be),_(bu,dO,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,ch,ci,bF),i,_(j,dP,l,ck),A,cl,bH,_(bI,dQ,bK,dR)),bq,_(),bM,_(),bQ,be),_(bu,dS,bw,h,bx,cp,u,cq,bA,cq,bC,bD,z,_(i,_(j,cQ,l,cs),ct,_(cu,_(A,cv),cw,_(A,cx)),A,cy,bH,_(bI,cz,bK,dT)),cB,be,bq,_(),bM,_(),cC,h),_(bu,dU,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,ch,ci,bF),i,_(j,ca,l,ck),A,cl,bH,_(bI,dV,bK,dR)),bq,_(),bM,_(),bQ,be),_(bu,dW,bw,h,bx,cp,u,cq,bA,cq,bC,bD,z,_(i,_(j,cQ,l,cs),ct,_(cu,_(A,cv),cw,_(A,cx)),A,cy,bH,_(bI,cV,bK,dT)),cB,be,bq,_(),bM,_(),cC,h),_(bu,dX,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,ch,ci,bF),i,_(j,dY,l,ck),A,cl,bH,_(bI,dZ,bK,ea)),bq,_(),bM,_(),bQ,be),_(bu,eb,bw,h,bx,cp,u,cq,bA,cq,bC,bD,z,_(i,_(j,ec,l,ed),ct,_(cu,_(A,cv),cw,_(A,cx)),A,cy,bH,_(bI,cz,bK,ea)),cB,be,bq,_(),bM,_(),cC,h)])),ee,_(),ef,_(eg,_(eh,ei),ej,_(eh,ek),el,_(eh,em),en,_(eh,eo),ep,_(eh,eq),er,_(eh,es),et,_(eh,eu),ev,_(eh,ew),ex,_(eh,ey),ez,_(eh,eA),eB,_(eh,eC),eD,_(eh,eE),eF,_(eh,eG),eH,_(eh,eI),eJ,_(eh,eK),eL,_(eh,eM),eN,_(eh,eO),eP,_(eh,eQ),eR,_(eh,eS),eT,_(eh,eU),eV,_(eh,eW)));}; 
var b="url",c="供应商新增_编辑_详情.html",d="generationDate",e=new Date(1753855221391.86),f="defaultAdaptiveView",g="name",h="",i="size",j="width",k=0,l="height",m="adaptiveViews",n="sketchKeys",o="s0",p="variables",q="OnLoadVariable",r="page",s="packageId",t="afde79d6a54f4a56a9d6ffa264ab0e25",u="type",v="Axure:Page",w="供应商新增/编辑/详情",x="notes",y="annotations",z="style",A="baseStyle",B="627587b6038d43cca051c114ac41ad32",C="pageAlignment",D="center",E="fill",F="fillType",G="solid",H="color",I=0xFFFFFFFF,J="image",K="imageAlignment",L="near",M="imageRepeat",N="auto",O="favicon",P="sketchFactor",Q="0",R="colorStyle",S="appliedColor",T="fontName",U="Applied Font",V="borderWidth",W="borderVisibility",X="all",Y="borderFill",Z=0xFF797979,ba="cornerRadius",bb="cornerVisibility",bc="outerShadow",bd="on",be=false,bf="offsetX",bg=5,bh="offsetY",bi="blurRadius",bj="spread",bk="r",bl=0,bm="g",bn="b",bo="a",bp=0.349019607843137,bq="adaptiveStyles",br="interactionMap",bs="diagram",bt="objects",bu="id",bv="f60a26166ca54d66b0acbee84c1820b3",bw="label",bx="friendlyType",by="线段",bz="vectorShape",bA="styleType",bB="horizontalLine",bC="visible",bD=true,bE=1300,bF=1,bG="0327e893a7994793993b54c636419b7c",bH="location",bI="x",bJ=20,bK="y",bL=50,bM="imageOverrides",bN="images",bO="normal~",bP="images/客户管理/u350.svg",bQ="generateCompound",bR="e2365e52ab464531999d41f883a69340",bS="矩形",bT=235,bU=30,bV="4701f00c92714d4e9eed94e9fe75cfe8",bW=21,bX="1",bY=0xFFAAAAAA,bZ="92f39c408f294234943b160d26857f1a",ca=56,cb=19,cc="4b88aa200ad64025ad561857a6779b03",cd=1264,ce=32,cf="5fd8897f930649db91a28c46b346a202",cg="foreGroundFill",ch=0xFF000000,ci="opacity",cj=62,ck=16,cl="df3da3fd8cfa4c4a81f05df7784209fe",cm=258,cn=140,co="dc2bd7e7aa0441be81be8b35a4876aa8",cp="文本框",cq="textBox",cr=700,cs=26,ct="stateStyles",cu="hint",cv="4889d666e8ad4c5e81e59863039a5cc0",cw="disabled",cx="9bd0236217a94d89b0314c8c7fc75f16",cy="2170b7f9af5c48fba2adcd540f2ba1a0",cz=350,cA=130,cB="HideHintOnFocused",cC="placeholderText",cD="2ee48fb797014d268a21a1311f51408e",cE=1280,cF="e3ef6ac8dfc244e584a4cda2eee0749b",cG="fontWeight",cH="700",cI=72,cJ="8c7a4c5ad69a4369a5f7788171ac0b32",cK=48,cL=71,cM="c75a249293cc40a7a7fc4b68bbcc3e5e",cN=272,cO=191,cP="53bfe49e3f1c43abb3c7d2b9a2ae2508",cQ=200,cR=186,cS="307c55cd2ecf49089b2f6283656d53bb",cT=758,cU="899ad1ca0b6d4581ba4f428fba0f8cf2",cV=850,cW="f91e997fadfb4510b85d5560b15e22d9",cX=112,cY=919,cZ="6d368c5d8fc04fab872686b4bfc775a2",da=40,db="f9d2a29eec41403f99d04559928d6317",dc=606,dd=642,de="fontSize",df="16px",dg="onClick",dh="eventType",di="Click时",dj="description",dk="单击时",dl="cases",dm="conditionString",dn="isNewIfGroup",dp="caseColorHex",dq="AB68FF",dr="actions",ds="action",dt="linkWindow",du="打开 供应商管理 在 当前窗口",dv="displayName",dw="打开链接",dx="actionInfoDescriptions",dy="供应商管理",dz="target",dA="targetType",dB="供应商管理.html",dC="includeVariables",dD="linkType",dE="current",dF="tabbable",dG="73fe12100fe243d3b84dfb0b00995df8",dH=264,dI=298,dJ="3ba1c0a22bf54dc1b63eb65bcd0bccbb",dK=300,dL=170,dM="005450b8c9ab4e72bffa6c0bac80828f",dN="30px",dO="f4ad971eaa9f4d4fa86d181aa51c3390",dP=42,dQ=278,dR=247,dS="9c318f59bfbc4ebea16ebe8209c0100d",dT=242,dU="12ce9168e93d4e408db766304a964d6a",dV=764,dW="73e4f0255b0f4056bd9df058b5565054",dX="f47b847595dd4b90a6fc8323a777e36e",dY=28,dZ=296,ea=498,eb="40dc2831904b4cb8b17dd8beec8b9c18",ec=694,ed=98,ee="masters",ef="objectPaths",eg="f60a26166ca54d66b0acbee84c1820b3",eh="scriptId",ei="u2558",ej="e2365e52ab464531999d41f883a69340",ek="u2559",el="92f39c408f294234943b160d26857f1a",em="u2560",en="5fd8897f930649db91a28c46b346a202",eo="u2561",ep="dc2bd7e7aa0441be81be8b35a4876aa8",eq="u2562",er="2ee48fb797014d268a21a1311f51408e",es="u2563",et="e3ef6ac8dfc244e584a4cda2eee0749b",eu="u2564",ev="c75a249293cc40a7a7fc4b68bbcc3e5e",ew="u2565",ex="53bfe49e3f1c43abb3c7d2b9a2ae2508",ey="u2566",ez="307c55cd2ecf49089b2f6283656d53bb",eA="u2567",eB="899ad1ca0b6d4581ba4f428fba0f8cf2",eC="u2568",eD="f91e997fadfb4510b85d5560b15e22d9",eE="u2569",eF="6d368c5d8fc04fab872686b4bfc775a2",eG="u2570",eH="73fe12100fe243d3b84dfb0b00995df8",eI="u2571",eJ="3ba1c0a22bf54dc1b63eb65bcd0bccbb",eK="u2572",eL="f4ad971eaa9f4d4fa86d181aa51c3390",eM="u2573",eN="9c318f59bfbc4ebea16ebe8209c0100d",eO="u2574",eP="12ce9168e93d4e408db766304a964d6a",eQ="u2575",eR="73e4f0255b0f4056bd9df058b5565054",eS="u2576",eT="f47b847595dd4b90a6fc8323a777e36e",eU="u2577",eV="40dc2831904b4cb8b17dd8beec8b9c18",eW="u2578";
return _creator();
})());