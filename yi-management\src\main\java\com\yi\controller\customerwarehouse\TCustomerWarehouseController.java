package com.yi.controller.customerwarehouse;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yi.common.Result;
import com.yi.controller.customerwarehouse.model.CustomerWarehouseRequest;
import com.yi.controller.customerwarehouse.model.CustomerWarehouseQueryRequest;
import com.yi.controller.customerwarehouse.model.CustomerWarehousePageResponse;
import com.yi.controller.customerwarehouse.model.CustomerWarehouseDetailResponse;
import com.yi.controller.customerwarehouse.model.CustomerWarehouseOptionResponse;
import com.yi.service.TCustomerWarehouseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * 仓库表 前端控制器
 */
@RestController
@RequestMapping("/api/warehouse")
@Api(tags = "仓库管理")
public class TCustomerWarehouseController {

    @Autowired
    private TCustomerWarehouseService warehouseService;

    @ApiOperation("分页查询仓库列表")
    @PostMapping("/page")
    public Result<IPage<CustomerWarehousePageResponse>> getWarehousePage(@RequestBody CustomerWarehouseQueryRequest request) {
        IPage<CustomerWarehousePageResponse> page = warehouseService.getWarehousePageResponse(request);
        return Result.success(page);
    }

    @ApiOperation("根据ID获取仓库详情")
    @GetMapping("/{id}")
    public Result<CustomerWarehouseDetailResponse> getWarehouseById(@ApiParam("仓库ID") @PathVariable Long id) {
        CustomerWarehouseDetailResponse response = warehouseService.getWarehouseDetailById(id);
        if (response == null) {
            return Result.failed("仓库不存在");
        }
        return Result.success(response);
    }

    @ApiOperation("新增仓库")
    @PostMapping("/add")
    public Result<Boolean> addWarehouse(@Valid @RequestBody CustomerWarehouseRequest request) {
        try {
            boolean success = warehouseService.addWarehouse(request);
            if (success) {
                return Result.success("新增成功", true);
            }
            return Result.failed("新增失败");
        } catch (RuntimeException e) {
            return Result.failed(e.getMessage());
        }
    }

    @ApiOperation("更新仓库")
    @PutMapping("/update")
    public Result<Boolean> updateWarehouse(@Valid @RequestBody CustomerWarehouseRequest request) {
        if (request.getId() == null || request.getId().trim().isEmpty()) {
            return Result.validateFailed("仓库ID不能为空");
        }
        try {
            boolean success = warehouseService.updateWarehouse(request);
            if (success) {
                return Result.success("更新成功", true);
            }
            return Result.failed("更新失败");
        } catch (RuntimeException e) {
            return Result.failed(e.getMessage());
        }
    }


    @ApiOperation("启用/禁用仓库")
    @PutMapping("/{id}/status")
    public Result<Boolean> updateWarehouseStatus(@ApiParam("仓库ID") @PathVariable Long id,
                                                 @ApiParam("启用状态") @RequestParam Integer enabled) {
        boolean success = warehouseService.updateWarehouseStatus(id, enabled);
        if (success) {
            String message = enabled == 1 ? "启用成功" : "禁用成功";
            return Result.success(message, true);
        }
        return Result.failed("状态更新失败");
    }


    @ApiOperation("导出仓库列表")
    @PostMapping("/export")
    public void exportWarehouseList(@RequestBody CustomerWarehouseQueryRequest request,
                                    HttpServletResponse response) throws IOException {
        warehouseService.exportWarehouseList(request, response);
    }

    @ApiOperation("根据公司ID查询仓库选项列表（用于下拉框）")
    @GetMapping("/options")
    public Result<List<CustomerWarehouseOptionResponse>> getWarehouseOptions(
            @ApiParam("公司ID") @RequestParam String companyId) {
        List<CustomerWarehouseOptionResponse> options = warehouseService.getWarehouseOptionsByCompanyId(companyId);
        return Result.success(options);
    }
}
