package com.yi.controller.warehouse.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 入库单状态更新请求
 */
@Data
@ApiModel(value = "InboundStatusUpdateRequest", description = "入库单状态更新请求")
public class InboundStatusUpdateRequest {

    @NotNull(message = "入库单ID不能为空")
    @ApiModelProperty(value = "入库单ID", required = true)
    private Long id;

    @NotNull(message = "实际入库数不能为空")
    @Min(value = 0, message = "实际入库数不能小于0")
    @ApiModelProperty(value = "实际入库数", required = true)
    private Integer actualQuantity;

    @ApiModelProperty(value = "操作备注")
    private String remark;
}
