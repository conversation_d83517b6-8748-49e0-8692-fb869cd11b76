-- =============================================
-- 出入库管理系统初始化数据
-- =============================================

-- 1. 初始化单号序列数据（当前日期）
INSERT INTO `t_order_sequence` (`date_key`, `sequence_value`, `created_time`, `last_modified_time`) VALUES
(DATE_FORMAT(NOW(), '%Y%m%d'), 0, NOW(), NOW());

-- 2. 初始化测试出库单数据
INSERT INTO `t_outbound_order` (
    `order_no`, `status`, `outbound_type`, `outbound_company_id`, `outbound_company_name`, 
    `outbound_address`, `delivery_method`, `vehicle_number`, `driver_name`, `driver_phone`,
    `receive_company_id`, `receive_company_name`, `receive_address`, 
    `first_category`, `second_category`, `planned_quantity`, `actual_quantity`, 
    `outbound_time`, `created_by`, `created_time`, `last_modified_by`, `last_modified_time`, `valid`, `remark`
) VALUES
(
    CONCAT('F', DATE_FORMAT(NOW(), '%Y%m%d'), '0001'), 1, 1, 1, '易托盘科技有限公司',
    '上海市浦东新区张江高科技园区', '物流配送', '沪A12345', '张师傅', '13800138001',
    2, '客户公司A', '北京市朝阳区建国门外大街1号', 
    1, '标准托盘', 100, 0, 
    NULL, 'system', NOW(), 'system', NOW(), 1, '测试出库单1'
),
(
    CONCAT('F', DATE_FORMAT(NOW(), '%Y%m%d'), '0002'), 2, 2, 1, '易托盘科技有限公司',
    '上海市浦东新区张江高科技园区', '自提', '沪B67890', '李师傅', '13800138002',
    3, '客户公司B', '广州市天河区珠江新城', 
    1, '加强型托盘', 200, 180, 
    NOW(), 'system', NOW(), 'system', NOW(), 1, '测试出库单2'
),
(
    CONCAT('F', DATE_FORMAT(NOW(), '%Y%m%d'), '0003'), 3, 1, 1, '易托盘科技有限公司',
    '上海市浦东新区张江高科技园区', '快递配送', '沪C11111', '王师傅', '13800138003',
    4, '客户公司C', '深圳市南山区科技园', 
    1, '轻型托盘', 50, 50, 
    DATE_SUB(NOW(), INTERVAL 1 DAY), 'system', NOW(), 'system', NOW(), 1, '测试出库单3'
);

-- 3. 初始化测试入库单数据
INSERT INTO `t_inbound_order` (
    `order_no`, `status`, `inbound_type`, `inbound_warehouse_id`, `inbound_warehouse_name`,
    `delivery_method`, `vehicle_number`, `driver_name`, `driver_phone`,
    `sender_warehouse_id`, `sender_warehouse_name`, `sender_address`,
    `first_category`, `second_category`, `planned_quantity`, `actual_quantity`,
    `inbound_time`, `outbound_order_id`, `created_by`, `created_time`, `last_modified_by`, `last_modified_time`, `valid`, `remark`
) VALUES
(
    CONCAT('F', DATE_FORMAT(NOW(), '%Y%m%d'), '0001'), 1, 4, 2, '北京仓库',
    '物流配送', '沪A12345', '张师傅', '13800138001',
    1, '上海总仓', '上海市浦东新区张江高科技园区',
    1, '标准托盘', 100, 0,
    NULL, 1, 'system', NOW(), 'system', NOW(), 1, '测试入库单1'
),
(
    CONCAT('F', DATE_FORMAT(NOW(), '%Y%m%d'), '0002'), 2, 3, 3, '广州仓库',
    '自提', '沪B67890', '李师傅', '13800138002',
    1, '上海总仓', '上海市浦东新区张江高科技园区',
    1, '加强型托盘', 200, 150,
    NOW(), 2, 'system', NOW(), 'system', NOW(), 1, '测试入库单2'
),
(
    CONCAT('F', DATE_FORMAT(NOW(), '%Y%m%d'), '0003'), 3, 4, 4, '深圳仓库',
    '快递配送', '沪C11111', '王师傅', '13800138003',
    1, '上海总仓', '上海市浦东新区张江高科技园区',
    1, '轻型托盘', 50, 50,
    DATE_SUB(NOW(), INTERVAL 1 DAY), 3, 'system', NOW(), 'system', NOW(), 1, '测试入库单3'
);

-- 4. 更新单号序列（模拟已生成3个单号）
UPDATE `t_order_sequence` SET `sequence_value` = 3, `last_modified_time` = NOW() 
WHERE `date_key` = DATE_FORMAT(NOW(), '%Y%m%d');
