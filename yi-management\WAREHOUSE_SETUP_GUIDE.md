# 出入库管理系统部署指南

## 概述

本指南将帮助你完成出入库管理系统的完整部署，包括数据库初始化、代码编译和系统启动。

## 前置条件

### 1. 环境要求
- **Java**: JDK 8 或以上版本
- **Maven**: 3.6 或以上版本
- **MySQL**: 5.7 或以上版本
- **IDE**: IntelliJ IDEA 或 Eclipse（可选）

### 2. 数据库准备
确保MySQL服务已启动，并创建数据库：

```sql
CREATE DATABASE yi_management DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

## 部署步骤

### 第一步：数据库初始化

#### 1.1 执行RBAC系统表结构
```sql
-- 连接到数据库
USE yi_management;

-- 执行RBAC表结构
source src/main/resources/sql/rbac_ddl.sql;

-- 执行RBAC初始化数据
source src/main/resources/sql/rbac_dml.sql;
```

#### 1.2 执行出入库系统表结构
```sql
-- 执行出入库表结构
source src/main/resources/sql/warehouse_ddl.sql;

-- 执行出入库初始化数据
source src/main/resources/sql/warehouse_dml.sql;
```

#### 1.3 验证表创建
```sql
-- 查看所有表
SHOW TABLES;

-- 应该看到以下表：
-- t_sys_user, t_sys_role, t_sys_resource, t_sys_user_role, t_sys_role_resource, t_sys_dept
-- t_outbound_order, t_inbound_order, t_order_sequence
```

### 第二步：配置文件检查

#### 2.1 检查数据库连接配置
确保 `src/main/resources/application.yml` 或 `application.properties` 中的数据库配置正确：

```yaml
spring:
  datasource:
    url: *******************************************************************************************************
    username: your_username
    password: your_password
    driver-class-name: com.mysql.cj.jdbc.Driver
```

#### 2.2 检查MyBatis配置
```yaml
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.yi.entity
  configuration:
    map-underscore-to-camel-case: true
```

### 第三步：编译项目

#### 3.1 清理并编译
```bash
# 清理项目
mvn clean

# 编译项目
mvn compile

# 如果有测试，可以跳过测试编译
mvn compile -DskipTests
```

#### 3.2 解决可能的编译问题

**问题1：缺少依赖**
如果提示缺少某些依赖，检查 `pom.xml` 是否包含以下依赖：

```xml
<dependencies>
    <!-- Spring Boot Starter Web -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    
    <!-- MyBatis Plus -->
    <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
        <version>3.4.3</version>
    </dependency>
    
    <!-- MySQL Driver -->
    <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
        <scope>runtime</scope>
    </dependency>
    
    <!-- Validation -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>
    
    <!-- Swagger -->
    <dependency>
        <groupId>io.springfox</groupId>
        <artifactId>springfox-swagger2</artifactId>
        <version>2.9.2</version>
    </dependency>
    <dependency>
        <groupId>io.springfox</groupId>
        <artifactId>springfox-swagger-ui</artifactId>
        <version>2.9.2</version>
    </dependency>
    
    <!-- Lombok -->
    <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <optional>true</optional>
    </dependency>
</dependencies>
```

**问题2：包路径错误**
确保所有类的包路径正确，特别是：
- Controller类在 `com.yi.controller.warehouse` 包下
- Service类在 `com.yi.service` 包下
- Entity类在 `com.yi.entity` 包下
- Mapper类在 `com.yi.mapper` 包下

### 第四步：启动应用

#### 4.1 使用Maven启动
```bash
mvn spring-boot:run
```

#### 4.2 使用IDE启动
在IDE中找到主启动类（通常是 `Application.java` 或 `YiManagementApplication.java`），右键运行。

#### 4.3 使用JAR包启动
```bash
# 打包
mvn package -DskipTests

# 运行
java -jar target/yi-management-1.0.0.jar
```

### 第五步：验证部署

#### 5.1 检查启动日志
启动成功后，应该看到类似以下日志：
```
Started YiManagementApplication in 15.234 seconds (JVM running for 16.789)
```

#### 5.2 访问Swagger文档
打开浏览器访问：`http://localhost:8080/swagger-ui.html`

应该能看到以下API分组：
- 出库单管理
- 入库单管理
- 单号生成管理

#### 5.3 测试API接口

**测试单号生成：**
```bash
curl -X POST "http://localhost:8080/api/warehouse/order-number/outbound" \
  -H "Content-Type: application/json"
```

**测试出库单创建：**
```bash
curl -X POST "http://localhost:8080/api/warehouse/outbound" \
  -H "Content-Type: application/json" \
  -d '{
    "outboundType": 1,
    "outboundCompanyId": 1,
    "outboundCompanyName": "测试公司",
    "receiveCompanyId": 2,
    "receiveCompanyName": "收货公司",
    "firstCategory": 1,
    "plannedQuantity": 100
  }'
```

## 常见问题解决

### 问题1：数据库连接失败
**错误信息**：`Could not create connection to database server`

**解决方案**：
1. 检查MySQL服务是否启动
2. 检查数据库连接配置
3. 检查防火墙设置
4. 确认数据库用户权限

### 问题2：表不存在
**错误信息**：`Table 'yi_management.t_outbound_order' doesn't exist`

**解决方案**：
1. 确认已执行DDL脚本
2. 检查数据库名称是否正确
3. 重新执行建表脚本

### 问题3：编译错误
**错误信息**：`package com.yi.xxx does not exist`

**解决方案**：
1. 检查包路径是否正确
2. 确认所有必需的类都已创建
3. 刷新IDE项目结构
4. 重新导入Maven依赖

### 问题4：端口占用
**错误信息**：`Port 8080 was already in use`

**解决方案**：
1. 修改 `application.yml` 中的端口配置：
```yaml
server:
  port: 8081
```
2. 或者停止占用8080端口的进程

## 性能优化建议

### 1. 数据库优化
- 为常用查询字段添加索引
- 定期分析慢查询日志
- 考虑读写分离

### 2. 应用优化
- 配置连接池参数
- 启用缓存机制
- 配置JVM参数

### 3. 监控配置
- 配置应用监控
- 设置日志级别
- 配置健康检查

## 下一步

部署完成后，你可以：

1. **集成前端**：使用提供的API文档开发前端界面
2. **扩展功能**：根据业务需求添加新的功能模块
3. **性能调优**：根据实际使用情况进行性能优化
4. **安全加固**：添加认证授权机制

## 技术支持

如果在部署过程中遇到问题，可以：

1. 查看详细的错误日志
2. 参考相关文档：
   - `WAREHOUSE_MANAGEMENT_README.md` - 系统功能说明
   - `WAREHOUSE_CONTROLLER_API_GUIDE.md` - API使用指南
   - `WAREHOUSE_API_EXAMPLES.md` - API示例
3. 检查数据库连接和表结构
4. 验证配置文件设置

祝你部署顺利！🎉
