package com.yi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 发运订单动作枚举
 */
@Getter
@AllArgsConstructor
public enum ShippingOrderActionEnum {

    CANCEL(1000, "取消订单"),
    COMPLETE(2000, "完结订单");

    private final Integer code;
    private final String description;

    /**
     * 根据代码获取描述
     *
     * @param code 代码
     * @return 描述
     */
    public static String getDescriptionByCode(Integer code) {
        if (code == null) {
            return "";
        }
        for (ShippingOrderActionEnum actionEnum : values()) {
            if (actionEnum.getCode().equals(code)) {
                return actionEnum.getDescription();
            }
        }
        return "";
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 代码
     * @return 枚举
     */
    public static ShippingOrderActionEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ShippingOrderActionEnum actionEnum : values()) {
            if (actionEnum.getCode().equals(code)) {
                return actionEnum;
            }
        }
        return null;
    }
}
