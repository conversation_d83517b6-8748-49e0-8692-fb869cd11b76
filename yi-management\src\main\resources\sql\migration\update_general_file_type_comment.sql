-- =============================================
-- 更新t_general_file表type字段注释
-- 添加供应商营业执照类型支持
-- =============================================

-- 检查表是否存在
SELECT '=== 检查t_general_file表是否存在 ===' AS step;
SELECT COUNT(*) as table_exists 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 't_general_file';

-- 显示当前字段信息
SELECT '=== 当前type字段信息 ===' AS step;
SELECT 
    COLUMN_NAME as '字段名',
    DATA_TYPE as '数据类型',
    COLUMN_TYPE as '字段类型',
    IS_NULLABLE as '可为空',
    COLUMN_DEFAULT as '默认值',
    COLUMN_COMMENT as '当前注释'
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 't_general_file'
  AND COLUMN_NAME = 'type';

-- 更新字段注释
SELECT '=== 更新type字段注释 ===' AS step;
ALTER TABLE t_general_file 
MODIFY COLUMN `type` int(1) DEFAULT '1' 
COMMENT '类型: 1.销售合同, 2.发运订单, 3.供应商营业执照';

-- 验证更新结果
SELECT '=== 验证更新结果 ===' AS step;
SELECT 
    COLUMN_NAME as '字段名',
    DATA_TYPE as '数据类型',
    COLUMN_TYPE as '字段类型',
    IS_NULLABLE as '可为空',
    COLUMN_DEFAULT as '默认值',
    COLUMN_COMMENT as '更新后注释'
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 't_general_file'
  AND COLUMN_NAME = 'type';

-- 检查现有数据中的type值分布
SELECT '=== 现有数据type值分布 ===' AS step;
SELECT 
    type as '类型值',
    COUNT(*) as '记录数',
    CASE 
        WHEN type = 1 THEN '销售合同'
        WHEN type = 2 THEN '发运订单'
        WHEN type = 3 THEN '供应商营业执照'
        ELSE '未知类型'
    END as '类型说明'
FROM t_general_file 
WHERE valid = 1
GROUP BY type
ORDER BY type;

-- 显示表结构
SELECT '=== 完整表结构 ===' AS step;
DESCRIBE t_general_file;

SELECT '=== 更新完成 ===' AS step;

-- 使用说明
SELECT '=== 使用说明 ===' AS step;
SELECT '
新增的供应商营业执照类型使用方法：
1. type = 3 表示供应商营业执照
2. related_id 存储供应商ID
3. file_path 存储文件路径
4. file_type 存储文件扩展名（如jpg、pdf等）

示例插入语句：
INSERT INTO t_general_file (type, file_type, related_id, file_path, valid) 
VALUES (3, "jpg", 1, "/uploads/supplier/business-license/license1.jpg", 1);
' as usage_info;
