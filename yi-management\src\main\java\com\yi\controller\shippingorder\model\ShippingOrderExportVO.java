package com.yi.controller.shippingorder.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

/**
 * 发运订单导出VO
 */
@Data
@HeadRowHeight(20)
@ContentRowHeight(18)
public class ShippingOrderExportVO {

    @ExcelProperty(value = "订单号")
    @ColumnWidth(20)
    private String orderNo;

    @ExcelProperty(value = "合同编号")
    @ColumnWidth(20)
    private String contractCode;

    @ExcelProperty(value = "客户名称")
    @ColumnWidth(25)
    private String customerCompanyName;

    @ExcelProperty(value = "收货仓库")
    @ColumnWidth(20)
    private String warehouseName;

    @ExcelProperty(value = "收货地址")
    @ColumnWidth(40)
    private String warehouseAddress;

    @ExcelProperty(value = "收货人")
    @ColumnWidth(12)
    private String receiverName;

    @ExcelProperty(value = "产品")
    @ColumnWidth(25)
    private String product;

    @ExcelProperty(value = "需求数量")
    @ColumnWidth(12)
    private String count;

    @ExcelProperty(value = "发货数量")
    @ColumnWidth(12)
    private String shippedQuantity;

    @ExcelProperty(value = "签收数量")
    @ColumnWidth(12)
    private String receivedQuantity;

    @ExcelProperty(value = "订单状态")
    @ColumnWidth(12)
    private String status;

    @ExcelProperty(value = "创建人")
    @ColumnWidth(12)
    private String createdBy;

    @ExcelProperty(value = "创建时间")
    @ColumnWidth(20)
    private String createdTime;
}
