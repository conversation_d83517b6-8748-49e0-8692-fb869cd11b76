package com.yi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yi.entity.TGeneralFile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 通用文件表 Mapper 接口
 */
@Mapper
public interface TGeneralFileMapper extends BaseMapper<TGeneralFile> {

    /**
     * 根据关联ID和类型查询文件列表
     *
     * @param relatedId 关联ID
     * @param type 文件类型
     * @return 文件列表
     */
    List<TGeneralFile> selectByRelatedIdAndType(@Param("relatedId") Long relatedId, 
                                               @Param("type") Integer type);

    /**
     * 根据关联ID和类型删除文件
     *
     * @param relatedId 关联ID
     * @param type 文件类型
     * @return 影响行数
     */
    int deleteByRelatedIdAndType(@Param("relatedId") Long relatedId, 
                                @Param("type") Integer type);

    /**
     * 统计关联ID和类型的文件数量
     *
     * @param relatedId 关联ID
     * @param type 文件类型
     * @return 文件数量
     */
    int countByRelatedIdAndType(@Param("relatedId") Long relatedId, 
                               @Param("type") Integer type);
}
