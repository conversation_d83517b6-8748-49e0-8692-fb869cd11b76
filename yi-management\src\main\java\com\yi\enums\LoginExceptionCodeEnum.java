package com.yi.enums;


import com.yi.configuration.exception.BaseExceptionEnum;

/**
 * <AUTHOR>
 * @date 2022/10/19
 */
public enum LoginExceptionCodeEnum implements BaseExceptionEnum {

	/** 账密验证异常 */
	CLIENT_FORBIDDEN(4009, "客户端被禁止!"),
	USER_NAME_PWD_ERROR(4010, "帐号或者密码错误"),
	USER_NAME_EXIST(4011, "帐号已存在"),
	USER_NOT_EXIST(4012, "您的账号或密码错误"),
	VERIFICATION_CODE_ERROR(4013, "验证码错误或已失效"),
	USER_PERMISSION_NOT_ALLOW(4014, "您暂无查看权限，请联系公司管理员添加"),
	USER_HAVE_DISABLED(4015,"您的权限已被限制，无法登录！"),
	NOT_APPLY_DRIVER_ACCOUNT(4016,"该账号未开通权限，如有问题请联系客服"),
	NOT_APPLY_CARRIER_ACCOUNT(4017,"您的权限已被限制，如有问题联系工作人员"),
	CARRIER_ACCOUNT_CLOSED(4018,"您的账号被关闭，如有问题联系工作人员"),
	DRIVER_ACCOUNT_CLOSED(4019,"您的权限已经被关闭，如有疑问联系奇亚调度"),
	CUSTOMER_COMPANY_NOT_AUDIT(4020,"您的账号未完成认证，请先完成认证"),
	CLIENT_CUSTOMER_CAN_NOT_LOGIN(4021,"您暂时无法登录，请等待更新"),
	NOT_AUDIT_NOT_ALLOW_LOGIN(4022,"您的账号未被审核通过,无法登录"),
	SYSTEM_ABNORMAL(-1, "系统异常"),
	PWD_ERROR(4023, "您的密码错误"),
	MOBILE_NOT_REGISTER(4024, "手机号或邮箱未注册"),
	CUSTOMER_COMPANY_DIS_ENABLED(4026, "当前公司已被禁用"),
	PASSWORD_ERROR(4027, "账号或密码错误"),
	WMS_VERIFICATION_CODE_ERROR(4028, "验证码不正确"),
	CURRENT_USER_IS_DIS_ENABLED (4029, "该账号已被禁用，请联系管理员"),
	CURRENT_USER_NOT_ACTIVITY(4030, "该账号待激活，请激活后再登录"),
	CUSTOMER_COMPANY_NOT_EXIST(4031, "客户公司不存在"),
	DEVICE_NOT_BELONG_CURRENT_COMPANY(4032, "非账号公司所属设备，无法登录"),
	DEVICE_DIS_ENABLED(4033, "该设备已禁用，请联系管理员"),
	CURRENT_NO_PERMISSION_TO_LOGIN(4034, "当前账号无手持登录权限,请联系管理员"),
	CURRENT_LOGIN_PERSON_NOT_WAREHOUSE_TO_ACCESS_DEVICE(4035,"当前账号没有设备绑定仓库访问权限,请联系管理员分配权限"),

	LOGIN_ERROR_COUNT_MAX(4036,"您的账号已被锁定，{0}分钟后自动解锁，或者联系后台管理员修改密码"),
	LOGIN_ERROR_MSG(4037,"您的账号或密码错误，{0}次输入错误后账号将被锁定，10分钟后自动解锁，或者联系后台管理员修改密码"),
	ACCOUNT_NOT_HAVE_LOGIN_PERMISSION(4038,"该账号无登录权限，无法登录"),
	ACCOUNT_IS_DISABLED(4039,"账号被禁用，无法登录"),
	USER_SIGNATURE_ERROR(4040,"请使用有效工作终端访问系统，请联系管理员！"),
	SIGN_TIMEOUT(4041,"登录超时，请重新登录！"),
	SIGN_ERROR(4042,"令牌有误，请联系管理员！"),

	;

	private final int code;
	private final String msg;

	LoginExceptionCodeEnum(int code, String msg){
		this.code = code;
		this.msg = msg;
	}

	@Override
	public int getCode() {
		return code;
	}

	@Override
	public String getMessage() {
		return msg;
	}

}
