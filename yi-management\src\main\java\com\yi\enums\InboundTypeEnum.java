package com.yi.enums;

/**
 * 入库类型枚举
 */
public enum InboundTypeEnum {
    
    PURCHASE(1, "采购入库"),
    RECYCLE(2, "回收入库"),
    TRANSFER(3, "调拨入库"),
    SALES_RETURN(4, "销售入库");

    private final Integer code;
    private final String desc;

    InboundTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据类型码获取描述
     *
     * @param code 类型码
     * @return 类型描述
     */
    public static String getDescByCode(Integer code) {
        if (code == null) {
            return "";
        }
        for (InboundTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type.getDesc();
            }
        }
        return "";
    }

    /**
     * 根据类型码获取枚举
     *
     * @param code 类型码
     * @return 枚举对象
     */
    public static InboundTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (InboundTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 检查类型是否有效
     *
     * @param code 类型码
     * @return 是否有效
     */
    public static boolean isValid(Integer code) {
        return getByCode(code) != null;
    }
}
