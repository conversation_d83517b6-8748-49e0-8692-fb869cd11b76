package com.yi.utils;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * FormatUtils工具类测试
 */
public class FormatUtilsTest {

    @Test
    public void testMaskPhone() {
        // 测试正常11位手机号
        assertEquals("138****5678", FormatUtils.maskPhone("13812345678"));
        
        // 测试10位手机号
        assertEquals("138***5678", FormatUtils.maskPhone("1381235678"));
        
        // 测试8位号码
        assertEquals("138*5678", FormatUtils.maskPhone("13815678"));
        
        // 测试7位号码
        assertEquals("138*567", FormatUtils.maskPhone("1381567"));
        
        // 测试短号码（不脱敏）
        assertEquals("138156", FormatUtils.maskPhone("138156"));
        assertEquals("13815", FormatUtils.maskPhone("13815"));
        
        // 测试空值和null
        assertEquals("", FormatUtils.maskPhone(null));
        assertEquals("", FormatUtils.maskPhone(""));
        assertEquals("", FormatUtils.maskPhone("   "));
        
        // 测试带空格的号码
        assertEquals("138****5678", FormatUtils.maskPhone(" 13812345678 "));
    }

    @Test
    public void testCombineReceiverInfo() {
        // 测试正常情况
        assertEquals("张三 138****5678", FormatUtils.combineReceiverInfo("张三", "13812345678"));
        
        // 测试只有姓名
        assertEquals("张三", FormatUtils.combineReceiverInfo("张三", null));
        assertEquals("张三", FormatUtils.combineReceiverInfo("张三", ""));
        
        // 测试只有手机号
        assertEquals("138****5678", FormatUtils.combineReceiverInfo(null, "13812345678"));
        assertEquals("138****5678", FormatUtils.combineReceiverInfo("", "13812345678"));
        
        // 测试都为空
        assertEquals("", FormatUtils.combineReceiverInfo(null, null));
        assertEquals("", FormatUtils.combineReceiverInfo("", ""));
        
        // 测试带空格的输入
        assertEquals("张三 138****5678", FormatUtils.combineReceiverInfo(" 张三 ", " 13812345678 "));
        
        // 测试短号码
        assertEquals("张三 138156", FormatUtils.combineReceiverInfo("张三", "138156"));
    }
}
