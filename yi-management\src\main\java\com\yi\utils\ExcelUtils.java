package com.yi.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.List;

/**
 * EasyExcel导出工具类
 */
public class ExcelUtils {

    /**
     * 导出Excel文件（使用EasyExcel）
     *
     * @param response  HTTP响应
     * @param fileName  文件名（不含扩展名）
     * @param sheetName 工作表名称
     * @param clazz     导出数据的类型（必须有@ExcelProperty注解）
     * @param dataList  数据列表
     * @param <T>       数据类型
     * @throws IOException IO异常
     */
    public static <T> void exportExcel(HttpServletResponse response, String fileName, String sheetName,
                                       Class<T> clazz, List<T> dataList) throws IOException {

        // 设置响应头
        setResponseHeaders(response, fileName);

        // 使用EasyExcel导出
        EasyExcel.write(response.getOutputStream(), clazz)
                .sheet(sheetName)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()) // 自动调整列宽
                .doWrite(dataList);
    }

    /**
     * 导出Excel文件（带时间戳的文件名）
     *
     * @param response     HTTP响应
     * @param fileNameBase 文件名基础部分
     * @param sheetName    工作表名称
     * @param clazz        导出数据的类型（必须有@ExcelProperty注解）
     * @param dataList     数据列表
     * @param <T>          数据类型
     * @throws IOException IO异常
     */
    public static <T> void exportExcelWithTimestamp(HttpServletResponse response, String fileNameBase, String sheetName,
                                                    Class<T> clazz, List<T> dataList) throws IOException {
        String fileName = fileNameBase + "_" + FormatUtils.formatDateTime(LocalDateTime.now(), "yyyyMMdd_HHmmss");
        exportExcel(response, fileName, sheetName, clazz, dataList);
    }

    /**
     * 设置响应头
     */
    private static void setResponseHeaders(HttpServletResponse response, String fileName) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("UTF-8");

        String encodedFileName = URLEncoder.encode(fileName + ".xlsx", StandardCharsets.UTF_8.toString());
        response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"");
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Expires", "0");
    }
}
