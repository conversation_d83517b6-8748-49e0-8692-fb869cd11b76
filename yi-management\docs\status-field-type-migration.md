# 发运订单和发货需求状态字段类型迁移说明

## 概述

根据您的要求，将`t_shipping_order`和`t_shipping_demand`表的`status`字段从`varchar(20)`类型修改为`int(4)`类型，并相应地修改了所有相关的枚举类和代码实现。

## 🎯 **修改背景**

### **问题描述**：
- 原来的状态字段使用字符串类型（varchar），如"PENDING"、"SHIPPING"等
- 字符串类型占用存储空间较大，查询性能相对较低
- 不利于状态的扩展和维护

### **解决方案**：
- 将状态字段改为整型（int(4)），使用数字编码表示状态
- 创建对应的枚举类统一管理状态码和描述
- 提供完整的数据迁移脚本

## 📋 **修改内容**

### **1. 数据库表结构修改**

**发运订单表 (t_shipping_order)**：
```sql
-- 修改前
`status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '订单状态：PENDING-待发货，SHIPPING-发货中，COMPLETED-已完结，CANCELLED-已取消'

-- 修改后  
`status` int(4) NOT NULL DEFAULT 1000 COMMENT '订单状态：1000-待发货，2000-发货中，3000-已完结，4000-已取消'
```

**发货需求表 (t_shipping_demand)**：
```sql
-- 修改前
`status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '发货状态：PENDING-待发货，SHIPPED-已发货，COMPLETED-已完结，CANCELLED-已取消'

-- 修改后
`status` int(4) NOT NULL DEFAULT 1000 COMMENT '发货状态：1000-待发货，2000-已发货，3000-已完结，4000-已取消'
```

### **2. 状态枚举类创建**

**发运订单状态枚举 (ShippingOrderStatusEnum)**：
```java
@Getter
@AllArgsConstructor
public enum ShippingOrderStatusEnum {
    PENDING(1000, "待发货"),
    SHIPPING(2000, "发货中"),
    COMPLETED(3000, "已完结"),
    CANCELLED(4000, "已取消"),
    DEFAULT(-999, "");

    private final Integer code;
    private final String description;

    public static String getDescriptionByCode(Integer code) {
        // 根据状态码获取描述
    }

    public static boolean canCancel(Integer code) {
        // 判断是否可以取消
    }
}
```

**发货需求状态枚举 (ShippingDemandStatusEnum)**：
```java
@Getter
@AllArgsConstructor
public enum ShippingDemandStatusEnum {
    PENDING(1000, "待发货"),
    SHIPPED(2000, "已发货"),
    COMPLETED(3000, "已完结"),
    CANCELLED(4000, "已取消"),
    DEFAULT(-999, "");

    private final Integer code;
    private final String description;

    public static String getDescriptionByCode(Integer code) {
        // 根据状态码获取描述
    }

    public static boolean isShipped(Integer code) {
        // 判断是否已发货
    }
}
```

### **3. 实体类修改**

**TShippingOrder实体类**：
```java
/**
 * 订单状态：1000-待发货，2000-发货中，3000-已完结，4000-已取消
 */
private Integer status;  // 原来是 String status
```

**TShippingDemand实体类**：
```java
/**
 * 发货状态：1000-待发货，2000-已发货，3000-已完结，4000-已取消
 */
private Integer status;  // 原来是 String status
```

### **4. 服务类修改**

**TShippingOrderService修改**：
```java
// 状态设置
order.setStatus(ShippingOrderStatusEnum.PENDING.getCode()); // 原来是 "PENDING"

// 状态校验
if (!ShippingOrderStatusEnum.PENDING.getCode().equals(existingOrder.getStatus())) {
    throw new RuntimeException("只有待发货状态的订单才能编辑");
}

// 状态名称获取
private String getStatusName(Integer status) {
    return ShippingOrderStatusEnum.getDescriptionByCode(status);
}
```

**TShippingDemandService修改**：
```java
// 状态设置
demand.setStatus(ShippingDemandStatusEnum.PENDING.getCode()); // 原来是 "PENDING"

// 状态名称获取
private String getStatusName(Integer status) {
    return ShippingDemandStatusEnum.getDescriptionByCode(status);
}
```

### **5. VO类修改**

**ShippingOrderPageVO**：
```java
/**
 * 订单状态：1000-待发货，2000-发货中，3000-已完结，4000-已取消
 */
private Integer status;  // 原来是 String status
```

**ShippingDemandPageVO**：
```java
/**
 * 发货状态：1000-待发货，2000-已发货，3000-已完结，4000-已取消
 */
private Integer status;  // 原来是 String status
```

## 🔧 **状态码映射表**

### **发运订单状态映射**

| 原字符串状态 | 新数字状态 | 状态描述 | 枚举常量 |
|-------------|-----------|----------|----------|
| PENDING | 1000 | 待发货 | ShippingOrderStatusEnum.PENDING |
| SHIPPING | 2000 | 发货中 | ShippingOrderStatusEnum.SHIPPING |
| COMPLETED | 3000 | 已完结 | ShippingOrderStatusEnum.COMPLETED |
| CANCELLED | 4000 | 已取消 | ShippingOrderStatusEnum.CANCELLED |

### **发货需求状态映射**

| 原字符串状态 | 新数字状态 | 状态描述 | 枚举常量 |
|-------------|-----------|----------|----------|
| PENDING | 1000 | 待发货 | ShippingDemandStatusEnum.PENDING |
| SHIPPED | 2000 | 已发货 | ShippingDemandStatusEnum.SHIPPED |
| COMPLETED | 3000 | 已完结 | ShippingDemandStatusEnum.COMPLETED |
| CANCELLED | 4000 | 已取消 | ShippingDemandStatusEnum.CANCELLED |

## 📊 **数据迁移脚本**

### **迁移脚本位置**：
`yi-management/src/main/resources/sql/status-migration.sql`

### **迁移步骤**：
1. **备份现有数据**（可选，生产环境建议）
2. **添加新的int类型status字段**
3. **数据转换** - 将字符串状态转换为对应的数字状态
4. **验证迁移结果**
5. **删除旧的varchar字段**
6. **重命名新字段**
7. **更新相关索引**
8. **最终验证**

### **关键转换逻辑**：
```sql
-- 发运订单状态转换
UPDATE `t_shipping_order` 
SET `status_new` = CASE 
    WHEN `status` = 'PENDING' THEN 1000
    WHEN `status` = 'SHIPPING' THEN 2000
    WHEN `status` = 'COMPLETED' THEN 3000
    WHEN `status` = 'CANCELLED' THEN 4000
    ELSE 1000  -- 默认为待发货
END;

-- 发货需求状态转换
UPDATE `t_shipping_demand` 
SET `status_new` = CASE 
    WHEN `status` = 'PENDING' THEN 1000
    WHEN `status` = 'SHIPPED' THEN 2000
    WHEN `status` = 'COMPLETED' THEN 3000
    WHEN `status` = 'CANCELLED' THEN 4000
    ELSE 1000  -- 默认为待发货
END;
```

## 🧪 **测试修改**

### **测试文件修改**：
- `TShippingOrderFieldTest.java`
- `TShippingOrderUpdateTest.java`
- `TShippingOrderDetailTest.java`
- `TShippingOrderServiceAddTest.java`

### **测试修改示例**：
```java
// 修改前
order.setStatus("PENDING");
assertEquals("PENDING", response.getStatus());

// 修改后
order.setStatus(1000); // PENDING
assertEquals("1000", response.getStatus());
```

## ✅ **修改优势**

### **1. 性能提升**
- **存储空间** - int(4)比varchar(20)节省存储空间
- **查询性能** - 整型比较比字符串比较更快
- **索引效率** - 整型索引比字符串索引更高效

### **2. 可维护性**
- **统一管理** - 通过枚举类统一管理状态码和描述
- **类型安全** - 编译时检查，避免字符串拼写错误
- **扩展性好** - 新增状态只需在枚举中添加

### **3. 业务逻辑**
- **状态判断** - 提供丰富的状态判断方法
- **业务规则** - 封装状态相关的业务规则
- **国际化支持** - 便于后续支持多语言

## ⚠️ **注意事项**

### **1. 数据迁移**
- **备份数据** - 生产环境执行前务必备份数据
- **分批执行** - 大数据量时建议分批执行迁移
- **验证完整性** - 迁移后验证数据完整性

### **2. 代码兼容**
- **API接口** - 确保前端调用的API返回格式正确
- **第三方集成** - 检查是否有第三方系统依赖状态字段
- **报表查询** - 更新相关的报表查询逻辑

### **3. 部署顺序**
1. **数据库迁移** - 先执行数据库结构和数据迁移
2. **代码部署** - 再部署修改后的应用代码
3. **功能验证** - 验证各项功能正常工作

## 🚀 **后续扩展**

### **1. 状态流转**
```java
public enum ShippingOrderStatusEnum {
    // 添加状态流转方法
    public boolean canTransitionTo(ShippingOrderStatusEnum targetStatus) {
        // 定义状态流转规则
    }
}
```

### **2. 状态历史**
```java
// 可以考虑添加状态变更历史表
CREATE TABLE `t_shipping_order_status_history` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `order_id` bigint(20) NOT NULL,
    `old_status` int(4),
    `new_status` int(4) NOT NULL,
    `change_reason` varchar(200),
    `created_by` varchar(50),
    `created_time` datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
);
```

### **3. 状态监控**
```java
// 添加状态变更的监控和告警
@EventListener
public void handleStatusChange(OrderStatusChangeEvent event) {
    // 记录状态变更日志
    // 发送通知
    // 更新统计数据
}
```

通过这次修改，系统的状态管理更加规范化和高效化，为后续的功能扩展和性能优化奠定了良好的基础！🎉
