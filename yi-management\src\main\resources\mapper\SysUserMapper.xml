<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yi.mapper.SysUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yi.entity.SysUser">
        <id column="id" property="id" />
        <result column="username" property="username" />
        <result column="password" property="password" />
        <result column="real_name" property="realName" />
        <result column="email" property="email" />
        <result column="phone" property="phone" />
        <result column="avatar" property="avatar" />
        <result column="gender" property="gender" />
        <result column="birthday" property="birthday" />
        <result column="dept_id" property="deptId" />
        <result column="status" property="status" />
        <result column="login_ip" property="loginIp" />
        <result column="login_time" property="loginTime" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="last_modified_by" property="lastModifiedBy" />
        <result column="last_modified_time" property="lastModifiedTime" />
        <result column="valid" property="valid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, username, password, real_name, email, phone, avatar, gender, birthday, 
        dept_id, status, login_ip, login_time, created_by, created_time, 
        last_modified_by, last_modified_time, valid
    </sql>

    <!-- 分页查询用户列表 -->
    <select id="selectUserPage" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM t_sys_user
        WHERE valid = 1
        <if test="username != null and username != ''">
            AND username LIKE CONCAT('%', #{username}, '%')
        </if>
        <if test="realName != null and realName != ''">
            AND real_name LIKE CONCAT('%', #{realName}, '%')
        </if>
        <if test="email != null and email != ''">
            AND email LIKE CONCAT('%', #{email}, '%')
        </if>
        <if test="phone != null and phone != ''">
            AND phone LIKE CONCAT('%', #{phone}, '%')
        </if>
        <if test="deptId != null">
            AND dept_id = #{deptId}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY created_time DESC
    </select>

    <!-- 根据用户名查询用户 -->
    <select id="selectByUsername" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM t_sys_user
        WHERE username = #{username} AND valid = 1
    </select>

    <!-- 根据邮箱查询用户 -->
    <select id="selectByEmail" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM t_sys_user
        WHERE email = #{email} AND valid = 1
    </select>

    <!-- 根据手机号查询用户 -->
    <select id="selectByPhone" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM t_sys_user
        WHERE phone = #{phone} AND valid = 1
    </select>

    <!-- 根据部门ID查询用户列表 -->
    <select id="selectByDeptId" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM t_sys_user
        WHERE dept_id = #{deptId} AND valid = 1
        ORDER BY created_time DESC
    </select>

    <!-- 根据角色ID查询用户列表 -->
    <select id="selectByRoleId" resultMap="BaseResultMap">
        SELECT
            u.id, u.username, u.password, u.real_name, u.email, u.phone, u.avatar,
            u.gender, u.birthday, u.dept_id, u.status, u.login_ip, u.login_time,
            u.created_by, u.created_time, u.last_modified_by, u.last_modified_time, u.valid
        FROM t_sys_user u
        INNER JOIN t_sys_user_role ur ON u.id = ur.user_id
        WHERE ur.role_id = #{roleId} AND u.valid = 1 AND ur.valid = 1
        ORDER BY u.created_time DESC
    </select>

    <!-- 查询用户的角色列表 -->
    <select id="selectRoleIdsByUserId" resultType="java.lang.Long">
        SELECT ur.role_id
        FROM t_sys_user_role ur
        INNER JOIN t_sys_role r ON ur.role_id = r.id
        WHERE ur.user_id = #{userId} AND ur.valid = 1 AND r.valid = 1 AND r.status = 1
    </select>

    <!-- 查询用户的权限列表 -->
    <select id="selectPermissionsByUserId" resultType="java.lang.String">
        SELECT DISTINCT res.perms
        FROM t_sys_user_role ur
        INNER JOIN t_sys_role_resource rr ON ur.role_id = rr.role_id
        INNER JOIN t_sys_resource res ON rr.resource_id = res.id
        WHERE ur.user_id = #{userId}
        AND ur.valid = 1 AND rr.valid = 1 AND res.valid = 1
        AND res.status = 1 AND res.perms IS NOT NULL AND res.perms != ''
    </select>

</mapper>
