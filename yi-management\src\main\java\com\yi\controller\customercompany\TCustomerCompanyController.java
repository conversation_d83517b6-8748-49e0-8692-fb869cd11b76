package com.yi.controller.customercompany;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yi.common.Result;
import com.yi.controller.customercompany.model.CustomerCompanyDetailResponse;
import com.yi.controller.customercompany.model.CustomerCompanyPageResponse;
import com.yi.controller.customercompany.model.CustomerCompanyQueryRequest;
import com.yi.controller.customercompany.model.CustomerCompanyRequest;
import com.yi.entity.TCustomerCompany;
import com.yi.service.TCustomerCompanyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * 客户公司表 前端控制器
 */
@RestController
@RequestMapping("/api/customer-company")
@Api(tags = "客户公司管理")
public class TCustomerCompanyController {

    @Autowired
    private TCustomerCompanyService customerCompanyService;

    @ApiOperation("分页查询客户公司列表")
    @PostMapping("/page")
    public Result<IPage<CustomerCompanyPageResponse>> getCustomerCompanyPage(@RequestBody CustomerCompanyQueryRequest request) {
        IPage<CustomerCompanyPageResponse> page = customerCompanyService.getCustomerCompanyPageResponse(request);
        return Result.success(page);
    }

    @ApiOperation("根据ID获取客户公司详情")
    @GetMapping("/{id}")
    public Result<CustomerCompanyDetailResponse> getCustomerCompanyById(@ApiParam("客户公司ID") @PathVariable Long id) {
        CustomerCompanyDetailResponse response = customerCompanyService.getCustomerCompanyDetailById(id);
        if (response == null) {
            return Result.failed("客户公司不存在");
        }
        return Result.success(response);
    }

    @ApiOperation("新增客户公司")
    @PostMapping("/add")
    public Result<Boolean> addCustomerCompany(@Valid @RequestBody CustomerCompanyRequest request) {
        try {
            boolean success = customerCompanyService.addCustomerCompany(request);
            if (success) {
                return Result.success("新增成功", true);
            }
            return Result.failed("新增失败");
        } catch (RuntimeException e) {
            return Result.failed(e.getMessage());
        }
    }

    @ApiOperation("更新客户公司")
    @PutMapping("/update")
    public Result<Boolean> updateCustomerCompany(@Valid @RequestBody CustomerCompanyRequest request) {
        if (request.getId() == null || request.getId().trim().isEmpty()) {
            return Result.validateFailed("客户公司ID不能为空");
        }
        try {
            boolean success = customerCompanyService.updateCustomerCompany(request);
            if (success) {
                return Result.success("更新成功", true);
            }
            return Result.failed("更新失败");
        } catch (RuntimeException e) {
            return Result.failed(e.getMessage());
        }
    }


    @ApiOperation("启用/禁用客户公司")
    @PutMapping("/{id}/status")
    public Result<Boolean> updateCustomerCompanyStatus(@ApiParam("客户公司ID") @PathVariable Long id,
                                                       @ApiParam("启用状态") @RequestParam Integer enabled) {
        boolean success = customerCompanyService.updateCustomerCompanyStatus(id, enabled);
        if (success) {
            String message = enabled == 1 ? "启用成功" : "禁用成功";
            return Result.success(message, true);
        }
        return Result.failed("状态更新失败");
    }


    @ApiOperation("根据公司类型查询客户公司（用于新增仓库下拉框 关联下游客户仓库 合同新增下拉框）")
    @GetMapping("/by-type")
    public Result<List<TCustomerCompany>> getCustomerCompanyByType(@ApiParam("公司类型（可选，不传查询所有类型）") @RequestParam(required = false) String customerCompanyType) {
        List<TCustomerCompany> list = customerCompanyService.getCustomerCompanyByType(customerCompanyType);
        return Result.success(list);
    }

    @ApiOperation("导出客户公司列表")
    @PostMapping("/export")
    public void exportCustomerCompanyList(@RequestBody CustomerCompanyQueryRequest request,
                                          HttpServletResponse response) throws IOException {
        customerCompanyService.exportCustomerCompanyList(request, response);
    }

}
