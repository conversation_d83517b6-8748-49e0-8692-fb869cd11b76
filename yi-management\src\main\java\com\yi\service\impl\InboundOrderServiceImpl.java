package com.yi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yi.entity.InboundOrder;
import com.yi.enums.InboundStatusEnum;
import com.yi.mapper.InboundOrderMapper;
import com.yi.service.InboundOrderService;
import com.yi.service.OrderNumberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 入库单表 服务实现类
 */
@Service
public class InboundOrderServiceImpl extends ServiceImpl<InboundOrderMapper, InboundOrder> implements InboundOrderService {

    @Autowired
    private OrderNumberService orderNumberService;

    @Override
    public IPage<InboundOrder> selectInboundOrderPage(Page<InboundOrder> page, String orderNo, Integer status,
                                                      Integer inboundType, Long inboundWarehouseId, Long senderWarehouseId,
                                                      Integer firstCategory, LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.selectInboundOrderPage(page, orderNo, status, inboundType, inboundWarehouseId,
                senderWarehouseId, firstCategory, startTime, endTime);
    }

    @Override
    public InboundOrder selectByOrderNo(String orderNo) {
        return baseMapper.selectByOrderNo(orderNo);
    }

    @Override
    public List<InboundOrder> selectByStatus(Integer status) {
        return baseMapper.selectByStatus(status);
    }

    @Override
    public InboundOrder selectByOutboundOrderId(Long outboundOrderId) {
        return baseMapper.selectByOutboundOrderId(outboundOrderId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createInboundOrder(InboundOrder inboundOrder) {
        // 生成入库单号（如果没有提供）
        if (!StringUtils.hasText(inboundOrder.getOrderNo())) {
            String orderNo = orderNumberService.generateInboundOrderNo();
            inboundOrder.setOrderNo(orderNo);
        }

        // 检查单号是否已存在
        if (checkOrderNoExists(inboundOrder.getOrderNo(), null)) {
            throw new RuntimeException("入库单号已存在");
        }

        // 设置默认值
        if (inboundOrder.getStatus() == null) {
            inboundOrder.setStatus(InboundStatusEnum.PENDING.getCode());
        }
        if (inboundOrder.getActualQuantity() == null) {
            inboundOrder.setActualQuantity(0);
        }
        inboundOrder.setValid(1);
        inboundOrder.setCreatedTime(LocalDateTime.now());
        inboundOrder.setLastModifiedTime(LocalDateTime.now());

        return save(inboundOrder);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateInboundOrder(InboundOrder inboundOrder) {
        // 检查单号是否已存在（排除自己）
        if (StringUtils.hasText(inboundOrder.getOrderNo()) && 
            checkOrderNoExists(inboundOrder.getOrderNo(), inboundOrder.getId())) {
            throw new RuntimeException("入库单号已存在");
        }

        inboundOrder.setLastModifiedTime(LocalDateTime.now());
        return updateById(inboundOrder);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteInboundOrder(Long id) {
        InboundOrder order = getById(id);
        if (order == null) {
            throw new RuntimeException("入库单不存在");
        }

        // 只有待入库状态的订单才能删除
        if (!InboundStatusEnum.PENDING.getCode().equals(order.getStatus())) {
            throw new RuntimeException("只有待入库状态的订单才能删除");
        }

        // 逻辑删除
        InboundOrder updateOrder = new InboundOrder();
        updateOrder.setId(id);
        updateOrder.setValid(0);
        updateOrder.setLastModifiedTime(LocalDateTime.now());
        return updateById(updateOrder);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteInboundOrders(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return true;
        }

        for (Long id : ids) {
            deleteInboundOrder(id);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean partialInbound(Long id, Integer actualQuantity, String lastModifiedBy) {
        InboundOrder order = getById(id);
        if (order == null) {
            throw new RuntimeException("入库单不存在");
        }

        // 只有待入库状态的订单才能部分入库
        if (!InboundStatusEnum.PENDING.getCode().equals(order.getStatus())) {
            throw new RuntimeException("只有待入库状态的订单才能部分入库");
        }

        // 检查实际入库数不能超过计划入库数
        if (actualQuantity > order.getPlannedQuantity()) {
            throw new RuntimeException("实际入库数不能超过计划入库数");
        }

        // 更新状态为部分入库
        return baseMapper.updateStatus(id, InboundStatusEnum.PARTIAL.getCode(), 
                actualQuantity, LocalDateTime.now(), lastModifiedBy) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean completeInbound(Long id, Integer actualQuantity, String lastModifiedBy) {
        InboundOrder order = getById(id);
        if (order == null) {
            throw new RuntimeException("入库单不存在");
        }

        // 待入库或部分入库状态的订单才能完成入库
        if (!InboundStatusEnum.PENDING.getCode().equals(order.getStatus()) && 
            !InboundStatusEnum.PARTIAL.getCode().equals(order.getStatus())) {
            throw new RuntimeException("只有待入库或部分入库状态的订单才能完成入库");
        }

        // 检查实际入库数不能超过计划入库数
        if (actualQuantity > order.getPlannedQuantity()) {
            throw new RuntimeException("实际入库数不能超过计划入库数");
        }

        // 更新状态为已入库
        return baseMapper.updateStatus(id, InboundStatusEnum.COMPLETED.getCode(), 
                actualQuantity, LocalDateTime.now(), lastModifiedBy) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelInbound(Long id, String lastModifiedBy) {
        InboundOrder order = getById(id);
        if (order == null) {
            throw new RuntimeException("入库单不存在");
        }

        // 只有部分入库状态的订单才能取消
        if (!InboundStatusEnum.PARTIAL.getCode().equals(order.getStatus())) {
            throw new RuntimeException("只有部分入库状态的订单才能取消");
        }

        // 回退到待入库状态，清空实际入库数和入库时间
        return baseMapper.updateStatus(id, InboundStatusEnum.PENDING.getCode(), 
                0, null, lastModifiedBy) > 0;
    }

    @Override
    public List<Map<String, Object>> getStatusStatistics() {
        return baseMapper.selectStatusStatistics();
    }

    @Override
    public List<Map<String, Object>> getTypeStatistics() {
        return baseMapper.selectTypeStatistics();
    }

    @Override
    public List<InboundOrder> getPendingOrders() {
        return baseMapper.selectPendingOrders();
    }

    @Override
    public List<InboundOrder> getPartialOrders() {
        return baseMapper.selectPartialOrders();
    }

    @Override
    public boolean checkOrderNoExists(String orderNo, Long excludeId) {
        LambdaQueryWrapper<InboundOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InboundOrder::getOrderNo, orderNo)
               .eq(InboundOrder::getValid, 1);
        if (excludeId != null) {
            wrapper.ne(InboundOrder::getId, excludeId);
        }
        return count(wrapper) > 0;
    }
}
