﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-20px;
  width:1300px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u2558_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1301px;
  height:2px;
}
#u2558 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:50px;
  width:1300px;
  height:1px;
  display:flex;
}
#u2558 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2558_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2559_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:235px;
  height:30px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2559 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:21px;
  width:235px;
  height:30px;
  display:flex;
}
#u2559 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2559_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2560_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2560 {
  border-width:0px;
  position:absolute;
  left:1264px;
  top:32px;
  width:56px;
  height:19px;
  display:flex;
}
#u2560 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2560_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2561_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u2561 {
  border-width:0px;
  position:absolute;
  left:258px;
  top:140px;
  width:62px;
  height:16px;
  display:flex;
  color:#000000;
}
#u2561 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2561_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2562_input {
  position:absolute;
  left:0px;
  top:0px;
  width:700px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2562_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:700px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2562_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:700px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2562 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:130px;
  width:700px;
  height:26px;
  display:flex;
}
#u2562 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2562_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:700px;
  height:26px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2562.disabled {
}
#u2563_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1280px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2563 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:56px;
  width:1280px;
  height:50px;
  display:flex;
}
#u2563 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2563_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2564_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u2564 {
  border-width:0px;
  position:absolute;
  left:48px;
  top:71px;
  width:72px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u2564 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2564_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2565_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u2565 {
  border-width:0px;
  position:absolute;
  left:272px;
  top:191px;
  width:48px;
  height:16px;
  display:flex;
  color:#000000;
}
#u2565 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2565_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2566_input {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2566_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2566_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2566 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:186px;
  width:200px;
  height:26px;
  display:flex;
}
#u2566 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2566_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:26px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2566.disabled {
}
#u2567_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u2567 {
  border-width:0px;
  position:absolute;
  left:758px;
  top:191px;
  width:62px;
  height:16px;
  display:flex;
  color:#000000;
}
#u2567 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2567_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2568_input {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2568_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2568_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2568 {
  border-width:0px;
  position:absolute;
  left:850px;
  top:186px;
  width:200px;
  height:26px;
  display:flex;
}
#u2568 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2568_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:26px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2568.disabled {
}
#u2569_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u2569 {
  border-width:0px;
  position:absolute;
  left:919px;
  top:191px;
  width:112px;
  height:16px;
  display:flex;
  color:#AAAAAA;
}
#u2569 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2569_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2570_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u2570 {
  border-width:0px;
  position:absolute;
  left:606px;
  top:642px;
  width:140px;
  height:40px;
  display:flex;
  font-size:16px;
}
#u2570 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2570_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2571_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u2571 {
  border-width:0px;
  position:absolute;
  left:264px;
  top:298px;
  width:56px;
  height:16px;
  display:flex;
  color:#000000;
}
#u2571 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2571_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2572_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:170px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:30px;
}
#u2572 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:298px;
  width:300px;
  height:170px;
  display:flex;
  font-size:30px;
}
#u2572 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2572_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2573_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u2573 {
  border-width:0px;
  position:absolute;
  left:278px;
  top:247px;
  width:42px;
  height:16px;
  display:flex;
  color:#000000;
}
#u2573 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2573_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2574_input {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2574_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2574_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2574 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:242px;
  width:200px;
  height:26px;
  display:flex;
}
#u2574 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2574_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:26px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2574.disabled {
}
#u2575_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u2575 {
  border-width:0px;
  position:absolute;
  left:764px;
  top:247px;
  width:56px;
  height:16px;
  display:flex;
  color:#000000;
}
#u2575 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2575_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2576_input {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2576_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:26px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2576_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2576 {
  border-width:0px;
  position:absolute;
  left:850px;
  top:242px;
  width:200px;
  height:26px;
  display:flex;
}
#u2576 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2576_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:26px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2576.disabled {
}
#u2577_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u2577 {
  border-width:0px;
  position:absolute;
  left:296px;
  top:498px;
  width:28px;
  height:16px;
  display:flex;
  color:#000000;
}
#u2577 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2577_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2578_input {
  position:absolute;
  left:0px;
  top:0px;
  width:694px;
  height:98px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2578_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:694px;
  height:98px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2578_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:694px;
  height:98px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2578 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:498px;
  width:694px;
  height:98px;
  display:flex;
}
#u2578 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2578_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:694px;
  height:98px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2578.disabled {
}
