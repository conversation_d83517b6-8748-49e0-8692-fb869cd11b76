package com.yi.utils;

import com.github.pagehelper.PageHelper;
import lombok.Data;

import java.io.Serializable;

@Data
public class AbstractPageForm<T extends AbstractPageForm<T>> implements Serializable {
    protected int pageNum = 1;
    protected int pageSize = 25;

    /**
     * 启用分页
     * @return
     */
    @SuppressWarnings("unchecked")
    public final T enablePaging() {
        PageHelper.startPage(pageNum, pageSize);
        return (T) this;
    }
}
