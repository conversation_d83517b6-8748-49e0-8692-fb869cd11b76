package com.yi.controller.warehouse.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 出库单查询请求
 */
@Data
@ApiModel(value = "OutboundOrderQueryRequest", description = "出库单查询请求")
public class OutboundOrderQueryRequest {

    @ApiModelProperty(value = "当前页码", example = "1")
    private String current = "1";

    @ApiModelProperty(value = "每页大小", example = "10")
    private String size = "10";

    @ApiModelProperty(value = "出库单号（模糊查询）")
    private String orderNo;

    @ApiModelProperty(value = "出库状态：1-待出库，2-运输中，3-已出库")
    private Integer status;

    @ApiModelProperty(value = "出库类型：1-销售出库，2-调拨出库")
    private Integer outboundType;

    @ApiModelProperty(value = "出库公司ID")
    private Long outboundCompanyId;

    @ApiModelProperty(value = "出库公司名称（模糊查询）")
    private String outboundCompanyName;

    @ApiModelProperty(value = "收货公司ID")
    private Long receiveCompanyId;

    @ApiModelProperty(value = "收货公司名称（模糊查询）")
    private String receiveCompanyName;

    @ApiModelProperty(value = "一级类目：1-共享托盘")
    private Integer firstCategory;

    @ApiModelProperty(value = "二级类目（模糊查询）")
    private String secondCategory;

    @ApiModelProperty(value = "车牌号（模糊查询）")
    private String vehicleNumber;

    @ApiModelProperty(value = "司机姓名（模糊查询）")
    private String driverName;

    @ApiModelProperty(value = "开始时间（格式：yyyy-MM-dd HH:mm:ss）")
    private String startTime;

    @ApiModelProperty(value = "结束时间（格式：yyyy-MM-dd HH:mm:ss）")
    private String endTime;
}
