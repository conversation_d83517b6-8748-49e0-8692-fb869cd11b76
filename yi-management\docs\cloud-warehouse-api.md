# 云仓管理接口文档

## 1. 分页查询云仓列表

### 接口信息
- **接口路径**: `POST /api/cloud-warehouse/page`
- **接口描述**: 分页查询云仓列表
- **请求方式**: POST

### 请求参数
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "enabled": 1,
  "warehouseName": "北京仓库",
  "address": "北京",
  "warehouseType": 1,
  "warehouseAttribute": 1
}
```

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| pageNum | Integer | 否 | 页码，默认1 |
| pageSize | Integer | 否 | 每页大小，默认10 |
| enabled | Integer | 否 | 启用状态：1-启用，0-禁用 |
| warehouseName | String | 否 | 仓库名称 |
| address | String | 否 | 地址（省市区或详细地址） |
| warehouseType | Integer | 否 | 仓库类型：1-中心仓，2-卫星仓，3-虚拟仓 |
| warehouseAttribute | Integer | 否 | 仓库属性：1-自有，2-第三方 |

### 响应参数
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": "1",
        "enabledText": "启用中",
        "warehouseName": "北京中心仓",
        "address": "北京市北京市朝阳区 中关村大街123号",
        "contactPerson": "张经理",
        "contactPhone": "13800138000",
        "warehouseTypeText": "中心仓",
        "warehouseAttributeText": "自有",
        "workingHours": "08:00-18:00",
        "remark": "主要仓库",
        "createdBy": "admin",
        "createdTime": "2024-01-01 10:00:00"
      }
    ],
    "total": 1,
    "pages": 1,
    "current": 1,
    "size": 10
  }
}
```

## 2. 导出云仓列表

### 接口信息
- **接口路径**: `POST /api/cloud-warehouse/export`
- **接口描述**: 导出云仓列表到Excel
- **请求方式**: POST

### 请求参数
与分页查询接口相同（不需要pageNum和pageSize）

### 响应参数
直接下载Excel文件

## 3. 新增云仓

### 接口信息
- **接口路径**: `POST /api/cloud-warehouse`
- **接口描述**: 新增云仓
- **请求方式**: POST

### 请求参数
```json
{
  "warehouseType": 1,
  "warehouseAttribute": 1,
  "warehouseName": "北京中心仓",
  "provinceId": 110000,
  "provinceName": "北京市",
  "cityId": 110100,
  "cityName": "北京市",
  "areaId": 110101,
  "areaName": "朝阳区",
  "detailedAddress": "中关村大街123号",
  "contactPerson": "张经理",
  "contactPhone": "13800138000",
  "workingHours": "08:00-18:00",
  "remark": "主要仓库"
}
```

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| warehouseType | Integer | 是 | 仓库类型：1-中心仓，2-卫星仓，3-虚拟仓 |
| warehouseAttribute | Integer | 是 | 仓库属性：1-自有，2-第三方 |
| warehouseName | String | 是 | 仓库名称 |
| provinceId | Long | 否 | 省份ID |
| provinceName | String | 是 | 省份名称 |
| cityId | Long | 否 | 城市ID |
| cityName | String | 是 | 城市名称 |
| areaId | Long | 否 | 区县ID |
| areaName | String | 是 | 区县名称 |
| detailedAddress | String | 是 | 详细地址 |
| contactPerson | String | 是 | 联系人 |
| contactPhone | String | 是 | 联系方式 |
| workingHours | String | 否 | 作业时间 |
| remark | String | 否 | 备注 |

### 响应参数
```json
{
  "code": 200,
  "message": "新增成功",
  "data": true
}
```

## 4. 更新云仓

### 接口信息
- **接口路径**: `PUT /api/cloud-warehouse`
- **接口描述**: 更新云仓信息
- **请求方式**: PUT

### 请求参数
与新增接口相同，但需要包含id字段：
```json
{
  "id": "1",
  "warehouseType": 1,
  "warehouseAttribute": 1,
  "warehouseName": "北京中心仓（更新）",
  ...
}
```

### 响应参数
```json
{
  "code": 200,
  "message": "更新成功",
  "data": true
}
```

## 5. 启用/禁用云仓

### 接口信息
- **接口路径**: `PUT /api/cloud-warehouse/{id}/status`
- **接口描述**: 启用或禁用云仓
- **请求方式**: PUT

### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | Long | 是 | 云仓ID |
| enabled | Integer | 是 | 启用状态：1-启用，0-禁用 |

### 响应参数
```json
{
  "code": 200,
  "message": "启用成功",
  "data": true
}
```

## 6. 根据ID获取云仓详情

### 接口信息
- **接口路径**: `GET /api/cloud-warehouse/{id}`
- **接口描述**: 根据ID获取云仓详情信息
- **请求方式**: GET

### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | Long | 是 | 云仓ID |

### 响应参数
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "id": "1",
    "warehouseTypeText": "中心仓",
    "warehouseAttributeText": "自有",
    "warehouseName": "北京中心仓",
    "provinceName": "北京市",
    "cityName": "北京市",
    "areaName": "朝阳区",
    "detailedAddress": "中关村大街123号",
    "contactPerson": "张经理",
    "contactPhone": "13800138000",
    "workingHours": "08:00-18:00",
    "remark": "主要仓库"
  }
}
```

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | String | 云仓ID |
| warehouseTypeText | String | 仓库类型中文：中心仓、卫星仓、虚拟仓 |
| warehouseAttributeText | String | 仓库属性中文：自有、第三方 |
| warehouseName | String | 仓库名称 |
| provinceName | String | 省份名称 |
| cityName | String | 城市名称 |
| areaName | String | 区县名称 |
| detailedAddress | String | 详细地址 |
| contactPerson | String | 联系人 |
| contactPhone | String | 联系方式 |
| workingHours | String | 作业时间 |
| remark | String | 备注 |

## 枚举说明

### 仓库类型枚举 (WarehouseTypeEnum)
- 1: 中心仓
- 2: 卫星仓
- 3: 虚拟仓

### 仓库属性枚举 (WarehouseAttributeEnum)
- 1: 自有
- 2: 第三方

### 启用状态枚举 (EnabledStatusEnum)
- 1: 启用
- 0: 禁用

## 业务规则

### 1. 数据验证
- 仓库类型和仓库属性不能为空
- 仓库名称不能为空且不能重复
- 省市区信息不能为空
- 详细地址不能为空
- 联系人和联系方式不能为空

### 2. 业务约束
- 仓库名称全局唯一
- 新增的云仓默认为启用状态
- 只能操作有效状态的云仓

### 3. 地址格式规范
- 省市区和详细地址之间加空格分隔
- 地址显示格式：省份+城市+区县 详细地址

### 4. 枚举使用规范
- 所有状态、类型字段都使用枚举，不写死字符串或数字常量
- 返回给前端的类型和属性都是中文描述

## 测试用例

### 1. 新增云仓
```bash
curl -X POST "http://localhost:8080/api/cloud-warehouse" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "warehouseType": 1,
    "warehouseAttribute": 1,
    "warehouseName": "北京中心仓",
    "provinceName": "北京市",
    "cityName": "北京市",
    "areaName": "朝阳区",
    "detailedAddress": "中关村大街123号",
    "contactPerson": "张经理",
    "contactPhone": "13800138000",
    "workingHours": "08:00-18:00"
  }'
```

### 2. 分页查询
```bash
curl -X POST "http://localhost:8080/api/cloud-warehouse/page" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "pageNum": 1,
    "pageSize": 10,
    "warehouseType": 1
  }'
```

### 3. 启用云仓
```bash
curl -X PUT "http://localhost:8080/api/cloud-warehouse/1/status?enabled=1" \
  -H "Authorization: Bearer {token}"
```

### 4. 查询云仓详情
```bash
curl -X GET "http://localhost:8080/api/cloud-warehouse/1" \
  -H "Authorization: Bearer {token}"
```
