package com.yi.controller.supplier.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 供应商分页响应
 */
@Data
@ApiModel(value = "SupplierPageResponse", description = "供应商分页响应")
public class SupplierPageResponse {

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "联系人")
    private String contactPerson;

    @ApiModelProperty(value = "联系方式")
    private String contactPhone;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private String createdTime;
}
