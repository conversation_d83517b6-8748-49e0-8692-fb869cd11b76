﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-30px;
  width:1444px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u860_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1301px;
  height:2px;
}
#u860 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:37px;
  width:1300px;
  height:1px;
  display:flex;
}
#u860 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u860_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u861_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:180px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u861 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:7px;
  width:180px;
  height:30px;
  display:flex;
}
#u861 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u861_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u862_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u862 {
  border-width:0px;
  position:absolute;
  left:1274px;
  top:18px;
  width:56px;
  height:19px;
  display:flex;
}
#u862 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u862_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u863_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1300px;
  height:100px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u863 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:43px;
  width:1300px;
  height:100px;
  display:flex;
}
#u863 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u863_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u864_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u864 {
  border-width:0px;
  position:absolute;
  left:290px;
  top:59px;
  width:56px;
  height:16px;
  display:flex;
}
#u864 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u864_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u865_input {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u865_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u865_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u865 {
  border-width:0px;
  position:absolute;
  left:356px;
  top:55px;
  width:120px;
  height:24px;
  display:flex;
  text-align:center;
}
#u865 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u865_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u865.disabled {
}
#u866_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u866 {
  border-width:0px;
  position:absolute;
  left:536px;
  top:59px;
  width:56px;
  height:16px;
  display:flex;
}
#u866 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u866_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u867_input {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u867_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u867_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u867 {
  border-width:0px;
  position:absolute;
  left:597px;
  top:55px;
  width:120px;
  height:24px;
  display:flex;
  text-align:center;
}
#u867 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u867_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u867.disabled {
}
#u868_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u868 {
  border-width:0px;
  position:absolute;
  left:59px;
  top:59px;
  width:56px;
  height:16px;
  display:flex;
}
#u868 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u868_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u869_input {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u869_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u869_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u869 {
  border-width:0px;
  position:absolute;
  left:120px;
  top:55px;
  width:120px;
  height:24px;
  display:flex;
}
#u869 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u869_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u869.disabled {
}
.u869_input_option {
}
#u870_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u870 {
  border-width:0px;
  position:absolute;
  left:1110px;
  top:93px;
  width:80px;
  height:30px;
  display:flex;
}
#u870 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u870_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u871_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u871 {
  border-width:0px;
  position:absolute;
  left:1200px;
  top:93px;
  width:80px;
  height:30px;
  display:flex;
}
#u871 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u871_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u872_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u872 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:173px;
  width:140px;
  height:30px;
  display:flex;
}
#u872 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u872_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u873_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u873 {
  border-width:0px;
  position:absolute;
  left:204px;
  top:173px;
  width:80px;
  height:30px;
  display:flex;
}
#u873 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u873_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u874 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:213px;
  width:1444px;
  height:330px;
}
#u875_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:46px;
  height:30px;
}
#u875 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:46px;
  height:30px;
  display:flex;
}
#u875 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u875_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u876_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:157px;
  height:30px;
}
#u876 {
  border-width:0px;
  position:absolute;
  left:46px;
  top:0px;
  width:157px;
  height:30px;
  display:flex;
}
#u876 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u876_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u877_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:30px;
}
#u877 {
  border-width:0px;
  position:absolute;
  left:203px;
  top:0px;
  width:144px;
  height:30px;
  display:flex;
}
#u877 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u877_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u878_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:128px;
  height:30px;
}
#u878 {
  border-width:0px;
  position:absolute;
  left:347px;
  top:0px;
  width:128px;
  height:30px;
  display:flex;
}
#u878 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u878_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u879_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u879 {
  border-width:0px;
  position:absolute;
  left:475px;
  top:0px;
  width:139px;
  height:30px;
  display:flex;
}
#u879 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u879_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u880_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:30px;
}
#u880 {
  border-width:0px;
  position:absolute;
  left:614px;
  top:0px;
  width:162px;
  height:30px;
  display:flex;
}
#u880 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u880_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u881_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:179px;
  height:30px;
}
#u881 {
  border-width:0px;
  position:absolute;
  left:776px;
  top:0px;
  width:179px;
  height:30px;
  display:flex;
}
#u881 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u881_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u882_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u882 {
  border-width:0px;
  position:absolute;
  left:955px;
  top:0px;
  width:117px;
  height:30px;
  display:flex;
}
#u882 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u882_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u883_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:30px;
}
#u883 {
  border-width:0px;
  position:absolute;
  left:1072px;
  top:0px;
  width:182px;
  height:30px;
  display:flex;
}
#u883 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u883_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u884_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:190px;
  height:30px;
}
#u884 {
  border-width:0px;
  position:absolute;
  left:1254px;
  top:0px;
  width:190px;
  height:30px;
  display:flex;
}
#u884 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u884_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u885_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:46px;
  height:30px;
}
#u885 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:30px;
  width:46px;
  height:30px;
  display:flex;
}
#u885 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u885_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u886_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:157px;
  height:30px;
}
#u886 {
  border-width:0px;
  position:absolute;
  left:46px;
  top:30px;
  width:157px;
  height:30px;
  display:flex;
}
#u886 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u886_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u887_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:30px;
}
#u887 {
  border-width:0px;
  position:absolute;
  left:203px;
  top:30px;
  width:144px;
  height:30px;
  display:flex;
}
#u887 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u887_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u888_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:128px;
  height:30px;
}
#u888 {
  border-width:0px;
  position:absolute;
  left:347px;
  top:30px;
  width:128px;
  height:30px;
  display:flex;
}
#u888 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u888_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u889_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u889 {
  border-width:0px;
  position:absolute;
  left:475px;
  top:30px;
  width:139px;
  height:30px;
  display:flex;
}
#u889 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u889_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u890_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:30px;
}
#u890 {
  border-width:0px;
  position:absolute;
  left:614px;
  top:30px;
  width:162px;
  height:30px;
  display:flex;
}
#u890 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u890_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u891_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:179px;
  height:30px;
}
#u891 {
  border-width:0px;
  position:absolute;
  left:776px;
  top:30px;
  width:179px;
  height:30px;
  display:flex;
}
#u891 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u891_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u892_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u892 {
  border-width:0px;
  position:absolute;
  left:955px;
  top:30px;
  width:117px;
  height:30px;
  display:flex;
}
#u892 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u892_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u893_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:30px;
}
#u893 {
  border-width:0px;
  position:absolute;
  left:1072px;
  top:30px;
  width:182px;
  height:30px;
  display:flex;
}
#u893 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u893_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u894_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:190px;
  height:30px;
}
#u894 {
  border-width:0px;
  position:absolute;
  left:1254px;
  top:30px;
  width:190px;
  height:30px;
  display:flex;
}
#u894 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u894_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u895_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:46px;
  height:30px;
}
#u895 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:60px;
  width:46px;
  height:30px;
  display:flex;
}
#u895 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u895_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u896_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:157px;
  height:30px;
}
#u896 {
  border-width:0px;
  position:absolute;
  left:46px;
  top:60px;
  width:157px;
  height:30px;
  display:flex;
}
#u896 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u896_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u897_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:30px;
}
#u897 {
  border-width:0px;
  position:absolute;
  left:203px;
  top:60px;
  width:144px;
  height:30px;
  display:flex;
}
#u897 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u897_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u898_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:128px;
  height:30px;
}
#u898 {
  border-width:0px;
  position:absolute;
  left:347px;
  top:60px;
  width:128px;
  height:30px;
  display:flex;
}
#u898 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u898_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u899_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u899 {
  border-width:0px;
  position:absolute;
  left:475px;
  top:60px;
  width:139px;
  height:30px;
  display:flex;
}
#u899 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u899_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u900_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:30px;
}
#u900 {
  border-width:0px;
  position:absolute;
  left:614px;
  top:60px;
  width:162px;
  height:30px;
  display:flex;
}
#u900 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u900_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u901_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:179px;
  height:30px;
}
#u901 {
  border-width:0px;
  position:absolute;
  left:776px;
  top:60px;
  width:179px;
  height:30px;
  display:flex;
}
#u901 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u901_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u902_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u902 {
  border-width:0px;
  position:absolute;
  left:955px;
  top:60px;
  width:117px;
  height:30px;
  display:flex;
}
#u902 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u902_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u903_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:30px;
}
#u903 {
  border-width:0px;
  position:absolute;
  left:1072px;
  top:60px;
  width:182px;
  height:30px;
  display:flex;
}
#u903 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u903_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u904_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:190px;
  height:30px;
}
#u904 {
  border-width:0px;
  position:absolute;
  left:1254px;
  top:60px;
  width:190px;
  height:30px;
  display:flex;
}
#u904 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u904_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u905_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:46px;
  height:30px;
}
#u905 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:90px;
  width:46px;
  height:30px;
  display:flex;
}
#u905 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u905_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u906_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:157px;
  height:30px;
}
#u906 {
  border-width:0px;
  position:absolute;
  left:46px;
  top:90px;
  width:157px;
  height:30px;
  display:flex;
}
#u906 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u906_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u907_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:30px;
}
#u907 {
  border-width:0px;
  position:absolute;
  left:203px;
  top:90px;
  width:144px;
  height:30px;
  display:flex;
}
#u907 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u907_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u908_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:128px;
  height:30px;
}
#u908 {
  border-width:0px;
  position:absolute;
  left:347px;
  top:90px;
  width:128px;
  height:30px;
  display:flex;
}
#u908 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u908_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u909_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u909 {
  border-width:0px;
  position:absolute;
  left:475px;
  top:90px;
  width:139px;
  height:30px;
  display:flex;
}
#u909 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u909_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u910_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:30px;
}
#u910 {
  border-width:0px;
  position:absolute;
  left:614px;
  top:90px;
  width:162px;
  height:30px;
  display:flex;
}
#u910 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u910_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u911_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:179px;
  height:30px;
}
#u911 {
  border-width:0px;
  position:absolute;
  left:776px;
  top:90px;
  width:179px;
  height:30px;
  display:flex;
}
#u911 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u911_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u912_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u912 {
  border-width:0px;
  position:absolute;
  left:955px;
  top:90px;
  width:117px;
  height:30px;
  display:flex;
}
#u912 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u912_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u913_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:30px;
}
#u913 {
  border-width:0px;
  position:absolute;
  left:1072px;
  top:90px;
  width:182px;
  height:30px;
  display:flex;
}
#u913 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u913_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u914_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:190px;
  height:30px;
}
#u914 {
  border-width:0px;
  position:absolute;
  left:1254px;
  top:90px;
  width:190px;
  height:30px;
  display:flex;
}
#u914 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u914_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u915_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:46px;
  height:30px;
}
#u915 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:120px;
  width:46px;
  height:30px;
  display:flex;
}
#u915 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u915_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u916_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:157px;
  height:30px;
}
#u916 {
  border-width:0px;
  position:absolute;
  left:46px;
  top:120px;
  width:157px;
  height:30px;
  display:flex;
}
#u916 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u916_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u917_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:30px;
}
#u917 {
  border-width:0px;
  position:absolute;
  left:203px;
  top:120px;
  width:144px;
  height:30px;
  display:flex;
}
#u917 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u917_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u918_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:128px;
  height:30px;
}
#u918 {
  border-width:0px;
  position:absolute;
  left:347px;
  top:120px;
  width:128px;
  height:30px;
  display:flex;
}
#u918 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u918_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u919_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u919 {
  border-width:0px;
  position:absolute;
  left:475px;
  top:120px;
  width:139px;
  height:30px;
  display:flex;
}
#u919 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u919_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u920_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:30px;
}
#u920 {
  border-width:0px;
  position:absolute;
  left:614px;
  top:120px;
  width:162px;
  height:30px;
  display:flex;
}
#u920 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u920_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u921_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:179px;
  height:30px;
}
#u921 {
  border-width:0px;
  position:absolute;
  left:776px;
  top:120px;
  width:179px;
  height:30px;
  display:flex;
}
#u921 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u921_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u922_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u922 {
  border-width:0px;
  position:absolute;
  left:955px;
  top:120px;
  width:117px;
  height:30px;
  display:flex;
}
#u922 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u922_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u923_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:30px;
}
#u923 {
  border-width:0px;
  position:absolute;
  left:1072px;
  top:120px;
  width:182px;
  height:30px;
  display:flex;
}
#u923 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u923_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u924_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:190px;
  height:30px;
}
#u924 {
  border-width:0px;
  position:absolute;
  left:1254px;
  top:120px;
  width:190px;
  height:30px;
  display:flex;
}
#u924 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u924_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u925_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:46px;
  height:30px;
}
#u925 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:150px;
  width:46px;
  height:30px;
  display:flex;
}
#u925 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u925_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u926_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:157px;
  height:30px;
}
#u926 {
  border-width:0px;
  position:absolute;
  left:46px;
  top:150px;
  width:157px;
  height:30px;
  display:flex;
}
#u926 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u926_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u927_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:30px;
}
#u927 {
  border-width:0px;
  position:absolute;
  left:203px;
  top:150px;
  width:144px;
  height:30px;
  display:flex;
}
#u927 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u927_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u928_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:128px;
  height:30px;
}
#u928 {
  border-width:0px;
  position:absolute;
  left:347px;
  top:150px;
  width:128px;
  height:30px;
  display:flex;
}
#u928 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u928_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u929_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u929 {
  border-width:0px;
  position:absolute;
  left:475px;
  top:150px;
  width:139px;
  height:30px;
  display:flex;
}
#u929 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u929_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u930_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:30px;
}
#u930 {
  border-width:0px;
  position:absolute;
  left:614px;
  top:150px;
  width:162px;
  height:30px;
  display:flex;
}
#u930 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u930_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u931_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:179px;
  height:30px;
}
#u931 {
  border-width:0px;
  position:absolute;
  left:776px;
  top:150px;
  width:179px;
  height:30px;
  display:flex;
}
#u931 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u931_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u932_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u932 {
  border-width:0px;
  position:absolute;
  left:955px;
  top:150px;
  width:117px;
  height:30px;
  display:flex;
}
#u932 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u932_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u933_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:30px;
}
#u933 {
  border-width:0px;
  position:absolute;
  left:1072px;
  top:150px;
  width:182px;
  height:30px;
  display:flex;
}
#u933 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u933_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u934_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:190px;
  height:30px;
}
#u934 {
  border-width:0px;
  position:absolute;
  left:1254px;
  top:150px;
  width:190px;
  height:30px;
  display:flex;
}
#u934 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u934_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u935_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:46px;
  height:30px;
}
#u935 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:180px;
  width:46px;
  height:30px;
  display:flex;
}
#u935 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u935_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u936_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:157px;
  height:30px;
}
#u936 {
  border-width:0px;
  position:absolute;
  left:46px;
  top:180px;
  width:157px;
  height:30px;
  display:flex;
}
#u936 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u936_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u937_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:30px;
}
#u937 {
  border-width:0px;
  position:absolute;
  left:203px;
  top:180px;
  width:144px;
  height:30px;
  display:flex;
}
#u937 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u937_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u938_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:128px;
  height:30px;
}
#u938 {
  border-width:0px;
  position:absolute;
  left:347px;
  top:180px;
  width:128px;
  height:30px;
  display:flex;
}
#u938 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u938_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u939_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u939 {
  border-width:0px;
  position:absolute;
  left:475px;
  top:180px;
  width:139px;
  height:30px;
  display:flex;
}
#u939 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u939_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u940_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:30px;
}
#u940 {
  border-width:0px;
  position:absolute;
  left:614px;
  top:180px;
  width:162px;
  height:30px;
  display:flex;
}
#u940 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u940_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u941_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:179px;
  height:30px;
}
#u941 {
  border-width:0px;
  position:absolute;
  left:776px;
  top:180px;
  width:179px;
  height:30px;
  display:flex;
}
#u941 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u941_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u942_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u942 {
  border-width:0px;
  position:absolute;
  left:955px;
  top:180px;
  width:117px;
  height:30px;
  display:flex;
}
#u942 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u942_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u943_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:30px;
}
#u943 {
  border-width:0px;
  position:absolute;
  left:1072px;
  top:180px;
  width:182px;
  height:30px;
  display:flex;
}
#u943 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u943_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u944_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:190px;
  height:30px;
}
#u944 {
  border-width:0px;
  position:absolute;
  left:1254px;
  top:180px;
  width:190px;
  height:30px;
  display:flex;
}
#u944 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u944_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u945_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:46px;
  height:30px;
}
#u945 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:210px;
  width:46px;
  height:30px;
  display:flex;
}
#u945 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u945_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u946_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:157px;
  height:30px;
}
#u946 {
  border-width:0px;
  position:absolute;
  left:46px;
  top:210px;
  width:157px;
  height:30px;
  display:flex;
}
#u946 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u946_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u947_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:30px;
}
#u947 {
  border-width:0px;
  position:absolute;
  left:203px;
  top:210px;
  width:144px;
  height:30px;
  display:flex;
}
#u947 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u947_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u948_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:128px;
  height:30px;
}
#u948 {
  border-width:0px;
  position:absolute;
  left:347px;
  top:210px;
  width:128px;
  height:30px;
  display:flex;
}
#u948 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u948_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u949_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u949 {
  border-width:0px;
  position:absolute;
  left:475px;
  top:210px;
  width:139px;
  height:30px;
  display:flex;
}
#u949 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u949_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u950_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:30px;
}
#u950 {
  border-width:0px;
  position:absolute;
  left:614px;
  top:210px;
  width:162px;
  height:30px;
  display:flex;
}
#u950 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u950_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u951_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:179px;
  height:30px;
}
#u951 {
  border-width:0px;
  position:absolute;
  left:776px;
  top:210px;
  width:179px;
  height:30px;
  display:flex;
}
#u951 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u951_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u952_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u952 {
  border-width:0px;
  position:absolute;
  left:955px;
  top:210px;
  width:117px;
  height:30px;
  display:flex;
}
#u952 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u952_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u953_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:30px;
}
#u953 {
  border-width:0px;
  position:absolute;
  left:1072px;
  top:210px;
  width:182px;
  height:30px;
  display:flex;
}
#u953 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u953_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u954_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:190px;
  height:30px;
}
#u954 {
  border-width:0px;
  position:absolute;
  left:1254px;
  top:210px;
  width:190px;
  height:30px;
  display:flex;
}
#u954 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u954_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u955_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:46px;
  height:30px;
}
#u955 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:240px;
  width:46px;
  height:30px;
  display:flex;
}
#u955 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u955_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u956_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:157px;
  height:30px;
}
#u956 {
  border-width:0px;
  position:absolute;
  left:46px;
  top:240px;
  width:157px;
  height:30px;
  display:flex;
}
#u956 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u956_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u957_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:30px;
}
#u957 {
  border-width:0px;
  position:absolute;
  left:203px;
  top:240px;
  width:144px;
  height:30px;
  display:flex;
}
#u957 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u957_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u958_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:128px;
  height:30px;
}
#u958 {
  border-width:0px;
  position:absolute;
  left:347px;
  top:240px;
  width:128px;
  height:30px;
  display:flex;
}
#u958 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u958_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u959_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u959 {
  border-width:0px;
  position:absolute;
  left:475px;
  top:240px;
  width:139px;
  height:30px;
  display:flex;
}
#u959 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u959_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u960_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:30px;
}
#u960 {
  border-width:0px;
  position:absolute;
  left:614px;
  top:240px;
  width:162px;
  height:30px;
  display:flex;
}
#u960 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u960_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u961_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:179px;
  height:30px;
}
#u961 {
  border-width:0px;
  position:absolute;
  left:776px;
  top:240px;
  width:179px;
  height:30px;
  display:flex;
}
#u961 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u961_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u962_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u962 {
  border-width:0px;
  position:absolute;
  left:955px;
  top:240px;
  width:117px;
  height:30px;
  display:flex;
}
#u962 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u962_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u963_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:30px;
}
#u963 {
  border-width:0px;
  position:absolute;
  left:1072px;
  top:240px;
  width:182px;
  height:30px;
  display:flex;
}
#u963 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u963_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u964_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:190px;
  height:30px;
}
#u964 {
  border-width:0px;
  position:absolute;
  left:1254px;
  top:240px;
  width:190px;
  height:30px;
  display:flex;
}
#u964 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u964_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u965_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:46px;
  height:30px;
}
#u965 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:270px;
  width:46px;
  height:30px;
  display:flex;
}
#u965 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u965_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u966_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:157px;
  height:30px;
}
#u966 {
  border-width:0px;
  position:absolute;
  left:46px;
  top:270px;
  width:157px;
  height:30px;
  display:flex;
}
#u966 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u966_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u967_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:30px;
}
#u967 {
  border-width:0px;
  position:absolute;
  left:203px;
  top:270px;
  width:144px;
  height:30px;
  display:flex;
}
#u967 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u967_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u968_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:128px;
  height:30px;
}
#u968 {
  border-width:0px;
  position:absolute;
  left:347px;
  top:270px;
  width:128px;
  height:30px;
  display:flex;
}
#u968 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u968_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u969_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u969 {
  border-width:0px;
  position:absolute;
  left:475px;
  top:270px;
  width:139px;
  height:30px;
  display:flex;
}
#u969 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u969_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u970_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:30px;
}
#u970 {
  border-width:0px;
  position:absolute;
  left:614px;
  top:270px;
  width:162px;
  height:30px;
  display:flex;
}
#u970 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u970_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u971_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:179px;
  height:30px;
}
#u971 {
  border-width:0px;
  position:absolute;
  left:776px;
  top:270px;
  width:179px;
  height:30px;
  display:flex;
}
#u971 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u971_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u972_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u972 {
  border-width:0px;
  position:absolute;
  left:955px;
  top:270px;
  width:117px;
  height:30px;
  display:flex;
}
#u972 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u972_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u973_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:30px;
}
#u973 {
  border-width:0px;
  position:absolute;
  left:1072px;
  top:270px;
  width:182px;
  height:30px;
  display:flex;
}
#u973 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u973_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u974_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:190px;
  height:30px;
}
#u974 {
  border-width:0px;
  position:absolute;
  left:1254px;
  top:270px;
  width:190px;
  height:30px;
  display:flex;
}
#u974 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u974_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u975_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:46px;
  height:30px;
}
#u975 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:300px;
  width:46px;
  height:30px;
  display:flex;
}
#u975 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u975_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u976_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:157px;
  height:30px;
}
#u976 {
  border-width:0px;
  position:absolute;
  left:46px;
  top:300px;
  width:157px;
  height:30px;
  display:flex;
}
#u976 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u976_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u977_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:30px;
}
#u977 {
  border-width:0px;
  position:absolute;
  left:203px;
  top:300px;
  width:144px;
  height:30px;
  display:flex;
}
#u977 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u977_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u978_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:128px;
  height:30px;
}
#u978 {
  border-width:0px;
  position:absolute;
  left:347px;
  top:300px;
  width:128px;
  height:30px;
  display:flex;
}
#u978 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u978_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u979_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u979 {
  border-width:0px;
  position:absolute;
  left:475px;
  top:300px;
  width:139px;
  height:30px;
  display:flex;
}
#u979 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u979_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u980_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:30px;
}
#u980 {
  border-width:0px;
  position:absolute;
  left:614px;
  top:300px;
  width:162px;
  height:30px;
  display:flex;
}
#u980 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u980_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u981_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:179px;
  height:30px;
}
#u981 {
  border-width:0px;
  position:absolute;
  left:776px;
  top:300px;
  width:179px;
  height:30px;
  display:flex;
}
#u981 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u981_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u982_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u982 {
  border-width:0px;
  position:absolute;
  left:955px;
  top:300px;
  width:117px;
  height:30px;
  display:flex;
}
#u982 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u982_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u983_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:30px;
}
#u983 {
  border-width:0px;
  position:absolute;
  left:1072px;
  top:300px;
  width:182px;
  height:30px;
  display:flex;
}
#u983 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u983_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u984_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:190px;
  height:30px;
}
#u984 {
  border-width:0px;
  position:absolute;
  left:1254px;
  top:300px;
  width:190px;
  height:30px;
  display:flex;
}
#u984 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u984_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u985_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u985 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:569px;
  width:57px;
  height:16px;
  display:flex;
}
#u985 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u985_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u986_input {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:22px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u986_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:22px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u986_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u986 {
  border-width:0px;
  position:absolute;
  left:97px;
  top:563px;
  width:80px;
  height:22px;
  display:flex;
}
#u986 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u986_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:22px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u986.disabled {
}
.u986_input_option {
}
#u987_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u987 {
  border-width:0px;
  position:absolute;
  left:187px;
  top:569px;
  width:168px;
  height:16px;
  display:flex;
}
#u987 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u987_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u988_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u988 {
  border-width:0px;
  position:absolute;
  left:365px;
  top:569px;
  width:28px;
  height:16px;
  display:flex;
}
#u988 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u988_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u989_input {
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:22px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u989_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:22px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u989_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u989 {
  border-width:0px;
  position:absolute;
  left:398px;
  top:563px;
  width:30px;
  height:22px;
  display:flex;
  text-align:center;
}
#u989 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u989_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:22px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u989.disabled {
}
#u990_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u990 {
  border-width:0px;
  position:absolute;
  left:433px;
  top:569px;
  width:14px;
  height:16px;
  display:flex;
}
#u990 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u990_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u991_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u991 {
  border-width:0px;
  position:absolute;
  left:1388px;
  top:252px;
  width:28px;
  height:16px;
  display:flex;
}
#u991 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u991_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u992_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u992 {
  border-width:0px;
  position:absolute;
  left:1340px;
  top:252px;
  width:28px;
  height:16px;
  display:flex;
}
#u992 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u992_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u993_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1300px;
  height:185px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  font-size:14px;
  line-height:20px;
}
#u993 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:638px;
  width:1300px;
  height:185px;
  display:flex;
  font-size:14px;
  line-height:20px;
}
#u993 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u993_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u994_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:152px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#000000;
  line-height:19px;
}
#u994 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:646px;
  width:501px;
  height:152px;
  display:flex;
  font-size:15px;
  color:#000000;
  line-height:19px;
}
#u994 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u994_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
