package com.yi.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yi.controller.shippingorder.model.*;
import com.yi.enums.ShippingOrderActionEnum;
import com.yi.mapper.vo.ShippingOrderPageVO;
import com.yi.entity.TShippingOrder;
import com.yi.entity.TContractSku;
import com.yi.entity.TCustomerCompany;
import com.yi.entity.TCustomerWarehouse;
import com.yi.entity.TShippingDemand;
import com.yi.entity.TWarehouseSku;
import com.yi.enums.SkuFirstCategoryEnum;
import com.yi.enums.ShippingOrderStatusEnum;
import com.yi.enums.ShippingDemandStatusEnum;
import com.yi.mapper.TShippingOrderMapper;
import com.yi.service.TContractSkuService;
import com.yi.service.TWarehouseSkuService;
import com.yi.service.TGeneralFileService;
import com.yi.utils.ExcelUtils;
import com.yi.utils.FormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

/**
 * 发运订单表 服务类
 */
@Slf4j
@Service
public class TShippingOrderService extends ServiceImpl<TShippingOrderMapper, TShippingOrder> {

    @Autowired
    private TContractSkuService contractSkuService;

    @Autowired
    private TWarehouseSkuService warehouseSkuService;

    @Autowired
    private TContractService contractService;

    @Autowired
    private TGeneralFileService generalFileService;

    @Autowired
    private TCustomerWarehouseService customerWarehouseService;

    @Autowired
    private TCustomerCompanyService customerCompanyService;

    @Autowired
    private TShippingDemandService shippingDemandService;

    @Autowired
    private TShippingOrderLogService shippingOrderLogService;

    /**
     * 分页查询发运订单列表
     *
     * @param request 查询条件
     * @return 分页结果
     */
    public IPage<ShippingOrderPageResponse> getShippingOrderPageResponse(ShippingOrderQueryRequest request) {
        // 转换分页参数
        Integer current = FormatUtils.safeToInteger(request.getCurrent(), 1);
        Integer size = FormatUtils.safeToInteger(request.getSize(), 10);
        Page<ShippingOrderPageVO> page = new Page<>(current, size);

        IPage<ShippingOrderPageVO> pageResult = this.baseMapper.selectShippingOrderPage(page, request);

        // 转换为响应对象
        IPage<ShippingOrderPageResponse> responsePage = new Page<>();
        responsePage.setCurrent(pageResult.getCurrent());
        responsePage.setSize(pageResult.getSize());
        responsePage.setTotal(pageResult.getTotal());
        responsePage.setPages(pageResult.getPages());

        List<ShippingOrderPageResponse> responseList = pageResult.getRecords().stream()
                .map(this::convertToPageResponse)
                .collect(Collectors.toList());
        responsePage.setRecords(responseList);

        return responsePage;
    }

    /**
     * 根据ID查询发运订单详情
     *
     * @param id 订单ID
     * @return 订单详情
     */
    public ShippingOrderDetailResponse getShippingOrderDetailById(Long id) {
        TShippingOrder order = this.getById(id);
        if (order == null || order.getValid() == 0) {
            return null;
        }

        return convertToDetailResponse(order);
    }

    /**
     * 新增发运订单
     *
     * @param orderRequest 订单请求
     * @return 是否成功
     */
    public boolean addShippingOrder(ShippingOrderRequest orderRequest) {
        TShippingOrder order = new TShippingOrder();
        // 生成订单号
        order.setOrderNo(generateOrderNo());
        order.setContractCode(FormatUtils.safeString(orderRequest.getContractCode()));
        order.setCustomerCompanyId(FormatUtils.safeToLong(orderRequest.getCustomerCompanyId()));
        order.setWarehouseId(FormatUtils.safeToLong(orderRequest.getWarehouseId()));
        order.setFirstCategory(FormatUtils.safeToInteger(orderRequest.getFirstCategory()));
        order.setSecondCategory(FormatUtils.safeString(orderRequest.getSecondCategory()));
        order.setCount(FormatUtils.safeToInteger(orderRequest.getCount()));
        order.setShippedQuantity(0); // 新增时发货数量默认为0
        order.setReceivedQuantity(0); // 新增时签收数量默认为0
        order.setDemandTime(FormatUtils.parseDate(orderRequest.getDemandTime()));
        order.setStatus(ShippingOrderStatusEnum.PENDING.getCode()); // 默认待发货状态
        order.setRemark(FormatUtils.safeString(orderRequest.getRemark()));
        order.setCreatedBy(getCurrentUser());
        order.setLastModifiedBy(getCurrentUser());
        order.setValid(1);
        boolean success = this.save(order);
        if (success) {
            // 保存附件到通用文件表
            if (orderRequest.getAttachmentUrls() != null && !orderRequest.getAttachmentUrls().isEmpty()) {
                Long orderId = order.getId();
                generalFileService.saveShippingOrderAttachments(orderId, orderRequest.getAttachmentUrls());
            }

            // 同步创建发货需求单
            createShippingDemand(order);
        }
        return success;
    }

    /**
     * 更新发运订单
     *
     * @param orderRequest 订单请求
     * @return 是否成功
     */
    public boolean updateShippingOrder(ShippingOrderRequest orderRequest) {
        Long orderId = FormatUtils.safeToLong(orderRequest.getId());
        if (orderId == null) {
            throw new RuntimeException("订单ID不能为空");
        }

        // 获取现有订单信息
        TShippingOrder existingOrder = this.getById(orderId);
        if (existingOrder == null) {
            throw new RuntimeException("发运订单不存在");
        }

        // 校验订单状态：只有待发货状态才能编辑
        if (!ShippingOrderStatusEnum.PENDING.getCode().equals(existingOrder.getStatus())) {
            throw new RuntimeException("只有待发货状态的订单才能编辑");
        }

        // 创建更新对象，只更新允许编辑的字段（与新增时相同的字段）
        TShippingOrder order = new TShippingOrder();
        order.setId(orderId);

        // 允许编辑的字段（与新增时相同）
        order.setContractCode(FormatUtils.safeString(orderRequest.getContractCode()));
        order.setCustomerCompanyId(FormatUtils.safeToLong(orderRequest.getCustomerCompanyId()));
        order.setWarehouseId(FormatUtils.safeToLong(orderRequest.getWarehouseId()));
        order.setFirstCategory(FormatUtils.safeToInteger(orderRequest.getFirstCategory()));
        order.setSecondCategory(FormatUtils.safeString(orderRequest.getSecondCategory()));
        order.setCount(FormatUtils.safeToInteger(orderRequest.getCount()));
        order.setDemandTime(FormatUtils.parseDate(orderRequest.getDemandTime()));
        order.setRemark(FormatUtils.safeString(orderRequest.getRemark()));

        // 设置修改人和修改时间
        order.setLastModifiedBy(getCurrentUser());
        order.setLastModifiedTime(LocalDateTime.now());

        boolean success = this.updateById(order);

        if (success) {
            // 处理附件更新（saveShippingOrderAttachments方法内部会先删除原有附件）
            if (orderRequest.getAttachmentUrls() != null) {
                generalFileService.saveShippingOrderAttachments(orderId, orderRequest.getAttachmentUrls());
            }

            // 同步更新发货需求单
            TShippingOrder updatedOrder = this.getById(orderId);
            if (updatedOrder != null) {
                shippingDemandService.updateShippingDemandByOrder(updatedOrder);
            }
        }

        return success;
    }


    /**
     * 取消发运订单
     *
     * @param id 订单ID
     * @param reason 取消原因
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelShippingOrder(Long id, String reason) {
        // 获取现有订单信息
        TShippingOrder existingOrder = this.getById(id);
        if (existingOrder == null) {
            throw new RuntimeException("发运订单不存在");
        }

        // 校验订单状态：只有待发货和发货中状态才能取消
        if (!ShippingOrderStatusEnum.PENDING.getCode().equals(existingOrder.getStatus())
            && !ShippingOrderStatusEnum.SHIPPING.getCode().equals(existingOrder.getStatus())) {
            throw new RuntimeException("只有待发货和发货中状态的订单才能取消");
        }

        // 更新订单状态和取消原因
        TShippingOrder order = new TShippingOrder();
        order.setId(id);
        order.setStatus(ShippingOrderStatusEnum.CANCELLED.getCode());
        order.setCancelReason(FormatUtils.safeString(reason));
        order.setLastModifiedBy(getCurrentUser());
        order.setLastModifiedTime(LocalDateTime.now());

        boolean success = this.updateById(order);

        if (success) {
            // 记录操作日志
            shippingOrderLogService.recordLog(id, ShippingOrderActionEnum.CANCEL, getCurrentUser(), reason);

            // 同步取消发货需求单
            shippingDemandService.cancelShippingDemandByOrderNo(existingOrder.getOrderNo(), reason);
        }

        return success;
    }


    /**
     * 导出发运订单列表
     *
     * @param request 查询条件
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    public void exportShippingOrderList(ShippingOrderQueryRequest request, HttpServletResponse response) throws IOException {
        // 使用统一的SQL联查直接获取所有符合条件的数据（不分页模式）
        List<ShippingOrderPageVO> dataList = this.baseMapper.selectShippingOrderList(request);

        // 转换为导出VO
        List<ShippingOrderExportVO> exportList = dataList.stream()
                .map(this::convertToExportVO)
                .collect(Collectors.toList());

        // 使用EasyExcel导出（文件名已包含时间戳）
        ExcelUtils.exportExcelWithTimestamp(response, "发运订单列表", "发运订单列表",
                ShippingOrderExportVO.class, exportList);
    }

    /**
     * 根据合同编号和仓库ID获取SKU类型下拉框数据
     *
     * @param contractCode 合同编号
     * @param warehouseId 仓库ID
     * @return SKU类型列表
     */
    public List<ShippingOrderSkuTypeResponse> getSkuTypesByContractAndWarehouse(String contractCode, Long warehouseId) {
        if (contractCode == null || contractCode.trim().isEmpty() || warehouseId == null) {
            return List.of();
        }

        // 1. 根据合同编号查询合同ID
        Long contractId = getContractIdByCode(contractCode);
        if (contractId == null) {
            return List.of();
        }

        // 2. 根据合同ID查询合同支持的一级类目
        List<TContractSku> contractSkuList = contractSkuService.getByContractId(contractId);
        if (contractSkuList.isEmpty()) {
            return List.of();
        }

        // 3. 提取合同支持的一级类目列表
        List<Integer> contractFirstCategories = contractSkuList.stream()
                .map(TContractSku::getFirstCategory)
                .distinct()
                .collect(Collectors.toList());

        // 4. 根据仓库ID和一级类目查询仓库支持的SKU类型
        List<TWarehouseSku> warehouseSkuList = getWarehouseSkuByWarehouseIdAndFirstCategories(warehouseId, contractFirstCategories);

        // 5. 转换为响应对象
        return warehouseSkuList.stream()
                .map(this::convertToSkuTypeResponse)
                .collect(Collectors.toList());
    }

    /**
     * 校验订单号是否唯一
     *
     * @param orderNo 订单号
     * @param excludeId 排除的订单ID（编辑时使用）
     */
    private void validateOrderNoUnique(String orderNo, Long excludeId) {
        if (!StringUtils.hasText(orderNo)) {
            return;
        }

        LambdaQueryWrapper<TShippingOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TShippingOrder::getOrderNo, orderNo.trim());
        wrapper.eq(TShippingOrder::getValid, 1);
        if (excludeId != null) {
            wrapper.ne(TShippingOrder::getId, excludeId);
        }

        int count = this.count(wrapper);
        if (count > 0) {
            throw new RuntimeException("订单号已存在");
        }
    }

    /**
     * 转换为分页响应对象
     *
     * @param orderVO 订单VO
     * @return 分页响应对象
     */
    private ShippingOrderPageResponse convertToPageResponse(ShippingOrderPageVO orderVO) {
        ShippingOrderPageResponse response = new ShippingOrderPageResponse();

        response.setId(FormatUtils.safeToString(orderVO.getId()));
        response.setOrderNo(FormatUtils.safeString(orderVO.getOrderNo()));
        response.setContractCode(FormatUtils.safeString(orderVO.getContractCode()));
        response.setCustomerCompanyName(FormatUtils.safeString(orderVO.getCustomerCompanyName()));
        response.setWarehouseName(FormatUtils.safeString(orderVO.getWarehouseName()));
        response.setWarehouseAddress(FormatUtils.safeString(orderVO.getWarehouseAddress()));
        response.setReceiverName(FormatUtils.combineReceiverInfo(orderVO.getReceiverName(), orderVO.getReceiverPhone()));
        response.setProduct(getProductName(orderVO.getFirstCategory(), orderVO.getSecondCategory()));
        response.setCount(FormatUtils.safeToString(orderVO.getCount()));
        response.setShippedQuantity(FormatUtils.safeToString(orderVO.getShippedQuantity()));
        response.setReceivedQuantity(FormatUtils.safeToString(orderVO.getReceivedQuantity()));
        response.setStatus(getStatusName(orderVO.getStatus()));
        response.setCreatedBy(FormatUtils.safeString(orderVO.getCreatedBy()));
        response.setCreatedTime(FormatUtils.formatDateTime(orderVO.getCreatedTime()));

        return response;
    }

    /**
     * 转换为详情响应对象
     *
     * @param order 订单实体
     * @return 详情响应对象
     */
    private ShippingOrderDetailResponse convertToDetailResponse(TShippingOrder order) {
        ShippingOrderDetailResponse response = new ShippingOrderDetailResponse();

        response.setId(FormatUtils.safeToString(order.getId()));
        response.setOrderNo(FormatUtils.safeString(order.getOrderNo()));
        response.setCustomerCompanyId(FormatUtils.safeToString(order.getCustomerCompanyId()));
        response.setContractCode(FormatUtils.safeString(order.getContractCode()));
        response.setWarehouseId(FormatUtils.safeToString(order.getWarehouseId()));

        // 通过关联查询获取收货人和地址信息
        populateWarehouseInfo(response, order.getWarehouseId());

        // 产品信息
        response.setFirstCategory(FormatUtils.safeToString(order.getFirstCategory()));
        response.setFirstCategoryName(SkuFirstCategoryEnum.getDescriptionByCode(order.getFirstCategory()));
        response.setSecondCategory(FormatUtils.safeString(order.getSecondCategory()));
        response.setProduct(getProductName(order.getFirstCategory(), order.getSecondCategory()));
        response.setCount(FormatUtils.safeToString(order.getCount()));
        response.setShippedQuantity(FormatUtils.safeToString(order.getShippedQuantity()));
        response.setReceivedQuantity(FormatUtils.safeToString(order.getReceivedQuantity()));
        response.setDemandTime(FormatUtils.formatDate(order.getDemandTime()));

        // 状态信息
        response.setStatus(FormatUtils.safeToString(order.getStatus()));
        response.setStatusName(getStatusName(order.getStatus()));
        response.setCancelReason(FormatUtils.safeString(order.getCancelReason()));
        response.setRemark(FormatUtils.safeString(order.getRemark()));

        // 创建信息
        response.setCreatedBy(FormatUtils.safeString(order.getCreatedBy()));
        response.setCreatedTime(FormatUtils.formatDateTime(order.getCreatedTime()));
        response.setLastModifiedBy(FormatUtils.safeString(order.getLastModifiedBy()));
        response.setLastModifiedTime(FormatUtils.formatDateTime(order.getLastModifiedTime()));

        // 查询关联信息
        populateRelatedInfo(response, order);

        // 查询附件信息
        response.setAttachmentUrls(getOrderAttachments(order.getId()));

        return response;
    }

    /**
     * 填充仓库信息（收货人和地址）
     */
    private void populateWarehouseInfo(ShippingOrderDetailResponse response, Long warehouseId) {
        if (warehouseId == null) {
            return;
        }

        try {
            TCustomerWarehouse warehouse = customerWarehouseService.getById(warehouseId);
            if (warehouse != null) {
                // 收货人信息
                response.setReceiverName(FormatUtils.safeString(warehouse.getContactPerson()));
                response.setReceiverPhone(FormatUtils.safeString(warehouse.getMobilePhone()));

                // 地址信息
                response.setProvince(FormatUtils.safeString(warehouse.getProvinceName()));
                response.setCity(FormatUtils.safeString(warehouse.getCityName()));
                response.setDistrict(FormatUtils.safeString(warehouse.getAreaName()));
                response.setDetailAddress(FormatUtils.safeString(warehouse.getDetailedAddress()));
                response.setFullAddress(buildFullAddress(warehouse));

                // 仓库名称
                response.setWarehouseName(FormatUtils.safeString(warehouse.getWarehouseName()));
            }
        } catch (Exception e) {
            log.warn("获取仓库信息失败，仓库ID: " + warehouseId + ", 错误: " + e.getMessage());
        }
    }

    /**
     * 构建完整地址
     */
    private String buildFullAddress(TCustomerWarehouse warehouse) {
        StringBuilder fullAddress = new StringBuilder();

        if (warehouse.getProvinceName() != null && !warehouse.getProvinceName().trim().isEmpty()) {
            fullAddress.append(warehouse.getProvinceName());
        }
        if (warehouse.getCityName() != null && !warehouse.getCityName().trim().isEmpty()) {
            fullAddress.append(warehouse.getCityName());
        }
        if (warehouse.getAreaName() != null && !warehouse.getAreaName().trim().isEmpty()) {
            fullAddress.append(warehouse.getAreaName());
        }
        if (warehouse.getDetailedAddress() != null && !warehouse.getDetailedAddress().trim().isEmpty()) {
            fullAddress.append(warehouse.getDetailedAddress());
        }

        return fullAddress.toString();
    }

    /**
     * 填充关联信息
     */
    private void populateRelatedInfo(ShippingOrderDetailResponse response, TShippingOrder order) {
        // 查询客户公司名称
        if (order.getCustomerCompanyId() != null) {
            try {
                TCustomerCompany customerCompany = customerCompanyService.getById(order.getCustomerCompanyId());
                if (customerCompany != null) {
                    response.setCustomerCompanyName(FormatUtils.safeString(customerCompany.getCompanyName()));
                }
            } catch (Exception e) {
                log.warn("获取客户公司信息失败，公司ID: " + order.getCustomerCompanyId() + ", 错误: " + e.getMessage());
            }
        }
    }

    /**
     * 获取订单附件列表
     */
    private List<String> getOrderAttachments(Long orderId) {
        try {
            return generalFileService.getShippingOrderAttachments(orderId)
                    .stream()
                    .map(file -> file.getFilePath())
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.warn("获取订单附件失败，订单ID: " + orderId + ", 错误: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 获取一级类目名称
     */
    private String getFirstCategoryName(Integer firstCategory) {
        return SkuFirstCategoryEnum.getDescriptionByCode(firstCategory);
    }

    /**
     * 获取产品名称
     */
    private String getProductName(Integer firstCategory, String secondCategory) {
        StringBuilder product = new StringBuilder();

        // 一级类目
        if (firstCategory != null) {
            product.append(getFirstCategoryName(firstCategory));
        }

        // 二级类目
        if (secondCategory != null && !secondCategory.trim().isEmpty()) {
            if (product.length() > 0) {
                product.append("-");
            }
            product.append(secondCategory);
        }

        return product.toString();
    }

    /**
     * 获取状态名称
     */
    private String getStatusName(Integer status) {
        return ShippingOrderStatusEnum.getDescriptionByCode(status);
    }

    /**
     * 转换为导出VO
     *
     * @param orderVO 订单VO
     * @return 导出VO
     */
    private ShippingOrderExportVO convertToExportVO(ShippingOrderPageVO orderVO) {
        ShippingOrderExportVO exportVO = new ShippingOrderExportVO();

        exportVO.setOrderNo(FormatUtils.safeString(orderVO.getOrderNo()));
        exportVO.setContractCode(FormatUtils.safeString(orderVO.getContractCode()));
        exportVO.setCustomerCompanyName(FormatUtils.safeString(orderVO.getCustomerCompanyName()));
        exportVO.setWarehouseName(FormatUtils.safeString(orderVO.getWarehouseName()));
        exportVO.setWarehouseAddress(FormatUtils.safeString(orderVO.getWarehouseAddress()));
        exportVO.setReceiverName(FormatUtils.combineReceiverInfo(orderVO.getReceiverName(), orderVO.getReceiverPhone()));
        exportVO.setProduct(getProductName(orderVO.getFirstCategory(), orderVO.getSecondCategory()));
        exportVO.setCount(FormatUtils.safeToString(orderVO.getCount()));
        exportVO.setShippedQuantity(FormatUtils.safeToString(orderVO.getShippedQuantity()));
        exportVO.setReceivedQuantity(FormatUtils.safeToString(orderVO.getReceivedQuantity()));
        exportVO.setStatus(getStatusName(orderVO.getStatus()));
        exportVO.setCreatedBy(FormatUtils.safeString(orderVO.getCreatedBy()));
        exportVO.setCreatedTime(FormatUtils.formatDateTime(orderVO.getCreatedTime()));

        return exportVO;
    }

    /**
     * 根据合同编号获取合同ID
     *
     * @param contractCode 合同编号
     * @return 合同ID
     */
    private Long getContractIdByCode(String contractCode) {
        return contractService.getContractIdByNo(contractCode);
    }

    /**
     * 根据仓库ID和一级类目列表查询仓库SKU
     *
     * @param warehouseId 仓库ID
     * @param firstCategories 一级类目列表
     * @return 仓库SKU列表
     */
    private List<TWarehouseSku> getWarehouseSkuByWarehouseIdAndFirstCategories(Long warehouseId, List<Integer> firstCategories) {
        LambdaQueryWrapper<TWarehouseSku> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TWarehouseSku::getValid, 1)
                .eq(TWarehouseSku::getEnabled, 1)
                .eq(TWarehouseSku::getWarehouseId, warehouseId)
                .in(TWarehouseSku::getFirstCategory, firstCategories)
                .orderBy(true, true, TWarehouseSku::getFirstCategory, TWarehouseSku::getSecondCategory);

        return warehouseSkuService.list(wrapper);
    }

    /**
     * 转换为SKU类型响应对象
     *
     * @param warehouseSku 仓库SKU
     * @return SKU类型响应对象
     */
    private ShippingOrderSkuTypeResponse convertToSkuTypeResponse(TWarehouseSku warehouseSku) {
        ShippingOrderSkuTypeResponse response = new ShippingOrderSkuTypeResponse();

        response.setFirstCategoryId(FormatUtils.safeToString(warehouseSku.getFirstCategory()));
        response.setFirstCategoryName(SkuFirstCategoryEnum.getDescriptionByCode(warehouseSku.getFirstCategory()));
        response.setSecondCategory(FormatUtils.safeString(warehouseSku.getSecondCategory()));

        // 生成显示文本：一级类目名称 二级类目
        StringBuilder displayText = new StringBuilder();
        String firstCategoryName = response.getFirstCategoryName();
        String secondCategory = response.getSecondCategory();

        if (firstCategoryName != null && !firstCategoryName.trim().isEmpty()) {
            displayText.append(firstCategoryName);
        }

        if (secondCategory != null && !secondCategory.trim().isEmpty()) {
            if (displayText.length() > 0) {
                displayText.append(" ");
            }
            displayText.append(secondCategory);
        }

        response.setDisplayText(displayText.toString());

        return response;
    }

    /**
     * 生成订单号（支持并发安全）
     *
     * @return 订单号
     */
    private String generateOrderNo() {
        int maxRetries = 5; // 最大重试次数

        for (int attempt = 0; attempt < maxRetries; attempt++) {
            try {
                // 生成格式：XSD + yyyyMMdd + 4位序号
                LocalDate now = LocalDate.now();
                String dateStr = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                String prefix = "XSD" + dateStr;

                // 查询当天最大序号
                int maxSeq = getMaxSequenceOfDay(dateStr);
                String sequence = String.format("%04d", maxSeq + 1);
                String orderNo = prefix + sequence;

                // 验证订单号唯一性（通过尝试插入临时记录）
                if (isOrderNoUnique(orderNo)) {
                    return orderNo;
                }

                // 如果不唯一，等待随机时间后重试
                Thread.sleep(10 + (long)(Math.random() * 20)); // 10-30ms随机等待

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("订单号生成被中断", e);
            } catch (Exception e) {
                // 记录日志但继续重试
                log.warn("订单号生成失败，尝试次数: " + (attempt + 1) + ", 错误: " + e.getMessage());
            }
        }

        throw new RuntimeException("订单号生成失败，已达到最大重试次数");
    }

    /**
     * 检查订单号是否唯一
     *
     * @param orderNo 订单号
     * @return 是否唯一
     */
    private boolean isOrderNoUnique(String orderNo) {
        LambdaQueryWrapper<TShippingOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TShippingOrder::getOrderNo, orderNo)
                .eq(TShippingOrder::getValid, 1);

        return this.count(wrapper) == 0;
    }

    /**
     * 获取当天最大序号
     *
     * @param dateStr 日期字符串
     * @return 最大序号
     */
    private int getMaxSequenceOfDay(String dateStr) {
        String prefix = "XSD" + dateStr;

        // 查询当天以该前缀开头的订单号
        LambdaQueryWrapper<TShippingOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.likeRight(TShippingOrder::getOrderNo, prefix)
                .eq(TShippingOrder::getValid, 1)
                .orderByDesc(TShippingOrder::getOrderNo)
                .last("LIMIT 1");

        TShippingOrder lastOrder = this.getOne(wrapper);
        if (lastOrder == null) {
            return 0;
        }

        // 从订单号中提取序号部分
        String orderNo = lastOrder.getOrderNo();
        if (orderNo.length() >= prefix.length() + 4) {
            String seqStr = orderNo.substring(prefix.length());
            try {
                return Integer.parseInt(seqStr);
            } catch (NumberFormatException e) {
                return 0;
            }
        }

        return 0;
    }

    /**
     * 同步创建发货需求单
     *
     * @param order 发运订单
     */
    private void createShippingDemand(TShippingOrder order) {
        try {
            TShippingDemand demand = new TShippingDemand();

            // 关联订单号
            demand.setOrderNo(order.getOrderNo());

            // 基本信息
            demand.setCustomerCompanyId(order.getCustomerCompanyId());
            demand.setWarehouseId(order.getWarehouseId());
            demand.setFirstCategory(order.getFirstCategory());
            demand.setSecondCategory(order.getSecondCategory());

            // 数量信息
            demand.setDemandQuantity(order.getCount());
            demand.setPendingQuantity(order.getCount()); // 初始待确认数等于需求数量
            demand.setShippedQuantity(0); // 初始发货数量为0
            demand.setUnexecutedQuantity(0); // 初始未执行数量为0

            // 时间信息
            demand.setDemandTime(order.getDemandTime());

            // 状态信息
            demand.setStatus(ShippingDemandStatusEnum.PENDING.getCode()); // 初始状态：待发货
            demand.setRemark(order.getRemark());

            // 审计信息
            demand.setCreatedBy(getCurrentUser());
            demand.setLastModifiedBy(getCurrentUser());
            demand.setValid(1);

            // 保存发货需求
            shippingDemandService.save(demand);

            log.info("成功创建发货需求单，订单号: {}", order.getOrderNo());

        } catch (Exception e) {
            log.error("创建发货需求单失败，订单号: " + order.getOrderNo() + ", 错误: " + e.getMessage(), e);
            // 这里可以选择抛出异常或者记录错误日志
            // 根据业务需求决定是否影响订单创建
            throw new RuntimeException("创建发货需求单失败: " + e.getMessage(), e);
        }
    }



    /**
     * 获取当前用户
     */
    private String getCurrentUser() {
        // TODO: 从安全上下文获取当前用户
        return "system";
    }
}
