package com.yi.controller.supplier.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 供应商新增/更新请求
 */
@Data
@ApiModel(value = "SupplierSaveRequest", description = "供应商新增/更新请求")
public class SupplierSaveRequest {

    @ApiModelProperty(value = "主键ID（更新时必填）")
    private String id;

    @ApiModelProperty(value = "供应商名称", required = true)
    @NotBlank(message = "供应商名称不能为空")
    @Size(max = 200, message = "供应商名称长度不能超过200个字符")
    private String supplierName;

    @ApiModelProperty(value = "联系人")
    @Size(max = 50, message = "联系人长度不能超过50个字符")
    private String contactPerson;

    @ApiModelProperty(value = "联系方式")
    @Size(max = 50, message = "联系方式长度不能超过50个字符")
    private String contactPhone;

    @ApiModelProperty(value = "开户行")
    @Size(max = 200, message = "开户行长度不能超过200个字符")
    private String bankName;

    @ApiModelProperty(value = "银行账号")
    @Size(max = 50, message = "银行账号长度不能超过50个字符")
    private String bankAccount;

    @ApiModelProperty(value = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    @ApiModelProperty(value = "营业执照文件路径列表")
    private List<String> businessLicenseFiles;
}
