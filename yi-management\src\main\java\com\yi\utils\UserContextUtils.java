package com.yi.utils;

import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 用户上下文工具类
 * 用于获取当前登录用户信息
 */
public class UserContextUtils {

    /**
     * 用户ID在请求头中的键名
     */
    private static final String USER_ID_HEADER = "X-User-Id";

    /**
     * 用户名在请求头中的键名
     */
    private static final String USERNAME_HEADER = "X-Username";

    /**
     * 默认系统用户
     */
    private static final String DEFAULT_SYSTEM_USER = "system";

    /**
     * 获取当前登录用户ID
     *
     * @return 用户ID，如果获取不到则返回null
     */
    public static Long getCurrentUserId() {
        try {
            HttpServletRequest request = getCurrentRequest();
            if (request != null) {
                String userIdStr = request.getHeader(USER_ID_HEADER);
                if (userIdStr != null && !userIdStr.trim().isEmpty()) {
                    return Long.parseLong(userIdStr);
                }
            }
        } catch (Exception e) {
            // 忽略异常，返回null
        }
        return null;
    }

    /**
     * 获取当前登录用户名
     *
     * @return 用户名，如果获取不到则返回默认系统用户
     */
    public static String getCurrentUser() {
        try {
            HttpServletRequest request = getCurrentRequest();
            if (request != null) {
                String username = request.getHeader(USERNAME_HEADER);
                if (username != null && !username.trim().isEmpty()) {
                    return username;
                }
            }
        } catch (Exception e) {
            // 忽略异常，返回默认用户
        }
        return DEFAULT_SYSTEM_USER;
    }

    /**
     * 获取当前登录用户名（带默认值）
     *
     * @param defaultUser 默认用户名
     * @return 用户名
     */
    public static String getCurrentUser(String defaultUser) {
        try {
            HttpServletRequest request = getCurrentRequest();
            if (request != null) {
                String username = request.getHeader(USERNAME_HEADER);
                if (username != null && !username.trim().isEmpty()) {
                    return username;
                }
            }
        } catch (Exception e) {
            // 忽略异常，返回默认用户
        }
        return defaultUser != null ? defaultUser : DEFAULT_SYSTEM_USER;
    }

    /**
     * 设置当前用户信息到请求头（用于测试或内部调用）
     *
     * @param userId   用户ID
     * @param username 用户名
     */
    public static void setCurrentUser(Long userId, String username) {
        try {
            HttpServletRequest request = getCurrentRequest();
            if (request != null) {
                // 注意：HttpServletRequest的header是只读的，这里只是示例
                // 实际使用中可能需要通过ThreadLocal或其他方式存储
                request.setAttribute(USER_ID_HEADER, userId);
                request.setAttribute(USERNAME_HEADER, username);
            }
        } catch (Exception e) {
            // 忽略异常
        }
    }

    /**
     * 清除当前用户信息
     */
    public static void clearCurrentUser() {
        try {
            HttpServletRequest request = getCurrentRequest();
            if (request != null) {
                request.removeAttribute(USER_ID_HEADER);
                request.removeAttribute(USERNAME_HEADER);
            }
        } catch (Exception e) {
            // 忽略异常
        }
    }

    /**
     * 检查是否有当前用户
     *
     * @return 是否有当前用户
     */
    public static boolean hasCurrentUser() {
        return getCurrentUserId() != null;
    }

    /**
     * 获取当前HTTP请求
     *
     * @return HttpServletRequest对象，如果不在Web环境中则返回null
     */
    private static HttpServletRequest getCurrentRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            return attributes != null ? attributes.getRequest() : null;
        } catch (Exception e) {
            return null;
        }
    }
}
