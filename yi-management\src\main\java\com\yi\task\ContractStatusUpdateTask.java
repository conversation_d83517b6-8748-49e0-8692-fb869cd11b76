package com.yi.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yi.entity.TContract;
import com.yi.enums.ContractStatusEnum;
import com.yi.service.TContractService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;

/**
 * 合同状态定时更新任务
 */
@Slf4j
@Component
public class ContractStatusUpdateTask {

    @Autowired
    private TContractService contractService;

    /**
     * 定时更新合同状态
     * 每天凌晨1点执行一次
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void updateContractStatus() {
        log.info("开始执行合同状态定时更新任务");
        
        try {
            LocalDate today = LocalDate.now();
            
            // 1. 更新待生效合同为生效中
            updatePendingToActive(today);
            
            // 2. 更新生效中合同为已到期
            updateActiveToExpired(today);
            
            log.info("合同状态定时更新任务执行完成");
            
        } catch (Exception e) {
            log.error("合同状态定时更新任务执行失败", e);
        }
    }

    /**
     * 更新待生效合同为生效中
     * 条件：当前日期 >= 生效日期 且 当前日期 <= 失效日期
     *
     * @param today 当前日期
     */
    private void updatePendingToActive(LocalDate today) {
        try {
            LambdaQueryWrapper<TContract> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TContract::getContractStatus, ContractStatusEnum.PENDING.getCode()) // 待生效
                    .eq(TContract::getValid, 1) // 有效
                    .le(TContract::getEffectiveDate, today) // 生效日期 <= 今天
                    .ge(TContract::getExpiryDate, today); // 失效日期 >= 今天

            List<TContract> pendingContracts = contractService.list(queryWrapper);

            if (!pendingContracts.isEmpty()) {
                log.info("找到 {} 个待生效合同需要更新为生效中状态", pendingContracts.size());

                // 记录具体的合同信息
                for (TContract contract : pendingContracts) {
                    log.debug("合同 {} (ID: {}) 从待生效更新为生效中，生效日期: {}, 失效日期: {}",
                            contract.getContractNo(), contract.getId(),
                            contract.getEffectiveDate(), contract.getExpiryDate());
                }

                LambdaUpdateWrapper<TContract> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(TContract::getContractStatus, ContractStatusEnum.PENDING.getCode())
                        .eq(TContract::getValid, 1)
                        .le(TContract::getEffectiveDate, today)
                        .ge(TContract::getExpiryDate, today)
                        .set(TContract::getContractStatus, ContractStatusEnum.ACTIVE.getCode());

                boolean success = contractService.update(updateWrapper);

                if (success) {
                    log.info("成功更新 {} 个待生效合同为生效中状态", pendingContracts.size());
                } else {
                    log.error("更新待生效合同状态失败");
                }
            } else {
                log.info("没有需要更新为生效中状态的合同");
            }
        } catch (Exception e) {
            log.error("更新待生效合同为生效中状态时发生异常", e);
        }
    }

    /**
     * 更新生效中合同为已到期
     * 条件：当前日期 > 失效日期
     *
     * @param today 当前日期
     */
    private void updateActiveToExpired(LocalDate today) {
        try {
            LambdaQueryWrapper<TContract> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TContract::getContractStatus, ContractStatusEnum.ACTIVE.getCode()) // 生效中
                    .eq(TContract::getValid, 1) // 有效
                    .lt(TContract::getExpiryDate, today); // 失效日期 < 今天

            List<TContract> activeContracts = contractService.list(queryWrapper);

            if (!activeContracts.isEmpty()) {
                log.info("找到 {} 个生效中合同需要更新为已到期状态", activeContracts.size());

                // 记录具体的合同信息
                for (TContract contract : activeContracts) {
                    log.debug("合同 {} (ID: {}) 从生效中更新为已到期，失效日期: {}",
                            contract.getContractNo(), contract.getId(), contract.getExpiryDate());
                }

                LambdaUpdateWrapper<TContract> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(TContract::getContractStatus, ContractStatusEnum.ACTIVE.getCode())
                        .eq(TContract::getValid, 1)
                        .lt(TContract::getExpiryDate, today)
                        .set(TContract::getContractStatus, ContractStatusEnum.EXPIRED.getCode());

                boolean success = contractService.update(updateWrapper);

                if (success) {
                    log.info("成功更新 {} 个生效中合同为已到期状态", activeContracts.size());
                } else {
                    log.error("更新生效中合同状态失败");
                }
            } else {
                log.info("没有需要更新为已到期状态的合同");
            }
        } catch (Exception e) {
            log.error("更新生效中合同为已到期状态时发生异常", e);
        }
    }

    /**
     * 手动触发合同状态更新（用于测试或紧急情况）
     */
    public void manualUpdateContractStatus() {
        log.info("手动触发合同状态更新任务");
        updateContractStatus();
    }
}
