# 出入库管理系统 Controller API 使用指南

## 概述

本文档详细介绍了出入库管理系统的所有Controller API接口，包括出库单管理、入库单管理和单号生成等功能。

## API 基础信息

- **基础路径**: `/api/warehouse`
- **认证方式**: Bearer <PERSON>（TODO: 根据项目实际情况调整）
- **响应格式**: JSON
- **时间格式**: `yyyy-MM-dd HH:mm:ss`

## 1. 出库单管理 API

### 基础路径: `/api/warehouse/outbound`

#### 1.1 分页查询出库单列表

```http
POST /api/warehouse/outbound/page
Content-Type: application/json

{
  "current": "1",
  "size": "10",
  "orderNo": "F20241201",
  "status": 1,
  "outboundType": 1,
  "outboundCompanyId": 1,
  "receiveCompanyId": 2,
  "firstCategory": 1,
  "startTime": "2024-12-01 00:00:00",
  "endTime": "2024-12-01 23:59:59"
}
```

#### 1.2 查询出库单详情

```http
# 根据ID查询
GET /api/warehouse/outbound/{id}

# 根据单号查询
GET /api/warehouse/outbound/orderNo/{orderNo}
```

#### 1.3 创建出库单

```http
POST /api/warehouse/outbound
Content-Type: application/json

{
  "outboundType": 1,
  "outboundCompanyId": 1,
  "outboundCompanyName": "易托盘科技有限公司",
  "outboundAddress": "上海市浦东新区张江高科技园区",
  "deliveryMethod": "物流配送",
  "vehicleNumber": "沪A12345",
  "driverName": "张师傅",
  "driverPhone": "13800138001",
  "receiveCompanyId": 2,
  "receiveCompanyName": "客户公司A",
  "receiveAddress": "北京市朝阳区建国门外大街1号",
  "firstCategory": 1,
  "secondCategory": "标准托盘",
  "plannedQuantity": 100,
  "remark": "测试出库单"
}
```

#### 1.4 更新出库单

```http
PUT /api/warehouse/outbound
Content-Type: application/json

{
  "id": 1,
  "outboundType": 1,
  "outboundCompanyId": 1,
  "outboundCompanyName": "易托盘科技有限公司",
  // ... 其他字段
}
```

#### 1.5 删除出库单

```http
# 单个删除
DELETE /api/warehouse/outbound/{id}

# 批量删除
DELETE /api/warehouse/outbound/batch
Content-Type: application/json

[1, 2, 3]
```

#### 1.6 出库状态管理

```http
# 确认出库（待出库 → 运输中）
POST /api/warehouse/outbound/confirm
Content-Type: application/json

{
  "id": 1,
  "actualQuantity": 95,
  "remark": "实际出库95个"
}

# 完成出库（运输中 → 已出库）
POST /api/warehouse/outbound/complete
Content-Type: application/json

{
  "id": 1,
  "remark": "货物已送达"
}

# 取消出库（运输中 → 待出库）
POST /api/warehouse/outbound/cancel
Content-Type: application/json

{
  "id": 1,
  "remark": "取消出库"
}
```

#### 1.7 统计查询

```http
# 状态统计
GET /api/warehouse/outbound/statistics/status

# 类型统计
GET /api/warehouse/outbound/statistics/type

# 待出库订单
GET /api/warehouse/outbound/pending

# 运输中订单
GET /api/warehouse/outbound/in-transit
```

## 2. 入库单管理 API

### 基础路径: `/api/warehouse/inbound`

#### 2.1 分页查询入库单列表

```http
POST /api/warehouse/inbound/page
Content-Type: application/json

{
  "current": "1",
  "size": "10",
  "orderNo": "F20241201",
  "status": 1,
  "inboundType": 4,
  "inboundWarehouseId": 2,
  "senderWarehouseId": 1,
  "firstCategory": 1,
  "startTime": "2024-12-01 00:00:00",
  "endTime": "2024-12-01 23:59:59"
}
```

#### 2.2 查询入库单详情

```http
# 根据ID查询
GET /api/warehouse/inbound/{id}

# 根据单号查询
GET /api/warehouse/inbound/orderNo/{orderNo}

# 根据出库单ID查询
GET /api/warehouse/inbound/outbound/{outboundOrderId}
```

#### 2.3 创建入库单

```http
POST /api/warehouse/inbound
Content-Type: application/json

{
  "orderNo": "F202412010001",
  "inboundType": 4,
  "inboundWarehouseId": 2,
  "inboundWarehouseName": "北京仓库",
  "deliveryMethod": "物流配送",
  "vehicleNumber": "沪A12345",
  "driverName": "张师傅",
  "driverPhone": "13800138001",
  "senderWarehouseId": 1,
  "senderWarehouseName": "上海总仓",
  "senderAddress": "上海市浦东新区张江高科技园区",
  "firstCategory": 1,
  "secondCategory": "标准托盘",
  "plannedQuantity": 100,
  "outboundOrderId": 1,
  "remark": "测试入库单"
}
```

#### 2.4 入库状态管理

```http
# 部分入库（待入库 → 部分入库）
POST /api/warehouse/inbound/partial
Content-Type: application/json

{
  "id": 1,
  "actualQuantity": 50,
  "remark": "部分入库50个"
}

# 完成入库（部分入库/待入库 → 已入库）
POST /api/warehouse/inbound/complete
Content-Type: application/json

{
  "id": 1,
  "actualQuantity": 100,
  "remark": "全部入库完成"
}

# 取消入库（部分入库 → 待入库）
POST /api/warehouse/inbound/cancel
Content-Type: application/json

{
  "id": 1,
  "remark": "取消入库"
}
```

#### 2.5 统计查询

```http
# 状态统计
GET /api/warehouse/inbound/statistics/status

# 类型统计
GET /api/warehouse/inbound/statistics/type

# 待入库订单
GET /api/warehouse/inbound/pending

# 部分入库订单
GET /api/warehouse/inbound/partial
```

## 3. 单号生成 API

### 基础路径: `/api/warehouse/order-number`

#### 3.1 生成单号

```http
# 生成出库单号
POST /api/warehouse/order-number/outbound

# 生成入库单号
POST /api/warehouse/order-number/inbound

# 根据指定日期生成单号
POST /api/warehouse/order-number/generate/20241201
```

#### 3.2 查询单号信息

```http
# 获取当日已生成的单号数量
GET /api/warehouse/order-number/count/today

# 获取指定日期已生成的单号数量
GET /api/warehouse/order-number/count/20241201

# 获取单号生成信息
GET /api/warehouse/order-number/info

# 预览下一个单号（不实际生成）
GET /api/warehouse/order-number/preview
```

## 4. 响应格式

### 4.1 成功响应

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体数据
  },
  "timestamp": "2024-12-01T10:30:00"
}
```

### 4.2 错误响应

```json
{
  "code": 500,
  "message": "操作失败：具体错误信息",
  "data": null,
  "timestamp": "2024-12-01T10:30:00"
}
```

### 4.3 分页响应

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "records": [
      // 数据列表
    ],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

## 5. 状态码说明

### 5.1 出库状态
- **1** - 待出库
- **2** - 运输中
- **3** - 已出库

### 5.2 入库状态
- **1** - 待入库
- **2** - 部分入库
- **3** - 已入库

### 5.3 出库类型
- **1** - 销售出库
- **2** - 调拨出库

### 5.4 入库类型
- **1** - 采购入库
- **2** - 回收入库
- **3** - 调拨入库
- **4** - 销售入库

### 5.5 一级类目
- **1** - 共享托盘

## 6. 业务流程示例

### 6.1 销售出库完整流程

```bash
# 1. 创建出库单
POST /api/warehouse/outbound
# 返回: {"code": 200, "data": "F202412010001"}

# 2. 创建对应入库单
POST /api/warehouse/inbound
# 使用相同单号: F202412010001

# 3. 确认出库
POST /api/warehouse/outbound/confirm
# {"id": 1, "actualQuantity": 95}

# 4. 完成出库
POST /api/warehouse/outbound/complete
# {"id": 1}

# 5. 完成入库
POST /api/warehouse/inbound/complete
# {"id": 1, "actualQuantity": 95}
```

### 6.2 仓库调拨流程

```bash
# 1. 创建调拨出库单
POST /api/warehouse/outbound
# {"outboundType": 2, ...}

# 2. 创建调拨入库单
POST /api/warehouse/inbound
# {"inboundType": 3, ...}

# 3. 执行调拨流程
POST /api/warehouse/outbound/confirm
POST /api/warehouse/outbound/complete
POST /api/warehouse/inbound/complete
```

## 7. 注意事项

1. **认证**: 所有API都需要有效的认证token
2. **权限**: 不同操作可能需要不同的权限级别
3. **状态流转**: 严格按照业务流程进行状态流转
4. **数据验证**: 请求参数会进行严格的数据验证
5. **并发安全**: 单号生成和状态更新都是并发安全的
6. **事务处理**: 关键操作使用事务保证数据一致性

这个API指南提供了完整的接口使用说明，可以帮助前端开发人员快速集成出入库管理功能。
