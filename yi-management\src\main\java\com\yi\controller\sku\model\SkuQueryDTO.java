package com.yi.controller.sku.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * SKU查询DTO
 */
@Data
@ApiModel(value = "SkuQueryDTO", description = "SKU查询条件")
public class SkuQueryDTO {

    @ApiModelProperty(value = "当前页码", example = "1")
    private Integer current = 1;

    @ApiModelProperty(value = "每页大小", example = "10")
    private Integer size = 10;

    @ApiModelProperty(value = "一级类目 1:共享托盘")
    private Integer firstCategory;

    @ApiModelProperty(value = "二级类目")
    private Integer secondCategory;

    @ApiModelProperty(value = "三级类目")
    private Integer thirdCategory;

    @ApiModelProperty(value = "长度(mm)")
    private Integer length;

    @ApiModelProperty(value = "宽度(mm)")
    private Integer width;

    @ApiModelProperty(value = "高度(mm)")
    private Integer height;

    @ApiModelProperty(value = "启用状态：1-启用，0-禁用")
    private Integer enabled;
}
