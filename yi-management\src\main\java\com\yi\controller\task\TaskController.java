package com.yi.controller.task;

import com.yi.common.Result;
import com.yi.task.ContractStatusUpdateTask;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 定时任务管理Controller
 */
@Slf4j
@RestController
@RequestMapping("/api/task")
@Api(tags = "定时任务管理")
public class TaskController {

    @Autowired
    private ContractStatusUpdateTask contractStatusUpdateTask;

    @ApiOperation("手动触发合同状态更新任务")
    @PostMapping("/contract-status-update")
    public Result<String> manualUpdateContractStatus() {
        try {
            contractStatusUpdateTask.manualUpdateContractStatus();
            return Result.success("合同状态更新任务执行成功");
        } catch (Exception e) {
            log.error("手动触发合同状态更新任务失败", e);
            return Result.failed("合同状态更新任务执行失败：" + e.getMessage());
        }
    }
}
