# 供应商管理API文档

## 概述

供应商管理模块提供完整的供应商信息管理功能，包括供应商的增删改查、启用禁用等操作。

## 数据库表结构

### t_supplier 供应商表

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) | - | 是 | AUTO_INCREMENT | 主键ID |
| supplier_name | varchar(200) | 200 | 是 | - | 供应商名称 |
| contact_person | varchar(50) | 50 | 否 | NULL | 联系人 |
| contact_phone | varchar(50) | 50 | 否 | NULL | 联系方式 |
| bank_name | varchar(200) | 200 | 否 | NULL | 开户行 |
| bank_account | varchar(50) | 50 | 否 | NULL | 银行账号 |
| remark | varchar(500) | 500 | 否 | NULL | 备注 |
| enabled | tinyint(4) | - | 是 | 1 | 启用状态：1-启用，0-禁用 |
| created_by | varchar(50) | 50 | 否 | NULL | 创建人 |
| created_time | datetime | - | 否 | CURRENT_TIMESTAMP | 创建时间 |
| last_modified_by | varchar(50) | 50 | 否 | NULL | 最后修改人 |
| last_modified_time | datetime | - | 否 | CURRENT_TIMESTAMP ON UPDATE | 最后修改时间 |
| valid | tinyint(4) | - | 是 | 1 | 有效性：1-有效，0-无效 |

### 索引设计

```sql
-- 主键索引
PRIMARY KEY (`id`)

-- 供应商名称索引（用于名称查询和唯一性检查）
KEY `idx_supplier_name` (`supplier_name`)

-- 启用状态和有效性复合索引（用于状态筛选）
KEY `idx_enabled_valid` (`enabled`, `valid`)
```

## API接口

### 1. 分页查询供应商列表

**接口地址**: `POST /api/supplier/page`

**请求参数**:
- `pageNum`: 页码（默认1）
- `pageSize`: 页大小（默认10）
- `request`: 查询条件（可选）

**查询条件**:
```json
{
    "supplierName": "供应商名称（模糊查询）",
    "contactPerson": "联系人（模糊查询）",
    "contactPhone": "联系方式（模糊查询）",
    "enabled": 1,
    "createdBy": "创建人（模糊查询）",
    "createdTimeStart": "2024-01-01 00:00:00",
    "createdTimeEnd": "2024-12-31 23:59:59"
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "records": [
            {
                "id": "1",
                "supplierName": "测试供应商",
                "contactPerson": "张三",
                "contactPhone": "***********",
                "bankName": "中国银行",
                "bankAccount": "**********",
                "remark": "测试备注",
                "enabled": "1",
                "enabledName": "启用",
                "createdBy": "admin",
                "createdTime": "2024-07-29 10:00:00",
                "lastModifiedBy": "admin",
                "lastModifiedTime": "2024-07-29 10:00:00"
            }
        ],
        "total": 1,
        "size": 10,
        "current": 1,
        "pages": 1
    }
}
```

### 2. 查询供应商列表（不分页）

**接口地址**: `POST /api/supplier/list`

**请求参数**: 同分页查询的查询条件

**响应示例**: 返回数组格式的供应商列表

### 3. 根据ID查询供应商详情

**接口地址**: `GET /api/supplier/{id}`

**路径参数**:
- `id`: 供应商ID

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": "1",
        "supplierName": "测试供应商",
        "contactPerson": "张三",
        "contactPhone": "***********",
        "bankName": "中国银行",
        "bankAccount": "**********",
        "remark": "测试备注",
        "enabled": "1",
        "enabledName": "启用",
        "createdBy": "admin",
        "createdTime": "2024-07-29 10:00:00",
        "lastModifiedBy": "admin",
        "lastModifiedTime": "2024-07-29 10:00:00"
    }
}
```

### 4. 新增供应商

**接口地址**: `POST /api/supplier`

**请求体**:
```json
{
    "supplierName": "新供应商",
    "contactPerson": "李四",
    "contactPhone": "***********",
    "bankName": "工商银行",
    "bankAccount": "**********",
    "remark": "新增供应商备注",
    "enabled": 1
}
```

**字段验证**:
- `supplierName`: 必填，最大200字符
- `contactPerson`: 可选，最大50字符
- `contactPhone`: 可选，最大50字符
- `bankName`: 可选，最大200字符
- `bankAccount`: 可选，最大50字符
- `remark`: 可选，最大500字符
- `enabled`: 可选，默认1

**响应示例**:
```json
{
    "code": 200,
    "message": "新增供应商成功",
    "data": "2"
}
```

### 5. 更新供应商

**接口地址**: `PUT /api/supplier`

**请求体**:
```json
{
    "id": "1",
    "supplierName": "更新后的供应商名称",
    "contactPerson": "王五",
    "contactPhone": "***********",
    "bankName": "建设银行",
    "bankAccount": "**********",
    "remark": "更新后的备注",
    "enabled": 1
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "更新供应商成功"
}
```

### 6. 删除供应商

**接口地址**: `DELETE /api/supplier/{id}`

**路径参数**:
- `id`: 供应商ID

**响应示例**:
```json
{
    "code": 200,
    "message": "删除供应商成功"
}
```

**说明**: 采用逻辑删除，将`valid`字段设置为0

### 7. 启用/禁用供应商

**接口地址**: `PUT /api/supplier/{id}/enabled`

**路径参数**:
- `id`: 供应商ID

**请求参数**:
- `enabled`: 启用状态（1-启用，0-禁用）

**响应示例**:
```json
{
    "code": 200,
    "message": "启用供应商成功"
}
```

### 8. 上传供应商营业执照

**接口地址**: `POST /api/supplier/{id}/business-license`

**路径参数**:
- `id`: 供应商ID

**请求体**:
```json
[
    "/uploads/supplier/business-license/license1.jpg",
    "/uploads/supplier/business-license/license2.pdf"
]
```

**响应示例**:
```json
{
    "code": 200,
    "message": "上传供应商营业执照成功"
}
```

### 9. 获取供应商营业执照列表

**接口地址**: `GET /api/supplier/{id}/business-license`

**路径参数**:
- `id`: 供应商ID

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "id": 1,
            "type": 3,
            "fileType": "jpg",
            "relatedId": 1,
            "filePath": "/uploads/supplier/business-license/license1.jpg",
            "createdBy": "admin",
            "createdTime": "2024-07-29T10:00:00",
            "lastModifiedBy": "admin",
            "lastModifiedTime": "2024-07-29T10:00:00",
            "valid": 1
        }
    ]
}
```

### 10. 删除供应商营业执照

**接口地址**: `DELETE /api/supplier/{id}/business-license`

**路径参数**:
- `id`: 供应商ID

**响应示例**:
```json
{
    "code": 200,
    "message": "删除供应商营业执照成功"
}
```

## 业务规则

### 1. 数据唯一性
- 供应商名称在有效记录中必须唯一
- 新增和更新时都会检查名称重复

### 2. 状态管理
- `enabled`: 控制供应商是否可用于业务操作
- `valid`: 控制记录是否有效（逻辑删除标识）

### 3. 审计字段
- 自动记录创建人、创建时间
- 自动记录最后修改人、最后修改时间

### 4. 数据验证
- 供应商名称必填且不能超过200字符
- 联系方式、银行信息等为可选字段
- 所有字符串字段都有长度限制

### 5. 营业执照管理
- 每个供应商可以上传多个营业执照文件
- 支持常见图片格式（jpg、png、gif）和PDF文档
- 上传新文件会替换原有文件
- 文件存储在通用文件表中，类型为3（供应商营业执照）
- 删除供应商时，相关营业执照文件会被逻辑删除

## 错误处理

### 常见错误码

| 错误信息 | 说明 | 解决方案 |
|----------|------|----------|
| 供应商名称已存在 | 名称重复 | 使用不同的供应商名称 |
| 供应商不存在 | ID无效或已删除 | 检查供应商ID是否正确 |
| 供应商ID格式错误 | ID参数格式不正确 | 确保ID为有效的数字 |
| 启用状态参数错误 | enabled参数不是0或1 | 使用正确的状态值 |

## 使用示例

### 创建供应商的完整流程

```bash
# 1. 新增供应商
curl -X POST "http://localhost:8080/api/supplier" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "supplierName": "优质供应商有限公司",
    "contactPerson": "张经理",
    "contactPhone": "***********",
    "bankName": "中国银行北京分行",
    "bankAccount": "**********123456",
    "remark": "主要供应托盘产品",
    "enabled": 1
  }'

# 2. 查询供应商详情
curl -X GET "http://localhost:8080/api/supplier/1" \
  -H "Authorization: Bearer {token}"

# 3. 分页查询供应商列表
curl -X POST "http://localhost:8080/api/supplier/page?pageNum=1&pageSize=10" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "supplierName": "优质",
    "enabled": 1
  }'

# 4. 上传供应商营业执照
curl -X POST "http://localhost:8080/api/supplier/1/business-license" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '[
    "/uploads/supplier/business-license/license1.jpg",
    "/uploads/supplier/business-license/license2.pdf"
  ]'

# 5. 获取供应商营业执照
curl -X GET "http://localhost:8080/api/supplier/1/business-license" \
  -H "Authorization: Bearer {token}"

# 6. 删除供应商营业执照
curl -X DELETE "http://localhost:8080/api/supplier/1/business-license" \
  -H "Authorization: Bearer {token}"
```

## 注意事项

1. **权限控制**: 所有接口都需要有效的JWT token
2. **数据完整性**: 删除供应商前应检查是否有关联的业务数据
3. **性能优化**: 大量数据查询时建议使用分页接口
4. **数据备份**: 重要操作前建议备份相关数据
