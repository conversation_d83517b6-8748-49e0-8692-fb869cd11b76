package com.yi.configuration.jwt;


import com.yi.configuration.exception.BizException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class JwtTokenUtil {

    @Value("${jwt.expire:7200}")
    public int expire;
    @Value("${jwt.appExp:43200}")
    public int warehouseAppExp;
    @Value("${jwt.pri-key.path}")
    public String priKeyPath;
    @Value("${jwt.pub-key.path}")
    public String pubKeyPath;

    public IJWTInfo getInfoFromToken(String token) throws BizException {
        return JWTHelper.getInfoFromToken(token, pubKeyPath);
    }

    public TokenVo generateToken(IJWTInfo jwtInfo) throws BizException {
            return JWTHelper.generateToken(jwtInfo, priKeyPath, expire);

    }


}
