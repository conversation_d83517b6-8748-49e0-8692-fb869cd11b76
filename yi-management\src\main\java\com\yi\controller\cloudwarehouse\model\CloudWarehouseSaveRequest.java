package com.yi.controller.cloudwarehouse.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 云仓保存请求
 */
@Data
@ApiModel("云仓保存请求")
public class CloudWarehouseSaveRequest {

    @ApiModelProperty("主键ID（编辑时传入）")
    private String id;

    @ApiModelProperty(value = "仓库类型：1-中心仓，2-卫星仓，3-虚拟仓", required = true)
    @NotNull(message = "仓库类型不能为空")
    private Integer warehouseType;

    @ApiModelProperty(value = "仓库属性：1-自有，2-第三方", required = true)
    @NotNull(message = "仓库属性不能为空")
    private Integer warehouseAttribute;

    @ApiModelProperty(value = "仓库名称", required = true)
    @NotBlank(message = "仓库名称不能为空")
    private String warehouseName;

    @ApiModelProperty("省份ID")
    private Long provinceId;

    @ApiModelProperty(value = "省份名称", required = true)
    @NotBlank(message = "省份名称不能为空")
    private String provinceName;

    @ApiModelProperty("城市ID")
    private Long cityId;

    @ApiModelProperty(value = "城市名称", required = true)
    @NotBlank(message = "城市名称不能为空")
    private String cityName;

    @ApiModelProperty("区县ID")
    private Long areaId;

    @ApiModelProperty(value = "区县名称", required = true)
    @NotBlank(message = "区县名称不能为空")
    private String areaName;

    @ApiModelProperty(value = "详细地址", required = true)
    @NotBlank(message = "详细地址不能为空")
    private String detailedAddress;

    @ApiModelProperty(value = "联系人", required = true)
    @NotBlank(message = "联系人不能为空")
    private String contactPerson;

    @ApiModelProperty(value = "联系方式", required = true)
    @NotBlank(message = "联系方式不能为空")
    private String contactPhone;

    @ApiModelProperty("作业时间")
    private String workingHours;

    @ApiModelProperty("备注")
    private String remark;
}
