package com.yi.configuration.jwt;

import java.io.Serializable;
import java.util.List;


public class JWTInfo implements Serializable, IJWTInfo {
    private String userName;
    private Long userId;
    private String nickName;
    private String appId;
    private Long customerCompanyId;
    private String userCode;

    private Long factoryId;
    private Long supplierId;
    private List<Long> userRoles;

    public JWTInfo() {

    }

    public JWTInfo(Long userId, String userName) {
        this.userName = userName;
        this.userId = userId;
    }

    public JWTInfo(Long supplierId, Long factoryId, String userName, Long userId, String nickName,
                   Long customerCompanyId,
                   String appId, String userCode, List<Long> userRoles) {
        this.supplierId = supplierId;
        this.factoryId = factoryId;
        this.userName = userName;
        this.userId = userId;
        this.nickName = nickName;
        this.appId = appId;
        this.customerCompanyId = customerCompanyId;
        this.userCode = userCode;
        this.userRoles = userRoles;
    }

    @Override
    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    @Override
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Override
    public String getNickName() {
        return nickName;
    }

    @Override
    public Long getCustomerCompanyId() {
        return customerCompanyId;
    }

    @Override
    public String getUserCode() {
        return userCode;
    }

    @Override
    public List<Long> getUserRoles() {
        return userRoles;
    }

    public void setUserRoles(List<Long> userRoles) {
        this.userRoles = userRoles;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    @Override
    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public void setCustomerCompanyId(Long customerCompanyId) {
        this.customerCompanyId = customerCompanyId;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public Long getFactoryId() {
        return factoryId;
    }

    public void setFactoryId(Long factoryId) {
        this.factoryId = factoryId;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    /**
     * 重写equals 方法， 必须appid,username,userId都相等， 两个对象相等
     *
     * @param o
     * @return
     */
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        JWTInfo jwtInfo = (JWTInfo) o;

        if (appId != null ? !appId.equals(jwtInfo.appId) : jwtInfo.appId != null) {
            return false;
        }
        if (userName != null ? !userName.equals(jwtInfo.userName) : jwtInfo.userName != null) {
            return false;
        }
        return userId != null ? userId.equals(jwtInfo.userId) : jwtInfo.userId == null;

    }

    @Override
    public int hashCode() {
        int result = appId != null ? appId.hashCode() : 0;
        result = 31 * result + (userName != null ? userName.hashCode() : 0);
        result = 31 * result + (userId != null ? userId.hashCode() : 0);
        return result;
    }
}
