package com.yi.configuration.jwt;

import java.util.List;

public interface IJWTInfo {
    /**
     * 获取用户登录名
     *
     * @return
     */
    String getUserName();

    /**
     * 获取用户账号ID
     *
     * @return
     */
    Long getUserId();

    /**
     * 获取用户昵称
     *
     * @return
     */
    String getNickName();

    /**
     * application id
     */
    String getAppId();

    /**
     * 客户公司id
     *
     * @return
     */
    Long getCustomerCompanyId();

    /**
     * 客户识别码
     * @return
     */
    String getUserCode();

    /**
     * 客户角色id
     * @return
     */
    List<Long> getUserRoles();

    /**
     * 获取工厂id.
     * @return
     */
    Long getFactoryId();

    /**
     * 获取供应商id
     * @return
     */
    Long getSupplierId();

}
