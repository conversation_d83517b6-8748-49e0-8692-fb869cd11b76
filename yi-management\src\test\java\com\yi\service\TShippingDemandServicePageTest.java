package com.yi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yi.controller.shippingdemand.model.ShippingDemandPageResponse;
import com.yi.controller.shippingdemand.model.ShippingDemandQueryRequest;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 发货需求分页查询功能测试
 */
@SpringBootTest
public class TShippingDemandServicePageTest {

    @MockBean
    private TShippingDemandService shippingDemandService;

    @Test
    public void testGetShippingDemandPage_WithAllFilters() {
        // 准备测试数据
        ShippingDemandQueryRequest request = new ShippingDemandQueryRequest();
        request.setCurrent("1");
        request.setSize("10");
        request.setStatus("1000"); // 待发货
        request.setOrderNo("SO20240101000001");
        request.setCustomerCompanyName("测试客户");
        request.setWarehouseName("测试仓库");

        // 模拟返回数据
        IPage<ShippingDemandPageResponse> mockPage = mock(IPage.class);
        when(shippingDemandService.getShippingDemandPageResponse(request)).thenReturn(mockPage);

        // 调用方法
        IPage<ShippingDemandPageResponse> result = shippingDemandService.getShippingDemandPageResponse(request);

        // 验证结果
        assertNotNull(result);
        verify(shippingDemandService, times(1)).getShippingDemandPageResponse(request);
    }

    @Test
    public void testGetShippingDemandPage_WithStatusFilter() {
        // 测试状态筛选
        ShippingDemandQueryRequest request = new ShippingDemandQueryRequest();
        request.setStatus("2000"); // 已发货

        IPage<ShippingDemandPageResponse> mockPage = mock(IPage.class);
        when(shippingDemandService.getShippingDemandPageResponse(request)).thenReturn(mockPage);

        IPage<ShippingDemandPageResponse> result = shippingDemandService.getShippingDemandPageResponse(request);

        assertNotNull(result);
        assertEquals("2000", request.getStatus());
    }

    @Test
    public void testGetShippingDemandPage_WithOrderNoFilter() {
        // 测试订单号筛选
        ShippingDemandQueryRequest request = new ShippingDemandQueryRequest();
        request.setOrderNo("SO20240101");

        IPage<ShippingDemandPageResponse> mockPage = mock(IPage.class);
        when(shippingDemandService.getShippingDemandPageResponse(request)).thenReturn(mockPage);

        IPage<ShippingDemandPageResponse> result = shippingDemandService.getShippingDemandPageResponse(request);

        assertNotNull(result);
        assertEquals("SO20240101", request.getOrderNo());
    }

    @Test
    public void testGetShippingDemandPage_WithCustomerFilter() {
        // 测试客户主体筛选
        ShippingDemandQueryRequest request = new ShippingDemandQueryRequest();
        request.setCustomerCompanyName("客户A");

        IPage<ShippingDemandPageResponse> mockPage = mock(IPage.class);
        when(shippingDemandService.getShippingDemandPageResponse(request)).thenReturn(mockPage);

        IPage<ShippingDemandPageResponse> result = shippingDemandService.getShippingDemandPageResponse(request);

        assertNotNull(result);
        assertEquals("客户A", request.getCustomerCompanyName());
    }

    @Test
    public void testGetShippingDemandPage_WithWarehouseFilter() {
        // 测试收货仓库筛选
        ShippingDemandQueryRequest request = new ShippingDemandQueryRequest();
        request.setWarehouseName("仓库B");

        IPage<ShippingDemandPageResponse> mockPage = mock(IPage.class);
        when(shippingDemandService.getShippingDemandPageResponse(request)).thenReturn(mockPage);

        IPage<ShippingDemandPageResponse> result = shippingDemandService.getShippingDemandPageResponse(request);

        assertNotNull(result);
        assertEquals("仓库B", request.getWarehouseName());
    }

    @Test
    public void testShippingDemandPageResponse_RequiredFields() {
        // 测试返回字段
        ShippingDemandPageResponse response = new ShippingDemandPageResponse();
        
        // 设置必要字段
        response.setId("1");
        response.setStatus("1000");
        response.setStatusName("待发货");
        response.setOrderNo("SO20240101000001");
        response.setCustomerCompanyName("测试客户公司");
        response.setWarehouseName("测试收货仓库");
        response.setReceivingAddress("测试收货地址");
        response.setReceiverName("张三");
        response.setProductName("共享托盘-标准托盘");
        response.setDemandQuantity("100");
        response.setPendingQuantity("80");
        response.setShippedQuantity("20");
        response.setUnexecutedQuantity("0");
        response.setDemandTime("2024-01-01");
        response.setCreatedBy("admin");
        response.setCreatedTime("2024-01-01 10:00:00");

        // 验证字段
        assertEquals("1", response.getId());
        assertEquals("1000", response.getStatus());
        assertEquals("待发货", response.getStatusName());
        assertEquals("SO20240101000001", response.getOrderNo());
        assertEquals("测试客户公司", response.getCustomerCompanyName());
        assertEquals("测试收货仓库", response.getWarehouseName());
        assertEquals("测试收货地址", response.getReceivingAddress());
        assertEquals("张三", response.getReceiverName());
        assertEquals("共享托盘-标准托盘", response.getProductName());
        assertEquals("100", response.getDemandQuantity());
        assertEquals("80", response.getPendingQuantity());
        assertEquals("20", response.getShippedQuantity());
        assertEquals("0", response.getUnexecutedQuantity());
        assertEquals("2024-01-01", response.getDemandTime());
        assertEquals("admin", response.getCreatedBy());
        assertEquals("2024-01-01 10:00:00", response.getCreatedTime());
    }

    @Test
    public void testShippingDemandQueryRequest_DefaultValues() {
        // 测试查询请求默认值
        ShippingDemandQueryRequest request = new ShippingDemandQueryRequest();
        
        // 验证默认分页参数
        assertEquals("1", request.getCurrent());
        assertEquals("10", request.getSize());
        
        // 验证筛选条件为空
        assertNull(request.getStatus());
        assertNull(request.getOrderNo());
        assertNull(request.getCustomerCompanyName());
        assertNull(request.getWarehouseName());
    }

    @Test
    public void testShippingDemandStatus_Values() {
        // 测试状态值
        String[] validStatuses = {"1000", "2000", "3000", "4000"};
        String[] statusNames = {"待发货", "已发货", "已完结", "已取消"};
        
        for (int i = 0; i < validStatuses.length; i++) {
            String status = validStatuses[i];
            String statusName = statusNames[i];
            
            // 验证状态值格式
            assertTrue(status.matches("\\d{4}"));
            assertNotNull(statusName);
            assertFalse(statusName.trim().isEmpty());
        }
    }

    @Test
    public void testProductName_Composition() {
        // 测试产品名称组合逻辑
        String firstCategoryName = "共享托盘";
        String secondCategory = "标准托盘";
        
        // 模拟产品名称组合
        String productName = firstCategoryName;
        if (secondCategory != null && !secondCategory.trim().isEmpty()) {
            productName += "-" + secondCategory.trim();
        }
        
        assertEquals("共享托盘-标准托盘", productName);
        
        // 测试只有一级类目的情况
        String productNameOnly = "共享托盘";
        String secondCategoryEmpty = "";
        
        String productNameResult = productNameOnly;
        if (secondCategoryEmpty != null && !secondCategoryEmpty.trim().isEmpty()) {
            productNameResult += "-" + secondCategoryEmpty.trim();
        }
        
        assertEquals("共享托盘", productNameResult);
    }

    @Test
    public void testQuantityFields_Validation() {
        // 测试数量字段验证
        ShippingDemandPageResponse response = new ShippingDemandPageResponse();
        
        response.setDemandQuantity("100");
        response.setPendingQuantity("60");
        response.setShippedQuantity("40");
        response.setUnexecutedQuantity("0");
        
        // 验证数量关系：需求数量 = 待确认数 + 发货数量 + 未执行数
        int demandQty = Integer.parseInt(response.getDemandQuantity());
        int pendingQty = Integer.parseInt(response.getPendingQuantity());
        int shippedQty = Integer.parseInt(response.getShippedQuantity());
        int unexecutedQty = Integer.parseInt(response.getUnexecutedQuantity());
        
        assertEquals(demandQty, pendingQty + shippedQty + unexecutedQty);
    }

    @Test
    public void testPagination_Parameters() {
        // 测试分页参数
        ShippingDemandQueryRequest request = new ShippingDemandQueryRequest();
        
        // 测试不同的分页参数
        request.setCurrent("2");
        request.setSize("20");
        
        assertEquals("2", request.getCurrent());
        assertEquals("20", request.getSize());
        
        // 测试分页参数转换
        int current = Integer.parseInt(request.getCurrent());
        int size = Integer.parseInt(request.getSize());
        
        assertTrue(current > 0);
        assertTrue(size > 0);
        assertTrue(size <= 100); // 假设最大分页大小为100
    }

    @Test
    public void testFilterCombination() {
        // 测试多条件组合筛选
        ShippingDemandQueryRequest request = new ShippingDemandQueryRequest();
        request.setStatus("1000");
        request.setOrderNo("SO2024");
        request.setCustomerCompanyName("测试");
        request.setWarehouseName("仓库");
        
        // 验证所有筛选条件都已设置
        assertNotNull(request.getStatus());
        assertNotNull(request.getOrderNo());
        assertNotNull(request.getCustomerCompanyName());
        assertNotNull(request.getWarehouseName());
        
        // 验证筛选条件值
        assertEquals("1000", request.getStatus());
        assertTrue(request.getOrderNo().contains("SO"));
        assertTrue(request.getCustomerCompanyName().contains("测试"));
        assertTrue(request.getWarehouseName().contains("仓库"));
    }
}
