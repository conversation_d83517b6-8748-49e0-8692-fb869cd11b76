package com.yi.service;

import com.github.pagehelper.PageInfo;
import com.yi.configuration.auth.MesAuthService;
import com.yi.configuration.exception.BizException;
import com.yi.controller.user.io.UserDetailResponseModel;
import com.yi.controller.user.io.UserIdRequestModel;
import com.yi.controller.user.io.UserListRequestModel;
import com.yi.controller.user.io.UserResponseModel;
import com.yi.enums.BasicDataExceptionEnum;
import com.yi.utils.ListUtils;
import com.yi.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UserService {
    @Resource
    private CommonService commonService;
//    @Resource
//    private TUserMapper tUserMapper;
//    @Resource
//    private TUserRoleMapper tUserRoleMapper;
//    @Resource
//    private TOrganizationUserRelationMapper tUserOrganizationRelationMapper;
    @Resource
    private RedisUtil redisUtil;
//    @Resource
//    private VerifyCodeBiz verifyCodeBiz;
//    @Resource
//    private TRoleMapper tRoleMapper;

    @Resource
    private MesAuthService mesAuthService;


    /**
     * 员工列表
     *
     * @param requestModel 请求参数
     * @return PageInfo<UserResponseModel>
     */
    public PageInfo<UserResponseModel> searchList(UserListRequestModel requestModel) {
//        if (StringUtils.isNotBlank(requestModel.getOrganizationIds())) {
//            StringBuilder sb = new StringBuilder(requestModel.getOrganizationIds());
//            //获取他的子部门id,然后查询
//            List<TOrganization> childOrganization =
//                    tOrganizationMapper.getChildOrganizationList(requestModel.getOrganizationIds());
//            for (TOrganization org : childOrganization) {
//                sb.append(",").append(org.getId());
//            }
//            requestModel.setOrganizationIds(sb.toString());
//        }
        requestModel.enablePaging();
        List<Long> idList = new ArrayList<>(); //tUserMapper.searchIdList(requestModel);
        PageInfo pageInfo = new PageInfo(idList);
        pageInfo.setList(new ArrayList());
        if (ListUtils.isNotEmpty(idList)) {
            //  pageInfo.setList(tUserMapper.searchList(StringUtils.listToString(idList, ',')));
        }
        return pageInfo;
    }

    /**
     * 员工信息详情
     *
     * @param requestModel 请求参数
     * @return UserDetailResponseModel 员工信息详情
     */
    public UserDetailResponseModel getUserInfo(UserIdRequestModel requestModel) {
        UserDetailResponseModel detail = null;//tUserMapper.getUserInfo(requestModel.getUserId());
        if (detail == null) {
            throw new BizException(BasicDataExceptionEnum.USER_INFO_NOT_EXIST);
        }
        return detail;
    }

//    /**
//     * 添加员工
//     *
//     * @param requestModel 请求参数
//     */
//    @Transactional
//    public void addUserInfo(UserAddRequestModel requestModel) {
//        if (!RegExpValidatorUtils.isMobile(requestModel.getMobilePhone())) {
//            throw new BizException(BasicDataExceptionEnum.MOBILE_FORMAT_ERROR);
//        }
//        TUser tUserAccount = tUserMapper.getByUserAccount(requestModel.getUserAccount());
//        if (tUserAccount != null) {
//            throw new BizException(BasicDataExceptionEnum.USER_ACCOUNT_EXIST);
//        }
//
//        TUser tUserByMobile = tUserMapper.getUserByMobile(requestModel.getMobilePhone());
//        if (null != tUserByMobile) {
//            throw new BizException(BasicDataExceptionEnum.USER_MOBILE_EXIST);
//        }
//        String userName = BaseContextHandler.getUserName();
//        TUser tUser = new TUser();
//        if (StringUtils.isBlank(requestModel.getUserCode())) {
//            tUser.setUserCode(generateUserCode());
//        } else {
//            tUser.setUserCode(requestModel.getUserCode());
//        }
//        TUser tUserByCode = tUserMapper.getUserByCode(tUser.getUserCode());
//        if (null != tUserByCode) {
//            throw new BizException(BasicDataExceptionEnum.USER_CODE_EXIST);
//        }
//        //如果没有设置供应商角色 需要置空
//        if (requestModel.getSupplierId() != null && !requestModel.getSupplierId().equals(CommonConstant.LONG_ZERO)) {
//            if (!checkUserSupplier(requestModel.getRoleIds())) {
//                requestModel.setSupplierId(CommonConstant.LONG_ZERO);
//            }
//        }
//        tUser.setSupplierId(requestModel.getSupplierId());
//        tUser.setFactoryId(BaseContextHandler.getFactoryId());
//        tUser.setUserName(requestModel.getUserName());
//        tUser.setUserAccount(requestModel.getUserAccount());
//        String salt = YeloBcryptUtils.genSaltForUser();
//        tUser.setPassword(YeloBcryptUtils.bcryptForPassword(requestModel.getPassword(), salt));
//        tUser.setUserSalt(salt);
//        tUser.setMobilePhone(requestModel.getMobilePhone());
//        commonService.setBaseEntityAdd(tUser, userName);
//        tUserMapper.insertSelectiveEncrypt(tUser);
//        if (ListUtils.isNotEmpty(requestModel.getRoleIds())) {
//            List<TUserRole> insertUserRoleRelationList = new ArrayList<>();
//            TUserRole tUserRoleRelation;
//            for (Long roleId : requestModel.getRoleIds()) {
//                tUserRoleRelation = new TUserRole();
//                tUserRoleRelation.setRoleId(roleId);
//                tUserRoleRelation.setUserId(tUser.getId());
//                commonService.setBaseEntityAdd(tUserRoleRelation, userName);
//                insertUserRoleRelationList.add(tUserRoleRelation);
//            }
//            tUserRoleMapper.batchInsert(insertUserRoleRelationList);
//        }
//        if (ListUtils.isNotEmpty(requestModel.getOrganizationIds())) {
//            List<TOrganizationUserRelation> insertUserOrganizationRelationList = new ArrayList<>();
//            for (Long item : requestModel.getOrganizationIds()) {
//                TOrganizationUserRelation tUserOrganizationRelation = new TOrganizationUserRelation();
//                tUserOrganizationRelation.setOrgId(item);
//                tUserOrganizationRelation.setUserId(tUser.getId());
//                if (CommonConstant.INTEGER_ONE.equals(requestModel.getIsAdmin())) {
//                    tUserOrganizationRelation.setIsAdmin(CommonConstant.INTEGER_ONE);
//                } else {
//                    tUserOrganizationRelation.setIsAdmin(CommonConstant.INTEGER_ZERO);
//                }
//
//                commonService.setBaseEntityAdd(tUserOrganizationRelation, userName);
//                insertUserOrganizationRelationList.add(tUserOrganizationRelation);
//            }
//            tUserOrganizationRelationMapper.batchInsert(insertUserOrganizationRelationList);
//        }
//    }

//    /**
//     * 修改员工信息
//     *
//     * @param requestModel 请求参数
//     */
//    @Transactional
//    public void modifyUserInfo(UserEditRequestModel requestModel) {
//        TUser tUser = tUserMapper.selectByPrimaryKeyDecrypt(requestModel.getUserId());
//        if (tUser == null) {
//            throw new BizException(BasicDataExceptionEnum.USER_INFO_NOT_EXIST);
//        }
//        if (!RegExpValidatorUtils.isMobile(requestModel.getMobilePhone())) {
//            throw new BizException(BasicDataExceptionEnum.MOBILE_FORMAT_ERROR);
//        }
//        TUser tUserAccount = tUserMapper.getByUserAccount(requestModel.getUserAccount());
//        if (tUserAccount != null && !tUserAccount.getId().equals(tUser.getId())) {
//            throw new BizException(BasicDataExceptionEnum.USER_ACCOUNT_EXIST);
//        }
//        TUser tUserByMobile = tUserMapper.getUserByMobile(requestModel.getMobilePhone());
//        if (null != tUserByMobile && !tUserByMobile.getId().equals(tUser.getId())) {
//            throw new BizException(BasicDataExceptionEnum.USER_MOBILE_EXIST);
//        }
//        String userName = BaseContextHandler.getUserName();
//
//        TUser tUserUp = new TUser();
//        if (StringUtils.isBlank(requestModel.getUserCode())) {
//            tUser.setUserCode(generateUserCode());
//        } else {
//            tUser.setUserCode(requestModel.getUserCode());
//        }
//        TUser tUserByCode = tUserMapper.getUserByCode(tUser.getUserCode());
//        if (null != tUserByCode && !tUserByCode.getId().equals(tUser.getId())) {
//            throw new BizException(BasicDataExceptionEnum.USER_CODE_EXIST);
//        }
//        //如果没有设置供应商角色 需要置空
//        if (requestModel.getSupplierId() != null && !requestModel.getSupplierId().equals(CommonConstant.LONG_ZERO)) {
//            if (!checkUserSupplier(requestModel.getRoleIds())) {
//                requestModel.setSupplierId(CommonConstant.LONG_ZERO);
//            }
//        }
//        tUser.setSupplierId(requestModel.getSupplierId());
//        tUserUp.setId(requestModel.getUserId());
//        tUserUp.setUserName(requestModel.getUserName());
//        tUserUp.setUserAccount(requestModel.getUserAccount());
//        tUserUp.setMobilePhone(requestModel.getMobilePhone());
//        tUserUp.setEnabled(requestModel.getEnabled());
//        //如果不相等  表示密码做了修改
////        if (!tUser.getPassword().equals(requestModel.getPassword())) {
////            Pattern passwordPattern = Pattern.compile("^(?![0-9]+$)(?![a-z]+$)(?![A-Z]+$)[0-9A-Za-z]{8,16}$");
////            Matcher password = passwordPattern.matcher(requestModel.getPassword());
////            if (!password.matches()) {
////                throw new BizException(BasicDataExceptionEnum.USER_PASSWORD_ERROR);
////            }
////            tUser.setPassword(YeloBcryptUtils.bcryptForPassword(requestModel.getPassword(), tUser.getUserSalt()));
////        }
//        commonService.setBaseEntityModify(tUserUp, userName);
//        tUserMapper.updateByPrimaryKeySelectiveEncrypt(tUserUp);
//        // 对员工的部门进行的修改
//        List<UserOrgInfoBO> userOrgList = tUserOrganizationRelationMapper.getOrgListByUserId(requestModel.getUserId());
//        List<Long> orgIdList = new ArrayList<>();
//        // 记录变更的 OrgCodeList
//        List<String> originalOrgCodeList = new ArrayList<>();
//        List<Long> orgUserRelationIdList = new ArrayList<>();
//        Map<Long, Long> orgUserRelationIdMap = new HashMap<>();
//        //记录旧的是否是管理员的map 用来判断是否是管理员 是否更新 (上面的userOrganizationRelationMap在更新时会变成更新后的数据)
//        Map<Long, Integer> IsAdminMap = new HashMap<>();
//        userOrgList.forEach(
//                item -> {
//                    orgIdList.add(item.getOrgId());
//                    orgUserRelationIdList.add(item.getId());
//                    orgUserRelationIdMap.put(item.getOrgId(), item.getId());
//                    originalOrgCodeList.add(item.getOrgCode());
//                    IsAdminMap.put(item.getId(), item.getIsAdmin());
//                });
//        List<Long> addOrgIdList = new ArrayList<>();
//        List<Long> existOrgUserRelationIdList = new ArrayList<>();
//        //对传入的值进行分类
//        for (Long orgId : requestModel.getOrganizationIds()) {
//            if (orgIdList.contains(orgId)) {
//                existOrgUserRelationIdList.add(orgUserRelationIdMap.get(orgId));
//            } else {
//                addOrgIdList.add(orgId);
//            }
//        }
//        //添加关系
//        if (ListUtils.isNotEmpty(addOrgIdList)) {
//            TOrganizationUserRelation organizationUserRelation;
//            List<TOrganizationUserRelation> addOrgRelationList = new ArrayList<>();
//            for (Long orgId : addOrgIdList) {
//                organizationUserRelation = new TOrganizationUserRelation();
//                organizationUserRelation.setOrgId(orgId);
//                organizationUserRelation.setUserId(tUser.getId());
//                if (CommonConstant.INTEGER_ONE.equals(requestModel.getIsAdmin())) {
//                    organizationUserRelation.setIsAdmin(CommonConstant.INTEGER_ONE);
//                } else {
//                    organizationUserRelation.setIsAdmin(CommonConstant.INTEGER_ZERO);
//                }
//                commonService.setBaseEntityAdd(organizationUserRelation, userName);
//                addOrgRelationList.add(organizationUserRelation);
//            }
//            tUserOrganizationRelationMapper.batchInsert(addOrgRelationList);
//        }
//        //对存在的数据进行修改  时间 人员
//        if (ListUtils.isNotEmpty(existOrgUserRelationIdList)) {
//            TOrganizationUserRelation organizationUserRelation;
//            List<TOrganizationUserRelation> updateOrgRelationList = new ArrayList<>();
//            for (Long aLong : existOrgUserRelationIdList) {
//                organizationUserRelation = new TOrganizationUserRelation();
//                organizationUserRelation.setId(aLong);
//                commonService.setBaseEntityModify(organizationUserRelation, BaseContextHandler.getUserName());
//                updateOrgRelationList.add(organizationUserRelation);
//            }
//            tUserOrganizationRelationMapper.batchUpdateSelective(updateOrgRelationList);
//        }
//        //获取需要删除的数据
//        orgUserRelationIdList.removeAll(existOrgUserRelationIdList);
//        if (ListUtils.isNotEmpty(orgUserRelationIdList)) {
//            tUserOrganizationRelationMapper.batchDeleteByIds(StringUtils.listToString(orgUserRelationIdList, ','), userName);
//        }
//        //对员工的角色进行的修改
//        List<TUserRole> tUserRoleRelationList = tUserRoleMapper.getByUserId(requestModel.getUserId());
//        List<Long> roleIdList = new ArrayList<>();
//        List<Long> userRoleIdList = new ArrayList<>();
//        Map<Long, Long> userRoleIdMap = new HashMap<>();
//        tUserRoleRelationList.stream().forEach(item -> {
//            roleIdList.add(item.getRoleId());
//            userRoleIdList.add(item.getId());
//            userRoleIdMap.put(item.getRoleId(), item.getId());
//        });
//        List<Long> addRoleIdList = new ArrayList<>();
//        List<Long> existUserRoleIdList = new ArrayList<>();
//        //对传入的角色进行分类
//        for (Long roleId : requestModel.getRoleIds()) {
//            if (roleIdList.contains(roleId)) {
//                existUserRoleIdList.add(userRoleIdMap.get(roleId));
//            } else {
//                addRoleIdList.add(roleId);
//            }
//        }
//        //新增角色关系
//        if (ListUtils.isNotEmpty(addRoleIdList)) {
//            TUserRole userRole;
//            List<TUserRole> addRoleRelationList = new ArrayList<>();
//            for (Long roleId : addRoleIdList) {
//                userRole = new TUserRole();
//                userRole.setRoleId(roleId);
//                userRole.setUserId(tUser.getId());
//                commonService.setBaseEntityAdd(userRole, userName);
//                addRoleRelationList.add(userRole);
//            }
//            if (ListUtils.isNotEmpty(addRoleRelationList)) {
//                tUserRoleMapper.batchInsert(addRoleRelationList);
//            }
//        }
//        //对存在的数据进行修改  时间 人员
//        if (ListUtils.isNotEmpty(existUserRoleIdList)) {
//            TUserRole userRole;
//            List<TUserRole> addRoleRelationList = new ArrayList<>();
//            for (Long aLong : existUserRoleIdList) {
//                userRole = new TUserRole();
//                userRole.setId(aLong);
//                commonService.setBaseEntityModify(userRole, BaseContextHandler.getUserName());
//                addRoleRelationList.add(userRole);
//            }
//            tUserRoleMapper.batchUpdateSelective(addRoleRelationList);
//        }
//        //删除角色
//        userRoleIdList.removeAll(existUserRoleIdList);
//        if (ListUtils.isNotEmpty(userRoleIdList)) {
//            tUserRoleMapper.batchDeleteByIds(StringUtils.listToString(userRoleIdList, ','), userName);
//        }
//        cleanUserToken(tUser);
//    }
//
//    /**
//     * 删除员工信息
//     *
//     * @param requestModel
//     */
//    @Transactional
//    public void deleteUserInfo(UserIdRequestModel requestModel) {
//        TUser tUser = tUserMapper.selectByPrimaryKeyDecrypt(requestModel.getUserId());
//        if (tUser == null) {
//            throw new BizException(BasicDataExceptionEnum.USER_INFO_NOT_EXIST);
//        }
//        TUser tUserUp = new TUser();
//        tUserUp.setId(tUser.getId());
//        tUserUp.setValid(CommonConstant.INTEGER_ZERO);
//        commonService.setBaseEntityModify(tUserUp, BaseContextHandler.getUserName());
//        tUserMapper.updateByPrimaryKeySelectiveEncrypt(tUserUp);
//
//        tUserRoleMapper.batchDeleteByUserIds(ConverterUtils.toString(tUser.getId()), BaseContextHandler.getUserName());
//
//        tUserOrganizationRelationMapper.batchDeleteByUserIds(ConverterUtils.toString(tUser.getId()), BaseContextHandler.getUserName());
//
//        cleanUserToken(tUser);
//    }
//
//    /**
//     * 禁用启用
//     *
//     * @param requestModel
//     */
//    @Transactional
//    public void enabledUserInfo(UserEnabledRequestModel requestModel) {
//        TUser tUser = tUserMapper.selectByPrimaryKeyDecrypt(requestModel.getUserId());
//        if (tUser == null || tUser.getValid().equals(CommonConstant.INTEGER_ZERO)) {
//            throw new BizException(BasicDataExceptionEnum.USER_INFO_NOT_EXIST);
//        }
//        String userName = BaseContextHandler.getUserName();
//        TUser tUserUp = new TUser();
//        tUserUp.setId(tUser.getId());
//        tUserUp.setEnabled(requestModel.getEnabled());
//        commonService.setBaseEntityModify(tUserUp, userName);
//        tUserMapper.updateByPrimaryKeySelectiveEncrypt(tUserUp);
//
//        cleanUserToken(tUser);
//    }
//
//    /**
//     * 清楚用户token
//     *
//     * @param tUser
//     */
//    private void cleanUserToken(TUser tUser) {
//        // 清除token,从head中读取token并清除
//        CleanUserTokenMessage cleanUserTokenMessage = new CleanUserTokenMessage();
//        cleanUserTokenMessage.setUserCode(tUser.getUserAccount());
//        cleanUserTokenMessage.setUserTokenType(JwtApplicationTokenPrefixEnum.MES_MANAGEMENT_WEB.getKey());
//        mesAuthService.cleanUserToken(cleanUserTokenMessage);
//    }
//
//    public GetUserByAccountResponseModel getUserByUserAccount(GetUserByAccountRequestModel requestModel) {
//        TUser byAccount = tUserMapper.getUserByUserAccount(requestModel.getUserAccount());
//        if (byAccount != null) {
//            return MapperUtils.mapper(byAccount, GetUserByAccountResponseModel.class);
//        }
//        return new GetUserByAccountResponseModel();
//
//    }
//
//
//    @Transactional
//    public void certificationUser(CertificationUserRequestModel requestModel) {
//        //验证认证码
//        verifyCodeBiz.checkVerificationCode(requestModel.getMobile(), requestModel.getSmsCode(), CommonConstant.INTEGER_ONE, CommonConstant.INTEGER_ONE, CommonConstant.INTEGER_ONE);
//        TUser tUser = tUserMapper.getUserByMobile(requestModel.getMobile());
//        if (tUser == null) {
//            throw new BizException(BasicDataExceptionEnum.MOBILE_PHONE_NOT_EXIST);
//        }
//        if (!requestModel.getUserAccount().equals(tUser.getUserAccount())) {
//            throw new BizException(BasicDataExceptionEnum.USER_ACCOUNT_NOT_EXIST);
//        }
//    }
//
//    /**
//     * 根据手机号和密码判断权限
//     *
//     * @param requestModel requestModel
//     * @return result
//     */
//    public CheckUserPasswordResModel checkUserPassword(CheckUserPasswordReqModel requestModel) {
//        TUser userInfo = tUserMapper.getUserByMobile(requestModel.getMobile());
//        if (Objects.isNull(userInfo)) {
//            throw new BizException(BasicDataExceptionEnum.CUSTOMER_ACCOUNT_NOT_EXIST);
//        }
//        CheckUserPasswordResModel result = new CheckUserPasswordResModel();
//        result.setUserId(userInfo.getId());
//        result.setMobilePhone(userInfo.getMobilePhone());
//        result.setUserName(userInfo.getUserName());
//        return result;
//    }
//
//    /**
//     * 根据原密码修改新密码
//     *
//     * @param model
//     * @return
//     */
//    @Transactional
//    public void changePassword(ChangePasswordRequestModel model) {
//        Long userId = BaseContextHandler.getUserId();
//        TUser tUser;
//        if (model.getAppId() == null || CommonConstant.INTEGER_ONE.equals(model.getAppId())) {
//            tUser = tUserMapper.selectByPrimaryKeyDecrypt(userId);
//        } else {
//            tUser = tUserMapper.getSuppUserById(userId);
//        }
//        if (tUser == null) {
//            throw new BizException(BasicDataExceptionEnum.USER_INFO_NOT_EXIST);
//        }
//        if (CommonConstant.INTEGER_ONE.equals(model.getChangeType())) {
//            //校验旧密码是否正确
//            if (!tUser.getPassword().equals(YeloBcryptUtils.bcryptForPassword(model.getOldPassword(),
//                    tUser.getUserSalt()))) {
//                throw new BizException(LoginExceptionCodeEnum.USER_NAME_PWD_ERROR);
//            }
//        }
//        TUser updateUser = new TUser();
//        updateUser.setId(userId);
//        updateUser.setPassword(YeloBcryptUtils.bcryptForPassword(model.getNewPassword(), tUser.getUserSalt()));
//        commonService.setBaseEntityModify(updateUser, BaseContextHandler.getUserName());
//        if (model.getAppId() == null || CommonConstant.INTEGER_ONE.equals(model.getAppId())) {
//            tUserMapper.updateByPrimaryKeySelectiveEncrypt(updateUser);
//        } else {
//            tUserMapper.updateSuppByPrimaryKeySelectiveEncrypt(updateUser);
//        }
//
//    }
//
//    /**
//     * 根据用户id修改用户密码
//     *
//     * @param model
//     */
//    @Transactional
//    public void changeUserPassword(ChangeUserPasswordRequestModel model) {
//        TUser tUser = tUserMapper.selectByPrimaryKeyDecrypt(model.getUserId());
//        if (tUser == null) {
//            throw new BizException(BasicDataExceptionEnum.USER_INFO_NOT_EXIST);
//        }
//        TUser updateUser = new TUser();
//        updateUser.setId(model.getUserId());
//        updateUser.setPassword(YeloBcryptUtils.bcryptForPassword(model.getPassword(), tUser.getUserSalt()));
//        commonService.setBaseEntityModify(updateUser, BaseContextHandler.getUserName());
//        tUserMapper.updateByPrimaryKeySelectiveEncrypt(updateUser);
//    }

}