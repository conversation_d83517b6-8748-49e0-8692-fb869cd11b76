package com.yi.service;

import com.yi.controller.shippingorder.model.ShippingOrderRequest;
import com.yi.entity.TShippingOrder;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 发运订单新增功能测试
 */
@SpringBootTest
public class TShippingOrderServiceAddTest {

    @MockBean
    private TShippingOrderService shippingOrderService;

    @MockBean
    private TGeneralFileService generalFileService;

    @Test
    public void testAddShippingOrder_Success() {
        // 准备测试数据
        ShippingOrderRequest request = new ShippingOrderRequest();
        request.setContractCode("CONTRACT001");
        request.setCustomerCompanyId("1");
        request.setWarehouseId("1");
        request.setFirstCategory("1");
        request.setSecondCategory("标准托盘");
        request.setCount("100");
        request.setDemandTime("2024-01-01");
        request.setRemark("测试订单");
        
        List<String> attachmentUrls = Arrays.asList(
            "/uploads/contract/file1.pdf",
            "/uploads/contract/file2.jpg"
        );
        request.setAttachmentUrls(attachmentUrls);

        // 模拟Service调用成功
        when(shippingOrderService.addShippingOrder(request)).thenReturn(true);

        // 调用方法
        boolean result = shippingOrderService.addShippingOrder(request);

        // 验证结果
        assertTrue(result);

        // 验证Service方法被调用
        verify(shippingOrderService, times(1)).addShippingOrder(request);
    }

    @Test
    public void testAddShippingOrder_WithoutAttachments() {
        // 准备测试数据（无附件）
        ShippingOrderRequest request = new ShippingOrderRequest();
        request.setContractCode("CONTRACT002");
        request.setCustomerCompanyId("2");
        request.setWarehouseId("2");
        request.setFirstCategory("1");
        request.setSecondCategory("加强托盘");
        request.setCount("50");
        request.setDemandTime("2024-01-02");
        request.setRemark("无附件订单");
        request.setAttachmentUrls(null); // 无附件

        // 模拟Service调用成功
        when(shippingOrderService.addShippingOrder(request)).thenReturn(true);

        // 调用方法
        boolean result = shippingOrderService.addShippingOrder(request);

        // 验证结果
        assertTrue(result);

        // 验证Service方法被调用
        verify(shippingOrderService, times(1)).addShippingOrder(request);
    }

    @Test
    public void testAddShippingOrder_RequiredFields() {
        // 测试必填字段验证
        ShippingOrderRequest request = new ShippingOrderRequest();
        
        // 只设置必填字段
        request.setContractCode("CONTRACT003");
        request.setCustomerCompanyId("3");
        request.setWarehouseId("3");
        request.setFirstCategory("1");
        request.setCount("200");
        request.setDemandTime("2024-01-03");

        // 模拟Service调用成功
        when(shippingOrderService.addShippingOrder(request)).thenReturn(true);

        // 调用方法
        boolean result = shippingOrderService.addShippingOrder(request);

        // 验证结果
        assertTrue(result);

        // 验证必填字段
        assertNotNull(request.getContractCode());
        assertNotNull(request.getCustomerCompanyId());
        assertNotNull(request.getWarehouseId());
        assertNotNull(request.getFirstCategory());
        assertNotNull(request.getCount());
        assertNotNull(request.getDemandTime());
    }

    @Test
    public void testShippingOrderDefaultValues() {
        // 测试订单默认值设置
        TShippingOrder order = new TShippingOrder();
        
        // 模拟新增订单时的默认值设置
        order.setOrderNo("SO20240101000001"); // 生成的订单号
        order.setStatus(1000); // PENDING - 默认状态
        order.setShippedQuantity(0); // 默认发货数量
        order.setReceivedQuantity(0); // 默认签收数量
        order.setValid(1); // 默认有效

        // 验证默认值
        assertTrue(order.getOrderNo().startsWith("SO"));
        assertEquals("PENDING", order.getStatus());
        assertEquals(Integer.valueOf(0), order.getShippedQuantity());
        assertEquals(Integer.valueOf(0), order.getReceivedQuantity());
        assertEquals(Integer.valueOf(1), order.getValid());
    }

    @Test
    public void testOrderNoGeneration() {
        // 测试订单号生成规则
        String orderNo = "SO20240101000001";
        
        // 验证订单号格式：SO + yyyyMMdd + 6位序号
        assertTrue(orderNo.startsWith("SO"));
        assertEquals(16, orderNo.length()); // SO(2) + 日期(8) + 序号(6)
        
        String dateStr = orderNo.substring(2, 10); // 提取日期部分
        String sequence = orderNo.substring(10); // 提取序号部分
        
        assertEquals(8, dateStr.length()); // 日期长度
        assertEquals(6, sequence.length()); // 序号长度
        assertTrue(sequence.matches("\\d{6}")); // 序号为6位数字
    }

    @Test
    public void testAttachmentUrlsValidation() {
        // 测试附件URL验证
        List<String> validUrls = Arrays.asList(
            "/uploads/shipping/file1.pdf",
            "/uploads/shipping/file2.jpg",
            "/uploads/shipping/file3.png"
        );

        List<String> invalidUrls = Arrays.asList(
            "", // 空字符串
            "   ", // 空白字符串
            null // null值
        );

        // 验证有效URL
        for (String url : validUrls) {
            assertNotNull(url);
            assertFalse(url.trim().isEmpty());
        }

        // 验证无效URL应该被过滤
        for (String url : invalidUrls) {
            if (url != null) {
                assertTrue(url.trim().isEmpty());
            }
        }
    }
}
