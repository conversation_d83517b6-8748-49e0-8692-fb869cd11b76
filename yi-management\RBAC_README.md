# RBAC权限管理系统

## 概述

本项目实现了基于角色的访问控制（RBAC）权限管理系统，包含用户、角色、资源（菜单/按钮/接口）、部门等核心功能模块。

## 数据库表结构

### 核心表

1. **sys_user** - 系统用户表
   - 存储用户基本信息：用户名、密码、真实姓名、邮箱、手机号等
   - 支持用户状态管理、部门关联

2. **sys_role** - 系统角色表
   - 存储角色信息：角色编码、角色名称、描述、数据权限范围等
   - 支持角色排序、状态管理

3. **sys_resource** - 系统资源表
   - 存储菜单、按钮、接口权限信息
   - 支持树形结构、权限标识、路由配置

4. **sys_dept** - 部门表
   - 存储部门信息，支持树形结构
   - 用于数据权限控制

### 关联表

5. **sys_user_role** - 用户角色关联表
   - 多对多关系：一个用户可以有多个角色，一个角色可以分配给多个用户

6. **sys_role_resource** - 角色资源关联表
   - 多对多关系：一个角色可以有多个权限，一个权限可以分配给多个角色

## 文件结构

```
src/main/
├── java/com/yi/
│   ├── entity/                 # 实体类
│   │   ├── SysUser.java       # 用户实体
│   │   ├── SysRole.java       # 角色实体
│   │   ├── SysResource.java   # 资源实体
│   │   ├── SysDept.java       # 部门实体
│   │   ├── SysUserRole.java   # 用户角色关联实体
│   │   └── SysRoleResource.java # 角色资源关联实体
│   ├── mapper/                # Mapper接口
│   │   ├── SysUserMapper.java
│   │   ├── SysRoleMapper.java
│   │   ├── SysResourceMapper.java
│   │   ├── SysDeptMapper.java
│   │   ├── SysUserRoleMapper.java
│   │   └── SysRoleResourceMapper.java
│   └── service/               # Service层
│       ├── SysUserService.java
│       ├── SysRoleService.java
│       ├── SysResourceService.java
│       ├── SysDeptService.java
│       ├── SysUserRoleService.java
│       ├── SysRoleResourceService.java
│       └── impl/              # Service实现类
│           ├── SysUserServiceImpl.java
│           ├── SysUserRoleServiceImpl.java
│           └── SysRoleResourceServiceImpl.java
└── resources/
    ├── mapper/                # MyBatis XML映射文件
    │   ├── SysUserMapper.xml
    │   ├── SysRoleMapper.xml
    │   ├── SysResourceMapper.xml
    │   ├── SysDeptMapper.xml
    │   ├── SysUserRoleMapper.xml
    │   └── SysRoleResourceMapper.xml
    └── sql/                   # SQL脚本
        ├── rbac_ddl.sql       # 表结构创建脚本
        └── rbac_dml.sql       # 初始化数据脚本
```

## 使用说明

### 1. 数据库初始化

```sql
-- 1. 执行表结构创建脚本
source src/main/resources/sql/rbac_ddl.sql;

-- 2. 执行初始化数据脚本
source src/main/resources/sql/rbac_dml.sql;
```

### 2. 默认账户

系统初始化后会创建以下默认账户（密码均为：123456）：

- **admin** - 超级管理员（拥有所有权限）
- **user_manager** - 用户管理员（用户管理相关权限）
- **business_user** - 业务用户（业务操作权限）
- **guest** - 访客（只读权限）

### 3. 权限设计

#### 资源类型
- **1** - 目录（一级菜单）
- **2** - 菜单（二级菜单）
- **3** - 按钮（操作权限）
- **4** - 接口（API权限）

#### 数据权限范围
- **1** - 全部数据权限
- **2** - 自定数据权限
- **3** - 本部门数据权限
- **4** - 本部门及以下数据权限
- **5** - 仅本人数据权限

### 4. 核心功能

#### 用户管理
- 用户CRUD操作
- 密码重置/修改
- 用户角色分配
- 用户状态管理

#### 角色管理
- 角色CRUD操作
- 角色权限分配
- 角色状态管理

#### 权限管理
- 菜单权限管理
- 按钮权限管理
- 接口权限管理
- 树形结构展示

#### 部门管理
- 部门CRUD操作
- 树形结构管理
- 数据权限控制

### 5. 扩展说明

如需扩展功能，可以：

1. **添加新的资源类型**：在sys_resource表中添加新的resource_type
2. **扩展用户属性**：在SysUser实体中添加新字段
3. **自定义权限验证**：实现自定义的权限验证逻辑
4. **集成Spring Security**：结合Spring Security实现完整的认证授权

### 6. 注意事项

1. 所有表都使用逻辑删除（valid字段）
2. 密码使用MD5加密存储
3. 支持MyBatis-Plus的自动填充功能
4. 所有关联查询都考虑了valid和status字段的过滤

## API示例

```java
// 用户服务使用示例
@Autowired
private SysUserService sysUserService;

// 分页查询用户
Page<SysUser> page = new Page<>(1, 10);
IPage<SysUser> result = sysUserService.selectUserPage(page, "admin", null, null, null, null, 1);

// 查询用户权限
List<String> permissions = sysUserService.selectPermissionsByUserId(1L);

// 分配用户角色
List<Long> roleIds = Arrays.asList(1L, 2L);
sysUserService.assignRoles(1L, roleIds);
```

这个RBAC系统提供了完整的权限管理功能，可以根据实际需求进行扩展和定制。
