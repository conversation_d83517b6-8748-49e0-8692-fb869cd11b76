﻿<!DOCTYPE html>
<html>
  <head>
    <title>供应商仓库添加/编辑/详情</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/供应商仓库添加_编辑_详情/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/供应商仓库添加_编辑_详情/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (线段) -->
      <div id="u2716" class="ax_default line1">
        <img id="u2716_img" class="img " src="images/客户管理/u350.svg"/>
        <div id="u2716_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2717" class="ax_default box_21">
        <div id="u2717_div" class=""></div>
        <div id="u2717_text" class="text ">
          <p><span>供应商仓库新增/编辑/详情&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; X</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2718" class="ax_default link_button">
        <div id="u2718_div" class=""></div>
        <div id="u2718_text" class="text ">
          <p><span>刷新本页</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2719" class="ax_default label">
        <div id="u2719_div" class=""></div>
        <div id="u2719_text" class="text ">
          <p><span>*供应商名称</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2720" class="ax_default label">
        <div id="u2720_div" class=""></div>
        <div id="u2720_text" class="text ">
          <p><span>*仓库名称</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u2721" class="ax_default text_field">
        <div id="u2721_div" class=""></div>
        <input id="u2721_input" type="text" value="" class="u2721_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2722" class="ax_default label">
        <div id="u2722_div" class=""></div>
        <div id="u2722_text" class="text ">
          <p><span>*地址详情</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u2723" class="ax_default text_field">
        <div id="u2723_div" class=""></div>
        <input id="u2723_input" type="text" value="" class="u2723_input"/>
      </div>

      <!-- Unnamed (下拉列表) -->
      <div id="u2724" class="ax_default droplist">
        <div id="u2724_div" class=""></div>
        <select id="u2724_input" class="u2724_input">
        </select>
      </div>

      <!-- Unnamed (下拉列表) -->
      <div id="u2725" class="ax_default droplist">
        <div id="u2725_div" class=""></div>
        <select id="u2725_input" class="u2725_input">
        </select>
      </div>

      <!-- Unnamed (下拉列表) -->
      <div id="u2726" class="ax_default droplist">
        <div id="u2726_div" class=""></div>
        <select id="u2726_input" class="u2726_input">
        </select>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2727" class="ax_default label">
        <div id="u2727_div" class=""></div>
        <div id="u2727_text" class="text ">
          <p><span>省</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2728" class="ax_default label">
        <div id="u2728_div" class=""></div>
        <div id="u2728_text" class="text ">
          <p><span>市</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2729" class="ax_default label">
        <div id="u2729_div" class=""></div>
        <div id="u2729_text" class="text ">
          <p><span>区</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2730" class="ax_default label">
        <div id="u2730_div" class=""></div>
        <div id="u2730_text" class="text ">
          <p><span>*收货人</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u2731" class="ax_default text_field">
        <div id="u2731_div" class=""></div>
        <input id="u2731_input" type="text" value="" class="u2731_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2732" class="ax_default label">
        <div id="u2732_div" class=""></div>
        <div id="u2732_text" class="text ">
          <p><span>*手机号</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u2733" class="ax_default text_field">
        <div id="u2733_div" class=""></div>
        <input id="u2733_input" type="text" value="" class="u2733_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2734" class="ax_default primary_button">
        <div id="u2734_div" class=""></div>
        <div id="u2734_text" class="text ">
          <p><span>保存</span></p>
        </div>
      </div>

      <!-- Unnamed (下拉列表) -->
      <div id="u2735" class="ax_default droplist">
        <div id="u2735_div" class=""></div>
        <select id="u2735_input" class="u2735_input">
        </select>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
