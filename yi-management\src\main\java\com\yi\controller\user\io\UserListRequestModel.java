package com.yi.controller.user.io;


import com.yi.utils.AbstractPageForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class UserListRequestModel extends AbstractPageForm<UserListRequestModel> {
    @ApiModelProperty("部门id")
    private String organizationIds;
    @ApiModelProperty("角色id")
    private String roleIds;
    @ApiModelProperty("员工姓名/电话")
    private String userKeyWord;
    @ApiModelProperty("禁启用状态")
    private Integer enabled;
    @ApiModelProperty("登录账号")
    private String userAccount;
    @ApiModelProperty("姓名")
    private String userName;
    @ApiModelProperty("手机号")
    private String mobilePhone;
}
