package com.yi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yi.entity.TCustomerCompany;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户公司表 Mapper 接口
 */
@Mapper
public interface TCustomerCompanyMapper extends BaseMapper<TCustomerCompany> {

    /**
     * 分页查询客户公司列表
     *
     * @param page 分页参数
     * @param companyName 公司名称（模糊查询）
     * @param customerCompanyType 公司类型
     * @param enabled 启用状态
     * @return 分页结果
     */
    IPage<TCustomerCompany> selectCustomerCompanyPage(Page<TCustomerCompany> page,
                                                      @Param("companyName") String companyName,
                                                      @Param("customerCompanyType") String customerCompanyType,
                                                      @Param("enabled") Integer enabled);

    /**
     * 根据公司名称查询客户公司
     *
     * @param companyName 公司名称
     * @return 客户公司列表
     */
    List<TCustomerCompany> selectByCompanyName(@Param("companyName") String companyName);

    /**
     * 根据公司类型查询客户公司
     *
     * @param customerCompanyType 公司类型
     * @return 客户公司列表
     */
    List<TCustomerCompany> selectByCompanyType(@Param("customerCompanyType") String customerCompanyType);
}
