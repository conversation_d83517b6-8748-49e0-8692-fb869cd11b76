package com.yi.enums;

/**
 * 通用文件类型枚举
 */
public enum GeneralFileTypeEnum {

    SALES_CONTRACT(1, "销售合同"),
    SHIPPING_ORDER(2, "发运订单"),
    SUPPLIER_BUSINESS_LICENSE(3, "供应商营业执照");
    
    private final Integer code;
    private final String name;
    
    GeneralFileTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    /**
     * 根据code获取枚举
     */
    public static GeneralFileTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (GeneralFileTypeEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
    
    /**
     * 根据code获取名称
     */
    public static String getNameByCode(Integer code) {
        GeneralFileTypeEnum item = getByCode(code);
        return item != null ? item.getName() : "";
    }
}
