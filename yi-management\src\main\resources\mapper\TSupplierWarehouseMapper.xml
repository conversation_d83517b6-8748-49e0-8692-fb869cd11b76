<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yi.mapper.TSupplierWarehouseMapper">

    <!-- 分页查询供应商仓库列表 -->
    <select id="selectSupplierWarehousePage" resultType="com.yi.mapper.vo.SupplierWarehousePageVO">
        SELECT 
            sw.id,
            sw.supplier_id,
            s.supplier_name,
            sw.warehouse_name,
            sw.province_name,
            sw.city_name,
            sw.area_name,
            sw.detailed_address,
            sw.contact_person,
            sw.contact_phone,
            sw.enabled,
            sw.created_by,
            sw.created_time,
            sw.last_modified_by,
            sw.last_modified_time,
            sw.valid
        FROM t_supplier_warehouse sw
        LEFT JOIN t_supplier s ON sw.supplier_id = s.id AND s.valid = 1
        WHERE sw.valid = 1
        <if test="request.enabled != null">
            AND sw.enabled = #{request.enabled}
        </if>
        <if test="request.supplierName != null and request.supplierName != ''">
            AND s.supplier_name LIKE CONCAT('%', #{request.supplierName}, '%')
        </if>
        <if test="request.warehouseName != null and request.warehouseName != ''">
            AND sw.warehouse_name LIKE CONCAT('%', #{request.warehouseName}, '%')
        </if>
        <if test="request.address != null and request.address != ''">
            AND (
                sw.province_name LIKE CONCAT('%', #{request.address}, '%')
                OR sw.city_name LIKE CONCAT('%', #{request.address}, '%')
                OR sw.area_name LIKE CONCAT('%', #{request.address}, '%')
                OR sw.detailed_address LIKE CONCAT('%', #{request.address}, '%')
            )
        </if>
        ORDER BY sw.created_time DESC
    </select>

    <!-- 根据ID查询供应商仓库详情（联查供应商名称） -->
    <select id="selectSupplierWarehouseDetailById" resultType="com.yi.mapper.vo.SupplierWarehouseDetailVO">
        SELECT
            sw.id,
            s.supplier_name AS supplierName,
            sw.warehouse_name AS warehouseName,
            sw.province_name AS provinceName,
            sw.city_name AS cityName,
            sw.area_name AS areaName,
            sw.detailed_address AS detailedAddress,
            sw.contact_person AS contactPerson,
            sw.contact_phone AS contactPhone
        FROM t_supplier_warehouse sw
        LEFT JOIN t_supplier s ON sw.supplier_id = s.id AND s.valid = 1
        WHERE sw.id = #{id}
          AND sw.valid = 1
    </select>

</mapper>
