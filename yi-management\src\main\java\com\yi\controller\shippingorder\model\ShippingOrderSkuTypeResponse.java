package com.yi.controller.shippingorder.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 发运订单SKU类型响应
 */
@Data
@ApiModel(value = "ShippingOrderSkuTypeResponse", description = "发运订单SKU类型响应")
public class ShippingOrderSkuTypeResponse {

    @ApiModelProperty(value = "一级类目ID")
    private String firstCategoryId;

    @ApiModelProperty(value = "一级类目名称")
    private String firstCategoryName;

    @ApiModelProperty(value = "二级类目")
    private String secondCategory;

    @ApiModelProperty(value = "显示文本（一级类目名称 二级类目）")
    private String displayText;
}
