package com.yi.controller.user.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

/**
 * 用户导出VO
 */
@Data
@HeadRowHeight(20)
@ContentRowHeight(18)
public class UserExportVO {

    @ExcelProperty(value = "状态")
    @ColumnWidth(10)
    private String status;

    @ExcelProperty(value = "用户名")
    @ColumnWidth(15)
    private String username;

    @ExcelProperty(value = "真实姓名")
    @ColumnWidth(15)
    private String realName;

    @ExcelProperty(value = "邮箱")
    @ColumnWidth(25)
    private String email;

    @ExcelProperty(value = "手机号")
    @ColumnWidth(15)
    private String phone;

    @ExcelProperty(value = "性别")
    @ColumnWidth(8)
    private String gender;

    @ExcelProperty(value = "生日")
    @ColumnWidth(12)
    private String birthday;

    @ExcelProperty(value = "部门")
    @ColumnWidth(20)
    private String deptName;

    @ExcelProperty(value = "角色")
    @ColumnWidth(30)
    private String roles;

    @ExcelProperty(value = "最后登录IP")
    @ColumnWidth(15)
    private String loginIp;

    @ExcelProperty(value = "最后登录时间")
    @ColumnWidth(20)
    private String loginTime;

    @ExcelProperty(value = "创建人")
    @ColumnWidth(12)
    private String createdBy;

    @ExcelProperty(value = "创建时间")
    @ColumnWidth(20)
    private String createdTime;
}
