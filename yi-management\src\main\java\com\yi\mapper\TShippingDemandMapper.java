package com.yi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yi.entity.TShippingDemand;
import com.yi.mapper.vo.ShippingDemandPageVO;
import com.yi.mapper.vo.ShippingDemandDetailVO;
import com.yi.controller.shippingdemand.model.ShippingDemandQueryRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 发货需求表 Mapper 接口
 */
@Mapper
public interface TShippingDemandMapper extends BaseMapper<TShippingDemand> {

    /**
     * 分页查询发货需求列表
     *
     * @param page 分页参数
     * @param request 查询条件
     * @return 分页结果
     */
    IPage<ShippingDemandPageVO> selectShippingDemandPage(Page<ShippingDemandPageVO> page, 
                                                         @Param("request") ShippingDemandQueryRequest request);

    /**
     * 查询发货需求列表（不分页）
     *
     * @param request 查询条件
     * @return 需求列表
     */
    List<ShippingDemandPageVO> selectShippingDemandList(@Param("request") ShippingDemandQueryRequest request);

    /**
     * 根据ID查询发货需求详情
     *
     * @param id 需求ID
     * @return 需求详情
     */
    ShippingDemandDetailVO selectShippingDemandDetailById(@Param("id") Long id);



    /**
     * 根据订单号查询发货需求列表
     *
     * @param orderNo 订单号
     * @return 需求列表
     */
    List<TShippingDemand> selectByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 根据客户公司ID查询发货需求数量
     *
     * @param customerCompanyId 客户公司ID
     * @return 需求数量
     */
    int countByCustomerCompanyId(@Param("customerCompanyId") Long customerCompanyId);

    /**
     * 根据仓库ID查询发货需求数量
     *
     * @param warehouseId 仓库ID
     * @return 需求数量
     */
    int countByWarehouseId(@Param("warehouseId") Long warehouseId);

    /**
     * 根据状态查询发货需求列表
     *
     * @param status 需求状态
     * @return 需求列表
     */
    List<TShippingDemand> selectByStatus(@Param("status") String status);

    /**
     * 更新发货需求状态
     *
     * @param id 需求ID
     * @param status 新状态
     * @param lastModifiedBy 修改人
     * @return 更新行数
     */
    int updateStatus(@Param("id") Long id, 
                    @Param("status") String status, 
                    @Param("lastModifiedBy") String lastModifiedBy);

    /**
     * 更新发货数量
     *
     * @param id 需求ID
     * @param shippedQuantity 发货数量
     * @param lastModifiedBy 修改人
     * @return 更新行数
     */
    int updateShippedQuantity(@Param("id") Long id, 
                             @Param("shippedQuantity") Integer shippedQuantity, 
                             @Param("lastModifiedBy") String lastModifiedBy);

    /**
     * 更新待确认数量
     *
     * @param id 需求ID
     * @param pendingQuantity 待确认数量
     * @param lastModifiedBy 修改人
     * @return 更新行数
     */
    int updatePendingQuantity(@Param("id") Long id, 
                             @Param("pendingQuantity") Integer pendingQuantity, 
                             @Param("lastModifiedBy") String lastModifiedBy);

    /**
     * 更新未执行数量
     *
     * @param id 需求ID
     * @param unexecutedQuantity 未执行数量
     * @param lastModifiedBy 修改人
     * @return 更新行数
     */
    int updateUnexecutedQuantity(@Param("id") Long id, 
                                @Param("unexecutedQuantity") Integer unexecutedQuantity, 
                                @Param("lastModifiedBy") String lastModifiedBy);
}
