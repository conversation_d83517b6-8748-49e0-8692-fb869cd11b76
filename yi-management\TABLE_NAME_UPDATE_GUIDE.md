# 数据库表名更新说明

## 概述

已将所有RBAC相关的数据库表名从 `sys_` 开头改为 `t_` 开头，以符合项目的命名规范。

## 表名变更对照

| 原表名 | 新表名 | 说明 |
|--------|--------|------|
| `sys_user` | `t_sys_user` | 系统用户表 |
| `sys_role` | `t_sys_role` | 系统角色表 |
| `sys_resource` | `t_sys_resource` | 系统资源表（菜单/按钮/接口权限） |
| `sys_user_role` | `t_sys_user_role` | 用户角色关联表 |
| `sys_role_resource` | `t_sys_role_resource` | 角色资源关联表 |
| `sys_dept` | `t_sys_dept` | 部门表 |

## 修改的文件清单

### 1. SQL脚本文件
- ✅ `src/main/resources/sql/rbac_ddl.sql` - 表结构创建脚本
- ✅ `src/main/resources/sql/rbac_dml.sql` - 初始化数据脚本

### 2. 实体类 @TableName 注解
- ✅ `src/main/java/com/yi/entity/SysUser.java`
- ✅ `src/main/java/com/yi/entity/SysRole.java`
- ✅ `src/main/java/com/yi/entity/SysResource.java`
- ✅ `src/main/java/com/yi/entity/SysUserRole.java`
- ✅ `src/main/java/com/yi/entity/SysRoleResource.java`
- ✅ `src/main/java/com/yi/entity/SysDept.java`

### 3. Mapper XML 映射文件
- ✅ `src/main/resources/mapper/SysUserMapper.xml`
- ✅ `src/main/resources/mapper/SysRoleMapper.xml`
- ✅ `src/main/resources/mapper/SysResourceMapper.xml`
- ✅ `src/main/resources/mapper/SysUserRoleMapper.xml`
- ✅ `src/main/resources/mapper/SysRoleResourceMapper.xml`
- ✅ `src/main/resources/mapper/SysDeptMapper.xml`

## 数据库迁移步骤

### 方案一：全新安装（推荐）
如果是全新环境，直接执行更新后的SQL脚本：

```sql
-- 1. 执行表结构创建
source src/main/resources/sql/rbac_ddl.sql;

-- 2. 执行初始化数据
source src/main/resources/sql/rbac_dml.sql;
```

### 方案二：现有数据迁移
如果已有 `sys_` 开头的表和数据，需要进行数据迁移：

```sql
-- 1. 创建新表结构
source src/main/resources/sql/rbac_ddl.sql;

-- 2. 迁移数据（如果有现有数据）
INSERT INTO t_sys_dept SELECT * FROM sys_dept;
INSERT INTO t_sys_role SELECT * FROM sys_role;
INSERT INTO t_sys_user SELECT * FROM sys_user;
INSERT INTO t_sys_resource SELECT * FROM sys_resource;
INSERT INTO t_sys_user_role SELECT * FROM sys_user_role;
INSERT INTO t_sys_role_resource SELECT * FROM sys_role_resource;

-- 3. 验证数据迁移
SELECT COUNT(*) FROM t_sys_user;
SELECT COUNT(*) FROM t_sys_role;
SELECT COUNT(*) FROM t_sys_resource;
SELECT COUNT(*) FROM t_sys_user_role;
SELECT COUNT(*) FROM t_sys_role_resource;
SELECT COUNT(*) FROM t_sys_dept;

-- 4. 确认无误后删除旧表（谨慎操作）
-- DROP TABLE sys_user;
-- DROP TABLE sys_role;
-- DROP TABLE sys_resource;
-- DROP TABLE sys_user_role;
-- DROP TABLE sys_role_resource;
-- DROP TABLE sys_dept;
```

## 验证清单

### 1. 编译验证
```bash
mvn clean compile
```
确保没有编译错误。

### 2. 数据库连接验证
启动应用，检查是否能正常连接数据库并操作新表。

### 3. 功能验证
- ✅ 用户登录功能
- ✅ 用户管理CRUD操作
- ✅ 角色管理功能
- ✅ 权限分配功能
- ✅ 部门管理功能

### 4. API测试
使用 `API_TEST_EXAMPLES.md` 中的测试用例验证所有接口功能。

## 注意事项

### 1. 数据备份
在执行任何数据库操作前，请务必备份现有数据：
```sql
mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 2. 环境一致性
确保开发、测试、生产环境都使用相同的表名规范。

### 3. 配置文件检查
检查是否有其他配置文件或脚本中硬编码了旧的表名。

### 4. 缓存清理
如果使用了MyBatis缓存或其他缓存机制，可能需要清理缓存。

## 回滚方案

如果需要回滚到原来的表名，可以：

1. **代码回滚**：使用Git回滚到修改前的版本
2. **数据库回滚**：如果已经迁移数据，可以重命名表：
```sql
RENAME TABLE t_sys_user TO sys_user;
RENAME TABLE t_sys_role TO sys_role;
RENAME TABLE t_sys_resource TO sys_resource;
RENAME TABLE t_sys_user_role TO sys_user_role;
RENAME TABLE t_sys_role_resource TO sys_role_resource;
RENAME TABLE t_sys_dept TO sys_dept;
```

## 相关文档

- `RBAC_README.md` - RBAC系统使用说明
- `USER_MANAGEMENT_README.md` - 账号管理模块说明
- `API_TEST_EXAMPLES.md` - API测试示例

## 更新日志

- **2024-12-XX**: 完成所有RBAC表名从 `sys_` 到 `t_` 的迁移
- 修改了6个实体类的@TableName注解
- 更新了6个Mapper XML文件中的所有表名引用
- 更新了DDL和DML脚本

---

**重要提醒**：在生产环境执行此更新前，请务必在测试环境充分验证，并做好数据备份！
