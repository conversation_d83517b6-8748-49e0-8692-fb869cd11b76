﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-36px;
  width:2069px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u28_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2001px;
  height:2px;
}
#u28 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:151px;
  width:2000px;
  height:1px;
  display:flex;
}
#u28 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u28_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u29_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:901px;
  height:2px;
}
#u29 {
  border-width:0px;
  position:absolute;
  left:550px;
  top:241px;
  width:900px;
  height:1px;
  display:flex;
}
#u29 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u29_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u30_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:1701px;
}
#u30 {
  border-width:0px;
  position:absolute;
  left:822px;
  top:185px;
  width:1px;
  height:1700px;
  display:flex;
}
#u30 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u30_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:1701px;
}
#u31 {
  border-width:0px;
  position:absolute;
  left:1144px;
  top:185px;
  width:1px;
  height:1700px;
  display:flex;
}
#u31 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u31_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u32_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u32 {
  border-width:0px;
  position:absolute;
  left:649px;
  top:204px;
  width:72px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u32 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u32_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u33_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u33 {
  border-width:0px;
  position:absolute;
  left:961px;
  top:204px;
  width:80px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u33 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u33_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u34_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u34 {
  border-width:0px;
  position:absolute;
  left:658px;
  top:285px;
  width:100px;
  height:60px;
  display:flex;
}
#u34 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u34_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u35_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u35 {
  border-width:0px;
  position:absolute;
  left:1289px;
  top:204px;
  width:74px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u35 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u35_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u36_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:1801px;
}
#u36 {
  border-width:0px;
  position:absolute;
  left:468px;
  top:92px;
  width:1px;
  height:1800px;
  display:flex;
}
#u36 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u36_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u37_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:1801px;
}
#u37 {
  border-width:0px;
  position:absolute;
  left:1536px;
  top:92px;
  width:1px;
  height:1800px;
  display:flex;
}
#u37 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u37_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u38_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u38 {
  border-width:0px;
  position:absolute;
  left:924px;
  top:92px;
  width:144px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u38 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u38_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u39_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u39 {
  border-width:0px;
  position:absolute;
  left:1760px;
  top:92px;
  width:71px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u39 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u39_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u40_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u40 {
  border-width:0px;
  position:absolute;
  left:199px;
  top:92px;
  width:108px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u40 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u40_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u41_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u41 {
  border-width:0px;
  position:absolute;
  left:658px;
  top:405px;
  width:100px;
  height:60px;
  display:flex;
}
#u41 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u41_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u42_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u42 {
  border-width:0px;
  position:absolute;
  left:658px;
  top:525px;
  width:100px;
  height:60px;
  display:flex;
}
#u42 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u42_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u43_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u43 {
  border-width:0px;
  position:absolute;
  left:948px;
  top:285px;
  width:100px;
  height:60px;
  display:flex;
}
#u43 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u43_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u44_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
}
#u44 {
  border-width:0px;
  position:absolute;
  left:948px;
  top:405px;
  width:100px;
  height:60px;
  display:flex;
}
#u44 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u44_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u45_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u45 {
  border-width:0px;
  position:absolute;
  left:1030px;
  top:525px;
  width:100px;
  height:60px;
  display:flex;
}
#u45 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u45_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u46_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:61px;
}
#u46 {
  border-width:0px;
  position:absolute;
  left:870px;
  top:525px;
  width:100px;
  height:60px;
  display:flex;
}
#u46 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u46_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u47 {
  border-width:0px;
  position:absolute;
  left:708px;
  top:345px;
  width:0px;
  height:0px;
}
#u47_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:65px;
}
#u47_seg1 {
  border-width:0px;
  position:absolute;
  left:-9px;
  top:47px;
  width:18px;
  height:19px;
}
#u47_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:22px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u48 {
  border-width:0px;
  position:absolute;
  left:708px;
  top:465px;
  width:0px;
  height:0px;
}
#u48_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:65px;
}
#u48_seg1 {
  border-width:0px;
  position:absolute;
  left:-9px;
  top:47px;
  width:18px;
  height:19px;
}
#u48_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:22px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u49 {
  border-width:0px;
  position:absolute;
  left:758px;
  top:555px;
  width:0px;
  height:0px;
}
#u49_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:97px;
  height:10px;
}
#u49_seg1 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:-245px;
  width:10px;
  height:250px;
}
#u49_seg2 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:-245px;
  width:103px;
  height:10px;
}
#u49_seg3 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:-249px;
  width:19px;
  height:18px;
}
#u49_text {
  border-width:0px;
  position:absolute;
  left:42px;
  top:-131px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u50 {
  border-width:0px;
  position:absolute;
  left:998px;
  top:345px;
  width:0px;
  height:0px;
}
#u50_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:65px;
}
#u50_seg1 {
  border-width:0px;
  position:absolute;
  left:-9px;
  top:47px;
  width:18px;
  height:19px;
}
#u50_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:22px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u51 {
  border-width:0px;
  position:absolute;
  left:948px;
  top:435px;
  width:0px;
  height:0px;
}
#u51_seg0 {
  border-width:0px;
  position:absolute;
  left:-33px;
  top:-5px;
  width:33px;
  height:10px;
}
#u51_seg1 {
  border-width:0px;
  position:absolute;
  left:-33px;
  top:-5px;
  width:10px;
  height:95px;
}
#u51_seg2 {
  border-width:0px;
  position:absolute;
  left:-37px;
  top:77px;
  width:18px;
  height:19px;
}
#u51_text {
  border-width:0px;
  position:absolute;
  left:-78px;
  top:24px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u52 {
  border-width:0px;
  position:absolute;
  left:1048px;
  top:435px;
  width:0px;
  height:0px;
}
#u52_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:37px;
  height:10px;
}
#u52_seg1 {
  border-width:0px;
  position:absolute;
  left:27px;
  top:-5px;
  width:10px;
  height:95px;
}
#u52_seg2 {
  border-width:0px;
  position:absolute;
  left:23px;
  top:77px;
  width:18px;
  height:19px;
}
#u52_text {
  border-width:0px;
  position:absolute;
  left:-18px;
  top:22px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u53_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u53 {
  border-width:0px;
  position:absolute;
  left:1276px;
  top:285px;
  width:100px;
  height:60px;
  display:flex;
}
#u53 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u53_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u54_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u54 {
  border-width:0px;
  position:absolute;
  left:1276px;
  top:405px;
  width:100px;
  height:60px;
  display:flex;
}
#u54 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u54_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u55_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u55 {
  border-width:0px;
  position:absolute;
  left:611px;
  top:832px;
  width:100px;
  height:60px;
  display:flex;
}
#u55 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u55_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u56_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u56 {
  border-width:0px;
  position:absolute;
  left:948px;
  top:731px;
  width:100px;
  height:60px;
  display:flex;
}
#u56 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u56_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u57 {
  border-width:0px;
  position:absolute;
  left:920px;
  top:585px;
  width:0px;
  height:0px;
}
#u57_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:20px;
}
#u57_seg1 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:10px;
  width:350px;
  height:10px;
}
#u57_seg2 {
  border-width:0px;
  position:absolute;
  left:335px;
  top:-275px;
  width:10px;
  height:295px;
}
#u57_seg3 {
  border-width:0px;
  position:absolute;
  left:335px;
  top:-275px;
  width:21px;
  height:10px;
}
#u57_seg4 {
  border-width:0px;
  position:absolute;
  left:343px;
  top:-279px;
  width:19px;
  height:18px;
}
#u57_text {
  border-width:0px;
  position:absolute;
  left:263px;
  top:7px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u58 {
  border-width:0px;
  position:absolute;
  left:1130px;
  top:555px;
  width:0px;
  height:0px;
}
#u58_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:75px;
  height:10px;
}
#u58_seg1 {
  border-width:0px;
  position:absolute;
  left:65px;
  top:-245px;
  width:10px;
  height:250px;
}
#u58_seg2 {
  border-width:0px;
  position:absolute;
  left:65px;
  top:-245px;
  width:81px;
  height:10px;
}
#u58_seg3 {
  border-width:0px;
  position:absolute;
  left:133px;
  top:-249px;
  width:19px;
  height:18px;
}
#u58_text {
  border-width:0px;
  position:absolute;
  left:20px;
  top:-131px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u59 {
  border-width:0px;
  position:absolute;
  left:1326px;
  top:345px;
  width:0px;
  height:0px;
}
#u59_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:65px;
}
#u59_seg1 {
  border-width:0px;
  position:absolute;
  left:-9px;
  top:47px;
  width:18px;
  height:19px;
}
#u59_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:22px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u60 {
  border-width:0px;
  position:absolute;
  left:1326px;
  top:465px;
  width:0px;
  height:0px;
}
#u60_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:183px;
}
#u60_seg1 {
  border-width:0px;
  position:absolute;
  left:-333px;
  top:173px;
  width:338px;
  height:10px;
}
#u60_seg2 {
  border-width:0px;
  position:absolute;
  left:-333px;
  top:173px;
  width:10px;
  height:93px;
}
#u60_seg3 {
  border-width:0px;
  position:absolute;
  left:-337px;
  top:253px;
  width:18px;
  height:19px;
}
#u60_text {
  border-width:0px;
  position:absolute;
  left:-169px;
  top:170px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u61_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u61 {
  border-width:0px;
  position:absolute;
  left:1723px;
  top:165px;
  width:100px;
  height:60px;
  display:flex;
}
#u61 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u61_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u62_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u62 {
  border-width:0px;
  position:absolute;
  left:1723px;
  top:285px;
  width:100px;
  height:60px;
  display:flex;
}
#u62 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u62_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u63_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u63 {
  border-width:0px;
  position:absolute;
  left:1593px;
  top:525px;
  width:100px;
  height:60px;
  display:flex;
}
#u63 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u63_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u64_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u64 {
  border-width:0px;
  position:absolute;
  left:1833px;
  top:525px;
  width:100px;
  height:60px;
  display:flex;
}
#u64 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u64_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u65 {
  border-width:0px;
  position:absolute;
  left:1773px;
  top:225px;
  width:0px;
  height:0px;
}
#u65_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:65px;
}
#u65_seg1 {
  border-width:0px;
  position:absolute;
  left:-9px;
  top:47px;
  width:18px;
  height:19px;
}
#u65_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:22px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u66 {
  border-width:0px;
  position:absolute;
  left:1773px;
  top:345px;
  width:0px;
  height:0px;
}
#u66_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:95px;
}
#u66_seg1 {
  border-width:0px;
  position:absolute;
  left:-135px;
  top:85px;
  width:140px;
  height:10px;
}
#u66_seg2 {
  border-width:0px;
  position:absolute;
  left:-135px;
  top:85px;
  width:10px;
  height:95px;
}
#u66_seg3 {
  border-width:0px;
  position:absolute;
  left:-139px;
  top:167px;
  width:18px;
  height:19px;
}
#u66_text {
  border-width:0px;
  position:absolute;
  left:-115px;
  top:82px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u67_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u67 {
  border-width:0px;
  position:absolute;
  left:207px;
  top:196px;
  width:100px;
  height:60px;
  display:flex;
}
#u67 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u67_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u68_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u68 {
  border-width:0px;
  position:absolute;
  left:207px;
  top:296px;
  width:100px;
  height:60px;
  display:flex;
}
#u68 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u68_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u69 {
  border-width:0px;
  position:absolute;
  left:257px;
  top:256px;
  width:0px;
  height:0px;
}
#u69_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:45px;
}
#u69_seg1 {
  border-width:0px;
  position:absolute;
  left:-9px;
  top:27px;
  width:18px;
  height:19px;
}
#u69_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:12px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u70_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u70 {
  border-width:0px;
  position:absolute;
  left:207px;
  top:416px;
  width:100px;
  height:60px;
  display:flex;
}
#u70 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u70_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u71_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u71 {
  border-width:0px;
  position:absolute;
  left:1593px;
  top:832px;
  width:100px;
  height:60px;
  display:flex;
}
#u71 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u71_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u72 {
  border-width:0px;
  position:absolute;
  left:1593px;
  top:862px;
  width:0px;
  height:0px;
}
#u72_seg0 {
  border-width:0px;
  position:absolute;
  left:-882px;
  top:-5px;
  width:887px;
  height:10px;
}
#u72_seg1 {
  border-width:0px;
  position:absolute;
  left:-888px;
  top:-9px;
  width:19px;
  height:18px;
}
#u72_text {
  border-width:0px;
  position:absolute;
  left:-491px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u73 {
  border-width:0px;
  position:absolute;
  left:948px;
  top:761px;
  width:0px;
  height:0px;
}
#u73_seg0 {
  border-width:0px;
  position:absolute;
  left:-292px;
  top:-5px;
  width:292px;
  height:10px;
}
#u73_seg1 {
  border-width:0px;
  position:absolute;
  left:-292px;
  top:-5px;
  width:10px;
  height:76px;
}
#u73_seg2 {
  border-width:0px;
  position:absolute;
  left:-296px;
  top:58px;
  width:18px;
  height:19px;
}
#u73_text {
  border-width:0px;
  position:absolute;
  left:-229px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u74 {
  border-width:0px;
  position:absolute;
  left:257px;
  top:356px;
  width:0px;
  height:0px;
}
#u74_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:65px;
}
#u74_seg1 {
  border-width:0px;
  position:absolute;
  left:-9px;
  top:47px;
  width:18px;
  height:19px;
}
#u74_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:22px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u75_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u75 {
  border-width:0px;
  position:absolute;
  left:1276px;
  top:1347px;
  width:100px;
  height:60px;
  display:flex;
}
#u75 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u75_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u76_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u76 {
  border-width:0px;
  position:absolute;
  left:1276px;
  top:1477px;
  width:100px;
  height:60px;
  display:flex;
}
#u76 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u76_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u77_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u77 {
  border-width:0px;
  position:absolute;
  left:948px;
  top:1548px;
  width:100px;
  height:60px;
  display:flex;
}
#u77 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u77_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u78_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u78 {
  border-width:0px;
  position:absolute;
  left:948px;
  top:1690px;
  width:100px;
  height:60px;
  display:flex;
}
#u78 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u78_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u79 {
  border-width:0px;
  position:absolute;
  left:1326px;
  top:1407px;
  width:0px;
  height:0px;
}
#u79_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:75px;
}
#u79_seg1 {
  border-width:0px;
  position:absolute;
  left:-9px;
  top:57px;
  width:18px;
  height:19px;
}
#u79_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:28px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u80 {
  border-width:0px;
  position:absolute;
  left:1276px;
  top:1507px;
  width:0px;
  height:0px;
}
#u80_seg0 {
  border-width:0px;
  position:absolute;
  left:-283px;
  top:-5px;
  width:283px;
  height:10px;
}
#u80_seg1 {
  border-width:0px;
  position:absolute;
  left:-283px;
  top:-5px;
  width:10px;
  height:46px;
}
#u80_seg2 {
  border-width:0px;
  position:absolute;
  left:-287px;
  top:28px;
  width:18px;
  height:19px;
}
#u80_text {
  border-width:0px;
  position:absolute;
  left:-210px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u81 {
  border-width:0px;
  position:absolute;
  left:1376px;
  top:1507px;
  width:0px;
  height:0px;
}
#u81_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:376px;
  height:10px;
}
#u81_seg1 {
  border-width:0px;
  position:absolute;
  left:366px;
  top:-957px;
  width:10px;
  height:962px;
}
#u81_seg2 {
  border-width:0px;
  position:absolute;
  left:366px;
  top:-957px;
  width:91px;
  height:10px;
}
#u81_seg3 {
  border-width:0px;
  position:absolute;
  left:444px;
  top:-961px;
  width:19px;
  height:18px;
}
#u81_text {
  border-width:0px;
  position:absolute;
  left:321px;
  top:-342px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u82_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u82 {
  border-width:0px;
  position:absolute;
  left:1833px;
  top:832px;
  width:100px;
  height:60px;
  display:flex;
}
#u82 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u82_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u83 {
  border-width:0px;
  position:absolute;
  left:1883px;
  top:585px;
  width:0px;
  height:0px;
}
#u83_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:252px;
}
#u83_seg1 {
  border-width:0px;
  position:absolute;
  left:-9px;
  top:234px;
  width:18px;
  height:19px;
}
#u83_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:116px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u84 {
  border-width:0px;
  position:absolute;
  left:1883px;
  top:892px;
  width:0px;
  height:0px;
}
#u84_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:691px;
}
#u84_seg1 {
  border-width:0px;
  position:absolute;
  left:-835px;
  top:681px;
  width:840px;
  height:10px;
}
#u84_seg2 {
  border-width:0px;
  position:absolute;
  left:-841px;
  top:677px;
  width:19px;
  height:18px;
}
#u84_text {
  border-width:0px;
  position:absolute;
  left:-124px;
  top:678px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u85 {
  border-width:0px;
  position:absolute;
  left:998px;
  top:1608px;
  width:0px;
  height:0px;
}
#u85_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:87px;
}
#u85_seg1 {
  border-width:0px;
  position:absolute;
  left:-9px;
  top:69px;
  width:18px;
  height:19px;
}
#u85_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:34px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u86_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u86 {
  border-width:0px;
  position:absolute;
  left:2005px;
  top:525px;
  width:100px;
  height:60px;
  display:flex;
}
#u86 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u86_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u87 {
  border-width:0px;
  position:absolute;
  left:1823px;
  top:315px;
  width:0px;
  height:0px;
}
#u87_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:237px;
  height:10px;
}
#u87_seg1 {
  border-width:0px;
  position:absolute;
  left:227px;
  top:-5px;
  width:10px;
  height:215px;
}
#u87_seg2 {
  border-width:0px;
  position:absolute;
  left:223px;
  top:197px;
  width:18px;
  height:19px;
}
#u87_text {
  border-width:0px;
  position:absolute;
  left:171px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u88 {
  border-width:0px;
  position:absolute;
  left:1643px;
  top:585px;
  width:0px;
  height:0px;
}
#u88_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:252px;
}
#u88_seg1 {
  border-width:0px;
  position:absolute;
  left:-9px;
  top:234px;
  width:18px;
  height:19px;
}
#u88_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:116px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u89 {
  border-width:0px;
  position:absolute;
  left:661px;
  top:892px;
  width:0px;
  height:0px;
}
#u89_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:79px;
}
#u89_seg1 {
  border-width:0px;
  position:absolute;
  left:-9px;
  top:61px;
  width:18px;
  height:19px;
}
#u89_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:30px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u90 {
  border-width:0px;
  position:absolute;
  left:1048px;
  top:761px;
  width:0px;
  height:0px;
}
#u90_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:369px;
  height:10px;
}
#u90_seg1 {
  border-width:0px;
  position:absolute;
  left:359px;
  top:-211px;
  width:10px;
  height:216px;
}
#u90_seg2 {
  border-width:0px;
  position:absolute;
  left:359px;
  top:-211px;
  width:186px;
  height:10px;
}
#u90_seg3 {
  border-width:0px;
  position:absolute;
  left:532px;
  top:-215px;
  width:19px;
  height:18px;
}
#u90_text {
  border-width:0px;
  position:absolute;
  left:314px;
  top:-20px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u91 {
  border-width:0px;
  position:absolute;
  left:1773px;
  top:345px;
  width:0px;
  height:0px;
}
#u91_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:95px;
}
#u91_seg1 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:85px;
  width:120px;
  height:10px;
}
#u91_seg2 {
  border-width:0px;
  position:absolute;
  left:105px;
  top:85px;
  width:10px;
  height:95px;
}
#u91_seg3 {
  border-width:0px;
  position:absolute;
  left:101px;
  top:167px;
  width:18px;
  height:19px;
}
#u91_text {
  border-width:0px;
  position:absolute;
  left:5px;
  top:82px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u92_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u92 {
  border-width:0px;
  position:absolute;
  left:2005px;
  top:832px;
  width:100px;
  height:60px;
  display:flex;
}
#u92 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u92_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u93 {
  border-width:0px;
  position:absolute;
  left:2055px;
  top:585px;
  width:0px;
  height:0px;
}
#u93_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:252px;
}
#u93_seg1 {
  border-width:0px;
  position:absolute;
  left:-9px;
  top:234px;
  width:18px;
  height:19px;
}
#u93_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:116px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u94_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u94 {
  border-width:0px;
  position:absolute;
  left:2005px;
  top:1116px;
  width:100px;
  height:60px;
  display:flex;
}
#u94 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u94_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u95 {
  border-width:0px;
  position:absolute;
  left:2055px;
  top:892px;
  width:0px;
  height:0px;
}
#u95_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:229px;
}
#u95_seg1 {
  border-width:0px;
  position:absolute;
  left:-9px;
  top:211px;
  width:18px;
  height:19px;
}
#u95_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:104px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u96_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u96 {
  border-width:0px;
  position:absolute;
  left:611px;
  top:966px;
  width:100px;
  height:60px;
  display:flex;
}
#u96 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u96_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u97_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u97 {
  border-width:0px;
  position:absolute;
  left:515px;
  top:1095px;
  width:100px;
  height:60px;
  display:flex;
}
#u97 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u97_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u98 {
  border-width:0px;
  position:absolute;
  left:611px;
  top:996px;
  width:0px;
  height:0px;
}
#u98_seg0 {
  border-width:0px;
  position:absolute;
  left:-51px;
  top:-5px;
  width:51px;
  height:10px;
}
#u98_seg1 {
  border-width:0px;
  position:absolute;
  left:-51px;
  top:-5px;
  width:10px;
  height:104px;
}
#u98_seg2 {
  border-width:0px;
  position:absolute;
  left:-55px;
  top:86px;
  width:18px;
  height:19px;
}
#u98_text {
  border-width:0px;
  position:absolute;
  left:-96px;
  top:18px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u99_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u99 {
  border-width:0px;
  position:absolute;
  left:515px;
  top:1231px;
  width:100px;
  height:60px;
  display:flex;
}
#u99 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u99_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u100 {
  border-width:0px;
  position:absolute;
  left:565px;
  top:1155px;
  width:0px;
  height:0px;
}
#u100_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:81px;
}
#u100_seg1 {
  border-width:0px;
  position:absolute;
  left:-9px;
  top:63px;
  width:18px;
  height:19px;
}
#u100_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:30px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u101_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u101 {
  border-width:0px;
  position:absolute;
  left:515px;
  top:1347px;
  width:100px;
  height:60px;
  display:flex;
}
#u101 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u101_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u102_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u102 {
  border-width:0px;
  position:absolute;
  left:705px;
  top:1347px;
  width:100px;
  height:60px;
  display:flex;
}
#u102 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u102_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u103_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u103 {
  border-width:0px;
  position:absolute;
  left:515px;
  top:1467px;
  width:100px;
  height:60px;
  display:flex;
}
#u103 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u103_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u104 {
  border-width:0px;
  position:absolute;
  left:565px;
  top:1291px;
  width:0px;
  height:0px;
}
#u104_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:61px;
}
#u104_seg1 {
  border-width:0px;
  position:absolute;
  left:-9px;
  top:43px;
  width:18px;
  height:19px;
}
#u104_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:20px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u105 {
  border-width:0px;
  position:absolute;
  left:565px;
  top:1407px;
  width:0px;
  height:0px;
}
#u105_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:65px;
}
#u105_seg1 {
  border-width:0px;
  position:absolute;
  left:-9px;
  top:47px;
  width:18px;
  height:19px;
}
#u105_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:22px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u106 {
  border-width:0px;
  position:absolute;
  left:711px;
  top:996px;
  width:0px;
  height:0px;
}
#u106_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:49px;
  height:10px;
}
#u106_seg1 {
  border-width:0px;
  position:absolute;
  left:39px;
  top:-5px;
  width:10px;
  height:356px;
}
#u106_seg2 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:338px;
  width:18px;
  height:19px;
}
#u106_text {
  border-width:0px;
  position:absolute;
  left:-6px;
  top:146px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u107 {
  border-width:0px;
  position:absolute;
  left:615px;
  top:1261px;
  width:0px;
  height:0px;
}
#u107_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:50px;
  height:10px;
}
#u107_seg1 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:-5px;
  width:10px;
  height:126px;
}
#u107_seg2 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:111px;
  width:50px;
  height:10px;
}
#u107_seg3 {
  border-width:0px;
  position:absolute;
  left:77px;
  top:107px;
  width:19px;
  height:18px;
}
#u107_text {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:50px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u108 {
  border-width:0px;
  position:absolute;
  left:615px;
  top:1497px;
  width:0px;
  height:0px;
}
#u108_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:145px;
  height:10px;
}
#u108_seg1 {
  border-width:0px;
  position:absolute;
  left:135px;
  top:-90px;
  width:10px;
  height:95px;
}
#u108_seg2 {
  border-width:0px;
  position:absolute;
  left:131px;
  top:-96px;
  width:18px;
  height:19px;
}
#u108_text {
  border-width:0px;
  position:absolute;
  left:65px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u109 {
  border-width:0px;
  position:absolute;
  left:805px;
  top:1377px;
  width:0px;
  height:0px;
}
#u109_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:476px;
  height:10px;
}
#u109_seg1 {
  border-width:0px;
  position:absolute;
  left:458px;
  top:-9px;
  width:19px;
  height:18px;
}
#u109_text {
  border-width:0px;
  position:absolute;
  left:186px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u110_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:61px;
}
#u110 {
  border-width:0px;
  position:absolute;
  left:496px;
  top:405px;
  width:100px;
  height:60px;
  display:flex;
}
#u110 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u110_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u111_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:72px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
}
#u111 {
  border-width:0px;
  position:absolute;
  left:471px;
  top:465px;
  width:150px;
  height:72px;
  display:flex;
}
#u111 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u111_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u112_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:61px;
}
#u112 {
  border-width:0px;
  position:absolute;
  left:521px;
  top:1588px;
  width:100px;
  height:60px;
  display:flex;
}
#u112 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u112_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u113_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:72px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
}
#u113 {
  border-width:0px;
  position:absolute;
  left:496px;
  top:1648px;
  width:150px;
  height:72px;
  display:flex;
}
#u113 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u113_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u114_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:60px;
  background:-webkit-linear-gradient(270deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:-moz-linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  background:linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(242, 242, 242, 1) 0%, rgba(228, 228, 228, 1) 100%, rgba(255, 255, 255, 1) 100%);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u114 {
  border-width:0px;
  position:absolute;
  left:948px;
  top:966px;
  width:100px;
  height:60px;
  display:flex;
}
#u114 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u114_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
