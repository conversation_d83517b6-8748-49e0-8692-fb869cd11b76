package com.yi.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 角色资源关联表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_sys_role_resource")
@ApiModel(value = "SysRoleResource对象", description = "角色资源关联表")
public class SysRoleResource extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 角色ID
     */
    @ApiModelProperty(value = "角色ID")
    @TableField("role_id")
    private Long roleId;

    /**
     * 资源ID
     */
    @ApiModelProperty(value = "资源ID")
    @TableField("resource_id")
    private Long resourceId;
}
