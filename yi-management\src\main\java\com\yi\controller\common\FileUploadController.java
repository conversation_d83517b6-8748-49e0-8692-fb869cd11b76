package com.yi.controller.common;

import com.yi.common.Result;
import com.yi.controller.common.model.FileUploadResponse;
import com.yi.service.FileUploadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 文件上传控制器
 */
@RestController
@RequestMapping("/api/common/file")
@Api(tags = "公共文件上传")
public class FileUploadController {

    @Autowired
    private FileUploadService fileUploadService;

    @ApiOperation("上传单个文件到临时目录")
    @PostMapping("/upload/temp")
    public Result<FileUploadResponse> uploadTempFile(
            @ApiParam("文件") @RequestParam("file") MultipartFile file,
            @ApiParam("模块名称（用于分类存储）") @RequestParam(value = "module", required = false, defaultValue = "common") String module) {
        try {
            FileUploadResponse response = fileUploadService.uploadTempFile(file, module);
            return Result.success("临时上传成功", response);
        } catch (IOException e) {
            return Result.failed("文件上传失败：" + e.getMessage());
        } catch (RuntimeException e) {
            return Result.failed(e.getMessage());
        }
    }

    @ApiOperation("上传单个文件到正式目录")
    @PostMapping("/upload")
    public Result<FileUploadResponse> uploadFile(
            @ApiParam("文件") @RequestParam("file") MultipartFile file,
            @ApiParam("模块名称（用于分类存储）") @RequestParam(value = "module", required = false, defaultValue = "common") String module) {
        try {
            FileUploadResponse response = fileUploadService.uploadFile(file, module);
            return Result.success("上传成功", response);
        } catch (IOException e) {
            return Result.failed("文件上传失败：" + e.getMessage());
        } catch (RuntimeException e) {
            return Result.failed(e.getMessage());
        }
    }

    @ApiOperation("批量上传文件到临时目录")
    @PostMapping("/upload/temp/batch")
    public Result<List<FileUploadResponse>> uploadTempFiles(
            @ApiParam("文件列表") @RequestParam("files") MultipartFile[] files,
            @ApiParam("模块名称（用于分类存储）") @RequestParam(value = "module", required = false, defaultValue = "common") String module) {

        if (files == null || files.length == 0) {
            return Result.validateFailed("请选择要上传的文件");
        }

        List<FileUploadResponse> responses = new ArrayList<>();
        List<String> errors = new ArrayList<>();

        for (int i = 0; i < files.length; i++) {
            MultipartFile file = files[i];
            try {
                FileUploadResponse response = fileUploadService.uploadTempFile(file, module);
                responses.add(response);
            } catch (IOException e) {
                errors.add("文件" + (i + 1) + "上传失败：" + e.getMessage());
            } catch (RuntimeException e) {
                errors.add("文件" + (i + 1) + "：" + e.getMessage());
            }
        }

        if (errors.isEmpty()) {
            return Result.success("批量临时上传成功", responses);
        } else if (responses.isEmpty()) {
            return Result.failed("批量上传失败：" + String.join("; ", errors));
        } else {
            return Result.success("部分文件上传成功，失败信息：" + String.join("; ", errors), responses);
        }
    }

    @ApiOperation("批量上传文件到正式目录")
    @PostMapping("/upload/batch")
    public Result<List<FileUploadResponse>> uploadFiles(
            @ApiParam("文件列表") @RequestParam("files") MultipartFile[] files,
            @ApiParam("模块名称（用于分类存储）") @RequestParam(value = "module", required = false, defaultValue = "common") String module) {

        if (files == null || files.length == 0) {
            return Result.validateFailed("请选择要上传的文件");
        }

        List<FileUploadResponse> responses = new ArrayList<>();
        List<String> errors = new ArrayList<>();

        for (int i = 0; i < files.length; i++) {
            MultipartFile file = files[i];
            try {
                FileUploadResponse response = fileUploadService.uploadFile(file, module);
                responses.add(response);
            } catch (IOException e) {
                errors.add("文件" + (i + 1) + "上传失败：" + e.getMessage());
            } catch (RuntimeException e) {
                errors.add("文件" + (i + 1) + "：" + e.getMessage());
            }
        }

        if (errors.isEmpty()) {
            return Result.success("批量上传成功", responses);
        } else if (responses.isEmpty()) {
            return Result.failed("批量上传失败：" + String.join("; ", errors));
        } else {
            return Result.success("部分文件上传成功，失败信息：" + String.join("; ", errors), responses);
        }
    }

    @ApiOperation("删除文件")
    @DeleteMapping("/delete")
    public Result<Boolean> deleteFile(
            @ApiParam("文件相对路径") @RequestParam("filePath") String filePath) {
        try {
            boolean success = fileUploadService.deleteFile(filePath);
            if (success) {
                return Result.success("删除成功", true);
            } else {
                return Result.failed("文件不存在或删除失败");
            }
        } catch (Exception e) {
            return Result.failed("删除失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取文件访问URL")
    @GetMapping("/url")
    public Result<String> getFileUrl(
            @ApiParam("文件相对路径") @RequestParam("filePath") String filePath,
            @ApiParam("是否为临时文件") @RequestParam(value = "isTemp", required = false, defaultValue = "false") Boolean isTemp) {
        try {
            String fileUrl = fileUploadService.getFileUrl(filePath, isTemp);
            if (fileUrl != null) {
                return Result.success("获取成功", fileUrl);
            } else {
                return Result.failed("文件路径无效");
            }
        } catch (Exception e) {
            return Result.failed("获取失败：" + e.getMessage());
        }
    }

    @ApiOperation("将临时文件移动到正式目录")
    @PostMapping("/move-to-formal")
    public Result<FileUploadResponse> moveTempFileToFormal(
            @ApiParam("临时文件相对路径") @RequestParam("tempFilePath") String tempFilePath,
            @ApiParam("目标模块名称") @RequestParam(value = "module", required = false, defaultValue = "common") String module) {
        try {
            FileUploadResponse response = fileUploadService.moveTempFileToFormal(tempFilePath, module);
            return Result.success("文件移动成功", response);
        } catch (IOException e) {
            return Result.failed("文件移动失败：" + e.getMessage());
        } catch (RuntimeException e) {
            return Result.failed(e.getMessage());
        }
    }

    @ApiOperation("批量将临时文件移动到正式目录")
    @PostMapping("/move-to-formal/batch")
    public Result<List<FileUploadResponse>> moveTempFilesToFormal(
            @ApiParam("临时文件路径列表") @RequestBody List<String> tempFilePaths,
            @ApiParam("目标模块名称") @RequestParam(value = "module", required = false, defaultValue = "common") String module) {
        try {
            List<FileUploadResponse> responses = fileUploadService.moveTempFilesToFormal(tempFilePaths, module);
            if (responses.isEmpty()) {
                return Result.failed("没有文件被成功移动");
            } else if (responses.size() == tempFilePaths.size()) {
                return Result.success("批量移动成功", responses);
            } else {
                return Result.success("部分文件移动成功", responses);
            }
        } catch (Exception e) {
            return Result.failed("批量移动失败：" + e.getMessage());
        }
    }
}
