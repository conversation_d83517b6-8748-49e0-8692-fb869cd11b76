package com.yi.controller.contract;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yi.common.Result;
import com.yi.controller.contract.model.*;
import com.yi.entity.TContract;
import com.yi.service.TContractService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * 合同管理表 前端控制器
 */
@RestController
@RequestMapping("/api/contract")
@Api(tags = "合同管理")
public class TContractController {

    @Autowired
    private TContractService contractService;

    @ApiOperation("分页查询合同列表")
    @PostMapping("/page")
    public Result<IPage<ContractPageResponse>> getContractPage(@RequestBody ContractQueryRequest request) {
        IPage<ContractPageResponse> page = contractService.getContractPageResponse(request);
        return Result.success(page);
    }

    @ApiOperation("根据ID获取合同详情")
    @GetMapping("/{id}")
    public Result<ContractDetailResponse> getContractDetail(@ApiParam("合同ID") @PathVariable Long id) {
        ContractDetailResponse response = contractService.getContractDetailById(id);
        if (response == null) {
            return Result.failed("合同不存在");
        }
        return Result.success(response);
    }

    @ApiOperation("新增合同")
    @PostMapping("/add")
    public Result<Boolean> addContract(@Valid @RequestBody ContractRequest request) {
        try {
            boolean success = contractService.addContract(request);
            if (success) {
                return Result.success("新增成功", true);
            }
            return Result.failed("新增失败");
        } catch (RuntimeException e) {
            return Result.failed(e.getMessage());
        }
    }

    @ApiOperation("更新合同")
    @PutMapping("/update")
    public Result<Boolean> updateContract(@Valid @RequestBody ContractRequest request) {
        if (request.getId() == null || request.getId().trim().isEmpty()) {
            return Result.validateFailed("合同ID不能为空");
        }
        try {
            boolean success = contractService.updateContract(request);
            if (success) {
                return Result.success("更新成功", true);
            }
            return Result.failed("更新失败");
        } catch (RuntimeException e) {
            return Result.failed(e.getMessage());
        }
    }

    @ApiOperation("作废合同")
    @PutMapping("/{id}/cancel")
    public Result<Boolean> cancelContract(@ApiParam("合同ID") @PathVariable Long id,
                                         @Valid @RequestBody ContractCancelRequest request) {
        try {
            boolean success = contractService.cancelContract(id, request.getCancelReason());
            if (success) {
                return Result.success("作废成功", true);
            }
            return Result.failed("作废失败");
        } catch (RuntimeException e) {
            return Result.failed(e.getMessage());
        }
    }

    @ApiOperation("获取合同操作日志")
    @GetMapping("/{id}/operation-logs")
    public Result<List<ContractLogResponse>> getContractOperationLogs(@ApiParam("合同ID") @PathVariable Long id) {
        List<ContractLogResponse> logs = contractService.getContractOperationLogs(id);
        return Result.success(logs);
    }

    @ApiOperation("根据客户ID获取有效合同列表 新增订单合同下拉框")
    @GetMapping("/by-customer/{customerCompanyId}")
    public Result<List<TContract>> getValidContractsByCustomerId(@ApiParam("客户公司ID") @PathVariable Long customerCompanyId) {
        List<TContract> contracts = contractService.getValidContractsByCustomerId(customerCompanyId);
        return Result.success(contracts);
    }

    @ApiOperation("检查合同编号是否唯一")
    @GetMapping("/check-contract-no")
    public Result<Boolean> checkContractNoUnique(@ApiParam("合同编号") @RequestParam String contractNo,
                                                @ApiParam("排除的合同ID") @RequestParam(required = false) Long excludeId) {
        Boolean isUnique = contractService.checkContractNoUnique(contractNo, excludeId);
        return Result.success(isUnique);
    }

    @ApiOperation("导出合同列表")
    @PostMapping("/export")
    public void exportContractList(@RequestBody ContractExportRequest request,
                                  HttpServletResponse response) throws IOException {
        contractService.exportContractList(request, response);
    }

    @ApiOperation("获取合同SKU明细列表")
    @GetMapping("/{id}/sku-list")
    public Result<List<ContractSkuResponse>> getContractSkuList(@ApiParam("合同ID") @PathVariable Long id) {
        List<ContractSkuResponse> skuList = contractService.getContractSkuList(id);
        return Result.success(skuList);
    }

}
