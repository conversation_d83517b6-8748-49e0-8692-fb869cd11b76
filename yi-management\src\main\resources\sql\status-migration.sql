-- =============================================
-- 发运订单和发货需求状态字段类型迁移脚本
-- 从 varchar 类型迁移到 int(4) 类型
-- =============================================

-- 1. 备份现有数据（可选，建议在生产环境执行）
-- CREATE TABLE t_shipping_order_backup AS SELECT * FROM t_shipping_order;
-- CREATE TABLE t_shipping_demand_backup AS SELECT * FROM t_shipping_demand;

-- 2. 添加新的状态字段（临时字段）
ALTER TABLE `t_shipping_order` 
ADD COLUMN `status_new` int(4) NOT NULL DEFAULT 1000 COMMENT '订单状态：1000-待发货，2000-发货中，3000-已完结，4000-已取消';

ALTER TABLE `t_shipping_demand` 
ADD COLUMN `status_new` int(4) NOT NULL DEFAULT 1000 COMMENT '发货状态：1000-待发货，2000-已发货，3000-已完结，4000-已取消';

-- 3. 数据迁移：将字符串状态转换为数字状态
-- 发运订单状态迁移
UPDATE `t_shipping_order` 
SET `status_new` = CASE 
    WHEN `status` = 'PENDING' THEN 1000
    WHEN `status` = 'SHIPPING' THEN 2000
    WHEN `status` = 'COMPLETED' THEN 3000
    WHEN `status` = 'CANCELLED' THEN 4000
    ELSE 1000  -- 默认为待发货
END;

-- 发货需求状态迁移
UPDATE `t_shipping_demand` 
SET `status_new` = CASE 
    WHEN `status` = 'PENDING' THEN 1000
    WHEN `status` = 'SHIPPED' THEN 2000
    WHEN `status` = 'COMPLETED' THEN 3000
    WHEN `status` = 'CANCELLED' THEN 4000
    ELSE 1000  -- 默认为待发货
END;

-- 4. 验证数据迁移结果
-- 检查发运订单状态迁移结果
SELECT 
    status as old_status,
    status_new as new_status,
    COUNT(*) as count
FROM t_shipping_order 
GROUP BY status, status_new
ORDER BY status;

-- 检查发货需求状态迁移结果
SELECT 
    status as old_status,
    status_new as new_status,
    COUNT(*) as count
FROM t_shipping_demand 
GROUP BY status, status_new
ORDER BY status;

-- 5. 删除旧的状态字段
ALTER TABLE `t_shipping_order` DROP COLUMN `status`;
ALTER TABLE `t_shipping_demand` DROP COLUMN `status`;

-- 6. 重命名新字段为原字段名
ALTER TABLE `t_shipping_order` CHANGE COLUMN `status_new` `status` int(4) NOT NULL DEFAULT 1000 COMMENT '订单状态：1000-待发货，2000-发货中，3000-已完结，4000-已取消';
ALTER TABLE `t_shipping_demand` CHANGE COLUMN `status_new` `status` int(4) NOT NULL DEFAULT 1000 COMMENT '发货状态：1000-待发货，2000-已发货，3000-已完结，4000-已取消';

-- 7. 更新索引（如果原来有基于status字段的索引）
-- 删除旧索引（如果存在）
-- DROP INDEX IF EXISTS idx_shipping_order_status ON t_shipping_order;
-- DROP INDEX IF EXISTS idx_shipping_demand_status ON t_shipping_demand;

-- 创建新索引
ALTER TABLE `t_shipping_order` 
ADD INDEX `idx_status_valid` (`status`, `valid`) COMMENT '状态和有效性复合索引';

ALTER TABLE `t_shipping_demand` 
ADD INDEX `idx_status_valid` (`status`, `valid`) COMMENT '状态和有效性复合索引';

-- 8. 验证最终结果
-- 检查表结构
DESCRIBE t_shipping_order;
DESCRIBE t_shipping_demand;

-- 检查数据完整性
SELECT 
    status,
    COUNT(*) as count,
    CASE 
        WHEN status = 1000 THEN '待发货'
        WHEN status = 2000 THEN '发货中'
        WHEN status = 3000 THEN '已完结'
        WHEN status = 4000 THEN '已取消'
        ELSE '未知状态'
    END as status_name
FROM t_shipping_order 
WHERE valid = 1
GROUP BY status
ORDER BY status;

SELECT 
    status,
    COUNT(*) as count,
    CASE 
        WHEN status = 1000 THEN '待发货'
        WHEN status = 2000 THEN '已发货'
        WHEN status = 3000 THEN '已完结'
        WHEN status = 4000 THEN '已取消'
        ELSE '未知状态'
    END as status_name
FROM t_shipping_demand 
WHERE valid = 1
GROUP BY status
ORDER BY status;

-- =============================================
-- 回滚脚本（如果需要回滚到原来的varchar类型）
-- =============================================

/*
-- 回滚步骤1：添加临时varchar字段
ALTER TABLE `t_shipping_order` 
ADD COLUMN `status_old` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '订单状态：PENDING-待发货，SHIPPING-发货中，COMPLETED-已完结，CANCELLED-已取消';

ALTER TABLE `t_shipping_demand` 
ADD COLUMN `status_old` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '发货状态：PENDING-待发货，SHIPPED-已发货，COMPLETED-已完结，CANCELLED-已取消';

-- 回滚步骤2：数据转换
UPDATE `t_shipping_order` 
SET `status_old` = CASE 
    WHEN `status` = 1000 THEN 'PENDING'
    WHEN `status` = 2000 THEN 'SHIPPING'
    WHEN `status` = 3000 THEN 'COMPLETED'
    WHEN `status` = 4000 THEN 'CANCELLED'
    ELSE 'PENDING'
END;

UPDATE `t_shipping_demand` 
SET `status_old` = CASE 
    WHEN `status` = 1000 THEN 'PENDING'
    WHEN `status` = 2000 THEN 'SHIPPED'
    WHEN `status` = 3000 THEN 'COMPLETED'
    WHEN `status` = 4000 THEN 'CANCELLED'
    ELSE 'PENDING'
END;

-- 回滚步骤3：删除int字段，重命名varchar字段
ALTER TABLE `t_shipping_order` DROP COLUMN `status`;
ALTER TABLE `t_shipping_order` CHANGE COLUMN `status_old` `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '订单状态：PENDING-待发货，SHIPPING-发货中，COMPLETED-已完结，CANCELLED-已取消';

ALTER TABLE `t_shipping_demand` DROP COLUMN `status`;
ALTER TABLE `t_shipping_demand` CHANGE COLUMN `status_old` `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '发货状态：PENDING-待发货，SHIPPED-已发货，COMPLETED-已完结，CANCELLED-已取消';
*/

-- =============================================
-- 状态枚举值说明
-- =============================================

/*
发运订单状态 (ShippingOrderStatusEnum):
- 1000: 待发货 (PENDING)
- 2000: 发货中 (SHIPPING)  
- 3000: 已完结 (COMPLETED)
- 4000: 已取消 (CANCELLED)

发货需求状态 (ShippingDemandStatusEnum):
- 1000: 待发货 (PENDING)
- 2000: 已发货 (SHIPPED)
- 3000: 已完结 (COMPLETED)
- 4000: 已取消 (CANCELLED)
*/
