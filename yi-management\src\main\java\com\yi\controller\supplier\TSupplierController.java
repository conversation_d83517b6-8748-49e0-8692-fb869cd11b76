package com.yi.controller.supplier;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yi.common.Result;
import com.yi.controller.supplier.model.*;
import com.yi.service.TSupplierService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * 供应商表 前端控制器
 */
@RestController
@RequestMapping("/api/supplier")
@Api(tags = "供应商管理")
public class TSupplierController {

    @Autowired
    private TSupplierService supplierService;

    @ApiOperation("分页查询供应商列表")
    @PostMapping("/page")
    public Result<IPage<SupplierPageResponse>> getSupplierPage(@RequestBody SupplierQueryRequest request) {
        IPage<SupplierPageResponse> page = supplierService.getSupplierPageResponse(request);
        return Result.success(page);
    }

    @ApiOperation("根据ID获取供应商详情")
    @GetMapping("/{id}")
    public Result<SupplierDetailResponse> getSupplierById(@ApiParam("供应商ID") @PathVariable String id) {
        SupplierDetailResponse response = supplierService.getSupplierDetailById(Long.valueOf(id));
        if (response == null) {
            return Result.failed("供应商不存在");
        }
        return Result.success(response);
    }

    @ApiOperation("新增供应商")
    @PostMapping("/add")
    public Result<Boolean> addSupplier(@Valid @RequestBody SupplierSaveRequest request) {
        try {
            boolean success = supplierService.addSupplier(request);
            if (success) {
                return Result.success("新增成功", true);
            }
            return Result.failed("新增失败");
        } catch (RuntimeException e) {
            return Result.failed(e.getMessage());
        }
    }

    @ApiOperation("更新供应商")
    @PutMapping("/update")
    public Result<Boolean> updateSupplier(@Valid @RequestBody SupplierSaveRequest request) {
        if (request.getId() == null || request.getId().trim().isEmpty()) {
            return Result.validateFailed("供应商ID不能为空");
        }
        try {
            boolean success = supplierService.updateSupplier(request);
            if (success) {
                return Result.success("更新成功", true);
            }
            return Result.failed("更新失败");
        } catch (RuntimeException e) {
            return Result.failed(e.getMessage());
        }
    }

    @ApiOperation("启用/禁用供应商")
    @PutMapping("/{id}/status")
    public Result<Boolean> updateSupplierStatus(@ApiParam("供应商ID") @PathVariable String id,
                                               @ApiParam("启用状态") @RequestParam String enabled) {
        boolean success = supplierService.updateSupplierStatus(Long.valueOf(id), Integer.valueOf(enabled));
        if (success) {
            String message = "1".equals(enabled) ? "启用成功" : "禁用成功";
            return Result.success(message, true);
        }
        return Result.failed("状态更新失败");
    }

    @ApiOperation("导出供应商列表")
    @PostMapping("/export")
    public void exportSupplierList(@RequestBody SupplierQueryRequest request,
                                  HttpServletResponse response) throws IOException {
        supplierService.exportSupplierList(request, response);
    }

    @ApiOperation("获取供应商下拉列表")
    @GetMapping("/dropdown")
    public Result<List<SupplierDropdownResponse>> getSupplierDropdownList() {
        List<SupplierDropdownResponse> list = supplierService.getSupplierDropdownList();
        return Result.success(list);
    }

}
