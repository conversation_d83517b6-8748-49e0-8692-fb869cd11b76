package com.yi.controller.customercompany.model;

import com.yi.utils.FormatUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 客户公司分页返回响应
 */
@Data
@ApiModel(value = "CustomerCompanyPageResponse", description = "客户公司分页返回响应")
public class CustomerCompanyPageResponse {

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "客户类型")
    private String customerType;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "注册地址")
    private String registrationAddress;

    @ApiModelProperty(value = "联系人")
    private String contactPerson;

    @ApiModelProperty(value = "联系方式")
    private String contactPhone;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private String createdTime;
}
