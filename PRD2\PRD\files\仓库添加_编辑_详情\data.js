﻿$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,_(g,h,i,_(j,k,l,k)),m,[],n,_(h,o),p,[q],r,_(s,t,u,v,g,w,x,_(),y,[],z,_(A,B,C,D,E,_(F,G,H,I),J,null,K,L,L,M,N,O,null,P,Q,R,S,T,U,V,Q,W,X,Y,_(F,G,H,Z),ba,Q,bb,X,bc,_(bd,be,bf,bg,bh,bg,bi,bg,bj,k,H,_(bk,bl,bm,bl,bn,bl,bo,bp)),i,_(j,k,l,k)),bq,_(),br,_(),bs,_(bt,[_(bu,bv,bw,h,bx,by,u,bz,bA,bB,bC,bD,z,_(i,_(j,bE,l,bF),A,bG,bH,_(bI,bJ,bK,bL)),bq,_(),bM,_(),bN,_(bO,bP),bQ,be),_(bu,bR,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(i,_(j,bT,l,bU),A,bV,bH,_(bI,bJ,bK,bW),V,bX,Y,_(F,G,H,bY)),bq,_(),bM,_(),bQ,be),_(bu,bZ,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(i,_(j,ca,l,cb),A,cc,bH,_(bI,cd,bK,ce)),bq,_(),bM,_(),bQ,be),_(bu,cf,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,ch,ci,bF),i,_(j,cj,l,ck),A,cl,bH,_(bI,cm,bK,cn)),bq,_(),bM,_(),bQ,be),_(bu,co,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,ch,ci,bF),i,_(j,cj,l,ck),A,cl,bH,_(bI,cm,bK,cp)),bq,_(),bM,_(),bQ,be),_(bu,cq,bw,h,bx,cr,u,cs,bA,cs,bC,bD,z,_(i,_(j,ct,l,cu),cv,_(cw,_(A,cx),cy,_(A,cz)),A,cA,bH,_(bI,cB,bK,cC)),cD,be,bq,_(),bM,_(),cE,h),_(bu,cF,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,ch,ci,bF),i,_(j,cj,l,ck),A,cl,bH,_(bI,cm,bK,cG)),bq,_(),bM,_(),bQ,be),_(bu,cH,bw,h,bx,cr,u,cs,bA,cs,bC,bD,z,_(i,_(j,cI,l,cJ),cv,_(cw,_(A,cx),cy,_(A,cz)),A,cA,bH,_(bI,cK,bK,cL)),cD,be,bq,_(),bM,_(),cE,h),_(bu,cM,bw,h,bx,cN,u,cO,bA,cO,bC,bD,z,_(i,_(j,cP,l,cJ),A,cQ,cv,_(cy,_(A,cz)),bH,_(bI,cB,bK,cL)),cD,be,bq,_(),bM,_()),_(bu,cR,bw,h,bx,cN,u,cO,bA,cO,bC,bD,z,_(i,_(j,cP,l,cJ),A,cQ,cv,_(cy,_(A,cz)),bH,_(bI,cS,bK,cL)),cD,be,bq,_(),bM,_()),_(bu,cT,bw,h,bx,cN,u,cO,bA,cO,bC,bD,z,_(i,_(j,cP,l,cJ),A,cQ,cv,_(cy,_(A,cz)),bH,_(bI,cU,bK,cL)),cD,be,bq,_(),bM,_()),_(bu,cV,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,bY,ci,bF),i,_(j,cW,l,cX),A,cl,bH,_(bI,cY,bK,cZ),da,db),bq,_(),bM,_(),bQ,be),_(bu,dc,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,bY,ci,bF),i,_(j,cW,l,cX),A,cl,bH,_(bI,dd,bK,cZ),da,db),bq,_(),bM,_(),bQ,be),_(bu,de,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,bY,ci,bF),i,_(j,cW,l,cX),A,cl,bH,_(bI,df,bK,cZ),da,db),bq,_(),bM,_(),bQ,be),_(bu,dg,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,ch,ci,bF),i,_(j,dh,l,ck),A,cl,bH,_(bI,di,bK,dj)),bq,_(),bM,_(),bQ,be),_(bu,dk,bw,h,bx,cr,u,cs,bA,cs,bC,bD,z,_(i,_(j,dl,l,cu),cv,_(cw,_(A,cx),cy,_(A,cz)),A,cA,bH,_(bI,cB,bK,dm)),cD,be,bq,_(),bM,_(),cE,h),_(bu,dn,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,ch,ci,bF),i,_(j,dh,l,ck),A,cl,bH,_(bI,dp,bK,dj)),bq,_(),bM,_(),bQ,be),_(bu,dq,bw,h,bx,cr,u,cs,bA,cs,bC,bD,z,_(i,_(j,dl,l,cu),cv,_(cw,_(A,cx),cy,_(A,cz)),A,cA,bH,_(bI,dr,bK,dm)),cD,be,bq,_(),bM,_(),cE,h),_(bu,ds,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(i,_(j,dt,l,du),A,dv,bH,_(bI,dw,bK,dx),da,dy),bq,_(),bM,_(),br,_(dz,_(dA,dB,dC,dD,dE,[_(dC,h,dF,h,dG,be,dH,dI,dJ,[_(dK,dL,dC,dM,dN,dO,dP,_(dQ,_(h,dM)),dR,_(dS,r,b,dT,dU,bD),dV,dW)])])),dX,bD,bQ,be),_(bu,dY,bw,h,bx,cN,u,cO,bA,cO,bC,bD,z,_(i,_(j,dl,l,cu),A,cQ,cv,_(cy,_(A,cz)),bH,_(bI,cB,bK,dZ)),cD,be,bq,_(),bM,_()),_(bu,ea,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,ch,ci,bF),i,_(j,cj,l,ck),A,cl,bH,_(bI,eb,bK,cn)),bq,_(),bM,_(),bQ,be),_(bu,ec,bw,h,bx,cN,u,cO,bA,cO,bC,bD,z,_(i,_(j,dl,l,cu),A,cQ,cv,_(cy,_(A,cz)),bH,_(bI,dr,bK,dZ)),cD,be,bq,_(),bM,_()),_(bu,ed,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,ch,ci,bF),i,_(j,cj,l,ck),A,cl,bH,_(bI,cm,bK,ee)),bq,_(),bM,_(),bQ,be),_(bu,ef,bw,h,bx,cr,u,cs,bA,cs,bC,bD,z,_(i,_(j,ct,l,cu),cv,_(cw,_(A,cx),cy,_(A,cz)),A,cA,bH,_(bI,cB,bK,eg)),cD,be,bq,_(),bM,_(),cE,h),_(bu,eh,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,ch,ci,bF),i,_(j,ei,l,ck),A,cl,bH,_(bI,ej,bK,ek)),bq,_(),bM,_(),bQ,be),_(bu,el,bw,h,bx,cr,u,cs,bA,cs,bC,bD,z,_(i,_(j,ct,l,em),cv,_(cw,_(A,cx),cy,_(A,cz)),A,cA,bH,_(bI,cB,bK,ek)),cD,be,bq,_(),bM,_(),cE,h)])),en,_(),eo,_(ep,_(eq,er),es,_(eq,et),eu,_(eq,ev),ew,_(eq,ex),ey,_(eq,ez),eA,_(eq,eB),eC,_(eq,eD),eE,_(eq,eF),eG,_(eq,eH),eI,_(eq,eJ),eK,_(eq,eL),eM,_(eq,eN),eO,_(eq,eP),eQ,_(eq,eR),eS,_(eq,eT),eU,_(eq,eV),eW,_(eq,eX),eY,_(eq,eZ),fa,_(eq,fb),fc,_(eq,fd),fe,_(eq,ff),fg,_(eq,fh),fi,_(eq,fj),fk,_(eq,fl),fm,_(eq,fn),fo,_(eq,fp)));}; 
var b="url",c="仓库添加_编辑_详情.html",d="generationDate",e=new Date(1753855224219.19),f="defaultAdaptiveView",g="name",h="",i="size",j="width",k=0,l="height",m="adaptiveViews",n="sketchKeys",o="s0",p="variables",q="OnLoadVariable",r="page",s="packageId",t="e2194236ebd94aa99cccf8a0d96f0be6",u="type",v="Axure:Page",w="仓库添加/编辑/详情",x="notes",y="annotations",z="style",A="baseStyle",B="627587b6038d43cca051c114ac41ad32",C="pageAlignment",D="center",E="fill",F="fillType",G="solid",H="color",I=0xFFFFFFFF,J="image",K="imageAlignment",L="near",M="imageRepeat",N="auto",O="favicon",P="sketchFactor",Q="0",R="colorStyle",S="appliedColor",T="fontName",U="Applied Font",V="borderWidth",W="borderVisibility",X="all",Y="borderFill",Z=0xFF797979,ba="cornerRadius",bb="cornerVisibility",bc="outerShadow",bd="on",be=false,bf="offsetX",bg=5,bh="offsetY",bi="blurRadius",bj="spread",bk="r",bl=0,bm="g",bn="b",bo="a",bp=0.349019607843137,bq="adaptiveStyles",br="interactionMap",bs="diagram",bt="objects",bu="id",bv="b70df7c03f124d4f8f5c372cf2f6194a",bw="label",bx="friendlyType",by="线段",bz="vectorShape",bA="styleType",bB="horizontalLine",bC="visible",bD=true,bE=1300,bF=1,bG="0327e893a7994793993b54c636419b7c",bH="location",bI="x",bJ=20,bK="y",bL=50,bM="imageOverrides",bN="images",bO="normal~",bP="images/客户管理/u350.svg",bQ="generateCompound",bR="9dc935076ecc4a6a9254ba85dee543c6",bS="矩形",bT=235,bU=30,bV="4701f00c92714d4e9eed94e9fe75cfe8",bW=21,bX="1",bY=0xFFAAAAAA,bZ="72eb351afcf74a9ab5acdfa3fa7330fc",ca=56,cb=19,cc="4b88aa200ad64025ad561857a6779b03",cd=1264,ce=32,cf="0e60a0179f5042f2b8d2f9fbd015d050",cg="foreGroundFill",ch=0xFF000000,ci="opacity",cj=62,ck=16,cl="df3da3fd8cfa4c4a81f05df7784209fe",cm=278,cn=108,co="20f57d8df37d49fab3181212bc36f01f",cp=159,cq="5cf5d40c8dff4b25b34100372632fff6",cr="文本框",cs="textBox",ct=700,cu=26,cv="stateStyles",cw="hint",cx="4889d666e8ad4c5e81e59863039a5cc0",cy="disabled",cz="9bd0236217a94d89b0314c8c7fc75f16",cA="2170b7f9af5c48fba2adcd540f2ba1a0",cB=350,cC=154,cD="HideHintOnFocused",cE="placeholderText",cF="e3647ab00e624648b2d37eadb390f5b6",cG=214,cH="3211711816774a6a9d49f722b4b7a413",cI=220,cJ=24,cK=830,cL=210,cM="4801708b79354b85aada1c354140042f",cN="下拉列表",cO="comboBox",cP=150,cQ="********************************",cR="9e1870945f5c4a3b85676d7c058e3081",cS=510,cT="4fa32c0908bd45df80d9f9260f292e95",cU=670,cV="da33f9102d994e759f6e93622c50a4e9",cW=12,cX=14,cY=466,cZ=215,da="fontSize",db="12px",dc="65170d1c3d574f64b63a394f4db0cbeb",dd=630,de="33c20a668bb942b88538e7449767014e",df=790,dg="42e9a259d22f40b6a7167e5368fd455f",dh=48,di=292,dj=269,dk="b64613b2fbc1481daccc9bcbf7c53d35",dl=200,dm=264,dn="374069021285493cbe54308e25a7d5f2",dp=792,dq="4dbdfb56c330488d8aa47c90068851bd",dr=850,ds="d502667d9bdc4363806bf339c0b2694a",dt=140,du=40,dv="f9d2a29eec41403f99d04559928d6317",dw=600,dx=486,dy="16px",dz="onClick",dA="eventType",dB="Click时",dC="description",dD="单击时",dE="cases",dF="conditionString",dG="isNewIfGroup",dH="caseColorHex",dI="AB68FF",dJ="actions",dK="action",dL="linkWindow",dM="打开 仓库管理 在 当前窗口",dN="displayName",dO="打开链接",dP="actionInfoDescriptions",dQ="仓库管理",dR="target",dS="targetType",dT="仓库管理.html",dU="includeVariables",dV="linkType",dW="current",dX="tabbable",dY="ff8b5fefaf774134b904f84461079ff1",dZ=103,ea="e9994bb511554d39a36bd9c8d8abedaa",eb=778,ec="a05594162e104ace8d5b44bea2a398f9",ed="7d705ac27fd24fa8a7a4087611e28bd0",ee=320,ef="129cae71f009401aaadae64e9e212ae3",eg=315,eh="5d1837da1af4423e8733c8f5fd8c970d",ei=28,ej=312,ek=366,el="a164235cbf3f4407a682501a2208dd2c",em=79,en="masters",eo="objectPaths",ep="b70df7c03f124d4f8f5c372cf2f6194a",eq="scriptId",er="u3980",es="9dc935076ecc4a6a9254ba85dee543c6",et="u3981",eu="72eb351afcf74a9ab5acdfa3fa7330fc",ev="u3982",ew="0e60a0179f5042f2b8d2f9fbd015d050",ex="u3983",ey="20f57d8df37d49fab3181212bc36f01f",ez="u3984",eA="5cf5d40c8dff4b25b34100372632fff6",eB="u3985",eC="e3647ab00e624648b2d37eadb390f5b6",eD="u3986",eE="3211711816774a6a9d49f722b4b7a413",eF="u3987",eG="4801708b79354b85aada1c354140042f",eH="u3988",eI="9e1870945f5c4a3b85676d7c058e3081",eJ="u3989",eK="4fa32c0908bd45df80d9f9260f292e95",eL="u3990",eM="da33f9102d994e759f6e93622c50a4e9",eN="u3991",eO="65170d1c3d574f64b63a394f4db0cbeb",eP="u3992",eQ="33c20a668bb942b88538e7449767014e",eR="u3993",eS="42e9a259d22f40b6a7167e5368fd455f",eT="u3994",eU="b64613b2fbc1481daccc9bcbf7c53d35",eV="u3995",eW="374069021285493cbe54308e25a7d5f2",eX="u3996",eY="4dbdfb56c330488d8aa47c90068851bd",eZ="u3997",fa="d502667d9bdc4363806bf339c0b2694a",fb="u3998",fc="ff8b5fefaf774134b904f84461079ff1",fd="u3999",fe="e9994bb511554d39a36bd9c8d8abedaa",ff="u4000",fg="a05594162e104ace8d5b44bea2a398f9",fh="u4001",fi="7d705ac27fd24fa8a7a4087611e28bd0",fj="u4002",fk="129cae71f009401aaadae64e9e212ae3",fl="u4003",fm="5d1837da1af4423e8733c8f5fd8c970d",fn="u4004",fo="a164235cbf3f4407a682501a2208dd2c",fp="u4005";
return _creator();
})());