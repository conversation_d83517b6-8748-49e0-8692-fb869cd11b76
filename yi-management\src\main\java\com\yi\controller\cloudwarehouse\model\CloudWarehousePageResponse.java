package com.yi.controller.cloudwarehouse.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 云仓分页响应
 */
@Data
@ApiModel("云仓分页响应")
public class CloudWarehousePageResponse {

    @ApiModelProperty("主键ID")
    private String id;

    @ApiModelProperty("启用状态")
    private String enabledText;

    @ApiModelProperty("仓库名称")
    private String warehouseName;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("联系人")
    private String contactPerson;

    @ApiModelProperty("联系方式")
    private String contactPhone;

    @ApiModelProperty("仓库类型")
    private String warehouseTypeText;

    @ApiModelProperty("仓库属性")
    private String warehouseAttributeText;

    @ApiModelProperty("作业时间")
    private String workingHours;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("创建人")
    private String createdBy;

    @ApiModelProperty("创建时间")
    private String createdTime;
}
