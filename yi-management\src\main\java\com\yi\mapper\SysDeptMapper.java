package com.yi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yi.entity.SysDept;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 部门表 Mapper 接口
 */
@Mapper
public interface SysDeptMapper extends BaseMapper<SysDept> {

    /**
     * 查询部门树形结构
     *
     * @param status 状态
     * @return 部门列表
     */
    List<SysDept> selectDeptTree(@Param("status") Integer status);

    /**
     * 根据父ID查询子部门
     *
     * @param parentId 父部门ID
     * @return 部门列表
     */
    List<SysDept> selectByParentId(@Param("parentId") Long parentId);

    /**
     * 根据部门编码查询部门
     *
     * @param deptCode 部门编码
     * @return 部门信息
     */
    SysDept selectByDeptCode(@Param("deptCode") String deptCode);

    /**
     * 查询部门及其所有子部门ID
     *
     * @param deptId 部门ID
     * @return 部门ID列表
     */
    List<Long> selectDeptAndChildrenIds(@Param("deptId") Long deptId);

    /**
     * 查询所有启用的部门
     *
     * @return 部门列表
     */
    List<SysDept> selectEnabledDepts();

    /**
     * 检查部门是否有子部门
     *
     * @param deptId 部门ID
     * @return 是否有子部门
     */
    boolean hasChildren(@Param("deptId") Long deptId);

    /**
     * 检查部门是否有用户
     *
     * @param deptId 部门ID
     * @return 是否有用户
     */
    boolean hasUsers(@Param("deptId") Long deptId);
}
