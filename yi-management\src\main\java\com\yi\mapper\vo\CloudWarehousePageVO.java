package com.yi.mapper.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 云仓分页VO
 */
@Data
public class CloudWarehousePageVO {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("启用状态：1-启用，0-禁用")
    private Integer enabled;

    @ApiModelProperty("仓库名称")
    @ExcelProperty("仓库名称")
    private String warehouseName;

    @ApiModelProperty("省份名称")
    private String provinceName;

    @ApiModelProperty("城市名称")
    private String cityName;

    @ApiModelProperty("区县名称")
    private String areaName;

    @ApiModelProperty("地址详情")
    private String detailedAddress;

    @ApiModelProperty("联系人")
    @ExcelProperty("联系人")
    private String contactPerson;

    @ApiModelProperty("联系方式")
    @ExcelProperty("联系方式")
    private String contactPhone;

    @ApiModelProperty("仓库类型：1-中心仓，2-卫星仓，3-虚拟仓")
    private Integer warehouseType;

    @ApiModelProperty("仓库属性：1-自有，2-第三方")
    private Integer warehouseAttribute;

    @ApiModelProperty("作业时间")
    @ExcelProperty("作业时间")
    private String workingHours;

    @ApiModelProperty("备注")
    @ExcelProperty("备注")
    private String remark;

    @ApiModelProperty("创建人")
    @ExcelProperty("创建人")
    private String createdBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime createdTime;
}
