-- =============================================
-- 为t_shipping_demand表添加remark字段
-- 执行时间：2024-07-29
-- 说明：为发货需求表添加备注字段，支持业务备注信息记录
-- =============================================

-- 检查字段是否已存在（防止重复执行）
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
      AND TABLE_NAME = 't_shipping_demand'
      AND COLUMN_NAME = 'remark'
);

-- 如果字段不存在则添加
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE `t_shipping_demand` ADD COLUMN `remark` varchar(500) DEFAULT NULL COMMENT ''备注'' AFTER `demand_time`',
    'SELECT ''字段remark已存在，跳过添加'' AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证字段添加结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 't_shipping_demand'
  AND COLUMN_NAME = 'remark';
