package com.yi.controller.supplierwarehouse.model;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 供应商仓库导出VO
 */
@Data
@ApiModel("供应商仓库导出VO")
public class SupplierWarehouseExportVO {

    @ExcelProperty("ID")
    @ApiModelProperty("主键ID")
    private String id;

    @ExcelProperty("状态")
    @ApiModelProperty("状态")
    private String enabledText;

    @ExcelProperty("供应商名称")
    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ExcelProperty("仓库名称")
    @ApiModelProperty("仓库名称")
    private String warehouseName;

    @ExcelProperty("地址")
    @ApiModelProperty("地址")
    private String address;

    @ExcelProperty("联系人")
    @ApiModelProperty("联系人")
    private String contactPerson;

    @ExcelProperty("联系方式")
    @ApiModelProperty("联系方式")
    private String contactPhone;

    @ExcelProperty("创建人")
    @ApiModelProperty("创建人")
    private String createdBy;

    @ExcelProperty("创建时间")
    @ApiModelProperty("创建时间")
    private String createdTime;
}
