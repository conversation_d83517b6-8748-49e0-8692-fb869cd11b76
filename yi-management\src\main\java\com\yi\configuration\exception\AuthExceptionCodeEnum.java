package com.yi.configuration.exception;

public enum AuthExceptionCodeEnum implements BaseExceptionEnum {
    //Token验证异常
    TOKEN_NOT_NULL(4001, "Token不能为空"),
    JWT_TOKEN_EXPIRED(4002, "token超时，请检查 token 的有效期"),
    JWT_SIGNATURE(4003, "不合法的token，请认真比对 token 的签名"),
    JWT_ILLEGAL_ARGUMENT(4004, "缺少token参数"),
    JWT_GEN_TOKEN_FAIL(4005, "生成token失败"),
    JWT_PARSER_TOKEN_FAIL(4006, "解析token失败"),
    JWT_APPID_SECRET_INVALID(4007, "获取 access_token 时 AppSecret 错误，或者 AppId 无效！"),
    JWT_APPID_ENABLED(4008, "AppId 已经被禁用！请联系管理员"),
    JWT_TOKEN_USER_CODE_NOT_EXIST(4002, "您的登录信息已发生改变,请重新登录"),
    PERMISSIONS_INSUFFICIENT(4002, "权限不足，请重新登录！"),

    //账密验证异常
    CLIENT_FORBIDDEN(4009, "客户端被禁止!"),
    USER_NAME_PWD_ERROR(4010, "帐号或者密码错误"),
    USER_NAME_EXIST(4011, "帐号已存在"),
    USER_NOT_EXIST(4012, "您的账号或密码错误"),
    VERIFICATION_CODE_ERROR(4013, "验证码错误或已失效"),
    USER_PERMISSION_NOT_ALLOW(4014, "您暂无查看权限，请联系公司管理员添加"),
    USER_HAVE_DISABLED(4015, "您的权限已被限制，无法登录！"),
    NOT_APPLY_DRIVER_ACCOUNT(4016, "该账号未开通权限，如有问题请联系客服"),
    NOT_APPLY_CARRIER_ACCOUNT(4017, "您的权限已被限制，如有问题联系工作人员"),
    CARRIER_ACCOUNT_CLOSED(4018, "您的账号被关闭，如有问题联系工作人员"),
    DRIVER_ACCOUNT_CLOSED(4019, "您的权限已经被关闭，如有疑问联系调度"),
    CUSTOMER_COMPANY_NOT_AUDIT(4020, "您的账号未完成认证，请先完成认证"),
    CLIENT_CUSTOMER_CAN_NOT_LOGIN(4021, "您暂时无法登录，请等待更新"),
    NOT_AUDIT_NOT_ALLOW_LOGIN(4022, "您的账号未被审核通过,无法登录"),
    FACTORY_ID_NOT_NULL(4022, "factoryId不能为空"),
    ;

    private int code;
    private String msg;

    AuthExceptionCodeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.msg;
    }
}
