package com.yi.controller.sku.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * SKU DTO
 */
@Data
@ApiModel(value = "SkuDTO", description = "SKU数据传输对象")
public class SkuDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @NotNull(message = "一级类目不能为空")
    @ApiModelProperty(value = "一级类目 1:共享托盘", required = true)
    private Integer firstCategory;

    @NotNull(message = "二级类目不能为空")
    @ApiModelProperty(value = "二级类目", required = true)
    private Integer secondCategory;

    @NotNull(message = "三级类目不能为空")
    @ApiModelProperty(value = "三级类目", required = true)
    private Integer thirdCategory;

    @NotNull(message = "长度不能为空")
    @ApiModelProperty(value = "长度(mm)", required = true)
    private Integer length;

    @NotNull(message = "宽度不能为空")
    @ApiModelProperty(value = "宽度(mm)", required = true)
    private Integer width;

    @NotNull(message = "高度不能为空")
    @ApiModelProperty(value = "高度(mm)", required = true)
    private Integer height;

    @ApiModelProperty(value = "重量(Kg)")
    private BigDecimal weight;

    @ApiModelProperty(value = "备注")
    private String remark;

    @NotNull(message = "启用状态不能为空")
    @ApiModelProperty(value = "启用状态：1-启用，0-禁用", required = true)
    private Integer enabled;
}
