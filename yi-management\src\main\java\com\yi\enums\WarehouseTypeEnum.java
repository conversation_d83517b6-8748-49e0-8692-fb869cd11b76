package com.yi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 仓库类型枚举
 */
@Getter
@AllArgsConstructor
public enum WarehouseTypeEnum {

    CENTER_WAREHOUSE(1, "中心仓"),
    SATELLITE_WAREHOUSE(2, "卫星仓"),
    VIRTUAL_WAREHOUSE(3, "虚拟仓");

    private final Integer key;
    private final String value;

    /**
     * 根据key获取value
     *
     * @param key 枚举key
     * @return 枚举value
     */
    public static String getValueByKey(Integer key) {
        if (key == null) {
            return "";
        }
        for (WarehouseTypeEnum typeEnum : values()) {
            if (typeEnum.getKey().equals(key)) {
                return typeEnum.getValue();
            }
        }
        return "";
    }

    /**
     * 根据key获取枚举
     *
     * @param key 枚举key
     * @return 枚举对象
     */
    public static WarehouseTypeEnum getByKey(Integer key) {
        if (key == null) {
            return null;
        }
        for (WarehouseTypeEnum typeEnum : values()) {
            if (typeEnum.getKey().equals(key)) {
                return typeEnum;
            }
        }
        return null;
    }
}
