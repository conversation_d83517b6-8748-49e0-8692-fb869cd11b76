package com.yi.enums;

/**
 * 我司主体枚举
 */
public enum OurCompanyEnum {
    
    YI_JU_TECH(1, "易矩科技");
    
    private final Integer code;
    private final String name;
    
    OurCompanyEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    /**
     * 根据code获取枚举
     */
    public static OurCompanyEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (OurCompanyEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
    
    /**
     * 根据code获取名称
     */
    public static String getNameByCode(Integer code) {
        OurCompanyEnum item = getByCode(code);
        return item != null ? item.getName() : "";
    }
}
