package com.yi.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 部门表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_sys_dept")
@ApiModel(value = "SysDept对象", description = "部门表")
public class SysDept extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 父部门ID
     */
    @ApiModelProperty(value = "父部门ID")
    @TableField("parent_id")
    private Long parentId;

    /**
     * 部门编码
     */
    @ApiModelProperty(value = "部门编码")
    @TableField("dept_code")
    private String deptCode;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    @TableField("dept_name")
    private String deptName;

    /**
     * 部门描述
     */
    @ApiModelProperty(value = "部门描述")
    @TableField("dept_desc")
    private String deptDesc;

    /**
     * 显示顺序
     */
    @ApiModelProperty(value = "显示顺序")
    @TableField("order_num")
    private Integer orderNum;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    @TableField("leader")
    private String leader;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    @TableField("phone")
    private String phone;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    @TableField("email")
    private String email;

    /**
     * 状态：0-禁用，1-启用
     */
    @ApiModelProperty(value = "状态：0-禁用，1-启用")
    @TableField("status")
    private Integer status;
}
