package com.yi.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yi.controller.shippingorder.model.ShippingOrderLogResponse;
import com.yi.entity.TShippingOrderLog;
import com.yi.enums.ShippingOrderActionEnum;
import com.yi.mapper.TShippingOrderLogMapper;
import com.yi.utils.FormatUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 发运订单日志表 服务类
 */
@Service
public class TShippingOrderLogService extends ServiceImpl<TShippingOrderLogMapper, TShippingOrderLog> {

    /**
     * 记录发运订单操作日志
     *
     * @param orderId 订单ID
     * @param action 操作动作
     * @param operator 操作人
     * @param remark 备注信息
     */
    public void recordLog(Long orderId, ShippingOrderActionEnum action, String operator, String remark) {
        TShippingOrderLog log = new TShippingOrderLog();
        log.setOrderId(orderId);
        log.setAction(action.getCode());
        log.setOperator(operator);
        log.setOperateTime(LocalDateTime.now());
        log.setRemark(remark);

        this.save(log);
    }

    /**
     * 根据订单ID查询操作日志列表
     *
     * @param orderId 订单ID
     * @return 操作日志列表
     */
    public List<TShippingOrderLog> getByOrderId(Long orderId) {
        LambdaQueryWrapper<TShippingOrderLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TShippingOrderLog::getOrderId, orderId)
                .orderByDesc(TShippingOrderLog::getOperateTime);
        return this.list(wrapper);
    }

    /**
     * 根据订单ID查询操作日志列表（返回Response格式）
     *
     * @param orderId 订单ID
     * @return 操作日志Response列表
     */
    public List<ShippingOrderLogResponse> getLogsByOrderId(Long orderId) {
        List<TShippingOrderLog> logs = getByOrderId(orderId);
        return logs.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    /**
     * 转换为响应对象
     *
     * @param log 日志实体
     * @return 响应对象
     */
    private ShippingOrderLogResponse convertToResponse(TShippingOrderLog log) {
        ShippingOrderLogResponse response = new ShippingOrderLogResponse();
        response.setActionName(ShippingOrderActionEnum.getDescriptionByCode(log.getAction()));
        response.setOperator(FormatUtils.safeString(log.getOperator()));
        response.setOperateTime(FormatUtils.formatDateTime(log.getOperateTime()));
        response.setRemark(FormatUtils.safeString(log.getRemark()));
        return response;
    }
}
