# 供应商仓库管理新增接口文档

## 1. 供应商下拉列表接口

### 接口信息
- **接口路径**: `GET /api/supplier/dropdown`
- **接口描述**: 获取所有启用状态的供应商下拉列表
- **请求方式**: GET

### 请求参数
无

### 响应参数
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "supplierName": "优质供应商有限公司"
    }
  ]
}
```

## 2. 供应商仓库详情接口

### 接口信息
- **接口路径**: `GET /api/supplier-warehouse/{id}`
- **接口描述**: 根据ID获取供应商仓库详情
- **请求方式**: GET

### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | Long | 是 | 供应商仓库ID |

### 响应参数
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": "1",
    "supplierName": "优质供应商有限公司",
    "warehouseName": "北京仓库",
    "provinceName": "北京市",
    "cityName": "北京市",
    "areaName": "东城区",
    "detailedAddress": "朝阳路123号",
    "contactPerson": "张经理",
    "contactPhone": "13800138000"
  }
}
```

## 3. 新增供应商仓库接口

### 接口信息
- **接口路径**: `POST /api/supplier-warehouse`
- **接口描述**: 新增供应商仓库
- **请求方式**: POST

### 请求参数
```json
{
  "supplierId": 1,
  "warehouseName": "北京仓库",
  "provinceId": 110000,
  "provinceName": "北京市",
  "cityId": 110100,
  "cityName": "北京市",
  "areaId": 110101,
  "areaName": "东城区",
  "detailedAddress": "朝阳路123号",
  "contactPerson": "张经理",
  "contactPhone": "13800138000"
}
```

### 响应参数
```json
{
  "code": 200,
  "message": "新增成功",
  "data": true
}
```

## 4. 更新供应商仓库接口

### 接口信息
- **接口路径**: `PUT /api/supplier-warehouse`
- **接口描述**: 更新供应商仓库信息
- **请求方式**: PUT

### 请求参数
```json
{
  "id": "1",
  "supplierId": 1,
  "warehouseName": "北京仓库（更新）",
  "provinceId": 110000,
  "provinceName": "北京市",
  "cityId": 110100,
  "cityName": "北京市",
  "areaId": 110101,
  "areaName": "东城区",
  "detailedAddress": "朝阳路456号",
  "contactPerson": "李经理",
  "contactPhone": "13900139000"
}
```

### 响应参数
```json
{
  "code": 200,
  "message": "更新成功",
  "data": true
}
```

## 5. 启用/禁用供应商仓库接口

### 接口信息
- **接口路径**: `PUT /api/supplier-warehouse/{id}/status`
- **接口描述**: 启用或禁用供应商仓库
- **请求方式**: PUT

### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | Long | 是 | 供应商仓库ID |
| enabled | Integer | 是 | 启用状态：1-启用，0-禁用 |

### 响应参数
```json
{
  "code": 200,
  "message": "启用成功",
  "data": true
}
```

## 业务规则

### 1. 数据验证
- 供应商ID不能为空
- 仓库名称不能为空
- 省市区信息不能为空
- 详细地址不能为空
- 联系人不能为空
- 联系方式不能为空

### 2. 业务约束
- 同一供应商下仓库名称不能重复
- 只能操作有效状态的供应商仓库
- 新增的供应商仓库默认为启用状态

### 3. 枚举使用
- 启用状态使用 `EnabledStatusEnum`
- 有效状态使用 `ValidStatusEnum`
- 地址拼接时省市区和详细地址之间加空格

## 测试用例

### 1. 获取供应商下拉列表
```bash
curl -X GET "http://localhost:8080/api/supplier/dropdown" \
  -H "Authorization: Bearer {token}"
```

### 2. 新增供应商仓库
```bash
curl -X POST "http://localhost:8080/api/supplier-warehouse" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "supplierId": 1,
    "warehouseName": "北京仓库",
    "provinceName": "北京市",
    "cityName": "北京市",
    "areaName": "东城区",
    "detailedAddress": "朝阳路123号",
    "contactPerson": "张经理",
    "contactPhone": "13800138000"
  }'
```

### 3. 启用供应商仓库
```bash
curl -X PUT "http://localhost:8080/api/supplier-warehouse/1/status?enabled=1" \
  -H "Authorization: Bearer {token}"
```
