package com.yi.controller.user;

import com.yi.common.Result;
import com.yi.configuration.auth.MesAuthService;
import com.yi.configuration.exception.BizException;
import com.yi.configuration.jwt.CleanUserTokenMessage;
import com.yi.configuration.threadlocal.ThreadContext;
import com.yi.constant.CommonConstant;
import com.yi.controller.user.io.*;
import com.yi.enums.BasicDataExceptionEnum;
import com.yi.service.CommonService;
import com.yi.service.LoginService;
import com.yi.service.UserService;
import com.yi.utils.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Api(value = "用户中心")
@RestController
public class UserController {

    @Resource
    private CommonService commonService;

    @Resource
    private LoginService loginService;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private UserService userService;

    @Resource
    private MesAuthService mesAuthService;

    @ApiOperation(value = "登录接口")
    @PostMapping(value = "/api/management/login")
    Result<ManagementLoginResponseModel> login(@RequestBody @Valid UserLoginRequest requestDto, HttpServletResponse httpServletResponse) {
        String account = commonService.decodeInfo(requestDto.getUserAccount());
        String password = commonService.decodeInfo(requestDto.getPassword());
        // 校验用户登录错误次数
        String errorCountKey = CommonConstant.LOGIN_ERROR_COUNT + account;

        LoginErrorDto loginErrorDto = (LoginErrorDto) redisUtil.get(errorCountKey);
        validateLoginErrorCount(loginErrorDto);

        ManagementLoginResponseModel moduleResult = null;
        try {
            // 登录信息参数
            ManagementLoginRequestModel loginRequestModel = new ManagementLoginRequestModel();
            loginRequestModel.setUserName(account);
            loginRequestModel.setPassWord(password);
            loginRequestModel.setRequestIp(getIp());
            moduleResult = loginService.managerLogin(loginRequestModel);
        } catch (Exception e) {
            if (e instanceof BizException) {
                BizException bizException = (BizException) e;
                if (bizException.getCode() == BasicDataExceptionEnum.USER_NAME_PWD_ERROR.getCode()) {
                    // 校验用户登录错误次数
                    calculateLoginErrorCount(errorCountKey, loginErrorDto);
                } else {
                    throw new BizException(bizException.getCode(), bizException.getMessage());
                }
            } else {
                throw new BizException(e.hashCode(), e.getMessage());
            }
        }
        if (moduleResult == null) {
            throw new BizException(BasicDataExceptionEnum.USER_LOGIN_SYSTEM_ERROR);
        }

        // 移除用户登录错误次数
        redisUtil.remove(errorCountKey);

        httpServletResponse.setHeader("token", moduleResult.getTokenModel().getToken());
        return Result.success(moduleResult);
    }

    private String getIp() {
        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        // 默认添加IP
        String ip = "";
        String xForwardedFor = request.getHeader("x-forwarded-for");
        if (StringUtils.isNotBlank(xForwardedFor)) {
            String[] ips = xForwardedFor.split(",");
            if (ips.length > CommonConstant.INTEGER_ZERO) {
                ip = ips[CommonConstant.INTEGER_ZERO];
            }
        }
        return ip;
    }

    /**
     * 计算登录错误次数
     * errorCount >= 5,返回错误信息
     * errorCount < 5, 记录错误信息并返回具体次数
     **/
    private void calculateLoginErrorCount(String errorCountKey, LoginErrorDto loginErrorDto) {
        Integer errorCount = CommonConstant.INTEGER_ZERO;
        if (loginErrorDto != null) {
            errorCount = loginErrorDto.getErrorCount();
            errorCount++;
        } else {
            loginErrorDto = new LoginErrorDto();
            errorCount += CommonConstant.INTEGER_ONE;
        }

        loginErrorDto.setErrorCount(errorCount);
        if (errorCount >= CommonConstant.INTEGER_FIVE) {
            loginErrorDto.setTimeout(new Date());
            redisUtil.set(errorCountKey, loginErrorDto, CommonConstant.YELO_ERROR_LOGIN_CHECK_PERIOD);
            throw new BizException(BasicDataExceptionEnum.LOGIN_ERROR_COUNT_MAX.getCode(),
                    MessageFormat.format(BasicDataExceptionEnum.LOGIN_ERROR_COUNT_MAX.getMessage(),
                            CommonConstant.INTEGER_TEN));
        } else {
            redisUtil.set(errorCountKey, loginErrorDto);
            throw new BizException(BasicDataExceptionEnum.LOGIN_ERROR_MSG.getCode(),
                    MessageFormat.format(BasicDataExceptionEnum.LOGIN_ERROR_MSG.getMessage(),
                            CommonConstant.INTEGER_FIVE - errorCount));
        }
    }

    /**
     * 校验登录错误次数
     **/
    private static void validateLoginErrorCount(LoginErrorDto loginErrorDto) {
        if (loginErrorDto != null && loginErrorDto.getErrorCount() >= CommonConstant.INTEGER_FIVE) {
            Date timeout = loginErrorDto.getTimeout();
            Date now = new Date();
            long minutes = (now.getTime() - timeout.getTime()) / 60000;
            // 错误大于5次锁定（限制登录）账号
            throw new BizException(BasicDataExceptionEnum.LOGIN_ERROR_COUNT_MAX.getCode(),
                    MessageFormat.format(BasicDataExceptionEnum.LOGIN_ERROR_COUNT_MAX.getMessage(),
                            CommonConstant.INTEGER_TEN - minutes));
        }
    }

    @ApiOperation(value = "登出")
    @PostMapping(value = "/api/management/logout")
    Result<Boolean> logout() {
        Long userId = ThreadContext.getUserId();
        UserIdRequestModel userIdRequestModel = new UserIdRequestModel();
        userIdRequestModel.setUserId(userId);
        UserDetailResponseModel responseModel = userService.getUserInfo(userIdRequestModel);

        // 清除token,从head中读取token并清除
        CleanUserTokenMessage cleanUserTokenMessage = new CleanUserTokenMessage();
        cleanUserTokenMessage.setUserCode(responseModel.getUserAccount());
        mesAuthService.cleanUserToken(cleanUserTokenMessage);
        return Result.success(true);
    }

    @PostMapping(value = "/api/management/loginHeartbeat")
    Result<Void> loginHeartbeat() {
        return Result.success(null);
    }
}
