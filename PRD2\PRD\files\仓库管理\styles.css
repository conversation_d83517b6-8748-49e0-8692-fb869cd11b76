﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-30px;
  width:1300px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u3808_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1300px;
  height:68px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3808 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:43px;
  width:1300px;
  height:68px;
  display:flex;
}
#u3808 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3808_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3809_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1301px;
  height:2px;
}
#u3809 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:37px;
  width:1300px;
  height:1px;
  display:flex;
}
#u3809 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3809_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3810_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3810 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:7px;
  width:150px;
  height:30px;
  display:flex;
}
#u3810 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3810_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3811_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3811 {
  border-width:0px;
  position:absolute;
  left:1274px;
  top:18px;
  width:56px;
  height:19px;
  display:flex;
}
#u3811 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3811_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3812_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3812 {
  border-width:0px;
  position:absolute;
  left:1101px;
  top:62px;
  width:80px;
  height:30px;
  display:flex;
}
#u3812 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3812_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3813_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3813 {
  border-width:0px;
  position:absolute;
  left:1191px;
  top:62px;
  width:80px;
  height:30px;
  display:flex;
}
#u3813 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3813_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3814_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3814 {
  border-width:0px;
  position:absolute;
  left:170px;
  top:180px;
  width:80px;
  height:30px;
  display:flex;
}
#u3814 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3814_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3815 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:220px;
  width:1300px;
  height:338px;
}
#u3816_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:31px;
}
#u3816 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:31px;
  display:flex;
}
#u3816 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3816_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3817_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:31px;
}
#u3817 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:0px;
  width:81px;
  height:31px;
  display:flex;
}
#u3817 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3817_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3818_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:31px;
}
#u3818 {
  border-width:0px;
  position:absolute;
  left:126px;
  top:0px;
  width:103px;
  height:31px;
  display:flex;
}
#u3818 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3818_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3819_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:31px;
}
#u3819 {
  border-width:0px;
  position:absolute;
  left:229px;
  top:0px;
  width:95px;
  height:31px;
  display:flex;
}
#u3819 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3819_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3820_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:31px;
}
#u3820 {
  border-width:0px;
  position:absolute;
  left:324px;
  top:0px;
  width:110px;
  height:31px;
  display:flex;
}
#u3820 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3820_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3821_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:31px;
}
#u3821 {
  border-width:0px;
  position:absolute;
  left:434px;
  top:0px;
  width:105px;
  height:31px;
  display:flex;
}
#u3821 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3821_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3822_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:107px;
  height:31px;
}
#u3822 {
  border-width:0px;
  position:absolute;
  left:539px;
  top:0px;
  width:107px;
  height:31px;
  display:flex;
}
#u3822 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3822_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3823_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:31px;
}
#u3823 {
  border-width:0px;
  position:absolute;
  left:646px;
  top:0px;
  width:89px;
  height:31px;
  display:flex;
}
#u3823 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3823_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3824_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:31px;
}
#u3824 {
  border-width:0px;
  position:absolute;
  left:735px;
  top:0px;
  width:116px;
  height:31px;
  display:flex;
}
#u3824 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3824_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3825_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:31px;
}
#u3825 {
  border-width:0px;
  position:absolute;
  left:851px;
  top:0px;
  width:120px;
  height:31px;
  display:flex;
}
#u3825 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3825_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3826_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:31px;
}
#u3826 {
  border-width:0px;
  position:absolute;
  left:971px;
  top:0px;
  width:81px;
  height:31px;
  display:flex;
}
#u3826 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3826_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3827_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:31px;
}
#u3827 {
  border-width:0px;
  position:absolute;
  left:1052px;
  top:0px;
  width:94px;
  height:31px;
  display:flex;
}
#u3827 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3827_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3828_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:154px;
  height:31px;
}
#u3828 {
  border-width:0px;
  position:absolute;
  left:1146px;
  top:0px;
  width:154px;
  height:31px;
  display:flex;
}
#u3828 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3828_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3829_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:34px;
}
#u3829 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:31px;
  width:45px;
  height:34px;
  display:flex;
}
#u3829 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3829_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3830_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:34px;
}
#u3830 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:31px;
  width:81px;
  height:34px;
  display:flex;
}
#u3830 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3830_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3831_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:34px;
}
#u3831 {
  border-width:0px;
  position:absolute;
  left:126px;
  top:31px;
  width:103px;
  height:34px;
  display:flex;
  font-size:14px;
}
#u3831 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3831_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3832_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:34px;
}
#u3832 {
  border-width:0px;
  position:absolute;
  left:229px;
  top:31px;
  width:95px;
  height:34px;
  display:flex;
  color:#000000;
}
#u3832 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3832_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3833_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:34px;
}
#u3833 {
  border-width:0px;
  position:absolute;
  left:324px;
  top:31px;
  width:110px;
  height:34px;
  display:flex;
}
#u3833 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3833_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3834_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:34px;
}
#u3834 {
  border-width:0px;
  position:absolute;
  left:434px;
  top:31px;
  width:105px;
  height:34px;
  display:flex;
}
#u3834 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3834_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3835_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:107px;
  height:34px;
}
#u3835 {
  border-width:0px;
  position:absolute;
  left:539px;
  top:31px;
  width:107px;
  height:34px;
  display:flex;
}
#u3835 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3835_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3836_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:34px;
}
#u3836 {
  border-width:0px;
  position:absolute;
  left:646px;
  top:31px;
  width:89px;
  height:34px;
  display:flex;
}
#u3836 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3836_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3837_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:34px;
}
#u3837 {
  border-width:0px;
  position:absolute;
  left:735px;
  top:31px;
  width:116px;
  height:34px;
  display:flex;
}
#u3837 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3837_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3838_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:34px;
}
#u3838 {
  border-width:0px;
  position:absolute;
  left:851px;
  top:31px;
  width:120px;
  height:34px;
  display:flex;
}
#u3838 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3838_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3839_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:34px;
}
#u3839 {
  border-width:0px;
  position:absolute;
  left:971px;
  top:31px;
  width:81px;
  height:34px;
  display:flex;
}
#u3839 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3839_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3840_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:34px;
}
#u3840 {
  border-width:0px;
  position:absolute;
  left:1052px;
  top:31px;
  width:94px;
  height:34px;
  display:flex;
}
#u3840 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3840_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3841_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:154px;
  height:34px;
}
#u3841 {
  border-width:0px;
  position:absolute;
  left:1146px;
  top:31px;
  width:154px;
  height:34px;
  display:flex;
}
#u3841 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3841_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3842_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:33px;
}
#u3842 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:65px;
  width:45px;
  height:33px;
  display:flex;
}
#u3842 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3842_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3843_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:33px;
}
#u3843 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:65px;
  width:81px;
  height:33px;
  display:flex;
}
#u3843 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3843_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3844_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:33px;
}
#u3844 {
  border-width:0px;
  position:absolute;
  left:126px;
  top:65px;
  width:103px;
  height:33px;
  display:flex;
}
#u3844 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3844_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3845_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:33px;
}
#u3845 {
  border-width:0px;
  position:absolute;
  left:229px;
  top:65px;
  width:95px;
  height:33px;
  display:flex;
}
#u3845 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3845_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3846_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:33px;
}
#u3846 {
  border-width:0px;
  position:absolute;
  left:324px;
  top:65px;
  width:110px;
  height:33px;
  display:flex;
}
#u3846 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3846_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3847_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:33px;
}
#u3847 {
  border-width:0px;
  position:absolute;
  left:434px;
  top:65px;
  width:105px;
  height:33px;
  display:flex;
}
#u3847 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3847_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3848_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:107px;
  height:33px;
}
#u3848 {
  border-width:0px;
  position:absolute;
  left:539px;
  top:65px;
  width:107px;
  height:33px;
  display:flex;
}
#u3848 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3848_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3849_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:33px;
}
#u3849 {
  border-width:0px;
  position:absolute;
  left:646px;
  top:65px;
  width:89px;
  height:33px;
  display:flex;
}
#u3849 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3849_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3850_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:33px;
}
#u3850 {
  border-width:0px;
  position:absolute;
  left:735px;
  top:65px;
  width:116px;
  height:33px;
  display:flex;
}
#u3850 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3850_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3851_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:33px;
}
#u3851 {
  border-width:0px;
  position:absolute;
  left:851px;
  top:65px;
  width:120px;
  height:33px;
  display:flex;
}
#u3851 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3851_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3852_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:33px;
}
#u3852 {
  border-width:0px;
  position:absolute;
  left:971px;
  top:65px;
  width:81px;
  height:33px;
  display:flex;
}
#u3852 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3852_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3853_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:33px;
}
#u3853 {
  border-width:0px;
  position:absolute;
  left:1052px;
  top:65px;
  width:94px;
  height:33px;
  display:flex;
}
#u3853 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3853_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3854_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:154px;
  height:33px;
}
#u3854 {
  border-width:0px;
  position:absolute;
  left:1146px;
  top:65px;
  width:154px;
  height:33px;
  display:flex;
}
#u3854 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3854_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3855_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:30px;
}
#u3855 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:98px;
  width:45px;
  height:30px;
  display:flex;
}
#u3855 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3855_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3856_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:30px;
}
#u3856 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:98px;
  width:81px;
  height:30px;
  display:flex;
}
#u3856 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3856_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3857_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:30px;
}
#u3857 {
  border-width:0px;
  position:absolute;
  left:126px;
  top:98px;
  width:103px;
  height:30px;
  display:flex;
}
#u3857 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3857_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3858_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:30px;
}
#u3858 {
  border-width:0px;
  position:absolute;
  left:229px;
  top:98px;
  width:95px;
  height:30px;
  display:flex;
}
#u3858 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3858_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3859_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
}
#u3859 {
  border-width:0px;
  position:absolute;
  left:324px;
  top:98px;
  width:110px;
  height:30px;
  display:flex;
}
#u3859 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3859_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3860_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:30px;
}
#u3860 {
  border-width:0px;
  position:absolute;
  left:434px;
  top:98px;
  width:105px;
  height:30px;
  display:flex;
}
#u3860 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3860_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3861_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:107px;
  height:30px;
}
#u3861 {
  border-width:0px;
  position:absolute;
  left:539px;
  top:98px;
  width:107px;
  height:30px;
  display:flex;
}
#u3861 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3861_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3862_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:30px;
}
#u3862 {
  border-width:0px;
  position:absolute;
  left:646px;
  top:98px;
  width:89px;
  height:30px;
  display:flex;
}
#u3862 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3862_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3863_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u3863 {
  border-width:0px;
  position:absolute;
  left:735px;
  top:98px;
  width:116px;
  height:30px;
  display:flex;
}
#u3863 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3863_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3864_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u3864 {
  border-width:0px;
  position:absolute;
  left:851px;
  top:98px;
  width:120px;
  height:30px;
  display:flex;
}
#u3864 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3864_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3865_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:30px;
}
#u3865 {
  border-width:0px;
  position:absolute;
  left:971px;
  top:98px;
  width:81px;
  height:30px;
  display:flex;
}
#u3865 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3865_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3866_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:30px;
}
#u3866 {
  border-width:0px;
  position:absolute;
  left:1052px;
  top:98px;
  width:94px;
  height:30px;
  display:flex;
}
#u3866 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3866_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3867_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:154px;
  height:30px;
}
#u3867 {
  border-width:0px;
  position:absolute;
  left:1146px;
  top:98px;
  width:154px;
  height:30px;
  display:flex;
}
#u3867 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3867_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3868_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:30px;
}
#u3868 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:128px;
  width:45px;
  height:30px;
  display:flex;
}
#u3868 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3868_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3869_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:30px;
}
#u3869 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:128px;
  width:81px;
  height:30px;
  display:flex;
}
#u3869 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3869_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3870_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:30px;
}
#u3870 {
  border-width:0px;
  position:absolute;
  left:126px;
  top:128px;
  width:103px;
  height:30px;
  display:flex;
}
#u3870 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3870_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3871_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:30px;
}
#u3871 {
  border-width:0px;
  position:absolute;
  left:229px;
  top:128px;
  width:95px;
  height:30px;
  display:flex;
}
#u3871 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3871_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3872_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
}
#u3872 {
  border-width:0px;
  position:absolute;
  left:324px;
  top:128px;
  width:110px;
  height:30px;
  display:flex;
}
#u3872 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3872_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3873_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:30px;
}
#u3873 {
  border-width:0px;
  position:absolute;
  left:434px;
  top:128px;
  width:105px;
  height:30px;
  display:flex;
}
#u3873 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3873_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3874_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:107px;
  height:30px;
}
#u3874 {
  border-width:0px;
  position:absolute;
  left:539px;
  top:128px;
  width:107px;
  height:30px;
  display:flex;
}
#u3874 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3874_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3875_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:30px;
}
#u3875 {
  border-width:0px;
  position:absolute;
  left:646px;
  top:128px;
  width:89px;
  height:30px;
  display:flex;
}
#u3875 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3875_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3876_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u3876 {
  border-width:0px;
  position:absolute;
  left:735px;
  top:128px;
  width:116px;
  height:30px;
  display:flex;
}
#u3876 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3876_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3877_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u3877 {
  border-width:0px;
  position:absolute;
  left:851px;
  top:128px;
  width:120px;
  height:30px;
  display:flex;
}
#u3877 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3877_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3878_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:30px;
}
#u3878 {
  border-width:0px;
  position:absolute;
  left:971px;
  top:128px;
  width:81px;
  height:30px;
  display:flex;
}
#u3878 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3878_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3879_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:30px;
}
#u3879 {
  border-width:0px;
  position:absolute;
  left:1052px;
  top:128px;
  width:94px;
  height:30px;
  display:flex;
}
#u3879 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3879_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3880_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:154px;
  height:30px;
}
#u3880 {
  border-width:0px;
  position:absolute;
  left:1146px;
  top:128px;
  width:154px;
  height:30px;
  display:flex;
}
#u3880 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3880_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3881_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:30px;
}
#u3881 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:158px;
  width:45px;
  height:30px;
  display:flex;
}
#u3881 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3881_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3882_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:30px;
}
#u3882 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:158px;
  width:81px;
  height:30px;
  display:flex;
}
#u3882 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3882_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3883_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:30px;
}
#u3883 {
  border-width:0px;
  position:absolute;
  left:126px;
  top:158px;
  width:103px;
  height:30px;
  display:flex;
}
#u3883 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3883_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3884_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:30px;
}
#u3884 {
  border-width:0px;
  position:absolute;
  left:229px;
  top:158px;
  width:95px;
  height:30px;
  display:flex;
}
#u3884 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3884_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3885_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
}
#u3885 {
  border-width:0px;
  position:absolute;
  left:324px;
  top:158px;
  width:110px;
  height:30px;
  display:flex;
}
#u3885 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3885_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3886_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:30px;
}
#u3886 {
  border-width:0px;
  position:absolute;
  left:434px;
  top:158px;
  width:105px;
  height:30px;
  display:flex;
}
#u3886 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3886_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3887_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:107px;
  height:30px;
}
#u3887 {
  border-width:0px;
  position:absolute;
  left:539px;
  top:158px;
  width:107px;
  height:30px;
  display:flex;
}
#u3887 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3887_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3888_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:30px;
}
#u3888 {
  border-width:0px;
  position:absolute;
  left:646px;
  top:158px;
  width:89px;
  height:30px;
  display:flex;
}
#u3888 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3888_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3889_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u3889 {
  border-width:0px;
  position:absolute;
  left:735px;
  top:158px;
  width:116px;
  height:30px;
  display:flex;
}
#u3889 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3889_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3890_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u3890 {
  border-width:0px;
  position:absolute;
  left:851px;
  top:158px;
  width:120px;
  height:30px;
  display:flex;
}
#u3890 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3890_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3891_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:30px;
}
#u3891 {
  border-width:0px;
  position:absolute;
  left:971px;
  top:158px;
  width:81px;
  height:30px;
  display:flex;
}
#u3891 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3891_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3892_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:30px;
}
#u3892 {
  border-width:0px;
  position:absolute;
  left:1052px;
  top:158px;
  width:94px;
  height:30px;
  display:flex;
}
#u3892 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3892_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3893_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:154px;
  height:30px;
}
#u3893 {
  border-width:0px;
  position:absolute;
  left:1146px;
  top:158px;
  width:154px;
  height:30px;
  display:flex;
}
#u3893 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3893_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3894_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:30px;
}
#u3894 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:188px;
  width:45px;
  height:30px;
  display:flex;
}
#u3894 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3894_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3895_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:30px;
}
#u3895 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:188px;
  width:81px;
  height:30px;
  display:flex;
}
#u3895 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3895_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3896_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:30px;
}
#u3896 {
  border-width:0px;
  position:absolute;
  left:126px;
  top:188px;
  width:103px;
  height:30px;
  display:flex;
}
#u3896 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3896_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3897_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:30px;
}
#u3897 {
  border-width:0px;
  position:absolute;
  left:229px;
  top:188px;
  width:95px;
  height:30px;
  display:flex;
}
#u3897 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3897_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3898_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
}
#u3898 {
  border-width:0px;
  position:absolute;
  left:324px;
  top:188px;
  width:110px;
  height:30px;
  display:flex;
}
#u3898 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3898_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3899_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:30px;
}
#u3899 {
  border-width:0px;
  position:absolute;
  left:434px;
  top:188px;
  width:105px;
  height:30px;
  display:flex;
}
#u3899 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3899_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3900_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:107px;
  height:30px;
}
#u3900 {
  border-width:0px;
  position:absolute;
  left:539px;
  top:188px;
  width:107px;
  height:30px;
  display:flex;
}
#u3900 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3900_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3901_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:30px;
}
#u3901 {
  border-width:0px;
  position:absolute;
  left:646px;
  top:188px;
  width:89px;
  height:30px;
  display:flex;
}
#u3901 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3901_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3902_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u3902 {
  border-width:0px;
  position:absolute;
  left:735px;
  top:188px;
  width:116px;
  height:30px;
  display:flex;
}
#u3902 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3902_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3903_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u3903 {
  border-width:0px;
  position:absolute;
  left:851px;
  top:188px;
  width:120px;
  height:30px;
  display:flex;
}
#u3903 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3903_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3904_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:30px;
}
#u3904 {
  border-width:0px;
  position:absolute;
  left:971px;
  top:188px;
  width:81px;
  height:30px;
  display:flex;
}
#u3904 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3904_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3905_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:30px;
}
#u3905 {
  border-width:0px;
  position:absolute;
  left:1052px;
  top:188px;
  width:94px;
  height:30px;
  display:flex;
}
#u3905 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3905_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3906_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:154px;
  height:30px;
}
#u3906 {
  border-width:0px;
  position:absolute;
  left:1146px;
  top:188px;
  width:154px;
  height:30px;
  display:flex;
}
#u3906 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3906_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3907_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:30px;
}
#u3907 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:218px;
  width:45px;
  height:30px;
  display:flex;
}
#u3907 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3907_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3908_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:30px;
}
#u3908 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:218px;
  width:81px;
  height:30px;
  display:flex;
}
#u3908 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3908_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3909_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:30px;
}
#u3909 {
  border-width:0px;
  position:absolute;
  left:126px;
  top:218px;
  width:103px;
  height:30px;
  display:flex;
}
#u3909 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3909_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3910_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:30px;
}
#u3910 {
  border-width:0px;
  position:absolute;
  left:229px;
  top:218px;
  width:95px;
  height:30px;
  display:flex;
}
#u3910 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3910_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3911_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
}
#u3911 {
  border-width:0px;
  position:absolute;
  left:324px;
  top:218px;
  width:110px;
  height:30px;
  display:flex;
}
#u3911 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3911_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3912_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:30px;
}
#u3912 {
  border-width:0px;
  position:absolute;
  left:434px;
  top:218px;
  width:105px;
  height:30px;
  display:flex;
}
#u3912 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3912_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3913_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:107px;
  height:30px;
}
#u3913 {
  border-width:0px;
  position:absolute;
  left:539px;
  top:218px;
  width:107px;
  height:30px;
  display:flex;
}
#u3913 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3913_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3914_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:30px;
}
#u3914 {
  border-width:0px;
  position:absolute;
  left:646px;
  top:218px;
  width:89px;
  height:30px;
  display:flex;
}
#u3914 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3914_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3915_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u3915 {
  border-width:0px;
  position:absolute;
  left:735px;
  top:218px;
  width:116px;
  height:30px;
  display:flex;
}
#u3915 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3915_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3916_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u3916 {
  border-width:0px;
  position:absolute;
  left:851px;
  top:218px;
  width:120px;
  height:30px;
  display:flex;
}
#u3916 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3916_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3917_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:30px;
}
#u3917 {
  border-width:0px;
  position:absolute;
  left:971px;
  top:218px;
  width:81px;
  height:30px;
  display:flex;
}
#u3917 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3917_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3918_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:30px;
}
#u3918 {
  border-width:0px;
  position:absolute;
  left:1052px;
  top:218px;
  width:94px;
  height:30px;
  display:flex;
}
#u3918 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3918_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3919_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:154px;
  height:30px;
}
#u3919 {
  border-width:0px;
  position:absolute;
  left:1146px;
  top:218px;
  width:154px;
  height:30px;
  display:flex;
}
#u3919 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3919_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3920_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:30px;
}
#u3920 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:248px;
  width:45px;
  height:30px;
  display:flex;
}
#u3920 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3920_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3921_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:30px;
}
#u3921 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:248px;
  width:81px;
  height:30px;
  display:flex;
}
#u3921 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3921_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3922_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:30px;
}
#u3922 {
  border-width:0px;
  position:absolute;
  left:126px;
  top:248px;
  width:103px;
  height:30px;
  display:flex;
}
#u3922 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3922_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3923_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:30px;
}
#u3923 {
  border-width:0px;
  position:absolute;
  left:229px;
  top:248px;
  width:95px;
  height:30px;
  display:flex;
}
#u3923 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3923_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3924_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
}
#u3924 {
  border-width:0px;
  position:absolute;
  left:324px;
  top:248px;
  width:110px;
  height:30px;
  display:flex;
}
#u3924 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3924_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3925_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:30px;
}
#u3925 {
  border-width:0px;
  position:absolute;
  left:434px;
  top:248px;
  width:105px;
  height:30px;
  display:flex;
}
#u3925 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3925_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3926_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:107px;
  height:30px;
}
#u3926 {
  border-width:0px;
  position:absolute;
  left:539px;
  top:248px;
  width:107px;
  height:30px;
  display:flex;
}
#u3926 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3926_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3927_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:30px;
}
#u3927 {
  border-width:0px;
  position:absolute;
  left:646px;
  top:248px;
  width:89px;
  height:30px;
  display:flex;
}
#u3927 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3927_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3928_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u3928 {
  border-width:0px;
  position:absolute;
  left:735px;
  top:248px;
  width:116px;
  height:30px;
  display:flex;
}
#u3928 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3928_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3929_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u3929 {
  border-width:0px;
  position:absolute;
  left:851px;
  top:248px;
  width:120px;
  height:30px;
  display:flex;
}
#u3929 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3929_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3930_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:30px;
}
#u3930 {
  border-width:0px;
  position:absolute;
  left:971px;
  top:248px;
  width:81px;
  height:30px;
  display:flex;
}
#u3930 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3930_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3931_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:30px;
}
#u3931 {
  border-width:0px;
  position:absolute;
  left:1052px;
  top:248px;
  width:94px;
  height:30px;
  display:flex;
}
#u3931 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3931_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3932_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:154px;
  height:30px;
}
#u3932 {
  border-width:0px;
  position:absolute;
  left:1146px;
  top:248px;
  width:154px;
  height:30px;
  display:flex;
}
#u3932 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3932_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3933_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:30px;
}
#u3933 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:278px;
  width:45px;
  height:30px;
  display:flex;
}
#u3933 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3933_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3934_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:30px;
}
#u3934 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:278px;
  width:81px;
  height:30px;
  display:flex;
}
#u3934 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3934_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3935_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:30px;
}
#u3935 {
  border-width:0px;
  position:absolute;
  left:126px;
  top:278px;
  width:103px;
  height:30px;
  display:flex;
}
#u3935 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3935_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3936_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:30px;
}
#u3936 {
  border-width:0px;
  position:absolute;
  left:229px;
  top:278px;
  width:95px;
  height:30px;
  display:flex;
}
#u3936 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3936_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3937_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
}
#u3937 {
  border-width:0px;
  position:absolute;
  left:324px;
  top:278px;
  width:110px;
  height:30px;
  display:flex;
}
#u3937 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3937_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3938_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:30px;
}
#u3938 {
  border-width:0px;
  position:absolute;
  left:434px;
  top:278px;
  width:105px;
  height:30px;
  display:flex;
}
#u3938 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3938_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3939_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:107px;
  height:30px;
}
#u3939 {
  border-width:0px;
  position:absolute;
  left:539px;
  top:278px;
  width:107px;
  height:30px;
  display:flex;
}
#u3939 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3939_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3940_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:30px;
}
#u3940 {
  border-width:0px;
  position:absolute;
  left:646px;
  top:278px;
  width:89px;
  height:30px;
  display:flex;
}
#u3940 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3940_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3941_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u3941 {
  border-width:0px;
  position:absolute;
  left:735px;
  top:278px;
  width:116px;
  height:30px;
  display:flex;
}
#u3941 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3941_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3942_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u3942 {
  border-width:0px;
  position:absolute;
  left:851px;
  top:278px;
  width:120px;
  height:30px;
  display:flex;
}
#u3942 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3942_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3943_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:30px;
}
#u3943 {
  border-width:0px;
  position:absolute;
  left:971px;
  top:278px;
  width:81px;
  height:30px;
  display:flex;
}
#u3943 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3943_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3944_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:30px;
}
#u3944 {
  border-width:0px;
  position:absolute;
  left:1052px;
  top:278px;
  width:94px;
  height:30px;
  display:flex;
}
#u3944 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3944_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3945_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:154px;
  height:30px;
}
#u3945 {
  border-width:0px;
  position:absolute;
  left:1146px;
  top:278px;
  width:154px;
  height:30px;
  display:flex;
}
#u3945 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3945_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3946_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:30px;
}
#u3946 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:308px;
  width:45px;
  height:30px;
  display:flex;
}
#u3946 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3946_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3947_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:30px;
}
#u3947 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:308px;
  width:81px;
  height:30px;
  display:flex;
}
#u3947 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3947_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3948_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:30px;
}
#u3948 {
  border-width:0px;
  position:absolute;
  left:126px;
  top:308px;
  width:103px;
  height:30px;
  display:flex;
}
#u3948 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3948_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3949_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:30px;
}
#u3949 {
  border-width:0px;
  position:absolute;
  left:229px;
  top:308px;
  width:95px;
  height:30px;
  display:flex;
}
#u3949 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3949_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3950_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
}
#u3950 {
  border-width:0px;
  position:absolute;
  left:324px;
  top:308px;
  width:110px;
  height:30px;
  display:flex;
}
#u3950 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3950_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3951_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:30px;
}
#u3951 {
  border-width:0px;
  position:absolute;
  left:434px;
  top:308px;
  width:105px;
  height:30px;
  display:flex;
}
#u3951 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3951_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3952_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:107px;
  height:30px;
}
#u3952 {
  border-width:0px;
  position:absolute;
  left:539px;
  top:308px;
  width:107px;
  height:30px;
  display:flex;
}
#u3952 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3952_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3953_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:30px;
}
#u3953 {
  border-width:0px;
  position:absolute;
  left:646px;
  top:308px;
  width:89px;
  height:30px;
  display:flex;
}
#u3953 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3953_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3954_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u3954 {
  border-width:0px;
  position:absolute;
  left:735px;
  top:308px;
  width:116px;
  height:30px;
  display:flex;
}
#u3954 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3954_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3955_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u3955 {
  border-width:0px;
  position:absolute;
  left:851px;
  top:308px;
  width:120px;
  height:30px;
  display:flex;
}
#u3955 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3955_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3956_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:30px;
}
#u3956 {
  border-width:0px;
  position:absolute;
  left:971px;
  top:308px;
  width:81px;
  height:30px;
  display:flex;
}
#u3956 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3956_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3957_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:30px;
}
#u3957 {
  border-width:0px;
  position:absolute;
  left:1052px;
  top:308px;
  width:94px;
  height:30px;
  display:flex;
}
#u3957 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3957_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3958_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:154px;
  height:30px;
}
#u3958 {
  border-width:0px;
  position:absolute;
  left:1146px;
  top:308px;
  width:154px;
  height:30px;
  display:flex;
}
#u3958 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3958_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3959_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3959 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:573px;
  width:57px;
  height:16px;
  display:flex;
}
#u3959 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3959_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3960_input {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:22px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3960_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:22px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3960_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3960 {
  border-width:0px;
  position:absolute;
  left:97px;
  top:567px;
  width:80px;
  height:22px;
  display:flex;
}
#u3960 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3960_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:22px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3960.disabled {
}
.u3960_input_option {
}
#u3961_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3961 {
  border-width:0px;
  position:absolute;
  left:187px;
  top:573px;
  width:168px;
  height:16px;
  display:flex;
}
#u3961 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3961_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3962_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3962 {
  border-width:0px;
  position:absolute;
  left:365px;
  top:573px;
  width:28px;
  height:16px;
  display:flex;
}
#u3962 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3962_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3963_input {
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:22px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3963_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:22px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3963_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u3963 {
  border-width:0px;
  position:absolute;
  left:398px;
  top:567px;
  width:30px;
  height:22px;
  display:flex;
  text-align:center;
}
#u3963 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3963_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:22px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u3963.disabled {
}
#u3964_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3964 {
  border-width:0px;
  position:absolute;
  left:433px;
  top:573px;
  width:14px;
  height:16px;
  display:flex;
}
#u3964 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3964_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3965_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3965 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:180px;
  width:120px;
  height:30px;
  display:flex;
}
#u3965 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3965_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3966_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3966 {
  border-width:0px;
  position:absolute;
  left:1183px;
  top:261px;
  width:28px;
  height:16px;
  display:flex;
}
#u3966 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3966_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3967_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3967 {
  border-width:0px;
  position:absolute;
  left:280px;
  top:69px;
  width:56px;
  height:16px;
  display:flex;
}
#u3967 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3967_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3968_input {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3968_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3968_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u3968 {
  border-width:0px;
  position:absolute;
  left:346px;
  top:65px;
  width:120px;
  height:24px;
  display:flex;
  text-align:center;
}
#u3968 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3968_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u3968.disabled {
}
#u3969_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3969 {
  border-width:0px;
  position:absolute;
  left:516px;
  top:69px;
  width:28px;
  height:16px;
  display:flex;
}
#u3969 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3969_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3970_input {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3970_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3970_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u3970 {
  border-width:0px;
  position:absolute;
  left:554px;
  top:65px;
  width:120px;
  height:24px;
  display:flex;
  text-align:center;
}
#u3970 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3970_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u3970.disabled {
}
#u3971_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3971 {
  border-width:0px;
  position:absolute;
  left:1241px;
  top:261px;
  width:28px;
  height:16px;
  display:flex;
}
#u3971 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3971_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3972_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3972 {
  border-width:0px;
  position:absolute;
  left:1295px;
  top:261px;
  width:28px;
  height:16px;
  display:flex;
}
#u3972 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3972_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3973_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3973 {
  border-width:0px;
  position:absolute;
  left:1295px;
  top:295px;
  width:28px;
  height:16px;
  display:flex;
}
#u3973 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3973_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3974_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3974 {
  border-width:0px;
  position:absolute;
  left:67px;
  top:69px;
  width:28px;
  height:16px;
  display:flex;
}
#u3974 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3974_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3975_input {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3975_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3975_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#333333;
}
#u3975 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:65px;
  width:120px;
  height:24px;
  display:flex;
  color:#333333;
}
#u3975 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3975_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#333333;
}
#u3975.disabled {
}
.u3975_input_option {
}
#u3976_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3976 {
  border-width:0px;
  position:absolute;
  left:714px;
  top:69px;
  width:28px;
  height:16px;
  display:flex;
}
#u3976 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3976_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3977_input {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3977_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3977_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#333333;
}
#u3977 {
  border-width:0px;
  position:absolute;
  left:747px;
  top:65px;
  width:120px;
  height:24px;
  display:flex;
  color:#333333;
}
#u3977 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3977_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#333333;
}
#u3977.disabled {
}
.u3977_input_option {
}
#u3978_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3978 {
  border-width:0px;
  position:absolute;
  left:897px;
  top:69px;
  width:28px;
  height:16px;
  display:flex;
}
#u3978 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3978_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3979_input {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3979_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3979_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#333333;
}
#u3979 {
  border-width:0px;
  position:absolute;
  left:930px;
  top:65px;
  width:120px;
  height:24px;
  display:flex;
  color:#333333;
}
#u3979 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3979_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#333333;
}
#u3979.disabled {
}
.u3979_input_option {
}
