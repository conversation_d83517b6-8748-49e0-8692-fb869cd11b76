package com.yi.controller.sku.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

/**
 * SKU导出VO
 */
@Data
@HeadRowHeight(20)
@ContentRowHeight(18)
public class SkuExportVO {

    @ExcelProperty(value = "状态")
    @ColumnWidth(10)
    private String status;

    @ExcelProperty(value = "一级类目")
    @ColumnWidth(15)
    private String firstCategory;

    @ExcelProperty(value = "二级类目")
    @ColumnWidth(20)
    private String secondCategory;

    @ExcelProperty(value = "三级类目")
    @ColumnWidth(20)
    private String thirdCategory;

    @ExcelProperty(value = "规格")
    @ColumnWidth(25)
    private String specification;

    @ExcelProperty(value = "重量(Kg)")
    @ColumnWidth(12)
    private String weight;

    @ExcelProperty(value = "备注")
    @ColumnWidth(30)
    private String remark;

    @ExcelProperty(value = "创建人")
    @ColumnWidth(12)
    private String createdBy;

    @ExcelProperty(value = "创建时间")
    @ColumnWidth(20)
    private String createdTime;
}
