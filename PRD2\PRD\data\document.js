﻿$axure.loadDocument(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,_(c,d,e,f,g,d,h,d,i,d,j,k,l,d,m,f,n,f,o,d,p,f),q,_(r,[_(s,t,u,v,w,x,y,z),_(s,A,u,B,w,x,y,C),_(s,D,u,E,w,x,y,F,G,[_(s,H,u,I,w,x,y,J)]),_(s,K,u,L,w,x,y,M),_(s,N,u,O,w,P,y,N,G,[_(s,Q,u,R,w,x,y,S),_(s,T,u,U,w,x,y,V),_(s,N,u,W,w,P,y,N,G,[_(s,X,u,Y,w,x,y,Z,G,[_(s,ba,u,bb,w,x,y,bc)]),_(s,bd,u,be,w,x,y,bf,G,[_(s,bg,u,bh,w,x,y,bi)]),_(s,bj,u,bk,w,x,y,bl,G,[_(s,bm,u,bn,w,x,y,bo)]),_(s,bp,u,bq,w,x,y,br,G,[_(s,bs,u,bt,w,x,y,bu),_(s,bv,u,bw,w,x,y,bx)]),_(s,by,u,bz,w,x,y,bA,G,[_(s,bB,u,bC,w,x,y,bD),_(s,bE,u,bF,w,x,y,bG),_(s,bH,u,bI,w,x,y,bJ)]),_(s,bK,u,bL,w,x,y,bM,G,[_(s,bN,u,bO,w,x,y,bP)]),_(s,bQ,u,bR,w,x,y,bS,G,[_(s,bT,u,bU,w,x,y,bV)])]),_(s,N,u,bW,w,P,y,N,G,[_(s,N,u,bX,w,P,y,N,G,[_(s,bY,u,bX,w,x,y,bZ,G,[_(s,ca,u,cb,w,x,y,cc),_(s,cd,u,ce,w,x,y,cf)])]),_(s,N,u,cg,w,P,y,N,G,[_(s,ch,u,cg,w,x,y,ci,G,[_(s,cj,u,ck,w,x,y,cl)]),_(s,cm,u,cn,w,x,y,co,G,[_(s,cp,u,cq,w,x,y,cr)]),_(s,cs,u,ct,w,x,y,cu,G,[_(s,cv,u,cw,w,x,y,cx),_(s,cy,u,cz,w,x,y,cA)])]),_(s,N,u,cB,w,P,y,N,G,[_(s,cC,u,cD,w,x,y,cE,G,[_(s,cF,u,cG,w,x,y,cH),_(s,cI,u,cJ,w,x,y,cK)]),_(s,cL,u,cM,w,x,y,cN,G,[_(s,cO,u,cP,w,x,y,cQ)])]),_(s,N,u,cR,w,P,y,N,G,[_(s,cS,u,cT,w,x,y,cU,G,[_(s,cV,u,cW,w,x,y,cX)]),_(s,cY,u,cZ,w,x,y,da),_(s,db,u,dc,w,x,y,dd)]),_(s,N,u,de,w,P,y,N,G,[_(s,df,u,dg,w,x,y,dh,G,[_(s,di,u,dj,w,x,y,dk)]),_(s,dl,u,dm,w,x,y,dn,G,[_(s,dp,u,dq,w,x,y,dr),_(s,ds,u,dt,w,x,y,du)]),_(s,dv,u,dw,w,x,y,dx,G,[_(s,dy,u,cw,w,x,y,dz),_(s,dA,u,cz,w,x,y,dB)]),_(s,dC,u,de,w,x,y,dD)])]),_(s,N,u,dE,w,P,y,N,G,[_(s,dF,u,dG,w,x,y,dH),_(s,dI,u,dJ,w,x,y,dK),_(s,dL,u,dM,w,x,y,dN)])])]),dO,[dP,dQ,dR],dS,[dT,dU,dV],dW,_(dX,N),dY,_(dZ,_(s,ea,eb,ec,ed,ee,ef,eg,eh,ei,ej,_(ek,el,em,en,eo,ep),eq,er,es,f,et,eu,ev,eg,ew,eg,ex,ey,ez,f,eA,_(eB,eC,eD,eC),eE,_(eF,eC,eG,eC),eH,d,eI,f,eJ,ea,eK,_(ek,el,em,eL),eM,_(ek,el,em,eN),eO,eP,eQ,el,eo,eP,eR,eS,eT,eU,eV,eW,eX,eY,eZ,eY,fa,eY,fb,eY,fc,_(),fd,null,fe,null,ff,eS,fg,_(fh,f,fi,fj,fk,fj,fl,fj,fm,eC,em,_(fn,fo,fp,fo,fq,fo,fr,fs)),ft,_(fh,f,fi,eC,fk,fj,fl,fj,fm,eC,em,_(fn,fo,fp,fo,fq,fo,fr,fs)),fu,_(fh,f,fi,ep,fk,ep,fl,fj,fm,eC,em,_(fn,fo,fp,fo,fq,fo,fr,fv)),fw,fx),fy,_(fd,_(s,fz,eO,eS),fA,_(s,fB,eq,fC,ed,fD,eO,eS,eK,_(ek,el,em,fE),et,fF,eV,fG,eX,eS,eZ,eS,fa,eS,fb,eS),fH,_(s,fI,eq,fJ,ed,fD,eO,eS,eK,_(ek,el,em,fE),et,fF,eV,fG,eX,eS,eZ,eS,fa,eS,fb,eS),fK,_(s,fL,eq,fM,ed,fD,eO,eS,eK,_(ek,el,em,fE),et,fF,eV,fG,eX,eS,eZ,eS,fa,eS,fb,eS),fN,_(s,fO,eq,fP,ed,fD,eO,eS,eK,_(ek,el,em,fE),et,fF,eV,fG,eX,eS,eZ,eS,fa,eS,fb,eS),fQ,_(s,fR,ed,fD,eO,eS,eK,_(ek,el,em,fE),et,fF,eV,fG,eX,eS,eZ,eS,fa,eS,fb,eS),fS,_(s,fT,eq,fU,ed,fD,eO,eS,eK,_(ek,el,em,fE),et,fF,eV,fG,eX,eS,eZ,eS,fa,eS,fb,eS),fV,_(s,fW,eO,eS,eK,_(ek,el,em,fE),et,fF,eV,fG,eX,eS,eZ,eS,fa,eS,fb,eS),fX,_(s,fY),fZ,_(s,ga,ej,_(ek,el,em,gb,eo,ep)),gc,_(s,gd,eK,_(ek,el,em,ge)),gf,_(s,gg,eK,_(ek,gh,gi,_(eB,gj,eD,eC),gk,_(eB,gj,eD,ep),gl,[_(em,eL,gm,eC),_(em,gn,gm,eC),_(em,go,gm,ep),_(em,eL,gm,ep)])),gp,_(s,gq,eM,_(ek,el,em,fE),eO,eS,eK,_(ek,el,em,fE),et,fF,eV,fG,eX,eS,eZ,eS,fa,eS,fb,eS),gr,_(s,gs,eK,_(ek,el,em,fE)),gt,_(s,gu,ed,gv,eq,fC,eM,_(ek,el,em,fE),eO,eS,eK,_(ek,el,em,fE),et,fF,eV,fG,eX,eS,eZ,eS,fa,eS,fb,eS),gw,_(s,gx,eM,_(ek,el,em,gy)),gz,_(s,gA),gB,_(s,gC,eq,fC,ed,fD,eO,eS,eK,_(ek,el,em,fE),et,fF,eV,fG,eX,eS,eZ,eS,fa,eS,fb,eS),gD,_(s,gE,eO,eS,eK,_(ek,el,em,gn)),gF,_(s,gG,ej,_(ek,el,em,gy,eo,ep),eO,eS),gH,_(s,gI,eK,_(ek,el,em,gn)),gJ,_(s,gK,eM,_(ek,el,em,gy)),gL,_(s,gM,eb,gN,ed,ee,ef,eg,eh,ei,ej,_(ek,el,em,gO,eo,ep),eq,fM,eM,_(ek,el,em,fE),eK,_(ek,el,em,fE),eX,eS,eZ,eS,fa,eS,fb,eS),gP,_(s,gQ,eK,_(ek,el,em,fE)),gR,_(s,gS,ej,_(ek,el,em,eL,eo,ep),eK,_(ek,el,em,gT),eO,eS,eR,ei),gU,_(s,gV,eR,ei),gW,_(s,gX,eK,_(ek,el,em,gY),eO,eS),gZ,_(s,ha,eq,fP,et,fF,eK,_(ek,el,em,fE),eO,eS,eV,fG,eX,eS,eZ,eS,fa,eS,fb,eS),hb,_(s,hc,eM,_(ek,el,em,hd),eQ,he),hf,_(s,hg,et,fF,eK,_(ek,el,em,hh),eO,eS,eV,fG,eX,hi,eZ,hi,fa,hi,fb,hi,fg,_(fh,d,fi,fj,fk,fj,fl,fj,fm,eC,em,_(fn,fo,fp,fo,fq,fo,fr,hj))),hk,_(s,hl,ej,_(ek,el,em,eL,eo,ep),eK,_(ek,el,em,hm),eM,_(ek,el,em,eL),fg,_(fh,d,fi,ep,fk,ep,fl,fj,fm,eC,em,_(fn,fo,fp,fo,fq,fo,fr,hn))),ho,_(s,hp,et,fF,eK,_(ek,el,em,hq),eO,eS,eV,fG,eX,hi,eZ,hi,fa,hi,fb,hi,fg,_(fh,d,fi,fj,fk,fj,fl,fj,fm,eC,em,_(fn,fo,fp,fo,fq,fo,fr,hj))),hr,_(s,hs,eK,_(ek,el,em,en),eO,eS),ht,_(s,hu,ej,_(ek,el,em,gy,eo,ep),et,fF,eV,fG),hv,_(s,hw,et,fF,eV,fG),hx,_(s,hy),hz,_(s,hA,et,fF,eK,_(ek,el,em,hB),eO,eS,eV,fG,eX,hi,eZ,hi,fa,hi,fb,hi,fg,_(fh,d,fi,fj,fk,fj,fl,fj,fm,eC,em,_(fn,fo,fp,fo,fq,fo,fr,hj))),hC,_(s,hD),hE,_(s,hF,eK,_(ek,el,em,gn),eO,eS),hG,_(s,hH,eb,hI,eK,_(ek,el,em,fE),eM,_(ek,el,em,fE),eO,eS,eQ,ey,eX,eS,eZ,eS,fa,eS,fb,eS),hJ,_(s,hK,eb,ec,ed,eg,ef,eg,eh,ei,ej,_(ek,el,em,en,eo,ep),eq,er,ed,eg,ef,eg,es,f,eM,_(ek,el,em,hL),eO,eP,eQ,el,eT,eU,eR,eS,eK,_(ek,el,em,fE),eo,eP,fg,_(fh,f,fi,fj,fk,fj,fl,fj,fm,eC,em,_(fn,fo,fp,fo,fq,fo,fr,fs)),ft,_(fh,f,fi,eC,fk,fj,fl,fj,fm,eC,em,_(fn,fo,fp,fo,fq,fo,fr,fs)),fu,_(fh,f,fi,ep,fk,ep,fl,fj,fm,eC,em,_(fn,fo,fp,fo,fq,fo,fr,fv)),et,eu,eV,eW,eX,eY,eZ,eY,fa,eY,fb,eY,ev,eg),hM,_(s,hN),hO,_(s,hP,eb,hQ,ed,gv,ej,_(ek,el,em,hR,eo,ep),eq,hS,eM,_(ek,el,em,fE),eO,eS,fg,_(fh,f,fi,eC,fk,eC,fl,hT,fm,eC,em,_(fn,fo,fp,fo,fq,fo,fr,hU)),ft,_(fh,f,fi,eC,fk,eC,fl,hT,fm,eC,em,_(fn,fo,fp,fo,fq,fo,fr,hU)),eX,hV),hW,_(s,hX,ej,_(ek,el,em,gy,eo,ep),et,fF,eV,fG),hY,_(s,hZ,eq,fP,et,fF,eK,_(ek,el,em,fE),eO,eS,eV,fG,eX,eS,eZ,eS,fa,eS,fb,eS),ia,_(s,ib,eK,_(ek,el,em,gn),eO,eS),ic,_(s,id,ej,_(ek,el,em,gy,eo,ep),eO,eS),ie,_(s,ig,eK,_(ek,el,em,ih),eM,_(ek,el,em,fE),eO,eS,eX,hi,eZ,ei,fa,ei,fb,ei,fg,_(fh,f,fi,eC,fk,eC,fl,hT,fm,eC,em,_(fn,fo,fp,fo,fq,fo,fr,hU)),ft,_(fh,f,fi,eC,fk,eC,fl,hT,fm,eC,em,_(fn,fo,fp,fo,fq,fo,fr,hU))),ii,_(s,ij,ej,_(ek,el,em,gy,eo,ep),et,fF,eV,eW),ik,_(s,il,eb,im,eM,_(ek,el,em,fE),eO,eS,eQ,ey,eK,_(ek,el,em,fE),eX,eS,eZ,eS,fa,eS,fb,eS),io,_(s,ip,eb,ec,ed,eg,ef,eg,eh,ei,ej,_(ek,el,em,eL,eo,ep),eq,iq,ed,eg,ef,eg,es,f,eM,_(ek,el,em,eN),eO,eS,eQ,el,eT,eU,eR,eS,eK,_(ek,el,em,eL),eo,eP,fg,_(fh,f,fi,fj,fk,fj,fl,fj,fm,eC,em,_(fn,fo,fp,fo,fq,fo,fr,fs)),ft,_(fh,f,fi,eC,fk,fj,fl,fj,fm,eC,em,_(fn,fo,fp,fo,fq,fo,fr,fs)),fu,_(fh,f,fi,ep,fk,ep,fl,fj,fm,eC,em,_(fn,fo,fp,fo,fq,fo,fr,fv)),et,eu,eV,eW,eX,eY,eZ,eY,fa,eY,fb,eY,ev,eg),ir,_(s,is),it,_(s,iu,ej,_(ek,el,em,gT,eo,ep),eK,_(ek,el,em,fE),eO,eS),iv,_(s,iw,eM,_(ek,el,em,en)),ix,_(s,iy,eO,eS),iz,_(s,iA,eq,fM,ed,fD,eO,eS,eK,_(ek,el,em,fE),et,fF,eV,fG,eX,eS,eZ,eS,fa,eS,fb,eS),iB,_(s,iC,ej,_(ek,el,em,gb,eo,ep)),iD,_(s,iE,eK,_(ek,el,em,ge)),iF,_(s,iG,eK,_(ek,el,em,fE),eO,ei),iH,_(s,iI,eO,eS),iJ,_(s,iK,et,fF,eV,fG),iL,_(s,iM)),iN,_(iO,gI,iP,gE,iQ,ha,iR,gs,iS,gX,iT,gG,iU,gE,iV,gd,iW,ga,iX,hN,iY,ga,iZ,gd,ja,gx,jb,hN,jc,gd,jd,ij,je,hN,jf,gE,jg,ga,jh,gd,ji,ij,jj,gs,jk,gI,jl,hs,jm,fB,jn,ha,jo,hy,jp,hN,jq,ha,jr,gE,js,gs,jt,hy,ju,iy,jv,hu,jw,ij)));}; 
var b="configuration",c="showPageNotes",d=true,e="showPageNoteNames",f=false,g="showAnnotations",h="showAnnotationsSidebar",i="showConsole",j="linkStyle",k="displayMultipleTargetsOnly",l="linkFlowsToPages",m="linkFlowsToPagesNewWindow",n="useLabels",o="useViews",p="loadFeedbackPlugin",q="sitemap",r="rootNodes",s="id",t="t3l6l2",u="pageName",v="版本记录",w="type",x="Wireframe",y="url",z="版本记录.html",A="71h5tq",B="项目背景",C="项目背景.html",D="8c1rcp",E="业务流程",F="业务流程.html",G="children",H="y9ubnk",I="采购流程",J="采购流程.html",K="c7jw6n",L="全局说明",M="全局说明.html",N="",O="后台管理系统",P="Folder",Q="4kqg1h",R="登录",S="登录.html",T="hjcbea",U="前端架构",V="前端架构.html",W="运营平台",X="7sedvc",Y="SKU管理",Z="sku管理.html",ba="8j0p6x",bb="SKU新增/编辑/详情页",bc="sku新增_编辑_详情页.html",bd="p27n9v",be="客户管理",bf="客户管理.html",bg="5tynq0",bh="客户新增/编辑/详情页",bi="客户新增_编辑_详情页.html",bj="5bbm3v",bk="客户仓库管理",bl="客户仓库管理.html",bm="vyeqi2",bn="客户仓库新增/编辑/详情页",bo="客户仓库新增_编辑_详情页.html",bp="i43oaa",bq="合同管理",br="合同管理.html",bs="pgidfu",bt="合同新增/编辑",bu="合同新增_编辑.html",bv="553wk0",bw="合同详情",bx="合同详情.html",by="nk2pvl",bz="订单管理",bA="订单管理.html",bB="9wjx4w",bC="订单新增/编辑",bD="订单新增_编辑.html",bE="mleit6",bF="订单详情页",bG="订单详情页.html",bH="e43d17",bI="订单管理与WMS系统交互",bJ="订单管理与wms系统交互.html",bK="uk0ref",bL="客户签收管理",bM="客户签收管理.html",bN="f74kha",bO="签收单云仓交互接口",bP="签收单云仓交互接口.html",bQ="csh773",bR="回收单管理",bS="回收单管理.html",bT="vyrybp",bU="回收单详情",bV="回收单详情.html",bW="仓储WMS",bX="发货需求",bY="344dyy",bZ="发货需求.html",ca="3u5bc7",cb="确认发货",cc="确认发货.html",cd="eknmoh",ce="发货需求详情",cf="发货需求详情.html",cg="供应商管理",ch="btqh0k",ci="供应商管理.html",cj="y08iax",ck="供应商新增/编辑/详情",cl="供应商新增_编辑_详情.html",cm="f1r266",cn="供应商仓库",co="供应商仓库.html",cp="cz3bmb",cq="供应商仓库添加/编辑/详情",cr="供应商仓库添加_编辑_详情.html",cs="u8ekkz",ct="供应商出库",cu="供应商出库.html",cv="vgc2wn",cw="确认出库",cx="确认出库.html",cy="t8vyrf",cz="出库单详情",cA="出库单详情.html",cB="采购管理",cC="p94bkr",cD="采购合同",cE="采购合同.html",cF="icqh03",cG="采购合同新增/编辑",cH="采购合同新增_编辑.html",cI="9od2tr",cJ="采购合同详情页",cK="采购合同详情页.html",cL="ur6j6n",cM="采购订单",cN="采购订单.html",cO="f7nli4",cP="采购订单详情",cQ="采购订单详情.html",cR="基础数据",cS="5fxktm",cT="仓库管理",cU="仓库管理.html",cV="52x4t8",cW="仓库添加/编辑/详情",cX="仓库添加_编辑_详情.html",cY="b1xket",cZ="人员管理",da="人员管理.html",db="her1qj",dc="设备管理",dd="设备管理.html",de="库存管理",df="x6t9xf",dg="编码管理",dh="编码管理.html",di="g42phs",dj="RFID编码详情",dk="rfid编码详情.html",dl="qtinqk",dm="入库管理",dn="入库管理.html",dp="w6um4j",dq="确认入库",dr="确认入库.html",ds="jaub2h",dt="入库单详情",du="入库单详情.html",dv="ssoplj",dw="出库管理",dx="出库管理.html",dy="c91zmo",dz="确认出库_1.html",dA="h4ki3q",dB="出库单详情_1.html",dC="gtunpy",dD="库存管理.html",dE="后台管理中心",dF="svt3u1",dG="菜单管理",dH="菜单管理.html",dI="w7lg1x",dJ="角色管理",dK="角色管理.html",dL="yq4vy0",dM="账号管理",dN="账号管理.html",dO="additionalJs",dP="plugins/sitemap/sitemap.js",dQ="plugins/page_notes/page_notes.js",dR="plugins/debug/debug.js",dS="additionalCss",dT="plugins/sitemap/styles/sitemap.css",dU="plugins/page_notes/styles/page_notes.css",dV="plugins/debug/styles/debug.css",dW="globalVariables",dX="onloadvariable",dY="stylesheet",dZ="defaultStyle",ea="627587b6038d43cca051c114ac41ad32",eb="fontName",ec="'Arial Normal', 'Arial', sans-serif",ed="fontWeight",ee="400",ef="fontStyle",eg="normal",eh="fontStretch",ei="5",ej="foreGroundFill",ek="fillType",el="solid",em="color",en=0xFF333333,eo="opacity",ep=1,eq="fontSize",er="13px",es="underline",et="horizontalAlignment",eu="center",ev="lineSpacing",ew="characterSpacing",ex="letterCase",ey="none",ez="strikethrough",eA="location",eB="x",eC=0,eD="y",eE="size",eF="width",eG="height",eH="visible",eI="limbo",eJ="baseStyle",eK="fill",eL=0xFFFFFFFF,eM="borderFill",eN=0xFF797979,eO="borderWidth",eP="1",eQ="linePattern",eR="cornerRadius",eS="0",eT="borderVisibility",eU="all",eV="verticalAlignment",eW="middle",eX="paddingLeft",eY="2",eZ="paddingTop",fa="paddingRight",fb="paddingBottom",fc="stateStyles",fd="image",fe="imageFilter",ff="rotation",fg="outerShadow",fh="on",fi="offsetX",fj=5,fk="offsetY",fl="blurRadius",fm="spread",fn="r",fo=0,fp="g",fq="b",fr="a",fs=0.349019607843137,ft="innerShadow",fu="textShadow",fv=0.647058823529412,fw="viewOverride",fx="19e82109f102476f933582835c373474",fy="customStyles",fz="75a91ee5b9d042cfa01b8d565fe289c0",fA="heading_1",fB="********************************",fC="32px",fD="bold",fE=0xFFFFFF,fF="left",fG="top",fH="heading_2",fI="b3a15c9ddde04520be40f94c8168891e",fJ="24px",fK="heading_3",fL="8c7a4c5ad69a4369a5f7788171ac0b32",fM="18px",fN="heading_4",fO="e995c891077945c89c0b5fe110d15a0b",fP="14px",fQ="heading_5",fR="386b19ef4be143bd9b6c392ded969f89",fS="heading_6",fT="fc3b9a13b5574fa098ef0a1db9aac861",fU="10px",fV="paragraph",fW="4988d43d80b44008a4a415096f1632af",fX="table_cell",fY="33ea2511485c479dbf973af3302f2352",fZ="form_hint",ga="4889d666e8ad4c5e81e59863039a5cc0",gb=0xFF999999,gc="form_disabled",gd="9bd0236217a94d89b0314c8c7fc75f16",ge=0xFFF0F0F0,gf="flow_shape",gg="df01900e3c4e43f284bafec04b0864c4",gh="linearGradient",gi="startPoint",gj=0.5,gk="endPoint",gl="stops",gm="offset",gn=0xFFF2F2F2,go=0xFFE4E4E4,gp="paragraph1",gq="e0621db17f4b42e0bd8f63006e6cfe5b",gr="line",gs="12e63bf1ccc1446488aa09e9482180bc",gt="heading_11",gu="922caedbf2d2483e8cf0bbbc50ba6e04",gv="700",gw="horizontal_line",gx="75a015e95a484881b32de65ff86808a9",gy=0xFF000000,gz="shape",gA="96fe18664bb44d8fb1e2f882b7f9a01e",gB="_一级标题",gC="4b8f5e102898448dba5411a5638ab2c9",gD="box_2",gE="********************************",gF="image1",gG="ca4260183c2644a8a871aab076cc5343",gH="placeholder",gI="47b939e366814c548b01bdfc89ac041a",gJ="vertical_line",gK="2ca94902ab764aab8e829de84285eabc",gL="iconfont",gM="f094e831ba764d0a99029dfb831cf97d",gN="'iconfont ', 'iconfont', sans-serif",gO=0xFF666666,gP="line1",gQ="0327e893a7994793993b54c636419b7c",gR="primary_button",gS="f9d2a29eec41403f99d04559928d6317",gT=0xFF169BD5,gU="button",gV="a9b576d5ce184cf79c9add2533771ed7",gW="box_3",gX="********************************",gY=0xFFD7D7D7,gZ="label",ha="df3da3fd8cfa4c4a81f05df7784209fe",hb="connector",hc="ef6d6e958c7a40c5b99bceea0bf45ef8",hd=0xFFAAAAAA,he="dashed",hf="sticky_2",hg="ec8253f38c164cb880018b92b1f199c2",hh=0xFF36A9CE,hi="10",hj=0.2,hk="marker",hl="a3f275eb5d5e4d2a8e58ec7b8b194ee3",hm=0xFF009DD9,hn=0.698039215686274,ho="sticky_1",hp="3106573e48474c3281b6db181d1a931f",hq=0xFFFFDF25,hr="icon",hs="1dd4fa5df9e944038ef21ce2a492b159",ht="droplist",hu="34051c82e56f4b559e0d2059489cfae6",hv="radio_button",hw="4eb5516f311c4bdfa0cb11d7ea75084e",hx="ellipse",hy="70e0f03cad8248b0b9df28783163eac9",hz="sticky_4",hA="4d378892942f4bfa806c3012975030c5",hB=0xFFEF5AA1,hC="_默认样式",hD="41c3289f39684ad5abdde1b77650a68f",hE="box_21",hF="********************************",hG="refs-design-material",hH="23d01608a3ea4cf7b3a9cf9184ee9664",hI="'Noto Sans CJK SC ', 'Noto Sans CJK SC', sans-serif",hJ="shape1",hK="90d35ce402044edaa21bc01893e3b17e",hL=0xFFFF3399,hM="box_1",hN="********************************",hO="_形状",hP="dd703d0d0ef445c8a2a1a5c340b927a1",hQ="'Microsoft YaHei UI Bold', 'Microsoft YaHei UI', sans-serif",hR=0xFFFF0066,hS="12px",hT=10,hU=0.313725490196078,hV="20",hW="text_area",hX="31e25fd64f1141f2aca1b3053c228d2d",hY="label1",hZ="221050eab8ec49809f054cb2ed08288e",ia="box_22",ib="********************************",ic="image2",id="0632fb4da6e946899c8708c118289531",ie="_形状1",ig="57e0a58fe415455db56acee5c65224d8",ih=0xFF199ED8,ii="text_field",ij="2170b7f9af5c48fba2adcd540f2ba1a0",ik="refs-webm-wechat",il="ceaf96f6b56a4dd08e3305c93256c0ff",im="'PingFang SC ', 'PingFang SC', sans-serif",io="_形状2",ip="dbedd8916aa640018081072055625ffe",iq="16px",ir="_形状3",is="63419a669b704de89819c2cfecefed0d",it="link_button",iu="4b88aa200ad64025ad561857a6779b03",iv="_连接",iw="9a554860cd984ee88d826baca493e649",ix="_图像_",iy="5a94201c04f8410b9999ce838697fd33",iz="_三级标题",iA="d917c8e1a1d443719a4ade367b8212ed",iB="_表单提示",iC="6376e9a4b7934f7386b10c75a26ee3d3",iD="_表单禁用",iE="0dbf24d2c7114186bfba326b070d97b0",iF="arrow",iG="856e4fbf4a7b496abe8501859a2b0838",iH="_图片",iI="11a20d8b5fc5487a9e38c57d774c7fee",iJ="checkbox",iK="********************************",iL="shape2",iM="064a91e709664fb89dac4ded19a9b2aa",iN="duplicateStyles",iO="c50e74f669b24b37bd9c18da7326bccd",iP="dcedf67fa034443b82dbf8834f12aa2b",iQ="a98bbf4b497e47598aaccf570a3b787d",iR="0d1d7c94dd714a039f289fe27f3728f9",iS="8c24695285374aa4a88be5c001763f6c",iT="78e8be46da0d4288ad5c74417d8a3b70",iU="74bf6caef3d9494687b41e834e5ab461",iV="f6d95a70206c46949adab64132b1e0ae",iW="a276f7a2a59144279f16e4b1f395b2ac",iX="d4152b971e23493f8f5abc2b667f99cb",iY="1ee06e3455794309b4a61f33dc0e6939",iZ="10dea2ea50ce49cba8d04a2650c71284",ja="0bb24488a19e42fc8585fda009d88ff1",jb="5aec28782e92449192c2396e11e2f34e",jc="eacf37b2b11344cd849e8972add83cde",jd="a9637558fe1b4efc95c9a7eb34de4763",je="61c94a4dd5a04e2a855e570108f0ed64",jf="b051d1e991da4141a3848719fb1269b2",jg="c6d270341a9b4bb4a92efcddf25ac2f0",jh="d38dc6e8db8742cd8005b4023986b759",ji="722506d735194319b154ff1238b390d3",jj="6a611f3f3748497aa0550212605b6013",jk="091052ea233a43c4b35ee9f5562936dd",jl="08f4d4e2986748eea332f05dbb860d35",jm="df5b05839bce463b8937d10b548a91fc",jn="e3de336e31594a60bc0966351496a9ce",jo="138c689a8ac7487db90517e51327842a",jp="6b1f3394a7ba47a191696e91e1d1c815",jq="feba76ce88f34fde9c07380e5d5fe224",jr="a2dcec9b92f143359daf3dcbecb60a10",js="de03ca83a1964289a0e958a7e96f030a",jt="de45eb7fe6d04887b5796be297c5c8b9",ju="bc30be0952154b3cbe16ecfe079036ea",jv="2f3f8290c314460dbd795b571e38d9d2",jw="81c35dae1a244e83b5ee8b7e93e2b59d";
return _creator();
})());