package com.yi.constant;

/**
 * 全局常量表
 */
public class CoreConstants {
    /**
     * 默认的根节点path
     */
    public static final String ROOT_PATH_DEF = ",";
    /**
     * 默认的父id
     */
    public static final Long PARENT_ID_DEF = -1L;
    /**
     * 帐号密码加密 salt
     */
    public static final int PW_ENCORDER_SALT = 12;
    /**
     * 默认的菜单组code
     */
    public static final String MENU_GROUP_CODE_DEF = "DEF";

    /**
     * 临时存在ThreadLocal中的 appId key
     */
    public static final String CONTEXT_KEY_APP_ID = "currentAppId";
    /**
     * 临时存在ThreadLocal中的 userId key
     */
    public static final String CONTEXT_KEY_USER_ID = "currentUserId";
    /**
     * 临时存在ThreadLocal中的 userRole key
     */
    public static final String JWT_KEY_USER_ROLES = "userRoles";
    /**
     * 临时存在ThreadLocal中的 登录名 key
     */
    public static final String CONTEXT_KEY_USERNAME = "currentUserName";
    /**
     * 临时存在ThreadLocal中的 昵称 key
     */
    public static final String CONTEXT_KEY_NICKNAME = "currentNickName";
    /**
     * 临时存在ThreadLocal中的 token key
     */
    public static final String CONTEXT_KEY_APP_TOKEN = "currentAppToken";
    /**
     * 临时存在ThreadLocal中的 user company key
     */
    public static final String CONTEXT_KEY_USER_COMPANY = "currentUserCompany";

    /**
     * 临时存在ThreadLocal中的 用户类型 userType
     */
    public static final String CONTEXT_KEY_USER_TYPE = "currentUserType";

    /**  */
    public static final String JWT_KEY_USER_ID = "userId";
    /**  */
    public static final String JWT_KEY_NICK_NAME = "nickName";
    /**  */
    public static final String JWT_KEY_USER_NAME = "userName";
    /**  */
    public static final String JWT_KEY_HOST_ID = "hostId";

    public static final String JWT_KEY_USER_COMPANY = "userCompany";

    public static final String JWT_APP_KEY = "appKey";

    public static final String JWT_APP_SECRET = "appSecret";

    public static final String ZONE = "zone";

    public static final String TIME_ZONE_OFFSET = "timeZoneOffset";

    /**
     * 请求头header
     */
    public static final String HEADER_FACTORY_ID = "factoryId";

    /**
     * 工厂id.
     */
    public static final String CONTEXT_KEY_FACTORY_ID = "currentFactoryId";

    public static final String CONTEXT_KEY_SUPPLIER_ID = "currentSupplierId";


}
