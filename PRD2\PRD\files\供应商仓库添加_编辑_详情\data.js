﻿$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,_(g,h,i,_(j,k,l,k)),m,[],n,_(h,o),p,[q],r,_(s,t,u,v,g,w,x,_(),y,[],z,_(A,B,C,D,E,_(F,G,H,I),J,null,K,L,L,M,N,O,null,P,Q,R,S,T,U,V,Q,W,X,Y,_(F,G,H,Z),ba,Q,bb,X,bc,_(bd,be,bf,bg,bh,bg,bi,bg,bj,k,H,_(bk,bl,bm,bl,bn,bl,bo,bp)),i,_(j,k,l,k)),bq,_(),br,_(),bs,_(bt,[_(bu,bv,bw,h,bx,by,u,bz,bA,bB,bC,bD,z,_(i,_(j,bE,l,bF),A,bG,bH,_(bI,bJ,bK,bL)),bq,_(),bM,_(),bN,_(bO,bP),bQ,be),_(bu,bR,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(i,_(j,bT,l,bU),A,bV,bH,_(bI,bJ,bK,bW),V,bX,Y,_(F,G,H,bY)),bq,_(),bM,_(),bQ,be),_(bu,bZ,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(i,_(j,ca,l,cb),A,cc,bH,_(bI,cd,bK,ce)),bq,_(),bM,_(),bQ,be),_(bu,cf,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,ch,ci,bF),i,_(j,cj,l,ck),A,cl,bH,_(bI,cm,bK,cn)),bq,_(),bM,_(),bQ,be),_(bu,co,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,ch,ci,bF),i,_(j,cp,l,ck),A,cl,bH,_(bI,cq,bK,cr)),bq,_(),bM,_(),bQ,be),_(bu,cs,bw,h,bx,ct,u,cu,bA,cu,bC,bD,z,_(i,_(j,cv,l,cw),cx,_(cy,_(A,cz),cA,_(A,cB)),A,cC,bH,_(bI,cD,bK,cE)),cF,be,bq,_(),bM,_(),cG,h),_(bu,cH,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,ch,ci,bF),i,_(j,cp,l,ck),A,cl,bH,_(bI,cq,bK,cI)),bq,_(),bM,_(),bQ,be),_(bu,cJ,bw,h,bx,ct,u,cu,bA,cu,bC,bD,z,_(i,_(j,cK,l,cL),cx,_(cy,_(A,cz),cA,_(A,cB)),A,cC,bH,_(bI,cM,bK,cN)),cF,be,bq,_(),bM,_(),cG,h),_(bu,cO,bw,h,bx,cP,u,cQ,bA,cQ,bC,bD,z,_(i,_(j,cR,l,cL),A,cS,cx,_(cA,_(A,cB)),bH,_(bI,cD,bK,cN)),cF,be,bq,_(),bM,_()),_(bu,cT,bw,h,bx,cP,u,cQ,bA,cQ,bC,bD,z,_(i,_(j,cR,l,cL),A,cS,cx,_(cA,_(A,cB)),bH,_(bI,cU,bK,cN)),cF,be,bq,_(),bM,_()),_(bu,cV,bw,h,bx,cP,u,cQ,bA,cQ,bC,bD,z,_(i,_(j,cR,l,cL),A,cS,cx,_(cA,_(A,cB)),bH,_(bI,cW,bK,cN)),cF,be,bq,_(),bM,_()),_(bu,cX,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,bY,ci,bF),i,_(j,cY,l,cZ),A,cl,bH,_(bI,da,bK,db),dc,dd),bq,_(),bM,_(),bQ,be),_(bu,de,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,bY,ci,bF),i,_(j,cY,l,cZ),A,cl,bH,_(bI,df,bK,db),dc,dd),bq,_(),bM,_(),bQ,be),_(bu,dg,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,bY,ci,bF),i,_(j,cY,l,cZ),A,cl,bH,_(bI,dh,bK,db),dc,dd),bq,_(),bM,_(),bQ,be),_(bu,di,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,ch,ci,bF),i,_(j,dj,l,ck),A,cl,bH,_(bI,dk,bK,dl)),bq,_(),bM,_(),bQ,be),_(bu,dm,bw,h,bx,ct,u,cu,bA,cu,bC,bD,z,_(i,_(j,dn,l,cw),cx,_(cy,_(A,cz),cA,_(A,cB)),A,cC,bH,_(bI,cD,bK,cm)),cF,be,bq,_(),bM,_(),cG,h),_(bu,dp,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(cg,_(F,G,H,ch,ci,bF),i,_(j,dj,l,ck),A,cl,bH,_(bI,dq,bK,dl)),bq,_(),bM,_(),bQ,be),_(bu,dr,bw,h,bx,ct,u,cu,bA,cu,bC,bD,z,_(i,_(j,dn,l,cw),cx,_(cy,_(A,cz),cA,_(A,cB)),A,cC,bH,_(bI,ds,bK,cm)),cF,be,bq,_(),bM,_(),cG,h),_(bu,dt,bw,h,bx,bS,u,bz,bA,bz,bC,bD,z,_(i,_(j,du,l,dv),A,dw,bH,_(bI,dx,bK,dy),dc,dz),bq,_(),bM,_(),br,_(dA,_(dB,dC,dD,dE,dF,[_(dD,h,dG,h,dH,be,dI,dJ,dK,[_(dL,dM,dD,dN,dO,dP,dQ,_(dR,_(h,dN)),dS,_(dT,r,b,dU,dV,bD),dW,dX)])])),dY,bD,bQ,be),_(bu,dZ,bw,h,bx,cP,u,cQ,bA,cQ,bC,bD,z,_(i,_(j,cv,l,cw),A,cS,cx,_(cA,_(A,cB)),bH,_(bI,cD,bK,ea)),cF,be,bq,_(),bM,_())])),eb,_(),ec,_(ed,_(ee,ef),eg,_(ee,eh),ei,_(ee,ej),ek,_(ee,el),em,_(ee,en),eo,_(ee,ep),eq,_(ee,er),es,_(ee,et),eu,_(ee,ev),ew,_(ee,ex),ey,_(ee,ez),eA,_(ee,eB),eC,_(ee,eD),eE,_(ee,eF),eG,_(ee,eH),eI,_(ee,eJ),eK,_(ee,eL),eM,_(ee,eN),eO,_(ee,eP),eQ,_(ee,eR)));}; 
var b="url",c="供应商仓库添加_编辑_详情.html",d="generationDate",e=new Date(1753855221683.96),f="defaultAdaptiveView",g="name",h="",i="size",j="width",k=0,l="height",m="adaptiveViews",n="sketchKeys",o="s0",p="variables",q="OnLoadVariable",r="page",s="packageId",t="3235c66bdffe44039320079aa3d60827",u="type",v="Axure:Page",w="供应商仓库添加/编辑/详情",x="notes",y="annotations",z="style",A="baseStyle",B="627587b6038d43cca051c114ac41ad32",C="pageAlignment",D="center",E="fill",F="fillType",G="solid",H="color",I=0xFFFFFFFF,J="image",K="imageAlignment",L="near",M="imageRepeat",N="auto",O="favicon",P="sketchFactor",Q="0",R="colorStyle",S="appliedColor",T="fontName",U="Applied Font",V="borderWidth",W="borderVisibility",X="all",Y="borderFill",Z=0xFF797979,ba="cornerRadius",bb="cornerVisibility",bc="outerShadow",bd="on",be=false,bf="offsetX",bg=5,bh="offsetY",bi="blurRadius",bj="spread",bk="r",bl=0,bm="g",bn="b",bo="a",bp=0.349019607843137,bq="adaptiveStyles",br="interactionMap",bs="diagram",bt="objects",bu="id",bv="abf152cc73974fd693a23c9f0c03d34e",bw="label",bx="friendlyType",by="线段",bz="vectorShape",bA="styleType",bB="horizontalLine",bC="visible",bD=true,bE=1300,bF=1,bG="0327e893a7994793993b54c636419b7c",bH="location",bI="x",bJ=20,bK="y",bL=50,bM="imageOverrides",bN="images",bO="normal~",bP="images/客户管理/u350.svg",bQ="generateCompound",bR="8d252b2ce2e14cb0a048339f1c4412be",bS="矩形",bT=235,bU=30,bV="4701f00c92714d4e9eed94e9fe75cfe8",bW=21,bX="1",bY=0xFFAAAAAA,bZ="1b7e2a80f71a4d97a926ae4dc2b14112",ca=56,cb=19,cc="4b88aa200ad64025ad561857a6779b03",cd=1264,ce=32,cf="bd0f60db5480460a9ec7e1f44c5a8dd3",cg="foreGroundFill",ch=0xFF000000,ci="opacity",cj=76,ck=16,cl="df3da3fd8cfa4c4a81f05df7784209fe",cm=264,cn=108,co="e6e72540b86c419cbd919a3211be0638",cp=62,cq=278,cr=159,cs="77fc96e17f154b71be19895bb0e4230a",ct="文本框",cu="textBox",cv=700,cw=26,cx="stateStyles",cy="hint",cz="4889d666e8ad4c5e81e59863039a5cc0",cA="disabled",cB="9bd0236217a94d89b0314c8c7fc75f16",cC="2170b7f9af5c48fba2adcd540f2ba1a0",cD=350,cE=154,cF="HideHintOnFocused",cG="placeholderText",cH="2d3e2290f4744bd9b59b59abc0c44986",cI=214,cJ="a384f57e612a43438181d456d92dafcc",cK=220,cL=24,cM=830,cN=210,cO="6a61a0967cfc46ba947e1834819827ac",cP="下拉列表",cQ="comboBox",cR=150,cS="********************************",cT="552bb6c268b14cd08e690851e1c9f61b",cU=510,cV="9df4320d009948feaccd3863efbf7ce2",cW=670,cX="fdbf94bb153f466d8ed53b96b8d9dcb6",cY=12,cZ=14,da=466,db=215,dc="fontSize",dd="12px",de="000fab08b5e847288b22e932d114fab9",df=630,dg="6fc8166818224b84af81c35f40fa3426",dh=790,di="059d8c816bee46958efbd294e3148152",dj=48,dk=292,dl=269,dm="632918e7f55f4cca8045058282430441",dn=200,dp="01947ba7bd814fb290b1cf0be1303a78",dq=792,dr="8f3bd94839294b8a902875ad453422be",ds=850,dt="3d91cdfa9c94462485ec2edf70176e0e",du=140,dv=40,dw="f9d2a29eec41403f99d04559928d6317",dx=605,dy=344,dz="16px",dA="onClick",dB="eventType",dC="Click时",dD="description",dE="单击时",dF="cases",dG="conditionString",dH="isNewIfGroup",dI="caseColorHex",dJ="AB68FF",dK="actions",dL="action",dM="linkWindow",dN="打开 供应商仓库 在 当前窗口",dO="displayName",dP="打开链接",dQ="actionInfoDescriptions",dR="供应商仓库",dS="target",dT="targetType",dU="供应商仓库.html",dV="includeVariables",dW="linkType",dX="current",dY="tabbable",dZ="9a7fecbccb5a49b884c14d1e115a3879",ea=103,eb="masters",ec="objectPaths",ed="abf152cc73974fd693a23c9f0c03d34e",ee="scriptId",ef="u2716",eg="8d252b2ce2e14cb0a048339f1c4412be",eh="u2717",ei="1b7e2a80f71a4d97a926ae4dc2b14112",ej="u2718",ek="bd0f60db5480460a9ec7e1f44c5a8dd3",el="u2719",em="e6e72540b86c419cbd919a3211be0638",en="u2720",eo="77fc96e17f154b71be19895bb0e4230a",ep="u2721",eq="2d3e2290f4744bd9b59b59abc0c44986",er="u2722",es="a384f57e612a43438181d456d92dafcc",et="u2723",eu="6a61a0967cfc46ba947e1834819827ac",ev="u2724",ew="552bb6c268b14cd08e690851e1c9f61b",ex="u2725",ey="9df4320d009948feaccd3863efbf7ce2",ez="u2726",eA="fdbf94bb153f466d8ed53b96b8d9dcb6",eB="u2727",eC="000fab08b5e847288b22e932d114fab9",eD="u2728",eE="6fc8166818224b84af81c35f40fa3426",eF="u2729",eG="059d8c816bee46958efbd294e3148152",eH="u2730",eI="632918e7f55f4cca8045058282430441",eJ="u2731",eK="01947ba7bd814fb290b1cf0be1303a78",eL="u2732",eM="8f3bd94839294b8a902875ad453422be",eN="u2733",eO="3d91cdfa9c94462485ec2edf70176e0e",eP="u2734",eQ="9a7fecbccb5a49b884c14d1e115a3879",eR="u2735";
return _creator();
})());