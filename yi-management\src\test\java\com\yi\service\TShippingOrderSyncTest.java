package com.yi.service;

import com.yi.controller.shippingorder.model.ShippingOrderRequest;
import com.yi.entity.TShippingDemand;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 发运订单同步发货需求单测试
 */
@ExtendWith(MockitoExtension.class)
@SpringBootTest
public class TShippingOrderSyncTest {

    @InjectMocks
    private TShippingOrderService shippingOrderService;

    @Mock
    private TShippingDemandService shippingDemandService;

    @Mock
    private TGeneralFileService generalFileService;

    @Mock
    private TCustomerWarehouseService customerWarehouseService;

    @Mock
    private TCustomerCompanyService customerCompanyService;

    private ShippingOrderRequest orderRequest;

    @BeforeEach
    void setUp() {
        // 准备订单请求数据
        orderRequest = new ShippingOrderRequest();
        orderRequest.setContractCode("CT001");
        orderRequest.setCustomerCompanyId("100");
        orderRequest.setWarehouseId("200");
        orderRequest.setFirstCategory("1");
        orderRequest.setSecondCategory("标准托盘");
        orderRequest.setCount("50");
        orderRequest.setDemandTime("2024-12-31");
        orderRequest.setRemark("测试订单");
        orderRequest.setAttachmentUrls(Arrays.asList("contract.pdf", "photo.jpg"));
    }

    @Test
    void testAddShippingOrder_ShouldCreateShippingDemand() {
        // Mock 发货需求保存
        when(shippingDemandService.save(any(TShippingDemand.class))).thenReturn(true);

        // Mock 附件保存
        doNothing().when(generalFileService).saveShippingOrderAttachments(anyLong(), anyList());

        // 执行订单创建
        boolean result = shippingOrderService.addShippingOrder(orderRequest);

        // 验证结果
        assertTrue(result);

        // 验证发货需求单被创建
        ArgumentCaptor<TShippingDemand> demandCaptor = ArgumentCaptor.forClass(TShippingDemand.class);
        verify(shippingDemandService, times(1)).save(demandCaptor.capture());

        TShippingDemand capturedDemand = demandCaptor.getValue();
        
        // 验证发货需求单的基本信息
        assertNotNull(capturedDemand.getOrderNo());
        assertTrue(capturedDemand.getOrderNo().startsWith("XSD"));
        
        // 验证关联信息
        assertEquals(Long.valueOf(100), capturedDemand.getCustomerCompanyId());
        assertEquals(Long.valueOf(200), capturedDemand.getWarehouseId());
        assertEquals(Integer.valueOf(1), capturedDemand.getFirstCategory());
        assertEquals("标准托盘", capturedDemand.getSecondCategory());
        
        // 验证数量信息
        assertEquals(Integer.valueOf(50), capturedDemand.getDemandQuantity());
        assertEquals(Integer.valueOf(50), capturedDemand.getPendingQuantity());
        assertEquals(Integer.valueOf(0), capturedDemand.getShippedQuantity());
        assertEquals(Integer.valueOf(0), capturedDemand.getUnexecutedQuantity());
        
        // 验证状态信息
        assertEquals("PENDING", capturedDemand.getStatus());
        assertEquals("测试订单", capturedDemand.getRemark());
        assertEquals("system", capturedDemand.getCreatedBy());
        assertEquals("system", capturedDemand.getLastModifiedBy());
        assertEquals(Integer.valueOf(1), capturedDemand.getValid());

        // 验证附件保存被调用
        verify(generalFileService, times(1)).saveShippingOrderAttachments(anyLong(), eq(Arrays.asList("contract.pdf", "photo.jpg")));
    }

    @Test
    void testAddShippingOrder_DemandCreationFails_ShouldThrowException() {
        // Mock 发货需求保存失败
        when(shippingDemandService.save(any(TShippingDemand.class))).thenThrow(new RuntimeException("数据库错误"));

        // 执行订单创建，应该抛出异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            shippingOrderService.addShippingOrder(orderRequest);
        });

        // 验证异常信息
        assertTrue(exception.getMessage().contains("创建发货需求单失败"));

        // 验证发货需求单创建被尝试
        verify(shippingDemandService, times(1)).save(any(TShippingDemand.class));
    }



    @Test
    void testAddShippingOrder_WithoutAttachments_ShouldStillCreateDemand() {
        // 准备没有附件的订单请求
        orderRequest.setAttachmentUrls(null);

        // Mock 发货需求保存
        when(shippingDemandService.save(any(TShippingDemand.class))).thenReturn(true);

        // 执行订单创建
        boolean result = shippingOrderService.addShippingOrder(orderRequest);

        // 验证结果
        assertTrue(result);

        // 验证发货需求单被创建
        verify(shippingDemandService, times(1)).save(any(TShippingDemand.class));

        // 验证附件保存没有被调用
        verify(generalFileService, never()).saveShippingOrderAttachments(anyLong(), anyList());
    }


}
