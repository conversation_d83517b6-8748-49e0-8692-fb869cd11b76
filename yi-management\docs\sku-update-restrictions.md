# SKU更新接口限制说明

## 概述

为了保证SKU数据的一致性和业务逻辑的完整性，对SKU更新接口(`updateSku`)添加了字段修改限制。

## 限制规则

### 不允许修改的字段

1. **一级类目 (firstCategory)**
   - 原因：一级类目是SKU的核心分类，修改会影响业务逻辑和数据关联
   - 错误信息：`"一级类目不允许修改"`

2. **二级类目 (secondCategory)**
   - 原因：二级类目与一级类目组合形成SKU的基础分类，修改会破坏数据完整性
   - 错误信息：`"二级类目不允许修改"`

### 允许修改的字段

- 三级类目 (thirdCategory)
- 长度 (length)
- 宽度 (width)
- 高度 (height)
- 重量 (weight)
- 备注 (remark)

## 实现逻辑

1. **获取原有数据**：通过ID查询现有SKU信息
2. **校验存在性**：确保SKU存在，不存在则抛出异常
3. **校验一级类目**：比较新旧一级类目，不一致则抛出异常
4. **校验二级类目**：比较新旧二级类目，不一致则抛出异常
5. **校验类目组合**：确保修改后的类目组合不重复
6. **执行更新**：通过校验后执行数据库更新操作

## 接口行为

### 成功场景
```json
{
  "code": 200,
  "message": "更新成功",
  "data": true
}
```

### 失败场景

#### 一级类目被修改
```json
{
  "code": 500,
  "message": "一级类目不允许修改",
  "data": null
}
```

#### 二级类目被修改
```json
{
  "code": 500,
  "message": "二级类目不允许修改",
  "data": null
}
```

#### SKU不存在
```json
{
  "code": 500,
  "message": "SKU不存在",
  "data": null
}
```

## 测试用例

已创建完整的单元测试覆盖以下场景：
- ✅ 正常更新（允许修改的字段）
- ❌ 修改一级类目（应抛出异常）
- ❌ 修改二级类目（应抛出异常）
- ❌ SKU不存在（应抛出异常）
- ❌ 类目组合重复（应抛出异常）
- ✅ 修改三级类目（应允许）
- ✅ 修改尺寸信息（应允许）

## 注意事项

1. **向后兼容性**：此限制是新增的业务规则，可能影响现有的更新操作
2. **数据迁移**：如需修改一级或二级类目，需要通过数据库直接操作或专门的迁移脚本
3. **前端适配**：前端界面应该将一级类目和二级类目设置为只读状态
4. **错误处理**：调用方需要妥善处理这些新增的异常情况

## 相关文件

- 服务实现：`yi-management/src/main/java/com/yi/service/TSkuService.java`
- 控制器：`yi-management/src/main/java/com/yi/controller/sku/TSkuController.java`
- 测试文件：`yi-management/src/test/java/com/yi/service/TSkuServiceUpdateTest.java`
- 实体类：`yi-management/src/main/java/com/yi/entity/TSku.java`
- 请求模型：`yi-management/src/main/java/com/yi/controller/sku/model/SkuRequest.java`
