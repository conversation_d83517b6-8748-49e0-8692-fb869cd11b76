package com.yi.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 文件上传配置
 */
@Component
@ConfigurationProperties(prefix = "file.upload")
public class FileUploadConfig {

    /**
     * 临时文件上传根路径
     */
    private String tempPath = "/uploads/temp";

    /**
     * 正式文件存储根路径
     */
    private String basePath = "/uploads";

    /**
     * 允许上传的文件类型
     */
    private String[] allowedTypes = {
            "jpg", "jpeg", "png", "gif", "bmp", "webp", // 图片
            "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", // 文档
            "txt", "csv", "zip", "rar", "7z" // 其他
    };

    /**
     * 单个文件最大大小（字节）默认10MB
     */
    private long maxFileSize = 10 * 1024 * 1024;

    /**
     * 总上传大小限制（字节）默认50MB
     */
    private long maxRequestSize = 50 * 1024 * 1024;

    /**
     * 临时文件访问URL前缀
     */
    private String tempUrlPrefix = "/temp-files";

    /**
     * 正式文件访问URL前缀
     */
    private String urlPrefix = "/files";

    /**
     * 临时文件保留时间（小时）默认24小时
     */
    private int tempFileRetentionHours = 24;

    public String getTempPath() {
        return tempPath;
    }

    public void setTempPath(String tempPath) {
        this.tempPath = tempPath;
    }

    public String getBasePath() {
        return basePath;
    }

    public void setBasePath(String basePath) {
        this.basePath = basePath;
    }

    public String[] getAllowedTypes() {
        return allowedTypes;
    }

    public void setAllowedTypes(String[] allowedTypes) {
        this.allowedTypes = allowedTypes;
    }

    public long getMaxFileSize() {
        return maxFileSize;
    }

    public void setMaxFileSize(long maxFileSize) {
        this.maxFileSize = maxFileSize;
    }

    public long getMaxRequestSize() {
        return maxRequestSize;
    }

    public void setMaxRequestSize(long maxRequestSize) {
        this.maxRequestSize = maxRequestSize;
    }

    public String getTempUrlPrefix() {
        return tempUrlPrefix;
    }

    public void setTempUrlPrefix(String tempUrlPrefix) {
        this.tempUrlPrefix = tempUrlPrefix;
    }

    public String getUrlPrefix() {
        return urlPrefix;
    }

    public void setUrlPrefix(String urlPrefix) {
        this.urlPrefix = urlPrefix;
    }

    public int getTempFileRetentionHours() {
        return tempFileRetentionHours;
    }

    public void setTempFileRetentionHours(int tempFileRetentionHours) {
        this.tempFileRetentionHours = tempFileRetentionHours;
    }
}
