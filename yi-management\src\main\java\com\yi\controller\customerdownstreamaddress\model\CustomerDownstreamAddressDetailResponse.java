package com.yi.controller.customerdownstreamaddress.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 客户下游地址关联详情响应
 */
@Data
@ApiModel(value = "CustomerDownstreamAddressDetailResponse", description = "客户下游地址关联详情响应")
public class CustomerDownstreamAddressDetailResponse {

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "下游公司名称")
    private String downstreamCompanyName;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "仓库地址")
    private String warehouseAddress;

    @ApiModelProperty(value = "联系人")
    private String contactPerson;

    @ApiModelProperty(value = "手机")
    private String mobilePhone;

    @ApiModelProperty(value = "座机")
    private String landlinePhone;

    @ApiModelProperty(value = "备注")
    private String remark;
}
