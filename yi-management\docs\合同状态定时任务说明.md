# 合同状态定时任务说明

## 功能概述
自动根据合同的生效日期和失效日期，结合当前时间，定时更新合同状态。

## 执行时间
- **定时执行**: 每天凌晨1点自动执行
- **Cron表达式**: `0 0 1 * * ?`
- **手动触发**: 通过API接口可手动触发执行

## 状态更新规则

### 1. 待生效 → 生效中
**触发条件**:
- 合同当前状态为"待生效"
- 当前日期 >= 合同生效日期
- 当前日期 <= 合同失效日期
- 合同有效性为1（有效）

**SQL条件**:
```sql
UPDATE t_contract 
SET contract_status = 2 
WHERE contract_status = 1 
  AND valid = 1 
  AND effective_date <= CURDATE() 
  AND expiry_date >= CURDATE()
```

### 2. 生效中 → 已到期
**触发条件**:
- 合同当前状态为"生效中"
- 当前日期 > 合同失效日期
- 合同有效性为1（有效）

**SQL条件**:
```sql
UPDATE t_contract 
SET contract_status = 3 
WHERE contract_status = 2 
  AND valid = 1 
  AND expiry_date < CURDATE()
```

## 状态枚举对照

| 状态码 | 状态名称 | 枚举值 |
|--------|----------|--------|
| 1 | 待生效 | ContractStatusEnum.PENDING |
| 2 | 生效中 | ContractStatusEnum.ACTIVE |
| 3 | 已到期 | ContractStatusEnum.EXPIRED |
| 4 | 已作废 | ContractStatusEnum.CANCELLED |

## 核心类文件

### 1. ContractStatusUpdateTask
- **路径**: `com.yi.task.ContractStatusUpdateTask`
- **功能**: 定时任务主类，包含状态更新逻辑
- **方法**:
  - `updateContractStatus()`: 主执行方法
  - `updatePendingToActive()`: 更新待生效为生效中
  - `updateActiveToExpired()`: 更新生效中为已到期
  - `manualUpdateContractStatus()`: 手动触发方法

### 2. ScheduleConfig
- **路径**: `com.yi.config.ScheduleConfig`
- **功能**: 定时任务配置类
- **特性**: 使用独立线程池，避免阻塞主线程

### 3. TaskController
- **路径**: `com.yi.controller.task.TaskController`
- **功能**: 定时任务管理接口
- **接口**: `POST /api/task/contract-status-update`

## 日志记录

### 正常执行日志
```
INFO  - 开始执行合同状态定时更新任务
INFO  - 找到 5 个待生效合同需要更新为生效中状态
INFO  - 成功更新 5 个待生效合同为生效中状态
INFO  - 找到 3 个生效中合同需要更新为已到期状态
INFO  - 成功更新 3 个生效中合同为已到期状态
INFO  - 合同状态定时更新任务执行完成
```

### 调试日志（DEBUG级别）
```
DEBUG - 合同 HT202412230001 (ID: 123) 从待生效更新为生效中，生效日期: 2024-12-23, 失效日期: 2025-12-23
DEBUG - 合同 HT202412220001 (ID: 124) 从生效中更新为已到期，失效日期: 2024-12-22
```

### 异常日志
```
ERROR - 合同状态定时更新任务执行失败
ERROR - 更新待生效合同为生效中状态时发生异常
ERROR - 更新生效中合同为已到期状态时发生异常
```

## 手动触发接口

### 接口信息
- **URL**: `POST /api/task/contract-status-update`
- **描述**: 手动触发合同状态更新任务
- **权限**: 需要管理员权限

### 请求示例
```bash
curl -X POST http://localhost:8080/api/task/contract-status-update
```

### 响应示例
```json
{
  "code": 200,
  "message": "操作成功",
  "data": "合同状态更新任务执行成功"
}
```

## 性能考虑

### 1. 批量更新
- 使用MyBatis Plus的批量更新功能
- 避免逐条更新，提高执行效率

### 2. 索引优化
建议在以下字段上创建索引：
```sql
-- 合同状态索引
CREATE INDEX idx_contract_status ON t_contract(contract_status);

-- 日期范围查询索引
CREATE INDEX idx_contract_dates ON t_contract(effective_date, expiry_date);

-- 组合索引
CREATE INDEX idx_contract_status_dates ON t_contract(contract_status, effective_date, expiry_date, valid);
```

### 3. 执行时间选择
- 选择凌晨1点执行，避免业务高峰期
- 可根据实际业务需求调整执行时间

## 监控建议

### 1. 日志监控
- 监控任务执行成功/失败日志
- 设置异常告警机制

### 2. 执行时间监控
- 记录任务执行耗时
- 设置超时告警

### 3. 数据一致性检查
- 定期检查合同状态与日期的一致性
- 发现异常数据及时处理

## 注意事项

1. **时区问题**: 确保服务器时区设置正确
2. **数据备份**: 执行前建议备份相关数据
3. **并发控制**: 避免手动触发与定时执行冲突
4. **异常处理**: 单个合同更新失败不影响其他合同
5. **日志级别**: 生产环境建议设置为INFO级别，调试时可设置为DEBUG
