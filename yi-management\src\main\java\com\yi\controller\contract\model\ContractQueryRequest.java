package com.yi.controller.contract.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 合同查询请求
 */
@Data
@ApiModel(value = "ContractQueryRequest", description = "合同查询请求")
public class ContractQueryRequest {

    @ApiModelProperty(value = "当前页码", example = "1")
    private String current = "1";

    @ApiModelProperty(value = "每页大小", example = "10")
    private String size = "10";

    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    @ApiModelProperty(value = "客户主体")
    private String customerCompanyName;

    @ApiModelProperty(value = "合同状态：1-待生效，2-生效中，3-已到期，4-已作废")
    private String contractStatus;
}
