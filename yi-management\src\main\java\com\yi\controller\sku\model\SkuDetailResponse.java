package com.yi.controller.sku.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * SKU详情响应
 */
@Data
@ApiModel(value = "SkuDetailResponse", description = "SKU详情响应")
public class SkuDetailResponse {

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "一级类目：1-循环托盘")
    private String firstCategory;

    @ApiModelProperty(value = "一级类目名称")
    private String firstCategoryName;

    @ApiModelProperty(value = "二级类目")
    private String secondCategory;

    @ApiModelProperty(value = "三级类目")
    private String thirdCategory;

    @ApiModelProperty(value = "长度(mm)")
    private String length;

    @ApiModelProperty(value = "宽度(mm)")
    private String width;

    @ApiModelProperty(value = "高度(mm)")
    private String height;

    @ApiModelProperty(value = "重量(Kg)")
    private String weight;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "启用状态：1-启用，0-禁用")
    private String enabled;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private String createdTime;

    @ApiModelProperty(value = "最后修改人")
    private String lastModifiedBy;

    @ApiModelProperty(value = "最后修改时间")
    private String lastModifiedTime;

    @ApiModelProperty(value = "有效性：1-有效，0-无效")
    private String valid;
}
