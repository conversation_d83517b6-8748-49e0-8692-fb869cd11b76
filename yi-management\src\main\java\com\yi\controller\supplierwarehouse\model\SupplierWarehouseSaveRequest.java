package com.yi.controller.supplierwarehouse.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 供应商仓库保存请求
 */
@Data
@ApiModel("供应商仓库保存请求")
public class SupplierWarehouseSaveRequest {

    @ApiModelProperty("主键ID（更新时必填）")
    private String id;

    @ApiModelProperty(value = "供应商ID", required = true)
    @NotNull(message = "供应商ID不能为空")
    private Long supplierId;

    @ApiModelProperty(value = "仓库名称", required = true)
    @NotBlank(message = "仓库名称不能为空")
    private String warehouseName;

    @ApiModelProperty("省份ID")
    private Long provinceId;

    @ApiModelProperty(value = "省份名称", required = true)
    @NotBlank(message = "省份不能为空")
    private String provinceName;

    @ApiModelProperty("城市ID")
    private Long cityId;

    @ApiModelProperty(value = "城市名称", required = true)
    @NotBlank(message = "城市不能为空")
    private String cityName;

    @ApiModelProperty("区县ID")
    private Long areaId;

    @ApiModelProperty(value = "区县名称", required = true)
    @NotBlank(message = "区县不能为空")
    private String areaName;

    @ApiModelProperty(value = "地址详情", required = true)
    @NotBlank(message = "地址详情不能为空")
    private String detailedAddress;

    @ApiModelProperty(value = "联系人", required = true)
    @NotBlank(message = "联系人不能为空")
    private String contactPerson;

    @ApiModelProperty(value = "联系方式", required = true)
    @NotBlank(message = "联系方式不能为空")
    private String contactPhone;
}
