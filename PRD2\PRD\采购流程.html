﻿<!DOCTYPE html>
<html>
  <head>
    <title>采购流程</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/采购流程/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/采购流程/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u115" class="ax_default primary_button">
        <div id="u115_div" class=""></div>
        <div id="u115_text" class="text ">
          <p><span>点击返回</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u116" class="ax_default flow_shape">
        <div id="u116_div" class=""></div>
        <div id="u116_text" class="text ">
          <p><span>开始</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u117" class="ax_default line1">
        <img id="u117_img" class="img " src="images/采购流程/u117.svg"/>
        <div id="u117_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u118" class="ax_default heading_3">
        <div id="u118_div" class=""></div>
        <div id="u118_text" class="text ">
          <p><span>采购流程说明</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u119" class="ax_default flow_shape">
        <div id="u119_div" class=""></div>
        <div id="u119_text" class="text ">
          <p><span>创建供应商</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u120" class="ax_default flow_shape">
        <div id="u120_div" class=""></div>
        <div id="u120_text" class="text ">
          <p><span>创建供应商仓库</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u121" class="ax_default flow_shape">
        <div id="u121_div" class=""></div>
        <div id="u121_text" class="text ">
          <p><span>创建采购合同</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u122" class="ax_default flow_shape">
        <div id="u122_div" class=""></div>
        <div id="u122_text" class="text ">
          <p><span>创建采购申请</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u123" class="ax_default flow_shape">
        <div id="u123_div" class=""></div>
        <div id="u123_text" class="text ">
          <p><span>创建采购订单</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u124" class="ax_default flow_shape">
        <div id="u124_div" class=""></div>
        <div id="u124_text" class="text ">
          <p><span>供应商仓库出库</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u125" class="ax_default flow_shape">
        <div id="u125_div" class=""></div>
        <div id="u125_text" class="text ">
          <p><span>客户入库单</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u126" class="ax_default flow_shape">
        <div id="u126_div" class=""></div>
        <div id="u126_text" class="text ">
          <p><span>结束</span></p>
        </div>
      </div>

      <!-- Unnamed (连接) -->
      <div id="u127" class="ax_default _连接">
        <img id="u127_seg0" class="img " src="images/采购流程/u127_seg0.svg" alt="u127_seg0"/>
        <img id="u127_seg1" class="img " src="images/采购流程/u127_seg1.svg" alt="u127_seg1"/>
        <div id="u127_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (连接) -->
      <div id="u128" class="ax_default _连接">
        <img id="u128_seg0" class="img " src="images/采购流程/u128_seg0.svg" alt="u128_seg0"/>
        <img id="u128_seg1" class="img " src="images/采购流程/u128_seg1.svg" alt="u128_seg1"/>
        <div id="u128_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (连接) -->
      <div id="u129" class="ax_default _连接">
        <img id="u129_seg0" class="img " src="images/采购流程/u129_seg0.svg" alt="u129_seg0"/>
        <img id="u129_seg1" class="img " src="images/采购流程/u129_seg1.svg" alt="u129_seg1"/>
        <div id="u129_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (连接) -->
      <div id="u130" class="ax_default _连接">
        <img id="u130_seg0" class="img " src="images/采购流程/u130_seg0.svg" alt="u130_seg0"/>
        <img id="u130_seg1" class="img " src="images/采购流程/u130_seg1.svg" alt="u130_seg1"/>
        <div id="u130_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (连接) -->
      <div id="u131" class="ax_default _连接">
        <img id="u131_seg0" class="img " src="images/采购流程/u131_seg0.svg" alt="u131_seg0"/>
        <img id="u131_seg1" class="img " src="images/采购流程/u131_seg1.svg" alt="u131_seg1"/>
        <div id="u131_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (连接) -->
      <div id="u132" class="ax_default _连接">
        <img id="u132_seg0" class="img " src="images/采购流程/u132_seg0.svg" alt="u132_seg0"/>
        <img id="u132_seg1" class="img " src="images/采购流程/u132_seg1.svg" alt="u132_seg1"/>
        <div id="u132_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (连接) -->
      <div id="u133" class="ax_default _连接">
        <img id="u133_seg0" class="img " src="images/采购流程/u133_seg0.svg" alt="u133_seg0"/>
        <img id="u133_seg1" class="img " src="images/采购流程/u133_seg1.svg" alt="u133_seg1"/>
        <div id="u133_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (连接) -->
      <div id="u134" class="ax_default _连接">
        <img id="u134_seg0" class="img " src="images/采购流程/u134_seg0.svg" alt="u134_seg0"/>
        <img id="u134_seg1" class="img " src="images/采购流程/u134_seg1.svg" alt="u134_seg1"/>
        <div id="u134_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u135" class="ax_default sticky_2">
        <div id="u135_div" class=""></div>
        <div id="u135_text" class="text ">
          <p><span>在供应商仓库扫码出库</span></p><p><span>后，后台同步生成采购入库单和供应商出库单</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
