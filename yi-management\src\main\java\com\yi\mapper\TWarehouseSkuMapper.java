package com.yi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yi.entity.TWarehouseSku;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 仓库SKU关联表 Mapper 接口
 */
@Mapper
public interface TWarehouseSkuMapper extends BaseMapper<TWarehouseSku> {

    /**
     * 分页查询仓库SKU关联列表
     *
     * @param page 分页参数
     * @param warehouseId 仓库ID
     * @param skuId SKU ID
     * @param enabled 启用状态
     * @return 分页结果
     */
    IPage<TWarehouseSku> selectWarehouseSkuPage(Page<TWarehouseSku> page,
                                                @Param("warehouseId") Long warehouseId,
                                                @Param("skuId") Long skuId,
                                                @Param("enabled") Integer enabled);

    /**
     * 根据仓库ID查询SKU关联列表
     *
     * @param warehouseId 仓库ID
     * @return SKU关联列表
     */
    List<TWarehouseSku> selectByWarehouseId(@Param("warehouseId") Long warehouseId);

    /**
     * 根据SKU ID查询仓库关联列表
     *
     * @param skuId SKU ID
     * @return 仓库关联列表
     */
    List<TWarehouseSku> selectBySkuId(@Param("skuId") Long skuId);

    /**
     * 根据仓库ID和SKU ID查询关联关系
     *
     * @param warehouseId 仓库ID
     * @param skuId SKU ID
     * @return 关联关系
     */
    TWarehouseSku selectByWarehouseIdAndSkuId(@Param("warehouseId") Long warehouseId, @Param("skuId") Long skuId);
}
