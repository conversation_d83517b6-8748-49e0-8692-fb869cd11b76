package com.yi.enums;

/**
 * 合同操作动作枚举
 */
public enum ContractActionEnum {
    
    EDIT(1, "编辑合同"),
    CANCEL(2, "作废合同");
    
    private final Integer code;
    private final String name;
    
    ContractActionEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    /**
     * 根据code获取枚举
     */
    public static ContractActionEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ContractActionEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
    
    /**
     * 根据code获取名称
     */
    public static String getNameByCode(Integer code) {
        ContractActionEnum item = getByCode(code);
        return item != null ? item.getName() : "";
    }
}
