package com.yi.controller.user.io;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class UserDetailResponseModel {
    @ApiModelProperty("员工编码")
    private String userCode;
    @ApiModelProperty("员工id")
    private Long userId;
    @ApiModelProperty("员工名称")
    private String userName;
    @ApiModelProperty("员工账号")
    private String userAccount;
    @ApiModelProperty("手机号码")
    private String mobilePhone;
    @ApiModelProperty("密码")
    private String password;
    @ApiModelProperty("角色")
    private List<UserRoleResponseModel> roleInfo;
    @ApiModelProperty("是否是负责人")
    private Integer isAdmin;
    @ApiModelProperty("是否禁用")
    private Integer enabled;

    @ApiModelProperty("供应商id 当选择的角色名称包含供应商时 可设置")
    private Long supplierId;
}
