package com.yi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yi.entity.OutboundOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 出库单表 Mapper 接口
 */
@Mapper
public interface OutboundOrderMapper extends BaseMapper<OutboundOrder> {

    /**
     * 分页查询出库单列表
     *
     * @param page 分页参数
     * @param orderNo 出库单号（模糊查询）
     * @param status 出库状态
     * @param outboundType 出库类型
     * @param outboundCompanyId 出库公司ID
     * @param receiveCompanyId 收货公司ID
     * @param firstCategory 一级类目
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分页结果
     */
    IPage<OutboundOrder> selectOutboundOrderPage(Page<OutboundOrder> page,
                                                 @Param("orderNo") String orderNo,
                                                 @Param("status") Integer status,
                                                 @Param("outboundType") Integer outboundType,
                                                 @Param("outboundCompanyId") Long outboundCompanyId,
                                                 @Param("receiveCompanyId") Long receiveCompanyId,
                                                 @Param("firstCategory") Integer firstCategory,
                                                 @Param("startTime") LocalDateTime startTime,
                                                 @Param("endTime") LocalDateTime endTime);

    /**
     * 根据出库单号查询出库单
     *
     * @param orderNo 出库单号
     * @return 出库单信息
     */
    OutboundOrder selectByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 根据状态查询出库单列表
     *
     * @param status 出库状态
     * @return 出库单列表
     */
    List<OutboundOrder> selectByStatus(@Param("status") Integer status);

    /**
     * 根据出库公司ID查询出库单列表
     *
     * @param outboundCompanyId 出库公司ID
     * @return 出库单列表
     */
    List<OutboundOrder> selectByOutboundCompanyId(@Param("outboundCompanyId") Long outboundCompanyId);

    /**
     * 根据收货公司ID查询出库单列表
     *
     * @param receiveCompanyId 收货公司ID
     * @return 出库单列表
     */
    List<OutboundOrder> selectByReceiveCompanyId(@Param("receiveCompanyId") Long receiveCompanyId);

    /**
     * 统计各状态的出库单数量
     *
     * @return 状态统计结果
     */
    List<java.util.Map<String, Object>> selectStatusStatistics();

    /**
     * 统计各类型的出库单数量
     *
     * @return 类型统计结果
     */
    List<java.util.Map<String, Object>> selectTypeStatistics();

    /**
     * 查询指定时间范围内的出库单
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 出库单列表
     */
    List<OutboundOrder> selectByTimeRange(@Param("startTime") LocalDateTime startTime,
                                          @Param("endTime") LocalDateTime endTime);

    /**
     * 查询待出库的订单（状态为待出库）
     *
     * @return 待出库订单列表
     */
    List<OutboundOrder> selectPendingOrders();

    /**
     * 查询运输中的订单（状态为运输中）
     *
     * @return 运输中订单列表
     */
    List<OutboundOrder> selectInTransitOrders();

    /**
     * 更新出库单状态
     *
     * @param id 出库单ID
     * @param status 新状态
     * @param actualQuantity 实际出库数
     * @param outboundTime 出库时间
     * @param lastModifiedBy 最后修改人
     * @return 更新行数
     */
    int updateStatus(@Param("id") Long id,
                     @Param("status") Integer status,
                     @Param("actualQuantity") Integer actualQuantity,
                     @Param("outboundTime") LocalDateTime outboundTime,
                     @Param("lastModifiedBy") String lastModifiedBy);
}
