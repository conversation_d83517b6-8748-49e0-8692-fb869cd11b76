package com.yi.service;

import com.yi.entity.TShippingOrder;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashSet;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 发运订单号生成功能测试
 */
@ExtendWith(MockitoExtension.class)
@SpringBootTest
public class TShippingOrderGenerateOrderNoTest {

    @InjectMocks
    private TShippingOrderService shippingOrderService;

    @Test
    void testGenerateOrderNo_Format() {
        // Mock getOne方法返回null（表示当天没有订单）
        when(shippingOrderService.getOne(any())).thenReturn(null);

        // 执行生成订单号
        String orderNo = shippingOrderService.generateOrderNo();

        // 验证订单号格式
        assertNotNull(orderNo);

        // 验证格式：XSD + yyyyMMdd + 4位序号
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String expectedPrefix = "XSD" + today;

        assertTrue(orderNo.startsWith(expectedPrefix), "订单号应该以XSD+日期开头");
        assertEquals(expectedPrefix.length() + 4, orderNo.length(), "订单号总长度应该是前缀+4位序号");

        // 验证序号部分是数字
        String sequence = orderNo.substring(expectedPrefix.length());
        assertTrue(sequence.matches("\\d{4}"), "序号部分应该是4位数字");
        assertEquals("0001", sequence, "第一个订单的序号应该是0001");
    }

    @Test
    void testGenerateOrderNo_WithExistingOrders() {
        // 创建模拟的现有订单
        TShippingOrder existingOrder = new TShippingOrder();
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        existingOrder.setOrderNo("XSD" + today + "0005");

        // Mock getOne方法返回现有订单
        when(shippingOrderService.getOne(any())).thenReturn(existingOrder);

        // 执行生成订单号
        String orderNo = shippingOrderService.generateOrderNo();

        // 验证新订单号
        String expectedOrderNo = "XSD" + today + "0006";
        assertEquals(expectedOrderNo, orderNo, "新订单号应该是现有最大序号+1");
    }

    @Test
    void testGenerateOrderNo_Uniqueness() {
        // Mock getOne方法返回null（表示当天没有订单）
        when(shippingOrderService.getOne(any())).thenReturn(null);

        // 生成多个订单号，验证唯一性
        Set<String> orderNos = new HashSet<>();
        for (int i = 0; i < 10; i++) {
            String orderNo = shippingOrderService.generateOrderNo();
            assertFalse(orderNos.contains(orderNo), "订单号应该是唯一的");
            orderNos.add(orderNo);
        }
    }

    @Test
    void testGenerateOrderNo_DateChange() {
        // 验证不同日期生成的订单号前缀不同
        String orderNo1 = shippingOrderService.generateOrderNo();

        // 验证今天的订单号格式
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        assertTrue(orderNo1.startsWith("XSD" + today), "订单号应该包含当前日期");
    }

    @Test
    void testGenerateOrderNo_SequenceFormat() {
        // Mock getOne方法返回null
        when(shippingOrderService.getOne(any())).thenReturn(null);

        String orderNo = shippingOrderService.generateOrderNo();

        // 提取序号部分
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String prefix = "XSD" + today;
        String sequence = orderNo.substring(prefix.length());

        // 验证序号格式
        assertEquals(4, sequence.length(), "序号应该是4位");
        assertTrue(sequence.matches("\\d{4}"), "序号应该全是数字");
        assertTrue(sequence.startsWith("0"), "序号应该以0开头（补零）");
    }

    @Test
    void testGetMaxSequenceOfDay_NoExistingOrders() {
        // Mock getOne方法返回null
        when(shippingOrderService.getOne(any())).thenReturn(null);

        // 测试获取最大序号
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        int maxSeq = shippingOrderService.getMaxSequenceOfDay(today);
        
        assertEquals(0, maxSeq, "没有现有订单时，最大序号应该是0");
    }

    @Test
    void testGetMaxSequenceOfDay_WithExistingOrders() {
        // 创建模拟的现有订单
        TShippingOrder existingOrder = new TShippingOrder();
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        existingOrder.setOrderNo("XSD" + today + "0123");

        // Mock getOne方法返回现有订单
        when(shippingOrderService.getOne(any())).thenReturn(existingOrder);

        // 测试获取最大序号
        int maxSeq = shippingOrderService.getMaxSequenceOfDay(today);

        assertEquals(123, maxSeq, "应该正确解析现有订单的序号");
    }

    @Test
    void testGetMaxSequenceOfDay_InvalidOrderNo() {
        // 创建模拟的无效订单号
        TShippingOrder existingOrder = new TShippingOrder();
        existingOrder.setOrderNo("INVALID_ORDER_NO");

        // Mock getOne方法返回无效订单
        when(shippingOrderService.getOne(any())).thenReturn(existingOrder);

        // 测试获取最大序号
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        int maxSeq = shippingOrderService.getMaxSequenceOfDay(today);
        
        assertEquals(0, maxSeq, "无效订单号时，最大序号应该是0");
    }

    @Test
    void testOrderNoComponents() {
        // Mock getOne方法返回null
        when(shippingOrderService.getOne(any())).thenReturn(null);

        String orderNo = shippingOrderService.generateOrderNo();

        // 验证订单号组成部分
        assertTrue(orderNo.startsWith("XSD"), "订单号应该以XSD开头");

        String dateStr = orderNo.substring(3, 11);
        assertTrue(dateStr.matches("\\d{8}"), "日期部分应该是8位数字");

        String sequence = orderNo.substring(11);
        assertTrue(sequence.matches("\\d{4}"), "序号部分应该是4位数字");

        // 验证日期是今天
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        assertEquals(today, dateStr, "日期部分应该是今天的日期");
    }

    @Test
    void testSequenceIncrement() {
        // 模拟连续生成订单号的场景
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        // 第一次调用，返回null（没有现有订单）
        when(shippingOrderService.getOne(any())).thenReturn(null);
        String orderNo1 = shippingOrderService.generateOrderNo();
        assertEquals("XSD" + today + "0001", orderNo1);

        // 第二次调用，返回第一个订单
        TShippingOrder order1 = new TShippingOrder();
        order1.setOrderNo(orderNo1);
        when(shippingOrderService.getOne(any())).thenReturn(order1);
        String orderNo2 = shippingOrderService.generateOrderNo();
        assertEquals("XSD" + today + "0002", orderNo2);

        // 第三次调用，返回第二个订单
        TShippingOrder order2 = new TShippingOrder();
        order2.setOrderNo(orderNo2);
        when(shippingOrderService.getOne(any())).thenReturn(order2);
        String orderNo3 = shippingOrderService.generateOrderNo();
        assertEquals("XSD" + today + "0003", orderNo3);
    }
}
