package com.yi.controller.shippingdemand.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

/**
 * 发货需求导出VO
 */
@Data
@HeadRowHeight(20)
@ContentRowHeight(18)
public class ShippingDemandExportVO {

    @ExcelProperty(value = "订单号")
    @ColumnWidth(20)
    private String orderNo;

    @ExcelProperty(value = "发货状态")
    @ColumnWidth(12)
    private String status;

    @ExcelProperty(value = "客户名称")
    @ColumnWidth(25)
    private String customerCompanyName;

    @ExcelProperty(value = "收货仓库")
    @ColumnWidth(20)
    private String warehouseName;

    @ExcelProperty(value = "收货地址")
    @ColumnWidth(40)
    private String receivingAddress;

    @ExcelProperty(value = "收货人")
    @ColumnWidth(12)
    private String receiverName;

    @ExcelProperty(value = "产品")
    @ColumnWidth(25)
    private String product;

    @ExcelProperty(value = "需求数量")
    @ColumnWidth(12)
    private String demandQuantity;

    @ExcelProperty(value = "待确认数量")
    @ColumnWidth(12)
    private String pendingQuantity;

    @ExcelProperty(value = "发货数量")
    @ColumnWidth(12)
    private String shippedQuantity;

    @ExcelProperty(value = "未执行数量")
    @ColumnWidth(12)
    private String unexecutedQuantity;

    @ExcelProperty(value = "需求时间")
    @ColumnWidth(15)
    private String demandTime;

    @ExcelProperty(value = "创建人")
    @ColumnWidth(12)
    private String createdBy;

    @ExcelProperty(value = "创建时间")
    @ColumnWidth(20)
    private String createdTime;
}
