package com.yi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yi.entity.OutboundOrder;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 出库单表 服务类
 */
public interface OutboundOrderService extends IService<OutboundOrder> {

    /**
     * 分页查询出库单列表
     *
     * @param page 分页参数
     * @param orderNo 出库单号（模糊查询）
     * @param status 出库状态
     * @param outboundType 出库类型
     * @param outboundCompanyId 出库公司ID
     * @param receiveCompanyId 收货公司ID
     * @param firstCategory 一级类目
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分页结果
     */
    IPage<OutboundOrder> selectOutboundOrderPage(Page<OutboundOrder> page, String orderNo, Integer status,
                                                 Integer outboundType, Long outboundCompanyId, Long receiveCompanyId,
                                                 Integer firstCategory, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据出库单号查询出库单
     *
     * @param orderNo 出库单号
     * @return 出库单信息
     */
    OutboundOrder selectByOrderNo(String orderNo);

    /**
     * 根据状态查询出库单列表
     *
     * @param status 出库状态
     * @return 出库单列表
     */
    List<OutboundOrder> selectByStatus(Integer status);

    /**
     * 创建出库单
     *
     * @param outboundOrder 出库单信息
     * @return 是否成功
     */
    boolean createOutboundOrder(OutboundOrder outboundOrder);

    /**
     * 更新出库单
     *
     * @param outboundOrder 出库单信息
     * @return 是否成功
     */
    boolean updateOutboundOrder(OutboundOrder outboundOrder);

    /**
     * 删除出库单
     *
     * @param id 出库单ID
     * @return 是否成功
     */
    boolean deleteOutboundOrder(Long id);

    /**
     * 批量删除出库单
     *
     * @param ids 出库单ID列表
     * @return 是否成功
     */
    boolean deleteOutboundOrders(List<Long> ids);

    /**
     * 确认出库（更新状态为运输中）
     *
     * @param id 出库单ID
     * @param actualQuantity 实际出库数
     * @param lastModifiedBy 最后修改人
     * @return 是否成功
     */
    boolean confirmOutbound(Long id, Integer actualQuantity, String lastModifiedBy);

    /**
     * 完成出库（更新状态为已出库）
     *
     * @param id 出库单ID
     * @param lastModifiedBy 最后修改人
     * @return 是否成功
     */
    boolean completeOutbound(Long id, String lastModifiedBy);

    /**
     * 取消出库（回退到待出库状态）
     *
     * @param id 出库单ID
     * @param lastModifiedBy 最后修改人
     * @return 是否成功
     */
    boolean cancelOutbound(Long id, String lastModifiedBy);

    /**
     * 统计各状态的出库单数量
     *
     * @return 状态统计结果
     */
    List<Map<String, Object>> getStatusStatistics();

    /**
     * 统计各类型的出库单数量
     *
     * @return 类型统计结果
     */
    List<Map<String, Object>> getTypeStatistics();

    /**
     * 查询待出库的订单
     *
     * @return 待出库订单列表
     */
    List<OutboundOrder> getPendingOrders();

    /**
     * 查询运输中的订单
     *
     * @return 运输中订单列表
     */
    List<OutboundOrder> getInTransitOrders();

    /**
     * 检查出库单号是否存在
     *
     * @param orderNo 出库单号
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean checkOrderNoExists(String orderNo, Long excludeId);
}
