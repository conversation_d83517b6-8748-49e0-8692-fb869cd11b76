package com.yi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yi.entity.SysRole;
import com.yi.entity.SysRoleResource;
import com.yi.mapper.SysRoleMapper;
import com.yi.service.SysRoleResourceService;
import com.yi.service.SysRoleService;
import com.yi.service.SysUserRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 系统角色表 服务实现类
 */
@Service
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRole> implements SysRoleService {

    @Autowired
    private SysUserRoleService sysUserRoleService;

    @Autowired
    private SysRoleResourceService sysRoleResourceService;

    @Override
    public IPage<SysRole> selectRolePage(Page<SysRole> page, String roleCode, String roleName, Integer status) {
        return baseMapper.selectRolePage(page, roleCode, roleName, status);
    }

    @Override
    public SysRole selectByRoleCode(String roleCode) {
        return baseMapper.selectByRoleCode(roleCode);
    }

    @Override
    public List<SysRole> selectByUserId(Long userId) {
        return baseMapper.selectByUserId(userId);
    }

    @Override
    public List<Long> selectResourceIdsByRoleId(Long roleId) {
        return baseMapper.selectResourceIdsByRoleId(roleId);
    }

    @Override
    public List<SysRole> selectEnabledRoles() {
        return baseMapper.selectEnabledRoles();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createRole(SysRole role) {
        // 检查角色编码是否存在
        if (checkRoleCodeExists(role.getRoleCode(), null)) {
            throw new RuntimeException("角色编码已存在");
        }
        
        // 设置默认值
        role.setStatus(1);
        role.setValid(1);
        role.setCreatedTime(LocalDateTime.now());
        role.setLastModifiedTime(LocalDateTime.now());
        
        return save(role);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRole(SysRole role) {
        // 检查角色编码是否存在
        if (checkRoleCodeExists(role.getRoleCode(), role.getId())) {
            throw new RuntimeException("角色编码已存在");
        }
        
        role.setLastModifiedTime(LocalDateTime.now());
        return updateById(role);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRole(Long roleId) {
        // 删除用户角色关联
        sysUserRoleService.deleteByRoleId(roleId);
        
        // 删除角色资源关联
        sysRoleResourceService.deleteByRoleId(roleId);
        
        // 逻辑删除角色
        SysRole role = new SysRole();
        role.setId(roleId);
        role.setValid(0);
        role.setLastModifiedTime(LocalDateTime.now());
        return updateById(role);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRoles(List<Long> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return true;
        }
        
        for (Long roleId : roleIds) {
            deleteRole(roleId);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignPermissions(Long roleId, List<Long> resourceIds) {
        // 先删除原有权限关联
        sysRoleResourceService.deleteByRoleId(roleId);
        
        // 添加新的权限关联
        if (!CollectionUtils.isEmpty(resourceIds)) {
            List<SysRoleResource> roleResources = resourceIds.stream().map(resourceId -> {
                SysRoleResource roleResource = new SysRoleResource();
                roleResource.setRoleId(roleId);
                roleResource.setResourceId(resourceId);
                roleResource.setValid(1);
                roleResource.setCreatedTime(LocalDateTime.now());
                roleResource.setLastModifiedTime(LocalDateTime.now());
                return roleResource;
            }).collect(Collectors.toList());
            
            return sysRoleResourceService.saveBatch(roleResources);
        }
        
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(Long roleId, Integer status) {
        SysRole role = new SysRole();
        role.setId(roleId);
        role.setStatus(status);
        role.setLastModifiedTime(LocalDateTime.now());
        return updateById(role);
    }

    @Override
    public boolean checkRoleCodeExists(String roleCode, Long excludeId) {
        LambdaQueryWrapper<SysRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRole::getRoleCode, roleCode)
               .eq(SysRole::getValid, 1);
        if (excludeId != null) {
            wrapper.ne(SysRole::getId, excludeId);
        }
        return count(wrapper) > 0;
    }
}
