package com.yi.controller.cloudwarehouse;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yi.common.Result;
import com.yi.controller.cloudwarehouse.model.*;
import com.yi.service.TCloudWarehouseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;

/**
 * 云仓表 前端控制器
 */
@RestController
@RequestMapping("/api/cloud-warehouse")
@Api(tags = "云仓管理")
public class TCloudWarehouseController {

    @Autowired
    private TCloudWarehouseService cloudWarehouseService;

    @ApiOperation("分页查询云仓列表")
    @PostMapping("/page")
    public Result<IPage<CloudWarehousePageResponse>> getCloudWarehousePage(@RequestBody CloudWarehouseQueryRequest request) {
        IPage<CloudWarehousePageResponse> page = cloudWarehouseService.getCloudWarehousePageResponse(request);
        return Result.success(page);
    }

    @ApiOperation("导出云仓列表")
    @PostMapping("/export")
    public void exportCloudWarehouseList(@RequestBody CloudWarehouseQueryRequest request,
                                        HttpServletResponse response) throws IOException {
        cloudWarehouseService.exportCloudWarehouseList(request, response);
    }

    @ApiOperation("新增云仓")
    @PostMapping
    public Result<Boolean> addCloudWarehouse(@Valid @RequestBody CloudWarehouseSaveRequest request) {
        boolean success = cloudWarehouseService.addCloudWarehouse(request);
        if (success) {
            return Result.success("新增成功", true);
        }
        return Result.failed("新增失败");
    }

    @ApiOperation("更新云仓")
    @PutMapping
    public Result<Boolean> updateCloudWarehouse(@Valid @RequestBody CloudWarehouseSaveRequest request) {
        boolean success = cloudWarehouseService.updateCloudWarehouse(request);
        if (success) {
            return Result.success("更新成功", true);
        }
        return Result.failed("更新失败");
    }

    @ApiOperation("启用/禁用云仓")
    @PutMapping("/{id}/status")
    public Result<Boolean> updateCloudWarehouseStatus(@ApiParam("云仓ID") @PathVariable String id,
                                                     @ApiParam("启用状态") @RequestParam String enabled) {
        boolean success = cloudWarehouseService.updateCloudWarehouseStatus(Long.valueOf(id), Integer.valueOf(enabled));
        if (success) {
            String message = "1".equals(enabled) ? "启用成功" : "禁用成功";
            return Result.success(message, true);
        }
        return Result.failed("状态更新失败");
    }

    @ApiOperation("根据ID获取云仓详情")
    @GetMapping("/{id}")
    public Result<CloudWarehouseDetailResponse> getCloudWarehouseDetail(@ApiParam("云仓ID") @PathVariable String id) {
        CloudWarehouseDetailResponse detail = cloudWarehouseService.getCloudWarehouseDetail(Long.valueOf(id));
        return Result.success("查询成功", detail);
    }
}
