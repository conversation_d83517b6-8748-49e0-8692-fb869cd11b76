package com.yi.controller.user.io;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PermissionDetailResponse {
    private Long menuId;
    @ApiModelProperty("菜单类型")
    private Integer permissionType;
    @ApiModelProperty("菜单地址")
    private String menuUrl="";
    @ApiModelProperty("菜单名称")
    private String permissionName;
    @ApiModelProperty("菜单编码")
    private String permissionCode;
    @ApiModelProperty("菜单排序")
    private Integer permissionSort;
    @ApiModelProperty("菜单父级id")
    private Long parentId;
    @ApiModelProperty("菜单描述")
    private String remark;

    @ApiModelProperty("菜单图片url")
    private String permissionImg;
}
