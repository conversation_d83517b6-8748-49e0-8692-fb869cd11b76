package com.yi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yi.entity.SysDept;
import com.yi.mapper.SysDeptMapper;
import com.yi.service.SysDeptService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 部门表 服务实现类
 */
@Service
public class SysDeptServiceImpl extends ServiceImpl<SysDeptMapper, SysDept> implements SysDeptService {

    @Override
    public List<SysDept> selectDeptTree(Integer status) {
        return baseMapper.selectDeptTree(status);
    }

    @Override
    public List<SysDept> selectByParentId(Long parentId) {
        return baseMapper.selectByParentId(parentId);
    }

    @Override
    public SysDept selectByDeptCode(String deptCode) {
        return baseMapper.selectByDeptCode(deptCode);
    }

    @Override
    public List<Long> selectDeptAndChildrenIds(Long deptId) {
        return baseMapper.selectDeptAndChildrenIds(deptId);
    }

    @Override
    public List<SysDept> selectEnabledDepts() {
        return baseMapper.selectEnabledDepts();
    }

    @Override
    public boolean hasChildren(Long deptId) {
        return baseMapper.hasChildren(deptId);
    }

    @Override
    public boolean hasUsers(Long deptId) {
        return baseMapper.hasUsers(deptId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createDept(SysDept dept) {
        // 检查部门编码是否存在
        if (checkDeptCodeExists(dept.getDeptCode(), null)) {
            throw new RuntimeException("部门编码已存在");
        }
        
        // 设置默认值
        dept.setStatus(1);
        dept.setValid(1);
        dept.setCreatedTime(LocalDateTime.now());
        dept.setLastModifiedTime(LocalDateTime.now());
        
        return save(dept);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDept(SysDept dept) {
        // 检查部门编码是否存在
        if (checkDeptCodeExists(dept.getDeptCode(), dept.getId())) {
            throw new RuntimeException("部门编码已存在");
        }
        
        dept.setLastModifiedTime(LocalDateTime.now());
        return updateById(dept);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDept(Long deptId) {
        // 检查是否有子部门
        if (hasChildren(deptId)) {
            throw new RuntimeException("存在子部门，无法删除");
        }
        
        // 检查是否有用户
        if (hasUsers(deptId)) {
            throw new RuntimeException("部门下存在用户，无法删除");
        }
        
        // 逻辑删除部门
        SysDept dept = new SysDept();
        dept.setId(deptId);
        dept.setValid(0);
        dept.setLastModifiedTime(LocalDateTime.now());
        return updateById(dept);
    }

    @Override
    public boolean checkDeptCodeExists(String deptCode, Long excludeId) {
        LambdaQueryWrapper<SysDept> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysDept::getDeptCode, deptCode)
               .eq(SysDept::getValid, 1);
        if (excludeId != null) {
            wrapper.ne(SysDept::getId, excludeId);
        }
        return count(wrapper) > 0;
    }
}
