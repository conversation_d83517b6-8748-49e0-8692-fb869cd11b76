package com.yi.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yi.controller.supplier.model.*;
import com.yi.entity.TSupplier;
import com.yi.entity.TGeneralFile;
import com.yi.enums.EnabledStatusEnum;
import com.yi.enums.ValidStatusEnum;
import com.yi.mapper.TSupplierMapper;
import com.yi.mapper.vo.SupplierPageVO;
import com.yi.utils.FormatUtils;
import com.yi.utils.UserContextUtils;
import com.yi.utils.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 供应商服务类
 */
@Slf4j
@Service
public class TSupplierService extends ServiceImpl<TSupplierMapper, TSupplier> {

    @Autowired
    private TGeneralFileService generalFileService;

    /**
     * 分页查询供应商列表
     *
     * @param request 查询条件
     * @return 分页结果
     */
    public IPage<SupplierPageResponse> getSupplierPageResponse(SupplierQueryRequest request) {
        Page<SupplierPageVO> page = new Page<>(request.getPageNum(), request.getPageSize());
        IPage<SupplierPageVO> supplierPage = this.baseMapper.selectSupplierPage(page, request);

        // 转换为响应对象
        IPage<SupplierPageResponse> responsePage = new Page<>(request.getPageNum(), request.getPageSize());
        responsePage.setTotal(supplierPage.getTotal());
        responsePage.setPages(supplierPage.getPages());

        List<SupplierPageResponse> responseList = supplierPage.getRecords().stream()
                .map(this::convertToPageResponse)
                .collect(Collectors.toList());
        responsePage.setRecords(responseList);

        return responsePage;
    }


    /**
     * 根据ID查询供应商详情
     *
     * @param id 供应商ID
     * @return 供应商详情
     */
    public SupplierDetailResponse getSupplierDetailById(Long id) {
        TSupplier supplier = this.getById(id);
        if (supplier == null || supplier.getValid() == 0) {
            return null;
        }
        return convertToDetailResponse(supplier);
    }

    /**
     * 新增供应商
     *
     * @param request 新增请求
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean addSupplier(SupplierSaveRequest request) {
        // 检查供应商名称是否已存在
        if (isSupplierNameExists(request.getSupplierName(), null)) {
            throw new RuntimeException("供应商名称已存在");
        }

        TSupplier supplier = new TSupplier();
        supplier.setSupplierName(request.getSupplierName());
        supplier.setContactPerson(request.getContactPerson());
        supplier.setContactPhone(request.getContactPhone());
        supplier.setBankName(request.getBankName());
        supplier.setBankAccount(request.getBankAccount());
        supplier.setRemark(request.getRemark());
        supplier.setValid(1);
        supplier.setCreatedBy(UserContextUtils.getCurrentUser());
        supplier.setCreatedTime(LocalDateTime.now());
        supplier.setLastModifiedBy(UserContextUtils.getCurrentUser());
        supplier.setLastModifiedTime(LocalDateTime.now());
        boolean success = this.save(supplier);
        if (success) {
            log.info("新增供应商成功，ID: {}, 名称: {}", supplier.getId(), supplier.getSupplierName());

            // 处理营业执照文件
            if (request.getBusinessLicenseFiles() != null && !request.getBusinessLicenseFiles().isEmpty()) {
                try {
                    saveSupplierBusinessLicense(supplier.getId(), request.getBusinessLicenseFiles());
                    log.info("保存供应商营业执照成功，供应商ID: {}", supplier.getId());
                } catch (Exception e) {
                    log.error("保存供应商营业执照失败，供应商ID: {}", supplier.getId(), e);
                    // 营业执照保存失败不影响供应商创建
                }
            }
        }

        return success;
    }

    /**
     * 更新供应商
     *
     * @param request 更新请求
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSupplier(SupplierSaveRequest request) {
        Long id = FormatUtils.safeParseLong(request.getId());
        if (id == null) {
            throw new RuntimeException("供应商ID不能为空");
        }

        TSupplier existingSupplier = this.getById(id);
        if (existingSupplier == null || existingSupplier.getValid() == 0) {
            throw new RuntimeException("供应商不存在");
        }

        // 检查供应商名称是否已存在（排除当前记录）
        if (isSupplierNameExists(request.getSupplierName(), id)) {
            throw new RuntimeException("供应商名称已存在");
        }

        existingSupplier.setSupplierName(request.getSupplierName());
        existingSupplier.setContactPerson(request.getContactPerson());
        existingSupplier.setContactPhone(request.getContactPhone());
        existingSupplier.setBankName(request.getBankName());
        existingSupplier.setBankAccount(request.getBankAccount());
        existingSupplier.setRemark(request.getRemark());
        existingSupplier.setLastModifiedBy(UserContextUtils.getCurrentUser());
        existingSupplier.setLastModifiedTime(LocalDateTime.now());

        boolean result = this.updateById(existingSupplier);
        if (result) {
            log.info("更新供应商成功，ID: {}, 名称: {}", id, existingSupplier.getSupplierName());

            // 处理营业执照文件更新
            if (request.getBusinessLicenseFiles() != null) {
                try {
                    // 如果有新的营业执照文件，则保存
                    if (!request.getBusinessLicenseFiles().isEmpty()) {
                        saveSupplierBusinessLicense(id, request.getBusinessLicenseFiles());
                        log.info("更新供应商营业执照成功，供应商ID: {}", id);
                    } else {
                        log.info("清空供应商营业执照，供应商ID: {}", id);
                    }
                } catch (Exception e) {
                    log.error("更新供应商营业执照失败，供应商ID: {}", id, e);
                    // 营业执照更新失败不影响供应商信息更新
                }
            }
        }

        return result;
    }


    /**
     * 启用/禁用供应商
     *
     * @param id 供应商ID
     * @param enabled 启用状态
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSupplierStatus(Long id, Integer enabled) {
        TSupplier supplier = this.getById(id);
        if (supplier == null || supplier.getValid() == 0) {
            throw new RuntimeException("供应商不存在");
        }

        supplier.setEnabled(enabled);
        supplier.setLastModifiedBy(UserContextUtils.getCurrentUser());
        supplier.setLastModifiedTime(LocalDateTime.now());

        boolean result = this.updateById(supplier);
        log.info("更新供应商启用状态成功，ID: {}, 状态: {}", id, enabled);

        return result;
    }

    /**
     * 检查供应商名称是否存在
     *
     * @param supplierName 供应商名称
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    private boolean isSupplierNameExists(String supplierName, Long excludeId) {
        if (!StringUtils.hasText(supplierName)) {
            return false;
        }
        int count = this.baseMapper.countBySupplierName(supplierName, excludeId);
        return count > 0;
    }

    /**
     * 转换为分页响应对象
     *
     * @param supplierVO 供应商VO
     * @return 分页响应对象
     */
    private SupplierPageResponse convertToPageResponse(SupplierPageVO supplierVO) {
        SupplierPageResponse response = new SupplierPageResponse();

        response.setId(FormatUtils.safeToString(supplierVO.getId()));
        response.setSupplierName(FormatUtils.safeString(supplierVO.getSupplierName()));
        response.setContactPerson(FormatUtils.safeString(supplierVO.getContactPerson()));
        response.setContactPhone(FormatUtils.safeString(supplierVO.getContactPhone()));
        response.setCreatedBy(FormatUtils.safeString(supplierVO.getCreatedBy()));
        response.setCreatedTime(FormatUtils.formatDateTime(supplierVO.getCreatedTime()));

        return response;
    }

    /**
     * 转换为详情响应对象
     *
     * @param supplier 供应商实体
     * @return 详情响应对象
     */
    private SupplierDetailResponse convertToDetailResponse(TSupplier supplier) {
        SupplierDetailResponse response = new SupplierDetailResponse();
        
        response.setId(FormatUtils.safeToString(supplier.getId()));
        response.setSupplierName(FormatUtils.safeString(supplier.getSupplierName()));
        response.setContactPerson(FormatUtils.safeString(supplier.getContactPerson()));
        response.setContactPhone(FormatUtils.safeString(supplier.getContactPhone()));
        response.setBankName(FormatUtils.safeString(supplier.getBankName()));
        response.setBankAccount(FormatUtils.safeString(supplier.getBankAccount()));
        response.setRemark(FormatUtils.safeString(supplier.getRemark()));
        response.setEnabled(FormatUtils.safeToString(supplier.getEnabled()));
        response.setCreatedBy(FormatUtils.safeString(supplier.getCreatedBy()));
        response.setCreatedTime(FormatUtils.formatDateTime(supplier.getCreatedTime()));
        response.setLastModifiedBy(FormatUtils.safeString(supplier.getLastModifiedBy()));
        response.setLastModifiedTime(FormatUtils.formatDateTime(supplier.getLastModifiedTime()));

        return response;
    }

    /**
     * 保存供应商营业执照
     *
     * @param supplierId 供应商ID
     * @param filePaths 文件路径列表
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveSupplierBusinessLicense(Long supplierId, List<String> filePaths) {
        // 检查供应商是否存在
        TSupplier supplier = this.getById(supplierId);
        if (supplier == null || supplier.getValid() == 0) {
            throw new RuntimeException("供应商不存在");
        }

        boolean result = generalFileService.saveSupplierBusinessLicense(supplierId, filePaths);
        log.info("保存供应商营业执照成功，供应商ID: {}, 文件数量: {}", supplierId,
                filePaths != null ? filePaths.size() : 0);

        return result;
    }




    /**
     * 导出供应商列表
     *
     * @param request 查询条件
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    public void exportSupplierList(SupplierQueryRequest request, HttpServletResponse response) throws IOException {
        // 查询所有符合条件的数据（不分页）
        LambdaQueryWrapper<TSupplier> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TSupplier::getValid, 1)
                .like(StringUtils.hasText(request.getSupplierName()), TSupplier::getSupplierName, request.getSupplierName())
                .orderByDesc(TSupplier::getCreatedTime);

        List<TSupplier> dataList = this.list(wrapper);

        // 转换为导出VO
        List<SupplierExportVO> exportList = dataList.stream()
                .map(this::convertToExportVO)
                .collect(Collectors.toList());

        // 使用EasyExcel导出（文件名已包含时间戳）
        ExcelUtils.exportExcelWithTimestamp(response, "供应商列表", "供应商列表",
                SupplierExportVO.class, exportList);
    }

    /**
     * 转换为导出VO
     *
     * @param supplier 供应商实体
     * @return 导出VO
     */
    private SupplierExportVO convertToExportVO(TSupplier supplier) {
        SupplierExportVO vo = new SupplierExportVO();
        vo.setId(supplier.getId().toString());
        vo.setSupplierName(supplier.getSupplierName());
        vo.setContactPerson(supplier.getContactPerson());
        vo.setContactPhone(supplier.getContactPhone());
        vo.setCreatedBy(supplier.getCreatedBy());
        vo.setCreatedTime(FormatUtils.formatDateTime(supplier.getCreatedTime()));
        return vo;
    }

    /**
     * 获取供应商下拉列表
     *
     * @return 供应商下拉列表
     */
    public List<SupplierDropdownResponse> getSupplierDropdownList() {
        LambdaQueryWrapper<TSupplier> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TSupplier::getValid, ValidStatusEnum.VALID.getKey())
                .eq(TSupplier::getEnabled, EnabledStatusEnum.ENABLED.getKey())
                .orderByAsc(TSupplier::getSupplierName);

        List<TSupplier> supplierList = this.list(wrapper);
        return supplierList.stream()
                .map(this::convertToDropdownResponse)
                .collect(Collectors.toList());
    }

    /**
     * 转换为下拉列表响应对象
     *
     * @param supplier 供应商实体
     * @return 下拉列表响应对象
     */
    private SupplierDropdownResponse convertToDropdownResponse(TSupplier supplier) {
        SupplierDropdownResponse response = new SupplierDropdownResponse();
        response.setId(supplier.getId());
        response.setSupplierName(supplier.getSupplierName());
        return response;
    }

}
