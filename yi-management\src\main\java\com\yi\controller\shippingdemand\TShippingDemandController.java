package com.yi.controller.shippingdemand;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yi.common.Result;
import com.yi.controller.shippingdemand.model.*;
import com.yi.service.TShippingDemandService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * 发货需求表 前端控制器
 */
@RestController
@RequestMapping("/api/shipping-demand")
@Api(tags = "发货需求管理")
public class TShippingDemandController {

    @Autowired
    private TShippingDemandService shippingDemandService;

    @ApiOperation("分页查询发货需求列表")
    @PostMapping("/page")
    public Result<IPage<ShippingDemandPageResponse>> getShippingDemandPage(@RequestBody ShippingDemandQueryRequest request) {
        IPage<ShippingDemandPageResponse> page = shippingDemandService.getShippingDemandPageResponse(request);
        return Result.success(page);
    }

    @ApiOperation("根据ID获取发货需求详情")
    @GetMapping("/{id}")
    public Result<ShippingDemandDetailResponse> getShippingDemandById(@ApiParam("发货需求ID") @PathVariable String id) {
        ShippingDemandDetailResponse response = shippingDemandService.getShippingDemandDetailById(Long.valueOf(id));
        if (response == null) {
            return Result.failed("发货需求不存在");
        }
        return Result.success(response);
    }


    @ApiOperation("导出发货需求列表")
    @PostMapping("/export")
    public void exportShippingDemandList(@RequestBody ShippingDemandQueryRequest request, HttpServletResponse response) {
        try {
            shippingDemandService.exportShippingDemandList(request, response);
        } catch (Exception e) {
            throw new RuntimeException("导出失败：" + e.getMessage());
        }
    }
}
