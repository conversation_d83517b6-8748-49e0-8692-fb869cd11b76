package com.yi.controller.sku.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Objects;

/**
 * SKU类目信息响应
 */
@Data
@ApiModel(value = "SkuCategoryResponse", description = "SKU类目信息响应")
public class SkuCategoryResponse {

    @ApiModelProperty(value = "一级类目编码")
    private String firstCategory;

    @ApiModelProperty(value = "一级类目名称")
    private String firstCategoryName;

    @ApiModelProperty(value = "二级类目")
    private String secondCategory;

    @ApiModelProperty(value = "类目组合显示名称")
    private String categoryDisplayName;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SkuCategoryResponse that = (SkuCategoryResponse) o;
        return Objects.equals(firstCategory, that.firstCategory) &&
                Objects.equals(secondCategory, that.secondCategory);
    }

    @Override
    public int hashCode() {
        return Objects.hash(firstCategory, secondCategory);
    }
}
