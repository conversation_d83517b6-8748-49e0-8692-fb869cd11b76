package com.yi.configuration.jwt;


import java.io.Serializable;
import java.util.Date;

public class TokenVo implements Serializable{
    /** token */
    private String token;
    /** 有效时间：单位：秒 */
    private Integer expire;

    private Date expireTime;

    public TokenVo() {
        super();
    }

    public TokenVo(String token, Integer expire, Date expireTime) {
        this.token = token;
        this.expire = expire;
        this.expireTime = expireTime;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Integer getExpire() {
        return expire;
    }

    public void setExpire(Integer expire) {
        this.expire = expire;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }
}
