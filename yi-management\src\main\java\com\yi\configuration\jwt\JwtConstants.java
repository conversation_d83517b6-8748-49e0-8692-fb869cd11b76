package com.yi.configuration.jwt;

import java.math.BigDecimal;

public class JwtConstants {

    private JwtConstants() {
    }


    public static final String RETURN_SUCCESS = "1";
    public static final String RETURN_FAILURE = "-1";

    public static final String ONE = "1";
    public static final String TWO = "2";
    public static final String THREE = "3";
    public static final String ZERO = "0";
    public static final String FOUR = "4";
    public static final String FIVE = "5";
    public static final String SIX = "6";
    public static final String SEVEN = "7";
    public static final String EIGHT = "8";
    public static final String NINE = "9";
    public static final String ONE_ZERO = "10";
    public static final String TWO_ZERO = "20";
    public static final String THREE_ZERO = "30";
    public static final String VALIDATION_GOODS = "validation_goods";
    public static final String LIMITED_IDEMPOTENT_MESSAGE = "请勿重复请求，稍后再试";
    public static final int WAIT_CARRIER_BIDDING_EXPIRED_TIME = 30;//委托单发布12m内无抢单，自动超时取消
    public static final BigDecimal BIG_DECIMAL_ZERO = new BigDecimal(0);
    public static final BigDecimal NEGATIVE_ONE = new BigDecimal(-1);
    public static final BigDecimal BIG_DECIMAL_ONE = new BigDecimal(1);
    public static final BigDecimal BIG_DECIMAL_TWO = new BigDecimal(2);
    public static final BigDecimal BIG_DECIMAL_THREE = new BigDecimal(3);
    public static final BigDecimal BIG_DECIMAL_FOUR = new BigDecimal(4);
    public static final String UPLOAD_FILE_PATH_TIMESTAMP_FORMATTER = "yyyyMM";
    public static final Integer NEGATIVE_INTEGER_ONE = -1;
    public static final Integer INTEGER_ONE = 1;
    public static final Integer INTEGER_ZERO = 0;
    public static final Integer INTEGER_TWO = 2;

    public  static final String ACCOUNT = "account";
    public static final String REQUEST_PARAMS = "requestParams";
    public  static final String TIMESTAMP = "timestamp";
    public  static final String SIGN_TYPE = "signType";
    public  static final String SIGN = "sign";
    public static  final    String RSA = "RSA";
    public static final Integer INTEGER_THREE = 3;
    public static final Integer INTEGER_FOUR = 4;
    public static final Integer INT_FIFTY = 50;
    public static final Integer INT_THREE = 3;
    public static final Integer INTEGER_FIVE = 5;
    public static final Integer INTEGER_SIX = 6;
    public static final Integer INTEGER_SEVEN = 7;
    public static final Integer INTEGER_EIGHT = 8;
    public static final Integer INTEGER_NINE = 9;
    public static final Integer INTEGER_TEN = 10;
    public static final Integer INTEGER_ELVEN = 11;
    public static final Integer INTEGER_TWELVE = 12;
    public static final Integer INTEGER_THIRTEEN = 13;
    public static final Integer INTEGER_FOURTEEN = 14;
    public static final Integer INTEGER_FIFTEEN = 15;
    public static final Integer INTEGER_SIXTEEN = 16;
    public static final Integer INTEGER_TWO_HUNDRED = 200;
    public static final Integer INTEGER_ONE_THOUSAND_ONE_HUNDRED = 1100;
    public static final Integer INTEGER_ONE_THOUSAND_ONE_HUNDRED_AND_FIFTY = 1350;
    public static final Long LONG_ZERO = 0L;
    public static final Long LONG_FOUR = 4L;
    public static final Long LONG_THREE = 3L;


    public static final Long LONG_ONE = 1L;
    public static final Long NEGATIVE_LONG_ONE = -1L;
    public static final boolean BOOLTRUE = true;
    public static final boolean BOOLFALES = false;

    public static final String USER_PERMISSION = "用户权限不正确";


    /**
     * 默认的根节点path
     */
    public static final String LOGISTICS_WEB = "1,3";
    public static final String LOGISTICS_DRIVER = "1,2";

    /**
     * 默认的根节点path
     */
    public static final String ROOT_PATH_DEF = ",";
    /**
     * 默认的父id
     */
    public static final Long PARENT_ID_DEF = -1L;
    /**
     * 帐号密码加密 salt
     */
    public static final int PW_ENCORDER_SALT = 12;
    /**
     * 默认的菜单组code
     */
    public static final String MENU_GROUP_CODE_DEF = "DEF";

    /**
     * 通过网关过滤后，统一添加的header
     */
    public static final String ZUUL_HEADER_KEY_APP_ID = "appId";
    /**
     * 临时存在ThreadLocal中的 appId key
     */
    public static final String CONTEXT_KEY_APP_ID = "currentAppId";
    /**
     * 临时存在ThreadLocal中的 userId key
     */
    public static final String CONTEXT_KEY_USER_ID = "currentUserId";
    /**
     * 临时存在ThreadLocal中的 登录名 key
     */
    public static final String CONTEXT_KEY_USERNAME = "currentUserName";
    /**
     * 临时存在ThreadLocal中的 昵称 key
     */
    public static final String CONTEXT_KEY_NICKNAME = "currentNickName";
    /**
     * 临时存在ThreadLocal中的 token key
     */
    public static final String CONTEXT_KEY_APP_TOKEN = "currentAppToken";
    /**
     * 临时存在ThreadLocal中的 user company key
     */
    public static final String CONTEXT_KEY_USER_COMPANY = "currentUserCompany";
    public static final  String JWT_KEY_USER_GROUP_COMPANY_ID="userGroupCompanyId";
    public static final String JWT_APP_KEY = "appKey";

    public static final String JWT_APP_SECRET = "appSecret";

    public static final String JWT_MERCHANTS_COMPANY_NAME = "merchantsCompanyName";
    /**  */
    public static final String JWT_KEY_USER_ID = "userId";
    /**  */
    public static final String JWT_KEY_NICK_NAME = "nickName";
    /**  */
    public static final String JWT_KEY_APP_ID = "appId";
    /**  */
    public static final String JWT_KEY_USER_NAME = "userName";
    public static final String JWT_KEY_USER_ROLES = "userRoles";

    public static final String JWT_KEY_FACTORY_ID = "factoryId";

    public static final String JWT_KEY_SUPPLIER_ID = "supplierId";

    public static final String JWT_KEY_USER_COMPANY = "userCompany";

    public static final String JWT_KEY_USER_CODE = "userCode";

    public static final String JWT_TOKEN_MAP_KEY_TOKEN = "token";

    public static final String JWT_TOKEN_MAP_KEY_TOKEN_EXPIRE_TIME = "expireTime";

    public static final String LOGISTICS_MANAGEMENT_WEB = "LOGISTICS_MANAGEMENT_WEB_KEY";
    public static final String LOGISTICS_WEBAPI_CARRIER_KEY = "LOGISTICS_WEBAPI_CARRIER_KEY";
    public static final String LOGISTICS_WEBAPI_ENTRUST_KEY = "LOGISTICS_WEBAPI_ENTRUST_KEY";
    public static final String LOGISTICS_WEBAPI_CARRIER = "LOGISTICS_WEBAPI_CARRIER";
    public static final String LOGISTICS_WEBAPI_ENTRUST = "LOGISTICS_WEBAPI_ENTRUST";
    public static final String LOGISTICS_WEBAPI_ACCOUNT_ROLE_KEY = "LOGISTICS_WEBAPI_ACCOUNT_ROLE_KEY";

	public static final String AES_KEY = "1W5hoD!qU^5&L#Ix";
}


