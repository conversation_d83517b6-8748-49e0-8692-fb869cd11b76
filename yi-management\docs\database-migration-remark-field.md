# 发货需求表添加备注字段 - 数据库迁移文档

## 变更概述

为`t_shipping_demand`表添加`remark`字段，用于存储发货需求的备注信息。

## 变更详情

### 1. 数据库结构变更

**表名**: `t_shipping_demand`

**新增字段**:
- `remark` varchar(500) DEFAULT NULL COMMENT '备注'

**字段位置**: 在`demand_time`字段之后

### 2. 变更原因

- 业务需求：发货需求详情接口需要返回备注信息
- 数据完整性：支持用户在创建和修改发货需求时添加备注说明
- 功能增强：提供更完整的业务信息记录

### 3. 执行步骤

#### 3.1 生产环境迁移

```sql
-- 执行迁移脚本
source yi-management/src/main/resources/sql/migration/add_remark_to_shipping_demand.sql
```

#### 3.2 新环境部署

使用更新后的建表脚本：
```sql
-- 执行完整建表脚本
source yi-management/src/main/resources/sql/ddl.sql
```

### 4. 影响范围

#### 4.1 代码变更

**实体类**:
- `TShippingDemand.java` - 已添加remark字段

**Mapper**:
- `TShippingDemandMapper.xml` - 已更新BaseResultMap和Base_Column_List

**VO类**:
- `ShippingDemandDetailVO.java` - 已添加remark字段

**响应类**:
- `ShippingDemandDetailResponse.java` - 已添加remark字段

**服务类**:
- `TShippingDemandService.java` - 已更新详情查询和转换方法

#### 4.2 接口变更

**影响接口**:
- `GET /api/shipping-demand/{id}` - 返回结果新增remark字段

**向后兼容性**: ✅ 完全兼容，新增字段不影响现有功能

### 5. 验证方法

#### 5.1 数据库验证

```sql
-- 检查字段是否存在
DESCRIBE t_shipping_demand;

-- 检查字段属性
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 't_shipping_demand'
  AND COLUMN_NAME = 'remark';
```

#### 5.2 应用验证

```bash
# 启动应用
mvn spring-boot:run

# 测试接口
curl -X GET "http://localhost:8080/api/shipping-demand/1" \
     -H "Authorization: Bearer {token}"
```

**预期结果**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": "1",
        "orderNo": "SO20240729001",
        "customerCompanyName": "测试客户",
        "warehouseName": "测试仓库",
        "receivingAddress": "北京市朝阳区测试地址",
        "receiverName": "张三",
        "remark": "测试备注信息",
        // ... 其他字段
    }
}
```

### 6. 回滚方案

如需回滚，执行以下SQL：

```sql
-- 删除remark字段
ALTER TABLE `t_shipping_demand` DROP COLUMN `remark`;
```

**注意**: 回滚将丢失所有备注数据，请谨慎操作。

### 7. 注意事项

1. **数据迁移**: 现有数据的remark字段将为NULL
2. **应用重启**: 代码变更需要重启应用生效
3. **缓存清理**: 如有相关缓存，需要清理
4. **监控**: 关注迁移后的应用性能和错误日志

### 8. 相关文件

- 迁移脚本: `yi-management/src/main/resources/sql/migration/add_remark_to_shipping_demand.sql`
- 建表脚本: `yi-management/src/main/resources/sql/ddl.sql`
- 实体类: `yi-management/src/main/java/com/yi/entity/TShippingDemand.java`
- Mapper: `yi-management/src/main/resources/mapper/TShippingDemandMapper.xml`

### 9. 变更记录

| 日期 | 操作人 | 变更内容 | 版本 |
|------|--------|----------|------|
| 2024-07-29 | 系统 | 添加remark字段到t_shipping_demand表 | v1.0 |
