package com.yi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yi.entity.SysRoleResource;

import java.util.List;

/**
 * 角色资源关联表 服务类
 */
public interface SysRoleResourceService extends IService<SysRoleResource> {

    /**
     * 根据角色ID删除角色资源关联
     *
     * @param roleId 角色ID
     * @return 删除数量
     */
    int deleteByRoleId(Long roleId);

    /**
     * 根据资源ID删除角色资源关联
     *
     * @param resourceId 资源ID
     * @return 删除数量
     */
    int deleteByResourceId(Long resourceId);

    /**
     * 批量插入角色资源关联
     *
     * @param roleResources 角色资源关联列表
     * @return 插入数量
     */
    int batchInsert(List<SysRoleResource> roleResources);

    /**
     * 根据角色ID查询资源ID列表
     *
     * @param roleId 角色ID
     * @return 资源ID列表
     */
    List<Long> selectResourceIdsByRoleId(Long roleId);

    /**
     * 根据资源ID查询角色ID列表
     *
     * @param resourceId 资源ID
     * @return 角色ID列表
     */
    List<Long> selectRoleIdsByResourceId(Long resourceId);

    /**
     * 检查角色是否拥有指定资源
     *
     * @param roleId 角色ID
     * @param resourceId 资源ID
     * @return 是否存在
     */
    boolean existsByRoleIdAndResourceId(Long roleId, Long resourceId);
}
