package com.yi.controller.warehouse.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 出库单请求
 */
@Data
@ApiModel(value = "OutboundOrderRequest", description = "出库单请求")
public class OutboundOrderRequest {

    @ApiModelProperty(value = "出库单ID（编辑时必填）")
    private Long id;

    @ApiModelProperty(value = "出库单号（系统自动生成，可不填）")
    private String orderNo;

    @NotNull(message = "出库类型不能为空")
    @ApiModelProperty(value = "出库类型：1-销售出库，2-调拨出库", required = true)
    private Integer outboundType;

    @NotNull(message = "出库公司ID不能为空")
    @ApiModelProperty(value = "出库公司ID", required = true)
    private Long outboundCompanyId;

    @NotBlank(message = "出库公司名称不能为空")
    @ApiModelProperty(value = "出库公司名称", required = true)
    private String outboundCompanyName;

    @ApiModelProperty(value = "出库地址")
    private String outboundAddress;

    @ApiModelProperty(value = "配送方式")
    private String deliveryMethod;

    @ApiModelProperty(value = "车牌号")
    private String vehicleNumber;

    @ApiModelProperty(value = "司机姓名")
    private String driverName;

    @ApiModelProperty(value = "司机联系方式")
    private String driverPhone;

    @NotNull(message = "收货公司ID不能为空")
    @ApiModelProperty(value = "收货公司ID", required = true)
    private Long receiveCompanyId;

    @NotBlank(message = "收货公司名称不能为空")
    @ApiModelProperty(value = "收货公司名称", required = true)
    private String receiveCompanyName;

    @ApiModelProperty(value = "收货地址")
    private String receiveAddress;

    @NotNull(message = "一级类目不能为空")
    @ApiModelProperty(value = "一级类目：1-共享托盘", required = true)
    private Integer firstCategory;

    @ApiModelProperty(value = "二级类目")
    private String secondCategory;

    @NotNull(message = "计划出库数不能为空")
    @Min(value = 1, message = "计划出库数必须大于0")
    @ApiModelProperty(value = "计划出库数", required = true)
    private Integer plannedQuantity;

    @ApiModelProperty(value = "备注")
    private String remark;
}
