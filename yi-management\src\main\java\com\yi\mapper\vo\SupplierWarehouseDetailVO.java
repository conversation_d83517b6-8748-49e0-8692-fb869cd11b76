package com.yi.mapper.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 供应商仓库详情VO
 */
@Data
public class SupplierWarehouseDetailVO {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("仓库名称")
    private String warehouseName;

    @ApiModelProperty("省份名称")
    private String provinceName;

    @ApiModelProperty("城市名称")
    private String cityName;

    @ApiModelProperty("区县名称")
    private String areaName;

    @ApiModelProperty("地址详情")
    private String detailedAddress;

    @ApiModelProperty("联系人")
    private String contactPerson;

    @ApiModelProperty("联系方式")
    private String contactPhone;
}
