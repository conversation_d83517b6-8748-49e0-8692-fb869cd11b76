# 发运订单新增接口

## 接口信息

**接口地址**: `POST /api/shipping-order/add`

**接口描述**: 新增发运订单，支持附件上传

## 请求参数

### 请求体

```json
{
    "contractCode": "CONTRACT001",
    "customerCompanyId": "1",
    "warehouseId": "1",
    "firstCategory": "1",
    "secondCategory": "标准托盘",
    "count": "100",
    "demandTime": "2024-01-01",
    "remark": "订单备注信息",
    "attachmentUrls": [
        "/uploads/shipping/file1.pdf",
        "/uploads/shipping/file2.jpg"
    ]
}
```

### 字段说明

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| contractCode | String | 是 | 合同编号 |
| customerCompanyId | String | 是 | 客户公司ID |
| warehouseId | String | 是 | 收货仓库ID |
| firstCategory | String | 是 | 一级类目（1-共享托盘） |
| secondCategory | String | 否 | 二级类目 |
| count | String | 是 | 需求数量 |
| demandTime | String | 是 | 需求时间（格式：yyyy-MM-dd） |
| remark | String | 否 | 备注信息 |
| attachmentUrls | List<String> | 否 | 附件链接列表 |

## 返回参数

### 成功响应

```json
{
    "code": 200,
    "message": "操作成功",
    "data": true
}
```

### 失败响应

```json
{
    "code": 400,
    "message": "参数验证失败：合同编号不能为空",
    "data": false
}
```

## 业务逻辑

### 订单创建流程

1. **参数验证**
   - 验证必填字段：contractCode、customerCompanyId、warehouseId、firstCategory、count、demandTime
   - 验证数据格式：日期格式、数字格式等

2. **订单号生成**
   - 格式：`SO + yyyyMMdd + 6位序号`
   - 示例：`SO20240101000001`
   - 序号从当天最大序号+1开始

3. **默认值设置**
   - 订单状态：`PENDING`（待发货）
   - 发货数量：`0`
   - 签收数量：`0`
   - 有效性：`1`（有效）

4. **数据保存**
   - 保存订单基本信息到`t_shipping_order`表
   - 如果有附件，保存附件信息到`t_general_file`表

5. **附件处理**
   - 文件类型：`2`（发运订单）
   - 关联ID：订单ID
   - 支持多个附件

### 订单号生成规则

```sql
-- 查询当天最大序号
SELECT COALESCE(MAX(CAST(SUBSTRING(order_no, LENGTH(#{prefix}) + 1) AS UNSIGNED)), 0)
FROM t_shipping_order 
WHERE order_no LIKE CONCAT(#{prefix}, '%') 
  AND LENGTH(order_no) = LENGTH(#{prefix}) + 6
  AND valid = 1
```

其中prefix = "SO" + yyyyMMdd，例如：SO20240101

### 数据库操作

#### 1. 插入订单记录

```sql
INSERT INTO t_shipping_order (
    order_no, contract_code, customer_company_id, warehouse_id,
    first_category, second_category, count, shipped_quantity,
    received_quantity, demand_time, status, remark,
    created_by, created_time, last_modified_by, last_modified_time, valid
) VALUES (
    #{orderNo}, #{contractCode}, #{customerCompanyId}, #{warehouseId},
    #{firstCategory}, #{secondCategory}, #{count}, 0,
    0, #{demandTime}, 'PENDING', #{remark},
    #{createdBy}, NOW(), #{lastModifiedBy}, NOW(), 1
)
```

#### 2. 插入附件记录（如果有附件）

```sql
INSERT INTO t_general_file (
    type, related_id, file_path, file_type,
    created_by, created_time, last_modified_by, last_modified_time, valid
) VALUES (
    2, #{orderId}, #{filePath}, #{fileType},
    #{createdBy}, NOW(), #{lastModifiedBy}, NOW(), 1
)
```

## 使用示例

### 基本订单创建

```bash
curl -X POST "http://localhost:8080/api/shipping-order/add" \
  -H "Content-Type: application/json" \
  -d '{
    "contractCode": "CONTRACT001",
    "customerCompanyId": "1",
    "warehouseId": "1",
    "firstCategory": "1",
    "secondCategory": "标准托盘",
    "count": "100",
    "demandTime": "2024-01-01",
    "remark": "测试订单"
  }'
```

### 带附件的订单创建

```bash
curl -X POST "http://localhost:8080/api/shipping-order/add" \
  -H "Content-Type: application/json" \
  -d '{
    "contractCode": "CONTRACT002",
    "customerCompanyId": "2",
    "warehouseId": "2",
    "firstCategory": "1",
    "count": "50",
    "demandTime": "2024-01-02",
    "attachmentUrls": [
      "/uploads/shipping/contract.pdf",
      "/uploads/shipping/requirements.jpg"
    ]
  }'
```

## 数据验证

### 必填字段验证

- **contractCode**: 不能为空或null
- **customerCompanyId**: 不能为空或null
- **warehouseId**: 不能为空或null
- **firstCategory**: 不能为空或null
- **count**: 不能为空或null，必须为正整数
- **demandTime**: 不能为空或null，必须为有效日期格式

### 数据格式验证

- **demandTime**: 格式必须为yyyy-MM-dd
- **count**: 必须为正整数字符串
- **firstCategory**: 目前支持"1"（共享托盘）
- **attachmentUrls**: 如果提供，必须为有效的文件路径列表

## 错误处理

### 常见错误

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 400 | 合同编号不能为空 | contractCode为空或null |
| 400 | 客户公司不能为空 | customerCompanyId为空或null |
| 400 | 收货仓库不能为空 | warehouseId为空或null |
| 400 | 一级类目不能为空 | firstCategory为空或null |
| 400 | 需求数量不能为空 | count为空或null |
| 400 | 需求时间不能为空 | demandTime为空或null |
| 400 | 日期格式错误 | demandTime格式不正确 |
| 400 | 数量格式错误 | count不是有效数字 |
| 500 | 订单号生成失败 | 系统内部错误 |
| 500 | 附件保存失败 | 文件系统错误 |

### 错误响应示例

```json
{
    "code": 400,
    "message": "参数验证失败：需求数量不能为空",
    "data": false
}
```

## 注意事项

1. **订单号唯一性**: 系统自动生成，确保唯一性
2. **状态管理**: 新增订单默认为"待发货"状态
3. **附件处理**: 附件URL应该是已上传到系统的有效路径
4. **数据一致性**: 确保客户公司、仓库等关联数据存在
5. **权限控制**: 确保用户有创建订单的权限
6. **事务处理**: 订单和附件保存在同一事务中，确保数据一致性

## 后续流程

订单创建成功后，可以进行以下操作：

1. **查看订单详情**: 通过订单ID查询详细信息
2. **编辑订单**: 在"待发货"状态下可以编辑
3. **状态流转**: 根据业务流程更新订单状态
4. **附件管理**: 查看、下载、更新附件
5. **WMS同步**: 订单信息同步到仓储管理系统
