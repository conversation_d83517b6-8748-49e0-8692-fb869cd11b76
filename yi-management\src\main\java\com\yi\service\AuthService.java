package com.yi.service;

import com.alibaba.excel.util.ListUtils;
import com.yi.configuration.jwt.*;
import com.yi.controller.user.io.LoginRequest;
import com.yi.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class AuthService {

    @Autowired
    private JwtTokenUtil jwtTokenUtil;
    @Autowired
    private RedisUtil redisUtil;

    public TokenModule loginCreateToken(Long userId, String userName) {
        TokenVo tokenVo = generateToken(new JWTInfo(userId, userName));
        return new TokenModule(tokenVo.getToken(), tokenVo.getExpire());
    }

    //生成token
    public TokenVo generateToken(IJWTInfo jwtInfo) {
        TokenVo tokenVo = jwtTokenUtil.generateToken(jwtInfo);
        Map<String, Object> tokenMap = new HashMap<>();
        tokenMap.put(JwtConstants.JWT_TOKEN_MAP_KEY_TOKEN, tokenVo.getToken());
        tokenMap.put(JwtConstants.JWT_TOKEN_MAP_KEY_TOKEN_EXPIRE_TIME, tokenVo.getExpireTime());
        redisUtil.set(jwtInfo.getUserCode(), tokenMap, Long.valueOf(tokenVo.getExpire()));
        log.info(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>generateToken " + tokenVo.getToken() + " ;add redis with key [" + jwtInfo.getUserCode() + "], value " + tokenMap);
        return tokenVo;
    }

    //刷新token
    public TokenModule refreshToken(String oldToken) {
        IJWTInfo infoFromToken = jwtTokenUtil.getInfoFromToken(oldToken);
        TokenVo tokenVo = generateToken(new JWTInfo(infoFromToken.getSupplierId(), infoFromToken.getFactoryId(),
                infoFromToken.getUserName(),
                infoFromToken.getUserId(),
                infoFromToken.getNickName(), infoFromToken.getCustomerCompanyId(), infoFromToken.getAppId(), infoFromToken.getUserCode(), infoFromToken.getUserRoles()));
        return new TokenModule(tokenVo.getToken(), tokenVo.getExpire());
    }

    //清空token
    public void cleanUserToken(CleanUserTokenMessage model) {
        //可能会影响多个系统的token，所以放list
        List<JwtApplicationTokenPrefixEnum> jwtApplicationTokenPrefixEnumList = new ArrayList<>();
        if (JwtApplicationTokenPrefixEnum.MES_MANAGEMENT_WEB.getKey().equals(model.getUserTokenType())) {
            jwtApplicationTokenPrefixEnumList.add(JwtApplicationTokenPrefixEnum.MES_MANAGEMENT_WEB);
        } else if (JwtApplicationTokenPrefixEnum.MES_CUSTOMER_WX.getKey().equals(model.getUserTokenType())) {
            jwtApplicationTokenPrefixEnumList.add(JwtApplicationTokenPrefixEnum.MES_CUSTOMER_WX);
        } else if (JwtApplicationTokenPrefixEnum.MES_CUSTOMER_APP.getKey().equals(model.getUserTokenType())) {
            jwtApplicationTokenPrefixEnumList.add(JwtApplicationTokenPrefixEnum.MES_CUSTOMER_APP);
        }
        //遍历会影响系统的token，逐一删除
        if (!jwtApplicationTokenPrefixEnumList.isEmpty()) {
            for (JwtApplicationTokenPrefixEnum item : jwtApplicationTokenPrefixEnumList) {
                redisUtil.remove(item.getKey() + model.getUserCode());
            }
        }
    }

}
