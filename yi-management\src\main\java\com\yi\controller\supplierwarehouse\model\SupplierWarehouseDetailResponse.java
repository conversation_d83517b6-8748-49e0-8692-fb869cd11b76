package com.yi.controller.supplierwarehouse.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 供应商仓库详情响应
 */
@Data
@ApiModel("供应商仓库详情响应")
public class SupplierWarehouseDetailResponse {

    @ApiModelProperty("主键ID")
    private String id;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("仓库名称")
    private String warehouseName;

    @ApiModelProperty("省份")
    private String provinceName;

    @ApiModelProperty("城市")
    private String cityName;

    @ApiModelProperty("区县")
    private String areaName;

    @ApiModelProperty("地址详情")
    private String detailedAddress;

    @ApiModelProperty("联系人")
    private String contactPerson;

    @ApiModelProperty("联系方式")
    private String contactPhone;
}
