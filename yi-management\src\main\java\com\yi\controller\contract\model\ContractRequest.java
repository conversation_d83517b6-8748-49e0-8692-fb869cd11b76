package com.yi.controller.contract.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 合同新增/编辑请求
 */
@Data
@ApiModel(value = "ContractRequest", description = "合同新增/编辑请求")
public class ContractRequest {

    @ApiModelProperty(value = "主键ID（编辑时必填）")
    private String id;

    @NotNull(message = "我司主体不能为空")
    @ApiModelProperty(value = "我司主体：1-易矩科技", required = true)
    private String outCustomerCompanyId;

    @NotBlank(message = "合同编号不能为空")
    @ApiModelProperty(value = "合同编号", required = true)
    private String contractNo;

    @NotBlank(message = "客户主体不能为空")
    @ApiModelProperty(value = "客户主体ID", required = true)
    private String customerCompanyId;

    @NotBlank(message = "合同生效日期不能为空")
    @ApiModelProperty(value = "合同生效日期", required = true)
    private String effectiveDate;

    @NotBlank(message = "合同失效日期不能为空")
    @ApiModelProperty(value = "合同失效日期", required = true)
    private String expiryDate;

    @ApiModelProperty(value = "备注信息")
    private String remark;

    @ApiModelProperty(value = "合同附件文件路径列表")
    private List<String> attachmentPaths;

    @ApiModelProperty(value = "SKU明细列表")
    private List<ContractSkuRequest> skuDetails;
}
