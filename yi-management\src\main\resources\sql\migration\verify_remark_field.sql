-- =============================================
-- 验证t_shipping_demand表remark字段
-- 用于确认字段添加成功并测试基本功能
-- =============================================

-- 1. 检查表结构
SELECT '=== 表结构检查 ===' AS step;
DESCRIBE t_shipping_demand;

-- 2. 检查remark字段详细信息
SELECT '=== remark字段详细信息 ===' AS step;
SELECT 
    COLUMN_NAME as '字段名',
    DATA_TYPE as '数据类型',
    CHARACTER_MAXIMUM_LENGTH as '最大长度',
    IS_NULLABLE as '可为空',
    COLUMN_DEFAULT as '默认值',
    COLUMN_COMMENT as '注释'
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 't_shipping_demand'
  AND COLUMN_NAME = 'remark';

-- 3. 检查字段位置（应该在demand_time之后）
SELECT '=== 字段顺序检查 ===' AS step;
SELECT 
    ORDINAL_POSITION as '位置',
    COLUMN_NAME as '字段名',
    COLUMN_COMMENT as '注释'
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 't_shipping_demand'
  AND ORDINAL_POSITION BETWEEN 
    (SELECT ORDINAL_POSITION FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 't_shipping_demand' AND COLUMN_NAME = 'demand_time') 
    AND 
    (SELECT ORDINAL_POSITION FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 't_shipping_demand' AND COLUMN_NAME = 'created_by')
ORDER BY ORDINAL_POSITION;

-- 4. 测试插入数据（如果表为空）
SELECT '=== 数据测试 ===' AS step;

-- 检查是否有测试数据
SELECT COUNT(*) as '现有记录数' FROM t_shipping_demand WHERE valid = 1;

-- 如果需要，可以插入测试数据（注释掉，按需执行）
/*
INSERT INTO t_shipping_demand (
    order_no, 
    status, 
    customer_company_id, 
    warehouse_id, 
    first_category, 
    second_category, 
    demand_quantity, 
    pending_quantity, 
    shipped_quantity, 
    unexecuted_quantity, 
    demand_time, 
    remark, 
    created_by, 
    valid
) VALUES (
    'TEST_ORDER_001', 
    1000, 
    1, 
    1, 
    1, 
    '测试二级类目', 
    100, 
    50, 
    0, 
    50, 
    CURDATE(), 
    '这是一个测试备注信息', 
    'system_test', 
    1
);
*/

-- 5. 查询测试（验证remark字段可以正常查询）
SELECT '=== 查询测试 ===' AS step;
SELECT 
    id,
    order_no,
    remark,
    created_time
FROM t_shipping_demand 
WHERE valid = 1 
LIMIT 5;

SELECT '=== 验证完成 ===' AS step;
