package com.yi.controller.contract.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 合同SKU明细响应
 */
@Data
@ApiModel(value = "ContractSkuResponse", description = "合同SKU明细响应")
public class ContractSkuResponse {

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "合同ID")
    private String contractId;

    @ApiModelProperty(value = "一级类目")
    private String firstCategory;

    @ApiModelProperty(value = "一级类目名称")
    private String firstCategoryName;

    @ApiModelProperty(value = "业务模式")
    private String businessMode;

    @ApiModelProperty(value = "业务模式名称")
    private String businessModeName;

    @ApiModelProperty(value = "创建时间")
    private String createdTime;
}
