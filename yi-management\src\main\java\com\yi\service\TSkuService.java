package com.yi.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yi.controller.sku.model.*;
import com.yi.entity.TSku;
import com.yi.mapper.TSkuMapper;
import com.yi.utils.ExcelUtils;
import com.yi.utils.FormatUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * SKU表 服务实现类
 */
@Service
public class TSkuService extends ServiceImpl<TSkuMapper, TSku> {

    /**
     * 分页查询SKU列表
     *
     * @param request 查询条件
     * @return 分页结果
     */
    public IPage<TSku> getSkuPage(SkuQueryRequest request) {
        // 转换分页参数
        Integer current = FormatUtils.safeToInteger(request.getCurrent(), 1);
        Integer size = FormatUtils.safeToInteger(request.getSize(), 10);
        Page<TSku> page = new Page<>(current, size);
        
        LambdaQueryWrapper<TSku> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TSku::getValid, 1)
                .eq(FormatUtils.safeToInteger(request.getEnabled()) != null, TSku::getEnabled, FormatUtils.safeToInteger(request.getEnabled()))
                .eq(FormatUtils.safeToInteger(request.getFirstCategory()) != null, TSku::getFirstCategory, FormatUtils.safeToInteger(request.getFirstCategory()))
                .like(StringUtils.hasText(request.getSecondCategory()), TSku::getSecondCategory, request.getSecondCategory())
                .orderByDesc(TSku::getCreatedTime);

        return this.page(page, wrapper);
    }

    /**
     * 分页查询SKU列表（返回Response格式）
     *
     * @param request 查询条件
     * @return 分页结果
     */
    public IPage<SkuPageResponse> getSkuPageResponse(SkuQueryRequest request) {
        // 先查询原始数据
        IPage<TSku> originalPage = getSkuPage(request);
        
        // 转换为Response对象
        List<SkuPageResponse> responseList = originalPage.getRecords().stream()
                .map(this::convertToPageResponse)
                .collect(Collectors.toList());
        
        // 构建返回的分页对象
        Page<SkuPageResponse> responsePage = new Page<>(originalPage.getCurrent(), originalPage.getSize(), originalPage.getTotal());
        responsePage.setRecords(responseList);
        
        return responsePage;
    }

    /**
     * 根据ID获取SKU详情
     *
     * @param id 主键ID
     * @return SKU详情
     */
    public TSku getSkuById(Long id) {
        LambdaQueryWrapper<TSku> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TSku::getId, id)
                .eq(TSku::getValid, 1);
        return this.getOne(wrapper);
    }

    /**
     * 根据ID获取SKU详情（返回Response格式）
     *
     * @param id 主键ID
     * @return SKU详情Response
     */
    public SkuDetailResponse getSkuDetailById(Long id) {
        TSku sku = getSkuById(id);
        if (sku == null) {
            return null;
        }
        return convertToDetailResponse(sku);
    }

    /**
     * 新增SKU
     *
     * @param request SKU信息
     * @return 是否成功
     */
    public boolean addSku(SkuRequest request) {
        // 校验类目组合是否重复
        if (isCategoryExists(request.getFirstCategory(), request.getSecondCategory(), request.getThirdCategory(), null)) {
            throw new RuntimeException("该类目组合已存在，不能重复");
        }

        TSku sku = new TSku();

        // 手动转换字段类型
        sku.setFirstCategory(FormatUtils.safeToInteger(request.getFirstCategory()));
        sku.setSecondCategory(request.getSecondCategory());
        sku.setThirdCategory(request.getThirdCategory());
        sku.setLength(FormatUtils.safeToInteger(request.getLength()));
        sku.setWidth(FormatUtils.safeToInteger(request.getWidth()));
        sku.setHeight(FormatUtils.safeToInteger(request.getHeight()));
        sku.setWeight(FormatUtils.safeToBigDecimal(request.getWeight()));
        sku.setRemark(request.getRemark());

        // 设置默认值
        sku.setEnabled(0); // 默认禁用状态
        sku.setValid(1);
        sku.setCreatedTime(LocalDateTime.now());
        sku.setLastModifiedTime(LocalDateTime.now());

        return this.save(sku);
    }

    /**
     * 更新SKU
     *
     * @param request SKU信息
     * @return 是否成功
     */
    public boolean updateSku(SkuRequest request) {
        Long id = FormatUtils.safeToLong(request.getId());

        // 获取原有SKU信息，用于校验一级类目和二级类目是否被修改
        TSku existingSku = getSkuById(id);
        if (existingSku == null) {
            throw new RuntimeException("SKU不存在");
        }

        // 校验一级类目是否被修改
        Integer newFirstCategory = FormatUtils.safeToInteger(request.getFirstCategory());
        if (!existingSku.getFirstCategory().equals(newFirstCategory)) {
            throw new RuntimeException("一级类目不允许修改");
        }

        // 校验二级类目是否被修改
        String newSecondCategory = request.getSecondCategory();
        if (!Objects.equals(existingSku.getSecondCategory(), newSecondCategory)) {
            throw new RuntimeException("二级类目不允许修改");
        }

        // 校验类目组合是否重复（排除当前记录）
        if (isCategoryExists(request.getFirstCategory(), request.getSecondCategory(), request.getThirdCategory(), id)) {
            throw new RuntimeException("该类目组合已存在，不能重复");
        }

        TSku sku = new TSku();

        // 手动转换字段类型
        sku.setId(id);
        // 注意：一级类目和二级类目不允许修改，但仍需要设置以保持数据完整性
        sku.setFirstCategory(newFirstCategory);
        sku.setSecondCategory(newSecondCategory);
        sku.setThirdCategory(request.getThirdCategory());
        sku.setLength(FormatUtils.safeToInteger(request.getLength()));
        sku.setWidth(FormatUtils.safeToInteger(request.getWidth()));
        sku.setHeight(FormatUtils.safeToInteger(request.getHeight()));
        sku.setWeight(FormatUtils.safeToBigDecimal(request.getWeight()));
        sku.setRemark(request.getRemark());

        sku.setLastModifiedTime(LocalDateTime.now());

        return this.updateById(sku);
    }


    /**
     * 启用/禁用SKU
     *
     * @param id 主键ID
     * @param enabled 启用状态
     * @return 是否成功
     */
    public boolean updateSkuStatus(Long id, Integer enabled) {
        TSku sku = new TSku();
        sku.setId(id);
        sku.setEnabled(enabled);
        sku.setLastModifiedTime(LocalDateTime.now());
        
        return this.updateById(sku);
    }

    /**
     * 导出SKU列表
     *
     * @param request 查询条件
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    public void exportSkuList(SkuQueryRequest request, HttpServletResponse response) throws IOException {
        // 查询所有符合条件的数据（不分页）
        LambdaQueryWrapper<TSku> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TSku::getValid, 1)
                .eq(FormatUtils.safeToInteger(request.getEnabled()) != null, TSku::getEnabled, FormatUtils.safeToInteger(request.getEnabled()))
                .eq(FormatUtils.safeToInteger(request.getFirstCategory()) != null, TSku::getFirstCategory, FormatUtils.safeToInteger(request.getFirstCategory()))
                .like(StringUtils.hasText(request.getSecondCategory()), TSku::getSecondCategory, request.getSecondCategory())
                .orderByDesc(TSku::getCreatedTime);
        
        List<TSku> dataList = this.list(wrapper);
        
        // 转换为导出VO
        List<SkuExportVO> exportList = dataList.stream()
                .map(this::convertToExportVO)
                .collect(Collectors.toList());
        
        // 使用EasyExcel导出（文件名已包含时间戳）
        ExcelUtils.exportExcelWithTimestamp(response, "SKU列表", "SKU列表",
                SkuExportVO.class, exportList);
    }

    /**
     * 转换为分页Response对象
     *
     * @param sku SKU实体
     * @return 分页Response对象
     */
    private SkuPageResponse convertToPageResponse(TSku sku) {
        SkuPageResponse response = new SkuPageResponse();

        // 基础字段转换
        response.setId(FormatUtils.safeToString(sku.getId()));
        response.setSecondCategory(FormatUtils.safeString(sku.getSecondCategory()));
        response.setThirdCategory(FormatUtils.safeString(sku.getThirdCategory()));
        response.setWeight(FormatUtils.safeToString(sku.getWeight()));
        response.setCreatedBy(FormatUtils.safeString(sku.getCreatedBy()));

        // 状态转换为中文
        response.setStatus(FormatUtils.getEnabledStatusDescription(sku.getEnabled()));

        // 一级类目转换为中文（显示名称）
        response.setFirstCategory(FormatUtils.getFirstCategoryDescription(sku.getFirstCategory()));

        // 规格拼接（长*宽*高格式）
        response.setSpecification(FormatUtils.formatSpecification(sku.getLength(), sku.getWidth(), sku.getHeight()));

        // 创建时间格式化
        response.setCreatedTime(FormatUtils.formatDateTime(sku.getCreatedTime()));

        return response;
    }

    /**
     * 转换为详情Response对象
     *
     * @param sku SKU实体
     * @return 详情Response对象
     */
    private SkuDetailResponse convertToDetailResponse(TSku sku) {
        SkuDetailResponse response = new SkuDetailResponse();

        // 基础字段转换（保持与数据库字段一致）
        response.setId(FormatUtils.safeToString(sku.getId()));
        response.setFirstCategory(FormatUtils.safeToString(sku.getFirstCategory()));
        response.setSecondCategory(FormatUtils.safeString(sku.getSecondCategory()));
        response.setThirdCategory(FormatUtils.safeString(sku.getThirdCategory()));
        response.setLength(FormatUtils.safeToString(sku.getLength()));
        response.setWidth(FormatUtils.safeToString(sku.getWidth()));
        response.setHeight(FormatUtils.safeToString(sku.getHeight()));
        response.setWeight(FormatUtils.safeToString(sku.getWeight()));
        response.setRemark(FormatUtils.safeString(sku.getRemark()));
        response.setEnabled(FormatUtils.safeToString(sku.getEnabled()));
        response.setCreatedBy(FormatUtils.safeString(sku.getCreatedBy()));
        response.setCreatedTime(FormatUtils.formatDateTime(sku.getCreatedTime()));
        response.setLastModifiedBy(FormatUtils.safeString(sku.getLastModifiedBy()));
        response.setLastModifiedTime(FormatUtils.formatDateTime(sku.getLastModifiedTime()));
        response.setValid(FormatUtils.safeToString(sku.getValid()));

        // 一级类目名称（枚举值对应的名称）
        response.setFirstCategoryName(FormatUtils.getFirstCategoryDescription(sku.getFirstCategory()));

        return response;
    }

    /**
     * 转换为导出VO对象
     *
     * @param sku SKU实体
     * @return 导出VO对象
     */
    private SkuExportVO convertToExportVO(TSku sku) {
        SkuExportVO exportVO = new SkuExportVO();

        // 基础字段转换
        exportVO.setSecondCategory(FormatUtils.safeString(sku.getSecondCategory()));
        exportVO.setThirdCategory(FormatUtils.safeString(sku.getThirdCategory()));
        exportVO.setWeight(FormatUtils.safeToString(sku.getWeight()));
        exportVO.setRemark(FormatUtils.safeString(sku.getRemark()));
        exportVO.setCreatedBy(FormatUtils.safeString(sku.getCreatedBy()));

        // 状态转换为中文
        exportVO.setStatus(FormatUtils.getEnabledStatusDescription(sku.getEnabled()));

        // 一级类目转换为中文（显示名称）
        exportVO.setFirstCategory(FormatUtils.getFirstCategoryDescription(sku.getFirstCategory()));

        // 规格拼接（长*宽*高格式）
        exportVO.setSpecification(FormatUtils.formatSpecification(sku.getLength(), sku.getWidth(), sku.getHeight()));

        // 创建时间格式化
        exportVO.setCreatedTime(FormatUtils.formatDateTime(sku.getCreatedTime()));

        return exportVO;
    }

    /**
     * 校验类目组合是否存在
     *
     * @param firstCategory 一级类目
     * @param secondCategory 二级类目
     * @param thirdCategory 三级类目
     * @param excludeId 排除的ID（更新时使用，新增时传null）
     * @return 是否存在
     */
    private boolean isCategoryExists(String firstCategory, String secondCategory, String thirdCategory, Long excludeId) {
        // 转换参数
        Integer firstCategoryInt = FormatUtils.safeToInteger(firstCategory);
        String secondCategoryStr = secondCategory == null ? "" : secondCategory.trim();
        String thirdCategoryStr = thirdCategory == null ? "" : thirdCategory.trim();

        // 如果必填字段为空，不进行校验
        if (firstCategoryInt == null) {
            return false;
        }

        LambdaQueryWrapper<TSku> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TSku::getValid, 1)
                .eq(TSku::getFirstCategory, firstCategoryInt);

        // 二级类目校验（精确匹配，包括空值）
        if (secondCategoryStr.isEmpty()) {
            wrapper.and(w -> w.isNull(TSku::getSecondCategory).or().eq(TSku::getSecondCategory, ""));
        } else {
            wrapper.eq(TSku::getSecondCategory, secondCategoryStr);
        }

        // 三级类目校验（精确匹配，包括空值）
        if (thirdCategoryStr.isEmpty()) {
            wrapper.and(w -> w.isNull(TSku::getThirdCategory).or().eq(TSku::getThirdCategory, ""));
        } else {
            wrapper.eq(TSku::getThirdCategory, thirdCategoryStr);
        }

        // 更新时排除当前记录
        if (excludeId != null) {
            wrapper.ne(TSku::getId, excludeId);
        }

        return this.count(wrapper) > 0;
    }

    /**
     * 查询SKU类目信息（去重）
     *
     * @return SKU类目信息列表
     */
    public List<SkuCategoryResponse> getSkuCategoryList() {
        // 查询所有有效的SKU
        LambdaQueryWrapper<TSku> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TSku::getValid, 1)
                .eq(TSku::getEnabled, 1) // 只查询启用的SKU
                .select(TSku::getFirstCategory, TSku::getSecondCategory)
                .orderBy(true, true, TSku::getFirstCategory, TSku::getSecondCategory);

        List<TSku> skuList = this.list(wrapper);

        // 转换并去重
        return skuList.stream()
                .map(this::convertToSkuCategoryResponse)
                .distinct() // 去重（需要在SkuCategoryResponse中实现equals和hashCode）
                .collect(Collectors.toList());
    }

    /**
     * 转换为SKU类目响应对象
     *
     * @param sku SKU实体
     * @return SKU类目响应对象
     */
    private SkuCategoryResponse convertToSkuCategoryResponse(TSku sku) {
        SkuCategoryResponse response = new SkuCategoryResponse();

        response.setFirstCategory(FormatUtils.safeToString(sku.getFirstCategory()));
        response.setFirstCategoryName(FormatUtils.getFirstCategoryDescription(sku.getFirstCategory()));
        response.setSecondCategory(FormatUtils.safeString(sku.getSecondCategory()));

        // 构建显示名称：一级类目名称 + 二级类目
        String displayName = response.getFirstCategoryName();
        if (StringUtils.hasText(response.getSecondCategory())) {
            displayName += " - " + response.getSecondCategory();
        }
        response.setCategoryDisplayName(displayName);

        return response;
    }
}
