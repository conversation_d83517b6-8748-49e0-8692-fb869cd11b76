<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yi.mapper.TCustomerWarehouseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yi.entity.TCustomerWarehouse">
        <id column="id" property="id" />
        <result column="company_id" property="companyId" />
        <result column="warehouse_name" property="warehouseName" />
        <result column="province_id" property="provinceId" />
        <result column="province_name" property="provinceName" />
        <result column="city_id" property="cityId" />
        <result column="city_name" property="cityName" />
        <result column="area_id" property="areaId" />
        <result column="area_name" property="areaName" />
        <result column="detailed_address" property="detailedAddress" />
        <result column="contact_person" property="contactPerson" />
        <result column="mobile_phone" property="mobilePhone" />
        <result column="landline_phone" property="landlinePhone" />
        <result column="enabled" property="enabled" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="last_modified_by" property="lastModifiedBy" />
        <result column="last_modified_time" property="lastModifiedTime" />
        <result column="valid" property="valid" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, company_id, warehouse_name, province_id, province_name, city_id, city_name, 
        area_id, area_name, detailed_address, contact_person, mobile_phone, landline_phone, 
        enabled, created_by, created_time, last_modified_by, last_modified_time, valid, remark
    </sql>

    <!-- 分页查询仓库列表 -->
    <select id="selectWarehousePage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_warehouse
        WHERE valid = 1
        <if test="companyId != null">
            AND company_id = #{companyId}
        </if>
        <if test="warehouseName != null and warehouseName != ''">
            AND warehouse_name LIKE CONCAT('%', #{warehouseName}, '%')
        </if>
        <if test="enabled != null">
            AND enabled = #{enabled}
        </if>
        ORDER BY created_time DESC
    </select>

    <!-- 根据公司ID查询仓库列表 -->
    <select id="selectByCompanyId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_warehouse
        WHERE valid = 1 AND company_id = #{companyId}
        ORDER BY created_time DESC
    </select>

    <!-- 根据仓库名称查询仓库 -->
    <select id="selectByWarehouseName" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_warehouse
        WHERE valid = 1 AND warehouse_name = #{warehouseName}
        ORDER BY created_time DESC
    </select>

    <!-- 查询仓库列表（左关联SKU类型）- 支持分页和不分页 -->
    <select id="selectWarehouseWithSku" resultType="com.yi.mapper.vo.WarehousePageVO">
        SELECT
            w.id AS warehouseId,
            w.warehouse_name AS warehouseName,
            w.company_id AS companyId,
            c.company_name AS companyName,
            w.province_name AS provinceName,
            w.city_name AS cityName,
            w.area_name AS areaName,
            w.detailed_address AS detailedAddress,
            w.contact_person AS contactPerson,
            w.mobile_phone AS mobilePhone,
            w.landline_phone AS landlinePhone,
            w.enabled AS enabled,
            ws.first_category AS firstCategory,
            ws.second_category AS secondCategory,
            w.created_by AS createdBy,
            w.created_time AS createdTime
        FROM t_warehouse w
        LEFT JOIN t_customer_company c ON w.company_id = c.id AND c.valid = 1
        LEFT JOIN t_warehouse_sku ws ON w.id = ws.warehouse_id AND ws.valid = 1
        WHERE w.valid = 1
        <if test="request.enabled != null and request.enabled != ''">
            AND w.enabled = #{request.enabled}
        </if>
        <if test="request.companyId != null and request.companyId != ''">
            AND w.company_id = #{request.companyId}
        </if>
        <if test="request.companyName != null and request.companyName != ''">
            AND c.company_name LIKE CONCAT('%', #{request.companyName}, '%')
        </if>
        <if test="request.warehouseName != null and request.warehouseName != ''">
            AND w.warehouse_name LIKE CONCAT('%', #{request.warehouseName}, '%')
        </if>
        <if test="request.address != null and request.address != ''">
            AND (w.province_name LIKE CONCAT('%', #{request.address}, '%')
                OR w.city_name LIKE CONCAT('%', #{request.address}, '%')
                OR w.area_name LIKE CONCAT('%', #{request.address}, '%')
                OR w.detailed_address LIKE CONCAT('%', #{request.address}, '%'))
        </if>
        ORDER BY w.created_time DESC, w.id ASC, ws.first_category ASC, ws.second_category ASC
    </select>

    <!-- 检查同一客户下仓库名称是否重复 -->
    <select id="countByCompanyIdAndWarehouseName" resultType="int">
        SELECT COUNT(1)
        FROM t_warehouse
        WHERE company_id = #{companyId}
          AND warehouse_name = #{warehouseName}
          AND valid = 1
        <if test="excludeId != null">
          AND id != #{excludeId}
        </if>
    </select>

</mapper>
