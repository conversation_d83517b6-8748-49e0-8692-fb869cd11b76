package com.yi.controller.warehouse.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 入库单查询请求
 */
@Data
@ApiModel(value = "InboundOrderQueryRequest", description = "入库单查询请求")
public class InboundOrderQueryRequest {

    @ApiModelProperty(value = "当前页码", example = "1")
    private String current = "1";

    @ApiModelProperty(value = "每页大小", example = "10")
    private String size = "10";

    @ApiModelProperty(value = "入库单号（模糊查询）")
    private String orderNo;

    @ApiModelProperty(value = "入库状态：1-待入库，2-部分入库，3-已入库")
    private Integer status;

    @ApiModelProperty(value = "入库类型：1-采购入库，2-回收入库，3-调拨入库，4-销售入库")
    private Integer inboundType;

    @ApiModelProperty(value = "入库仓库ID")
    private Long inboundWarehouseId;

    @ApiModelProperty(value = "入库仓库名称（模糊查询）")
    private String inboundWarehouseName;

    @ApiModelProperty(value = "发货仓库ID")
    private Long senderWarehouseId;

    @ApiModelProperty(value = "发货仓库名称（模糊查询）")
    private String senderWarehouseName;

    @ApiModelProperty(value = "一级类目：1-共享托盘")
    private Integer firstCategory;

    @ApiModelProperty(value = "二级类目（模糊查询）")
    private String secondCategory;

    @ApiModelProperty(value = "车牌号（模糊查询）")
    private String vehicleNumber;

    @ApiModelProperty(value = "司机姓名（模糊查询）")
    private String driverName;

    @ApiModelProperty(value = "关联出库单ID")
    private Long outboundOrderId;

    @ApiModelProperty(value = "开始时间（格式：yyyy-MM-dd HH:mm:ss）")
    private String startTime;

    @ApiModelProperty(value = "结束时间（格式：yyyy-MM-dd HH:mm:ss）")
    private String endTime;
}
