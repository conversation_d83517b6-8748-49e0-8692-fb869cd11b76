package com.yi.controller.contract.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 合同导出请求
 */
@Data
@ApiModel(value = "ContractExportRequest", description = "合同导出请求")
public class ContractExportRequest {

    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    @ApiModelProperty(value = "客户主体")
    private String customerCompanyName;

    @ApiModelProperty(value = "合同状态：1-待生效，2-生效中，3-已到期，4-已作废")
    private String contractStatus;
}
