﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-28px;
  width:1536px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u1669_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1300px;
  height:124px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1669 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:56px;
  width:1300px;
  height:124px;
  display:flex;
}
#u1669 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1669_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1670_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1301px;
  height:2px;
}
#u1670 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:56px;
  width:1300px;
  height:1px;
  display:flex;
}
#u1670 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1670_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1671_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:136px;
  height:32px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1671 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:24px;
  width:136px;
  height:32px;
  display:flex;
}
#u1671 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1671_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1672_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1672 {
  border-width:0px;
  position:absolute;
  left:42px;
  top:32px;
  width:70px;
  height:16px;
  display:flex;
}
#u1672 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1672_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1673_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:16px;
  background:inherit;
  background-color:rgba(217, 0, 27, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#D9001B;
}
#u1673 {
  border-width:0px;
  position:absolute;
  left:142px;
  top:33px;
  width:15px;
  height:16px;
  display:flex;
  color:#D9001B;
}
#u1673 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1673_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1674_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1674 {
  border-width:0px;
  position:absolute;
  left:881px;
  top:82px;
  width:56px;
  height:16px;
  display:flex;
}
#u1674 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1674_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1675_input {
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1675_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1675_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1675 {
  border-width:0px;
  position:absolute;
  left:942px;
  top:78px;
  width:150px;
  height:24px;
  display:flex;
}
#u1675 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1675_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1675.disabled {
}
#u1676_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:24px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1676 {
  border-width:0px;
  position:absolute;
  left:1179px;
  top:122px;
  width:60px;
  height:24px;
  display:flex;
}
#u1676 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1676_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1677_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1677 {
  border-width:0px;
  position:absolute;
  left:1259px;
  top:122px;
  width:60px;
  height:24px;
  display:flex;
}
#u1677 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1677_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1678_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:21px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1678 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:210px;
  width:56px;
  height:21px;
  display:flex;
}
#u1678 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1678_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1679 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:240px;
  width:1536px;
  height:336px;
}
#u1680_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:34px;
}
#u1680 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:34px;
  display:flex;
  font-size:12px;
}
#u1680 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1680_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1681_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:34px;
}
#u1681 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:0px;
  width:143px;
  height:34px;
  display:flex;
}
#u1681 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1681_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1682_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:34px;
}
#u1682 {
  border-width:0px;
  position:absolute;
  left:198px;
  top:0px;
  width:150px;
  height:34px;
  display:flex;
  font-size:12px;
}
#u1682 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1682_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1683_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:34px;
}
#u1683 {
  border-width:0px;
  position:absolute;
  left:348px;
  top:0px;
  width:145px;
  height:34px;
  display:flex;
  font-size:12px;
}
#u1683 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1683_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1684_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:34px;
}
#u1684 {
  border-width:0px;
  position:absolute;
  left:493px;
  top:0px;
  width:94px;
  height:34px;
  display:flex;
  font-size:12px;
}
#u1684 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1684_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1685_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:34px;
}
#u1685 {
  border-width:0px;
  position:absolute;
  left:587px;
  top:0px;
  width:116px;
  height:34px;
  display:flex;
}
#u1685 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1685_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1686_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:34px;
}
#u1686 {
  border-width:0px;
  position:absolute;
  left:703px;
  top:0px;
  width:118px;
  height:34px;
  display:flex;
}
#u1686 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1686_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1687_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:111px;
  height:34px;
}
#u1687 {
  border-width:0px;
  position:absolute;
  left:821px;
  top:0px;
  width:111px;
  height:34px;
  display:flex;
  font-size:12px;
}
#u1687 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1687_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1688_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:34px;
}
#u1688 {
  border-width:0px;
  position:absolute;
  left:932px;
  top:0px;
  width:130px;
  height:34px;
  display:flex;
  font-size:12px;
}
#u1688 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1688_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1689_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:34px;
}
#u1689 {
  border-width:0px;
  position:absolute;
  left:1062px;
  top:0px;
  width:104px;
  height:34px;
  display:flex;
  font-size:12px;
}
#u1689 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1689_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1690_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:34px;
}
#u1690 {
  border-width:0px;
  position:absolute;
  left:1166px;
  top:0px;
  width:123px;
  height:34px;
  display:flex;
}
#u1690 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1690_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1691_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:34px;
}
#u1691 {
  border-width:0px;
  position:absolute;
  left:1289px;
  top:0px;
  width:129px;
  height:34px;
  display:flex;
}
#u1691 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1691_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1692_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:34px;
}
#u1692 {
  border-width:0px;
  position:absolute;
  left:1418px;
  top:0px;
  width:118px;
  height:34px;
  display:flex;
}
#u1692 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1692_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1693_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:35px;
}
#u1693 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:34px;
  width:55px;
  height:35px;
  display:flex;
}
#u1693 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1693_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1694_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:35px;
}
#u1694 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:34px;
  width:143px;
  height:35px;
  display:flex;
}
#u1694 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1694_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1695_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:35px;
}
#u1695 {
  border-width:0px;
  position:absolute;
  left:198px;
  top:34px;
  width:150px;
  height:35px;
  display:flex;
}
#u1695 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1695_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1696_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:35px;
}
#u1696 {
  border-width:0px;
  position:absolute;
  left:348px;
  top:34px;
  width:145px;
  height:35px;
  display:flex;
}
#u1696 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1696_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1697_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:35px;
}
#u1697 {
  border-width:0px;
  position:absolute;
  left:493px;
  top:34px;
  width:94px;
  height:35px;
  display:flex;
}
#u1697 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1697_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1698_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:35px;
}
#u1698 {
  border-width:0px;
  position:absolute;
  left:587px;
  top:34px;
  width:116px;
  height:35px;
  display:flex;
}
#u1698 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1698_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1699_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:35px;
}
#u1699 {
  border-width:0px;
  position:absolute;
  left:703px;
  top:34px;
  width:118px;
  height:35px;
  display:flex;
}
#u1699 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1699_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1700_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:111px;
  height:35px;
}
#u1700 {
  border-width:0px;
  position:absolute;
  left:821px;
  top:34px;
  width:111px;
  height:35px;
  display:flex;
}
#u1700 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1700_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1701_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:35px;
}
#u1701 {
  border-width:0px;
  position:absolute;
  left:932px;
  top:34px;
  width:130px;
  height:35px;
  display:flex;
}
#u1701 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1701_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1702_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:35px;
}
#u1702 {
  border-width:0px;
  position:absolute;
  left:1062px;
  top:34px;
  width:104px;
  height:35px;
  display:flex;
}
#u1702 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1702_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1703_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:35px;
}
#u1703 {
  border-width:0px;
  position:absolute;
  left:1166px;
  top:34px;
  width:123px;
  height:35px;
  display:flex;
}
#u1703 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1703_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1704_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:35px;
}
#u1704 {
  border-width:0px;
  position:absolute;
  left:1289px;
  top:34px;
  width:129px;
  height:35px;
  display:flex;
}
#u1704 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1704_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1705_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:35px;
}
#u1705 {
  border-width:0px;
  position:absolute;
  left:1418px;
  top:34px;
  width:118px;
  height:35px;
  display:flex;
}
#u1705 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1705_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1706_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:30px;
}
#u1706 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:69px;
  width:55px;
  height:30px;
  display:flex;
}
#u1706 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1706_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1707_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:30px;
}
#u1707 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:69px;
  width:143px;
  height:30px;
  display:flex;
}
#u1707 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1707_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1708_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:30px;
}
#u1708 {
  border-width:0px;
  position:absolute;
  left:198px;
  top:69px;
  width:150px;
  height:30px;
  display:flex;
}
#u1708 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1708_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1709_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:30px;
}
#u1709 {
  border-width:0px;
  position:absolute;
  left:348px;
  top:69px;
  width:145px;
  height:30px;
  display:flex;
}
#u1709 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1709_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1710_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:30px;
}
#u1710 {
  border-width:0px;
  position:absolute;
  left:493px;
  top:69px;
  width:94px;
  height:30px;
  display:flex;
}
#u1710 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1710_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1711_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u1711 {
  border-width:0px;
  position:absolute;
  left:587px;
  top:69px;
  width:116px;
  height:30px;
  display:flex;
}
#u1711 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1711_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1712_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u1712 {
  border-width:0px;
  position:absolute;
  left:703px;
  top:69px;
  width:118px;
  height:30px;
  display:flex;
}
#u1712 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1712_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1713_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:111px;
  height:30px;
}
#u1713 {
  border-width:0px;
  position:absolute;
  left:821px;
  top:69px;
  width:111px;
  height:30px;
  display:flex;
}
#u1713 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1713_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1714_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:30px;
}
#u1714 {
  border-width:0px;
  position:absolute;
  left:932px;
  top:69px;
  width:130px;
  height:30px;
  display:flex;
}
#u1714 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1714_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1715_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:30px;
}
#u1715 {
  border-width:0px;
  position:absolute;
  left:1062px;
  top:69px;
  width:104px;
  height:30px;
  display:flex;
}
#u1715 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1715_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1716_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:30px;
}
#u1716 {
  border-width:0px;
  position:absolute;
  left:1166px;
  top:69px;
  width:123px;
  height:30px;
  display:flex;
}
#u1716 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1716_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1717_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:30px;
}
#u1717 {
  border-width:0px;
  position:absolute;
  left:1289px;
  top:69px;
  width:129px;
  height:30px;
  display:flex;
}
#u1717 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1717_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1718_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u1718 {
  border-width:0px;
  position:absolute;
  left:1418px;
  top:69px;
  width:118px;
  height:30px;
  display:flex;
}
#u1718 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1718_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1719_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:30px;
}
#u1719 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:99px;
  width:55px;
  height:30px;
  display:flex;
}
#u1719 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1719_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1720_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:30px;
}
#u1720 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:99px;
  width:143px;
  height:30px;
  display:flex;
}
#u1720 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1720_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1721_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:30px;
}
#u1721 {
  border-width:0px;
  position:absolute;
  left:198px;
  top:99px;
  width:150px;
  height:30px;
  display:flex;
}
#u1721 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1721_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1722_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:30px;
}
#u1722 {
  border-width:0px;
  position:absolute;
  left:348px;
  top:99px;
  width:145px;
  height:30px;
  display:flex;
}
#u1722 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1722_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1723_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:30px;
}
#u1723 {
  border-width:0px;
  position:absolute;
  left:493px;
  top:99px;
  width:94px;
  height:30px;
  display:flex;
}
#u1723 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1723_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1724_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u1724 {
  border-width:0px;
  position:absolute;
  left:587px;
  top:99px;
  width:116px;
  height:30px;
  display:flex;
}
#u1724 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1724_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1725_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u1725 {
  border-width:0px;
  position:absolute;
  left:703px;
  top:99px;
  width:118px;
  height:30px;
  display:flex;
}
#u1725 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1725_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1726_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:111px;
  height:30px;
}
#u1726 {
  border-width:0px;
  position:absolute;
  left:821px;
  top:99px;
  width:111px;
  height:30px;
  display:flex;
}
#u1726 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1726_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1727_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:30px;
}
#u1727 {
  border-width:0px;
  position:absolute;
  left:932px;
  top:99px;
  width:130px;
  height:30px;
  display:flex;
}
#u1727 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1727_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1728_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:30px;
}
#u1728 {
  border-width:0px;
  position:absolute;
  left:1062px;
  top:99px;
  width:104px;
  height:30px;
  display:flex;
}
#u1728 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1728_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1729_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:30px;
}
#u1729 {
  border-width:0px;
  position:absolute;
  left:1166px;
  top:99px;
  width:123px;
  height:30px;
  display:flex;
}
#u1729 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1729_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1730_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:30px;
}
#u1730 {
  border-width:0px;
  position:absolute;
  left:1289px;
  top:99px;
  width:129px;
  height:30px;
  display:flex;
}
#u1730 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1730_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1731_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u1731 {
  border-width:0px;
  position:absolute;
  left:1418px;
  top:99px;
  width:118px;
  height:30px;
  display:flex;
}
#u1731 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1731_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1732_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:30px;
}
#u1732 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:129px;
  width:55px;
  height:30px;
  display:flex;
}
#u1732 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1732_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1733_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:30px;
}
#u1733 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:129px;
  width:143px;
  height:30px;
  display:flex;
}
#u1733 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1733_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1734_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:30px;
}
#u1734 {
  border-width:0px;
  position:absolute;
  left:198px;
  top:129px;
  width:150px;
  height:30px;
  display:flex;
}
#u1734 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1734_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1735_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:30px;
}
#u1735 {
  border-width:0px;
  position:absolute;
  left:348px;
  top:129px;
  width:145px;
  height:30px;
  display:flex;
}
#u1735 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1735_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1736_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:30px;
}
#u1736 {
  border-width:0px;
  position:absolute;
  left:493px;
  top:129px;
  width:94px;
  height:30px;
  display:flex;
}
#u1736 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1736_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1737_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u1737 {
  border-width:0px;
  position:absolute;
  left:587px;
  top:129px;
  width:116px;
  height:30px;
  display:flex;
}
#u1737 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1737_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1738_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u1738 {
  border-width:0px;
  position:absolute;
  left:703px;
  top:129px;
  width:118px;
  height:30px;
  display:flex;
}
#u1738 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1738_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1739_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:111px;
  height:30px;
}
#u1739 {
  border-width:0px;
  position:absolute;
  left:821px;
  top:129px;
  width:111px;
  height:30px;
  display:flex;
}
#u1739 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1739_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1740_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:30px;
}
#u1740 {
  border-width:0px;
  position:absolute;
  left:932px;
  top:129px;
  width:130px;
  height:30px;
  display:flex;
}
#u1740 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1740_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1741_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:30px;
}
#u1741 {
  border-width:0px;
  position:absolute;
  left:1062px;
  top:129px;
  width:104px;
  height:30px;
  display:flex;
}
#u1741 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1741_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1742_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:30px;
}
#u1742 {
  border-width:0px;
  position:absolute;
  left:1166px;
  top:129px;
  width:123px;
  height:30px;
  display:flex;
}
#u1742 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1742_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1743_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:30px;
}
#u1743 {
  border-width:0px;
  position:absolute;
  left:1289px;
  top:129px;
  width:129px;
  height:30px;
  display:flex;
}
#u1743 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1743_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1744_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u1744 {
  border-width:0px;
  position:absolute;
  left:1418px;
  top:129px;
  width:118px;
  height:30px;
  display:flex;
}
#u1744 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1744_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1745_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:30px;
}
#u1745 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:159px;
  width:55px;
  height:30px;
  display:flex;
}
#u1745 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1745_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1746_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:30px;
}
#u1746 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:159px;
  width:143px;
  height:30px;
  display:flex;
}
#u1746 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1746_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1747_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:30px;
}
#u1747 {
  border-width:0px;
  position:absolute;
  left:198px;
  top:159px;
  width:150px;
  height:30px;
  display:flex;
}
#u1747 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1747_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1748_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:30px;
}
#u1748 {
  border-width:0px;
  position:absolute;
  left:348px;
  top:159px;
  width:145px;
  height:30px;
  display:flex;
}
#u1748 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1748_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1749_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:30px;
}
#u1749 {
  border-width:0px;
  position:absolute;
  left:493px;
  top:159px;
  width:94px;
  height:30px;
  display:flex;
}
#u1749 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1749_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1750_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u1750 {
  border-width:0px;
  position:absolute;
  left:587px;
  top:159px;
  width:116px;
  height:30px;
  display:flex;
}
#u1750 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1750_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1751_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u1751 {
  border-width:0px;
  position:absolute;
  left:703px;
  top:159px;
  width:118px;
  height:30px;
  display:flex;
}
#u1751 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1751_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1752_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:111px;
  height:30px;
}
#u1752 {
  border-width:0px;
  position:absolute;
  left:821px;
  top:159px;
  width:111px;
  height:30px;
  display:flex;
}
#u1752 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1752_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1753_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:30px;
}
#u1753 {
  border-width:0px;
  position:absolute;
  left:932px;
  top:159px;
  width:130px;
  height:30px;
  display:flex;
}
#u1753 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1753_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1754_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:30px;
}
#u1754 {
  border-width:0px;
  position:absolute;
  left:1062px;
  top:159px;
  width:104px;
  height:30px;
  display:flex;
}
#u1754 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1754_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1755_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:30px;
}
#u1755 {
  border-width:0px;
  position:absolute;
  left:1166px;
  top:159px;
  width:123px;
  height:30px;
  display:flex;
}
#u1755 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1755_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1756_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:30px;
}
#u1756 {
  border-width:0px;
  position:absolute;
  left:1289px;
  top:159px;
  width:129px;
  height:30px;
  display:flex;
}
#u1756 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1756_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1757_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u1757 {
  border-width:0px;
  position:absolute;
  left:1418px;
  top:159px;
  width:118px;
  height:30px;
  display:flex;
}
#u1757 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1757_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1758_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:30px;
}
#u1758 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:189px;
  width:55px;
  height:30px;
  display:flex;
}
#u1758 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1758_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1759_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:30px;
}
#u1759 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:189px;
  width:143px;
  height:30px;
  display:flex;
}
#u1759 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1759_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1760_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:30px;
}
#u1760 {
  border-width:0px;
  position:absolute;
  left:198px;
  top:189px;
  width:150px;
  height:30px;
  display:flex;
}
#u1760 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1760_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1761_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:30px;
}
#u1761 {
  border-width:0px;
  position:absolute;
  left:348px;
  top:189px;
  width:145px;
  height:30px;
  display:flex;
}
#u1761 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1761_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1762_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:30px;
}
#u1762 {
  border-width:0px;
  position:absolute;
  left:493px;
  top:189px;
  width:94px;
  height:30px;
  display:flex;
}
#u1762 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1762_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1763_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u1763 {
  border-width:0px;
  position:absolute;
  left:587px;
  top:189px;
  width:116px;
  height:30px;
  display:flex;
}
#u1763 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1763_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1764_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u1764 {
  border-width:0px;
  position:absolute;
  left:703px;
  top:189px;
  width:118px;
  height:30px;
  display:flex;
}
#u1764 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1764_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1765_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:111px;
  height:30px;
}
#u1765 {
  border-width:0px;
  position:absolute;
  left:821px;
  top:189px;
  width:111px;
  height:30px;
  display:flex;
}
#u1765 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1765_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1766_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:30px;
}
#u1766 {
  border-width:0px;
  position:absolute;
  left:932px;
  top:189px;
  width:130px;
  height:30px;
  display:flex;
}
#u1766 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1766_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1767_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:30px;
}
#u1767 {
  border-width:0px;
  position:absolute;
  left:1062px;
  top:189px;
  width:104px;
  height:30px;
  display:flex;
}
#u1767 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1767_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1768_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:30px;
}
#u1768 {
  border-width:0px;
  position:absolute;
  left:1166px;
  top:189px;
  width:123px;
  height:30px;
  display:flex;
}
#u1768 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1768_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1769_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:30px;
}
#u1769 {
  border-width:0px;
  position:absolute;
  left:1289px;
  top:189px;
  width:129px;
  height:30px;
  display:flex;
}
#u1769 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1769_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1770_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u1770 {
  border-width:0px;
  position:absolute;
  left:1418px;
  top:189px;
  width:118px;
  height:30px;
  display:flex;
}
#u1770 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1770_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1771_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:30px;
}
#u1771 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:219px;
  width:55px;
  height:30px;
  display:flex;
}
#u1771 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1771_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1772_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:30px;
}
#u1772 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:219px;
  width:143px;
  height:30px;
  display:flex;
}
#u1772 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1772_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1773_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:30px;
}
#u1773 {
  border-width:0px;
  position:absolute;
  left:198px;
  top:219px;
  width:150px;
  height:30px;
  display:flex;
}
#u1773 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1773_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1774_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:30px;
}
#u1774 {
  border-width:0px;
  position:absolute;
  left:348px;
  top:219px;
  width:145px;
  height:30px;
  display:flex;
}
#u1774 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1774_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1775_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:30px;
}
#u1775 {
  border-width:0px;
  position:absolute;
  left:493px;
  top:219px;
  width:94px;
  height:30px;
  display:flex;
}
#u1775 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1775_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1776_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u1776 {
  border-width:0px;
  position:absolute;
  left:587px;
  top:219px;
  width:116px;
  height:30px;
  display:flex;
}
#u1776 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1776_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1777_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u1777 {
  border-width:0px;
  position:absolute;
  left:703px;
  top:219px;
  width:118px;
  height:30px;
  display:flex;
}
#u1777 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1777_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1778_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:111px;
  height:30px;
}
#u1778 {
  border-width:0px;
  position:absolute;
  left:821px;
  top:219px;
  width:111px;
  height:30px;
  display:flex;
}
#u1778 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1778_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1779_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:30px;
}
#u1779 {
  border-width:0px;
  position:absolute;
  left:932px;
  top:219px;
  width:130px;
  height:30px;
  display:flex;
}
#u1779 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1779_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1780_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:30px;
}
#u1780 {
  border-width:0px;
  position:absolute;
  left:1062px;
  top:219px;
  width:104px;
  height:30px;
  display:flex;
}
#u1780 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1780_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1781_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:30px;
}
#u1781 {
  border-width:0px;
  position:absolute;
  left:1166px;
  top:219px;
  width:123px;
  height:30px;
  display:flex;
}
#u1781 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1781_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1782_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:30px;
}
#u1782 {
  border-width:0px;
  position:absolute;
  left:1289px;
  top:219px;
  width:129px;
  height:30px;
  display:flex;
}
#u1782 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1782_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1783_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u1783 {
  border-width:0px;
  position:absolute;
  left:1418px;
  top:219px;
  width:118px;
  height:30px;
  display:flex;
}
#u1783 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1783_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1784_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:30px;
}
#u1784 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:249px;
  width:55px;
  height:30px;
  display:flex;
}
#u1784 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1784_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1785_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:30px;
}
#u1785 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:249px;
  width:143px;
  height:30px;
  display:flex;
}
#u1785 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1785_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1786_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:30px;
}
#u1786 {
  border-width:0px;
  position:absolute;
  left:198px;
  top:249px;
  width:150px;
  height:30px;
  display:flex;
}
#u1786 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1786_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1787_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:30px;
}
#u1787 {
  border-width:0px;
  position:absolute;
  left:348px;
  top:249px;
  width:145px;
  height:30px;
  display:flex;
}
#u1787 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1787_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1788_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:30px;
}
#u1788 {
  border-width:0px;
  position:absolute;
  left:493px;
  top:249px;
  width:94px;
  height:30px;
  display:flex;
}
#u1788 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1788_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1789_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u1789 {
  border-width:0px;
  position:absolute;
  left:587px;
  top:249px;
  width:116px;
  height:30px;
  display:flex;
}
#u1789 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1789_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1790_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u1790 {
  border-width:0px;
  position:absolute;
  left:703px;
  top:249px;
  width:118px;
  height:30px;
  display:flex;
}
#u1790 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1790_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1791_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:111px;
  height:30px;
}
#u1791 {
  border-width:0px;
  position:absolute;
  left:821px;
  top:249px;
  width:111px;
  height:30px;
  display:flex;
}
#u1791 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1791_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1792_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:30px;
}
#u1792 {
  border-width:0px;
  position:absolute;
  left:932px;
  top:249px;
  width:130px;
  height:30px;
  display:flex;
}
#u1792 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1792_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1793_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:30px;
}
#u1793 {
  border-width:0px;
  position:absolute;
  left:1062px;
  top:249px;
  width:104px;
  height:30px;
  display:flex;
}
#u1793 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1793_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1794_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:30px;
}
#u1794 {
  border-width:0px;
  position:absolute;
  left:1166px;
  top:249px;
  width:123px;
  height:30px;
  display:flex;
}
#u1794 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1794_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1795_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:30px;
}
#u1795 {
  border-width:0px;
  position:absolute;
  left:1289px;
  top:249px;
  width:129px;
  height:30px;
  display:flex;
}
#u1795 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1795_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1796_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u1796 {
  border-width:0px;
  position:absolute;
  left:1418px;
  top:249px;
  width:118px;
  height:30px;
  display:flex;
}
#u1796 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1796_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1797_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:27px;
}
#u1797 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:279px;
  width:55px;
  height:27px;
  display:flex;
}
#u1797 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1797_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1798_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:27px;
}
#u1798 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:279px;
  width:143px;
  height:27px;
  display:flex;
}
#u1798 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1798_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1799_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:27px;
}
#u1799 {
  border-width:0px;
  position:absolute;
  left:198px;
  top:279px;
  width:150px;
  height:27px;
  display:flex;
}
#u1799 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1799_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1800_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:27px;
}
#u1800 {
  border-width:0px;
  position:absolute;
  left:348px;
  top:279px;
  width:145px;
  height:27px;
  display:flex;
}
#u1800 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1800_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1801_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:27px;
}
#u1801 {
  border-width:0px;
  position:absolute;
  left:493px;
  top:279px;
  width:94px;
  height:27px;
  display:flex;
}
#u1801 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1801_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1802_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:27px;
}
#u1802 {
  border-width:0px;
  position:absolute;
  left:587px;
  top:279px;
  width:116px;
  height:27px;
  display:flex;
}
#u1802 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1802_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1803_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:27px;
}
#u1803 {
  border-width:0px;
  position:absolute;
  left:703px;
  top:279px;
  width:118px;
  height:27px;
  display:flex;
}
#u1803 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1803_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1804_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:111px;
  height:27px;
}
#u1804 {
  border-width:0px;
  position:absolute;
  left:821px;
  top:279px;
  width:111px;
  height:27px;
  display:flex;
}
#u1804 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1804_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1805_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:27px;
}
#u1805 {
  border-width:0px;
  position:absolute;
  left:932px;
  top:279px;
  width:130px;
  height:27px;
  display:flex;
}
#u1805 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1805_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1806_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:27px;
}
#u1806 {
  border-width:0px;
  position:absolute;
  left:1062px;
  top:279px;
  width:104px;
  height:27px;
  display:flex;
}
#u1806 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1806_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1807_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:27px;
}
#u1807 {
  border-width:0px;
  position:absolute;
  left:1166px;
  top:279px;
  width:123px;
  height:27px;
  display:flex;
}
#u1807 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1807_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1808_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:27px;
}
#u1808 {
  border-width:0px;
  position:absolute;
  left:1289px;
  top:279px;
  width:129px;
  height:27px;
  display:flex;
}
#u1808 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1808_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1809_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:27px;
}
#u1809 {
  border-width:0px;
  position:absolute;
  left:1418px;
  top:279px;
  width:118px;
  height:27px;
  display:flex;
}
#u1809 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1809_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1810_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:30px;
}
#u1810 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:306px;
  width:55px;
  height:30px;
  display:flex;
}
#u1810 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1810_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1811_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:30px;
}
#u1811 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:306px;
  width:143px;
  height:30px;
  display:flex;
}
#u1811 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1811_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1812_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:30px;
}
#u1812 {
  border-width:0px;
  position:absolute;
  left:198px;
  top:306px;
  width:150px;
  height:30px;
  display:flex;
}
#u1812 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1812_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1813_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:30px;
}
#u1813 {
  border-width:0px;
  position:absolute;
  left:348px;
  top:306px;
  width:145px;
  height:30px;
  display:flex;
}
#u1813 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1813_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1814_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:30px;
}
#u1814 {
  border-width:0px;
  position:absolute;
  left:493px;
  top:306px;
  width:94px;
  height:30px;
  display:flex;
}
#u1814 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1814_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1815_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:30px;
}
#u1815 {
  border-width:0px;
  position:absolute;
  left:587px;
  top:306px;
  width:116px;
  height:30px;
  display:flex;
}
#u1815 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1815_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1816_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u1816 {
  border-width:0px;
  position:absolute;
  left:703px;
  top:306px;
  width:118px;
  height:30px;
  display:flex;
}
#u1816 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1816_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1817_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:111px;
  height:30px;
}
#u1817 {
  border-width:0px;
  position:absolute;
  left:821px;
  top:306px;
  width:111px;
  height:30px;
  display:flex;
}
#u1817 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1817_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1818_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:30px;
}
#u1818 {
  border-width:0px;
  position:absolute;
  left:932px;
  top:306px;
  width:130px;
  height:30px;
  display:flex;
}
#u1818 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1818_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1819_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:30px;
}
#u1819 {
  border-width:0px;
  position:absolute;
  left:1062px;
  top:306px;
  width:104px;
  height:30px;
  display:flex;
}
#u1819 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1819_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1820_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:30px;
}
#u1820 {
  border-width:0px;
  position:absolute;
  left:1166px;
  top:306px;
  width:123px;
  height:30px;
  display:flex;
}
#u1820 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1820_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1821_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:30px;
}
#u1821 {
  border-width:0px;
  position:absolute;
  left:1289px;
  top:306px;
  width:129px;
  height:30px;
  display:flex;
}
#u1821 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1821_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1822_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:30px;
}
#u1822 {
  border-width:0px;
  position:absolute;
  left:1418px;
  top:306px;
  width:118px;
  height:30px;
  display:flex;
}
#u1822 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1822_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1823_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1823 {
  border-width:0px;
  position:absolute;
  left:42px;
  top:82px;
  width:84px;
  height:16px;
  display:flex;
}
#u1823 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1823_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1824_input {
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1824_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1824_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1824 {
  border-width:0px;
  position:absolute;
  left:131px;
  top:78px;
  width:150px;
  height:24px;
  display:flex;
}
#u1824 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1824_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1824.disabled {
}
#u1825_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:52px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1825 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:594px;
  width:52px;
  height:16px;
  display:flex;
}
#u1825 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1825_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1826_input {
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:22px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1826_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:22px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1826_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1826 {
  border-width:0px;
  position:absolute;
  left:97px;
  top:591px;
  width:81px;
  height:22px;
  display:flex;
}
#u1826 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1826_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:22px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1826.disabled {
}
.u1826_input_option {
}
#u1827_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:8px;
  height:12px;
}
#u1827 {
  border-width:0px;
  position:absolute;
  left:209px;
  top:596px;
  width:8px;
  height:12px;
  display:flex;
}
#u1827 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1827_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1828_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:12px;
}
#u1828 {
  border-width:0px;
  position:absolute;
  left:416px;
  top:596px;
  width:7px;
  height:12px;
  display:flex;
}
#u1828 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1828_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1829_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:8px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1829 {
  border-width:0px;
  position:absolute;
  left:230px;
  top:594px;
  width:8px;
  height:16px;
  display:flex;
}
#u1829 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1829_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1830_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:8px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1830 {
  border-width:0px;
  position:absolute;
  left:273px;
  top:594px;
  width:8px;
  height:16px;
  display:flex;
}
#u1830 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1830_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1831_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:8px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1831 {
  border-width:0px;
  position:absolute;
  left:295px;
  top:594px;
  width:8px;
  height:16px;
  display:flex;
}
#u1831 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1831_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1832_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:8px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1832 {
  border-width:0px;
  position:absolute;
  left:316px;
  top:594px;
  width:8px;
  height:16px;
  display:flex;
}
#u1832 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1832_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1833_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:8px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1833 {
  border-width:0px;
  position:absolute;
  left:338px;
  top:594px;
  width:8px;
  height:16px;
  display:flex;
}
#u1833 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1833_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1834_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1834 {
  border-width:0px;
  position:absolute;
  left:359px;
  top:594px;
  width:14px;
  height:16px;
  display:flex;
}
#u1834 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1834_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1835_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1835 {
  border-width:0px;
  position:absolute;
  left:387px;
  top:594px;
  width:16px;
  height:16px;
  display:flex;
}
#u1835 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1835_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1836_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:8px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1836 {
  border-width:0px;
  position:absolute;
  left:252px;
  top:594px;
  width:8px;
  height:16px;
  display:flex;
}
#u1836 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1836_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1837_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1837 {
  border-width:0px;
  position:absolute;
  left:448px;
  top:594px;
  width:28px;
  height:16px;
  display:flex;
}
#u1837 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1837_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1838_input {
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:19px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1838_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:19px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1838_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u1838 {
  border-width:0px;
  position:absolute;
  left:484px;
  top:591px;
  width:19px;
  height:19px;
  display:flex;
  text-align:center;
}
#u1838 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1838_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:19px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u1838.disabled {
}
#u1839_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1839 {
  border-width:0px;
  position:absolute;
  left:511px;
  top:594px;
  width:14px;
  height:16px;
  display:flex;
}
#u1839 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1839_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1840_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1840 {
  border-width:0px;
  position:absolute;
  left:1108px;
  top:82px;
  width:56px;
  height:16px;
  display:flex;
}
#u1840 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1840_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1841_input {
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1841_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1841_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1841 {
  border-width:0px;
  position:absolute;
  left:1169px;
  top:78px;
  width:150px;
  height:24px;
  display:flex;
}
#u1841 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1841_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1841.disabled {
}
#u1842_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1842 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:82px;
  width:84px;
  height:16px;
  display:flex;
}
#u1842 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1842_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1843_input {
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1843_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1843_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1843 {
  border-width:0px;
  position:absolute;
  left:420px;
  top:78px;
  width:150px;
  height:24px;
  display:flex;
}
#u1843 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1843_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1843.disabled {
}
#u1844_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1844 {
  border-width:0px;
  position:absolute;
  left:1272px;
  top:36px;
  width:56px;
  height:20px;
  display:flex;
}
#u1844 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1844_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1845_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1845 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:126px;
  width:56px;
  height:16px;
  display:flex;
}
#u1845 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1845_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1846 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1847_input {
  position:absolute;
  left:0px;
  top:0px;
  width:178px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1847_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:178px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1847_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:178px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1847 {
  border-width:0px;
  position:absolute;
  left:392px;
  top:122px;
  width:178px;
  height:24px;
  display:flex;
}
#u1847 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1847_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:178px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1847.disabled {
}
#u1848_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  color:#AAAAAA;
}
#u1848 {
  border-width:0px;
  position:absolute;
  left:410px;
  top:129px;
  width:45px;
  height:11px;
  display:flex;
  font-size:10px;
  color:#AAAAAA;
}
#u1848 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1848_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1849_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1849 {
  border-width:0px;
  position:absolute;
  left:473px;
  top:127px;
  width:16px;
  height:16px;
  display:flex;
}
#u1849 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1849_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1850_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  color:#AAAAAA;
}
#u1850 {
  border-width:0px;
  position:absolute;
  left:508px;
  top:129px;
  width:45px;
  height:11px;
  display:flex;
  font-size:10px;
  color:#AAAAAA;
}
#u1850 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1850_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1851_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1851 {
  border-width:0px;
  position:absolute;
  left:84px;
  top:126px;
  width:42px;
  height:16px;
  display:flex;
}
#u1851 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1851_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1852_input {
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1852_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1852_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1852 {
  border-width:0px;
  position:absolute;
  left:131px;
  top:122px;
  width:150px;
  height:24px;
  display:flex;
}
#u1852 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1852_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1852.disabled {
}
#u1853_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1853 {
  border-width:0px;
  position:absolute;
  left:620px;
  top:82px;
  width:28px;
  height:16px;
  display:flex;
}
#u1853 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1853_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1854_input {
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1854_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  padding:2px 2px 2px 2px;
  font-family:'Arial Normal', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1854_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1854 {
  border-width:0px;
  position:absolute;
  left:658px;
  top:78px;
  width:150px;
  height:24px;
  display:flex;
}
#u1854 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1854_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:24px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1854.disabled {
}
.u1854_input_option {
}
#u1855_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1855 {
  border-width:0px;
  position:absolute;
  left:1492px;
  top:285px;
  width:28px;
  height:16px;
  display:flex;
}
#u1855 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1855_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1856_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1300px;
  height:92px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  font-size:14px;
  line-height:20px;
}
#u1856 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:638px;
  width:1300px;
  height:92px;
  display:flex;
  font-size:14px;
  line-height:20px;
}
#u1856 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u1856_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1857_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:600px;
  height:57px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#000000;
  line-height:19px;
}
#u1857 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:646px;
  width:600px;
  height:57px;
  display:flex;
  font-size:15px;
  color:#000000;
  line-height:19px;
}
#u1857 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1857_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
