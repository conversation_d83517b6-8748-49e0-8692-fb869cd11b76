package com.yi.controller;

import com.yi.controller.shippingorder.model.ShippingOrderQueryRequest;
import com.yi.controller.shippingorder.model.ShippingOrderRequest;
import com.yi.controller.shippingorder.model.ShippingOrderPageResponse;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 发运管理控制器测试
 */
@SpringBootTest
public class ShippingControllerTest {

    @Test
    public void testShippingOrderQueryRequest() {
        ShippingOrderQueryRequest request = new ShippingOrderQueryRequest();
        request.setCurrent("1");
        request.setSize("10");
        request.setOrderNo("TEST001");
        request.setStatus("PENDING");

        assertNotNull(request);
        assertEquals("1", request.getCurrent());
        assertEquals("10", request.getSize());
        assertEquals("TEST001", request.getOrderNo());
        assertEquals("PENDING", request.getStatus());
    }

    @Test
    public void testShippingOrderRequest() {
        ShippingOrderRequest request = new ShippingOrderRequest();
        request.setOrderNo("ORDER001");
        request.setCustomerCompanyId("1");
        request.setWarehouseId("1");
        request.setFirstCategory("1");
        request.setCount("100");
        request.setDemandTime("2024-01-01");

        assertNotNull(request);
        assertEquals("ORDER001", request.getOrderNo());
        assertEquals("1", request.getCustomerCompanyId());
        assertEquals("1", request.getWarehouseId());
        assertEquals("1", request.getFirstCategory());
        assertEquals("100", request.getCount());
        assertEquals("2024-01-01", request.getDemandTime());
    }

    @Test
    public void testShippingOrderPageResponse() {
        ShippingOrderPageResponse response = new ShippingOrderPageResponse();
        response.setId("1");
        response.setOrderNo("ORDER001");
        response.setContractCode("CONTRACT001");
        response.setCustomerCompanyName("测试客户");
        response.setWarehouseName("测试仓库");
        response.setWarehouseAddress("北京市朝阳区测试地址");
        response.setReceiverName("张三 138****5678");
        response.setProduct("共享托盘-标准托盘");
        response.setCount("100");
        response.setShippedQuantity("80");
        response.setReceivedQuantity("75");
        response.setStatus("SHIPPING");
        response.setCreatedBy("admin");
        response.setCreatedTime("2024-01-01 10:00:00");

        assertNotNull(response);
        assertEquals("1", response.getId());
        assertEquals("ORDER001", response.getOrderNo());
        assertEquals("测试客户", response.getCustomerCompanyName());
        assertEquals("测试仓库", response.getWarehouseName());
        assertEquals("北京市朝阳区测试地址", response.getWarehouseAddress());
        assertEquals("张三 138****5678", response.getReceiverName());
        assertEquals("共享托盘-标准托盘", response.getProduct());
        assertEquals("100", response.getCount());
        assertEquals("80", response.getShippedQuantity());
        assertEquals("75", response.getReceivedQuantity());
        assertEquals("SHIPPING", response.getStatus());
        assertEquals("admin", response.getCreatedBy());
        assertEquals("2024-01-01 10:00:00", response.getCreatedTime());
    }
}
