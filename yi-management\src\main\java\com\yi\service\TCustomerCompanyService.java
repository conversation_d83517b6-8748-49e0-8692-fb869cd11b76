package com.yi.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yi.controller.customercompany.model.*;
import com.yi.entity.TCustomerCompany;
import com.yi.mapper.TCustomerCompanyMapper;
import com.yi.utils.ExcelUtils;
import com.yi.utils.FormatUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 客户公司表 服务类
 */
@Service
public class TCustomerCompanyService extends ServiceImpl<TCustomerCompanyMapper, TCustomerCompany> {

    /**
     * 分页查询客户公司列表
     *
     * @param request 查询条件
     * @return 分页结果
     */
    public IPage<TCustomerCompany> getCustomerCompanyPage(CustomerCompanyQueryRequest request) {
        // 转换分页参数
        Integer current = FormatUtils.safeToInteger(request.getCurrent(), 1);
        Integer size = FormatUtils.safeToInteger(request.getSize(), 10);
        Page<TCustomerCompany> page = new Page<>(current, size);

        LambdaQueryWrapper<TCustomerCompany> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TCustomerCompany::getValid, 1)
                .like(StringUtils.hasText(request.getCompanyName()), TCustomerCompany::getCompanyName, request.getCompanyName())
                .eq(StringUtils.hasText(request.getCustomerCompanyType()), TCustomerCompany::getCustomerCompanyType, request.getCustomerCompanyType())
                .eq(FormatUtils.safeToLong(request.getProvinceId()) != null, TCustomerCompany::getProvinceId, FormatUtils.safeToLong(request.getProvinceId()))
                .eq(FormatUtils.safeToLong(request.getCityId()) != null, TCustomerCompany::getCityId, FormatUtils.safeToLong(request.getCityId()))
                .eq(FormatUtils.safeToLong(request.getAreaId()) != null, TCustomerCompany::getAreaId, FormatUtils.safeToLong(request.getAreaId()))
                .eq(FormatUtils.safeToInteger(request.getEnabled()) != null, TCustomerCompany::getEnabled, FormatUtils.safeToInteger(request.getEnabled()))
                .orderByDesc(TCustomerCompany::getCreatedTime);

        return this.page(page, wrapper);
    }

    /**
     * 分页查询客户公司列表（返回Response格式）
     *
     * @param request 查询条件
     * @return 分页结果
     */
    public IPage<CustomerCompanyPageResponse> getCustomerCompanyPageResponse(CustomerCompanyQueryRequest request) {
        // 先查询原始数据
        IPage<TCustomerCompany> originalPage = getCustomerCompanyPage(request);

        // 转换为Response对象
        List<CustomerCompanyPageResponse> responseList = originalPage.getRecords().stream()
                .map(this::convertToPageResponse)
                .collect(Collectors.toList());

        // 构建返回的分页对象
        Page<CustomerCompanyPageResponse> responsePage = new Page<>(originalPage.getCurrent(), originalPage.getSize(), originalPage.getTotal());
        responsePage.setRecords(responseList);

        return responsePage;
    }

    /**
     * 转换为分页Response对象
     *
     * @param customerCompany 客户公司实体
     * @return 分页Response对象
     */
    private CustomerCompanyPageResponse convertToPageResponse(TCustomerCompany customerCompany) {
        CustomerCompanyPageResponse response = new CustomerCompanyPageResponse();

        // 基础字段转换
        response.setId(FormatUtils.safeToString(customerCompany.getId()));
        response.setCompanyName(FormatUtils.safeString(customerCompany.getCompanyName()));
        response.setContactPerson(FormatUtils.safeString(customerCompany.getContactPerson()));
        response.setContactPhone(FormatUtils.safeString(customerCompany.getContactPhone()));
        response.setCreatedBy(FormatUtils.safeString(customerCompany.getCreatedBy()));

        // 状态转换为中文
        response.setStatus(FormatUtils.getEnabledStatusDescription(customerCompany.getEnabled()));

        // 客户类型转换为中文
        response.setCustomerType(FormatUtils.getCustomerTypeDescription(customerCompany.getCustomerCompanyType()));

        // 注册地址拼接
        response.setRegistrationAddress(FormatUtils.buildFullAddress(
                customerCompany.getProvinceName(),
                customerCompany.getCityName(),
                customerCompany.getAreaName(),
                customerCompany.getDetailedAddress()
        ));

        // 创建时间格式化
        response.setCreatedTime(FormatUtils.formatDateTime(customerCompany.getCreatedTime()));

        return response;
    }

    /**
     * 根据ID获取客户公司详情
     *
     * @param id 主键ID
     * @return 客户公司详情
     */
    public TCustomerCompany getCustomerCompanyById(Long id) {
        LambdaQueryWrapper<TCustomerCompany> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TCustomerCompany::getId, id)
                .eq(TCustomerCompany::getValid, 1);
        return this.getOne(wrapper);
    }

    /**
     * 根据ID获取客户公司详情（返回Response格式）
     *
     * @param id 主键ID
     * @return 客户公司详情Response
     */
    public CustomerCompanyDetailResponse getCustomerCompanyDetailById(Long id) {
        TCustomerCompany customerCompany = getCustomerCompanyById(id);
        if (customerCompany == null) {
            return null;
        }
        return convertToDetailResponse(customerCompany);
    }

    /**
     * 新增客户公司
     *
     * @param request 客户公司信息
     * @return 是否成功
     */
    public boolean addCustomerCompany(CustomerCompanyRequest request) {
        // 校验公司名称是否重复
        if (isCompanyNameExists(request.getCompanyName(), null)) {
            throw new RuntimeException("公司名称已存在，不能重复");
        }

        TCustomerCompany customerCompany = new TCustomerCompany();

        // 手动转换字段类型
        customerCompany.setCustomerCompanyType(request.getCustomerCompanyType());
        customerCompany.setCompanyName(request.getCompanyName());
        customerCompany.setProvinceId(FormatUtils.safeToLong(request.getProvinceId()));
        customerCompany.setProvinceName(request.getProvinceName());
        customerCompany.setCityId(FormatUtils.safeToLong(request.getCityId()));
        customerCompany.setCityName(request.getCityName());
        customerCompany.setAreaId(FormatUtils.safeToLong(request.getAreaId()));
        customerCompany.setAreaName(request.getAreaName());
        customerCompany.setDetailedAddress(request.getDetailedAddress());
        customerCompany.setContactPerson(request.getContactPerson());
        customerCompany.setContactPhone(request.getContactPhone());
        customerCompany.setInvoiceType(FormatUtils.safeToInteger(request.getInvoiceType()));
        customerCompany.setInvoiceCompanyName(request.getInvoiceCompanyName());
        customerCompany.setTaxNumber(request.getTaxNumber());
        customerCompany.setInvoiceContactPerson(request.getInvoiceContactPerson());
        customerCompany.setInvoiceMobile(request.getInvoiceMobile());
        customerCompany.setInvoiceEmail(request.getInvoiceEmail());
        customerCompany.setBankName(request.getBankName());
        customerCompany.setBankAccount(request.getBankAccount());
        customerCompany.setRemark(request.getRemark());

        // 设置默认值
        customerCompany.setEnabled(1); // 默认禁用状态
        customerCompany.setValid(1);
        customerCompany.setCreatedTime(LocalDateTime.now());
        customerCompany.setLastModifiedTime(LocalDateTime.now());

        return this.save(customerCompany);
    }

    /**
     * 更新客户公司
     *
     * @param request 客户公司信息
     * @return 是否成功
     */
    public boolean updateCustomerCompany(CustomerCompanyRequest request) {
        Long id = FormatUtils.safeToLong(request.getId());

        // 校验公司名称是否重复（排除当前记录）
        if (isCompanyNameExists(request.getCompanyName(), id)) {
            throw new RuntimeException("公司名称已存在，不能重复");
        }

        TCustomerCompany customerCompany = new TCustomerCompany();

        // 手动转换字段类型
        customerCompany.setId(id);
        customerCompany.setCustomerCompanyType(request.getCustomerCompanyType());
        customerCompany.setCompanyName(request.getCompanyName());
        customerCompany.setProvinceId(FormatUtils.safeToLong(request.getProvinceId()));
        customerCompany.setProvinceName(request.getProvinceName());
        customerCompany.setCityId(FormatUtils.safeToLong(request.getCityId()));
        customerCompany.setCityName(request.getCityName());
        customerCompany.setAreaId(FormatUtils.safeToLong(request.getAreaId()));
        customerCompany.setAreaName(request.getAreaName());
        customerCompany.setDetailedAddress(request.getDetailedAddress());
        customerCompany.setContactPerson(request.getContactPerson());
        customerCompany.setContactPhone(request.getContactPhone());
        customerCompany.setInvoiceType(FormatUtils.safeToInteger(request.getInvoiceType()));
        customerCompany.setInvoiceCompanyName(request.getInvoiceCompanyName());
        customerCompany.setTaxNumber(request.getTaxNumber());
        customerCompany.setInvoiceContactPerson(request.getInvoiceContactPerson());
        customerCompany.setInvoiceMobile(request.getInvoiceMobile());
        customerCompany.setInvoiceEmail(request.getInvoiceEmail());
        customerCompany.setBankName(request.getBankName());
        customerCompany.setBankAccount(request.getBankAccount());
        customerCompany.setRemark(request.getRemark());

        customerCompany.setLastModifiedTime(LocalDateTime.now());

        return this.updateById(customerCompany);
    }


    /**
     * 启用/禁用客户公司
     *
     * @param id 主键ID
     * @param enabled 启用状态
     * @return 是否成功
     */
    public boolean updateCustomerCompanyStatus(Long id, Integer enabled) {
        TCustomerCompany customerCompany = new TCustomerCompany();
        customerCompany.setId(id);
        customerCompany.setEnabled(enabled);
        customerCompany.setLastModifiedTime(LocalDateTime.now());

        return this.updateById(customerCompany);
    }


    /**
     * 根据公司类型查询客户公司
     *
     * @param customerCompanyType 公司类型（可选，为空时查询所有类型）
     * @return 客户公司列表
     */
    public List<TCustomerCompany> getCustomerCompanyByType(String customerCompanyType) {
        return baseMapper.selectByCompanyType(customerCompanyType);
    }

    /**
     * 导出客户公司列表
     *
     * @param request 查询条件
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    public void exportCustomerCompanyList(CustomerCompanyQueryRequest request, HttpServletResponse response) throws IOException {
        // 查询所有符合条件的数据（不分页）
        LambdaQueryWrapper<TCustomerCompany> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TCustomerCompany::getValid, 1)
                .like(StringUtils.hasText(request.getCompanyName()), TCustomerCompany::getCompanyName, request.getCompanyName())
                .eq(request.getCustomerCompanyType() != null, TCustomerCompany::getCustomerCompanyType, request.getCustomerCompanyType())
                .eq(request.getEnabled() != null, TCustomerCompany::getEnabled, request.getEnabled())
                .orderByDesc(TCustomerCompany::getCreatedTime);

        List<TCustomerCompany> dataList = this.list(wrapper);

        // 转换为导出VO
        List<CustomerCompanyExportVO> exportList = dataList.stream()
                .map(this::convertToExportVO)
                .collect(Collectors.toList());

        // 使用EasyExcel导出（文件名已包含时间戳）
        ExcelUtils.exportExcelWithTimestamp(response, "客户公司列表", "客户公司列表",
                CustomerCompanyExportVO.class, exportList);
    }

    /**
     * 转换为导出VO对象
     *
     * @param customerCompany 客户公司实体
     * @return 导出VO对象
     */
    private CustomerCompanyExportVO convertToExportVO(TCustomerCompany customerCompany) {
        CustomerCompanyExportVO exportVO = new CustomerCompanyExportVO();

        // 基础字段转换
        exportVO.setCompanyName(FormatUtils.safeString(customerCompany.getCompanyName()));
        exportVO.setContactPerson(FormatUtils.safeString(customerCompany.getContactPerson()));
        exportVO.setContactPhone(FormatUtils.safeString(customerCompany.getContactPhone()));
        exportVO.setCreatedBy(FormatUtils.safeString(customerCompany.getCreatedBy()));

        // 状态转换为中文
        exportVO.setStatus(FormatUtils.getEnabledStatusDescription(customerCompany.getEnabled()));

        // 客户类型转换为中文
        exportVO.setCustomerType(FormatUtils.getCustomerTypeDescription(customerCompany.getCustomerCompanyType()));

        // 注册地址拼接
        exportVO.setRegistrationAddress(FormatUtils.buildFullAddress(
                customerCompany.getProvinceName(),
                customerCompany.getCityName(),
                customerCompany.getAreaName(),
                customerCompany.getDetailedAddress()
        ));

        // 创建时间格式化
        exportVO.setCreatedTime(FormatUtils.formatDateTime(customerCompany.getCreatedTime()));

        return exportVO;
    }

    /**
     * 转换为详情Response对象
     *
     * @param customerCompany 客户公司实体
     * @return 详情Response对象
     */
    private CustomerCompanyDetailResponse convertToDetailResponse(TCustomerCompany customerCompany) {
        CustomerCompanyDetailResponse response = new CustomerCompanyDetailResponse();

        // 基础字段转换（保持与数据库字段一致）
        response.setId(FormatUtils.safeToString(customerCompany.getId()));
        response.setCustomerCompanyType(FormatUtils.safeString(customerCompany.getCustomerCompanyType()));
        response.setCompanyName(FormatUtils.safeString(customerCompany.getCompanyName()));
        response.setProvinceId(FormatUtils.safeToString(customerCompany.getProvinceId()));
        response.setProvinceName(FormatUtils.safeString(customerCompany.getProvinceName()));
        response.setCityId(FormatUtils.safeToString(customerCompany.getCityId()));
        response.setCityName(FormatUtils.safeString(customerCompany.getCityName()));
        response.setAreaId(FormatUtils.safeToString(customerCompany.getAreaId()));
        response.setAreaName(FormatUtils.safeString(customerCompany.getAreaName()));
        response.setDetailedAddress(FormatUtils.safeString(customerCompany.getDetailedAddress()));
        response.setContactPerson(FormatUtils.safeString(customerCompany.getContactPerson()));
        response.setContactPhone(FormatUtils.safeString(customerCompany.getContactPhone()));
        response.setInvoiceType(FormatUtils.safeToString(customerCompany.getInvoiceType()));
        response.setInvoiceCompanyName(FormatUtils.safeString(customerCompany.getInvoiceCompanyName()));
        response.setTaxNumber(FormatUtils.safeString(customerCompany.getTaxNumber()));
        response.setInvoiceContactPerson(FormatUtils.safeString(customerCompany.getInvoiceContactPerson()));
        response.setInvoiceMobile(FormatUtils.safeString(customerCompany.getInvoiceMobile()));
        response.setInvoiceEmail(FormatUtils.safeString(customerCompany.getInvoiceEmail()));
        response.setBankName(FormatUtils.safeString(customerCompany.getBankName()));
        response.setBankAccount(FormatUtils.safeString(customerCompany.getBankAccount()));
        response.setEnabled(FormatUtils.safeToString(customerCompany.getEnabled()));
        response.setCreatedBy(FormatUtils.safeString(customerCompany.getCreatedBy()));
        response.setCreatedTime(FormatUtils.formatDateTime(customerCompany.getCreatedTime()));
        response.setLastModifiedBy(FormatUtils.safeString(customerCompany.getLastModifiedBy()));
        response.setLastModifiedTime(FormatUtils.formatDateTime(customerCompany.getLastModifiedTime()));
        response.setValid(FormatUtils.safeToString(customerCompany.getValid()));
        response.setRemark(FormatUtils.safeString(customerCompany.getRemark()));

        return response;
    }

    /**
     * 校验公司名称是否存在
     *
     * @param companyName 公司名称
     * @param excludeId 排除的ID（更新时使用，新增时传null）
     * @return 是否存在
     */
    private boolean isCompanyNameExists(String companyName, Long excludeId) {
        if (companyName == null || companyName.trim().isEmpty()) {
            return false;
        }

        LambdaQueryWrapper<TCustomerCompany> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TCustomerCompany::getValid, 1)
                .eq(TCustomerCompany::getCompanyName, companyName.trim());

        // 更新时排除当前记录
        if (excludeId != null) {
            wrapper.ne(TCustomerCompany::getId, excludeId);
        }

        return this.count(wrapper) > 0;
    }


}