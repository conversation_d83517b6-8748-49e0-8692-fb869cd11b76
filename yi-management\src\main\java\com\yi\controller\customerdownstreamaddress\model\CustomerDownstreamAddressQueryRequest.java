package com.yi.controller.customerdownstreamaddress.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 客户下游地址关联查询请求
 */
@Data
@ApiModel(value = "CustomerDownstreamAddressQueryRequest", description = "客户下游地址关联查询请求")
public class CustomerDownstreamAddressQueryRequest {

    @ApiModelProperty(value = "当前页码", example = "1")
    private String current = "1";

    @ApiModelProperty(value = "每页大小", example = "10")
    private String size = "10";

    @ApiModelProperty(value = "启用状态：1-启用，0-禁用（下拉框搜索）")
    private String enabled;

    @ApiModelProperty(value = "公司ID")
    private String companyId;

    @ApiModelProperty(value = "仓库名称或地址（模糊查询）")
    private String warehouseKeyword;
}
