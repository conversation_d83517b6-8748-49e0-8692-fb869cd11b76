# 发运订单SKU类型查询接口

## 接口信息

**接口地址**: `GET /api/shipping-order/sku-types`

**接口描述**: 根据合同编号和仓库ID获取SKU类型下拉框数据，用于订单创建时的SKU类型选择

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| contractCode | String | 是 | 合同编号 |
| warehouseId | Long | 是 | 仓库ID |

## 返回参数

### 成功响应

```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "firstCategoryId": "1",
            "firstCategoryName": "共享托盘",
            "secondCategory": "标准托盘",
            "displayText": "共享托盘 标准托盘"
        },
        {
            "firstCategoryId": "1",
            "firstCategoryName": "共享托盘",
            "secondCategory": "加强托盘",
            "displayText": "共享托盘 加强托盘"
        }
    ]
}
```

### 字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| firstCategoryId | String | 一级类目ID |
| firstCategoryName | String | 一级类目名称 |
| secondCategory | String | 二级类目 |
| displayText | String | 显示文本（一级类目名称 + 空格 + 二级类目） |

## 业务逻辑

### 数据获取流程

1. **根据合同编号查询合同ID**
   - 调用合同服务根据`contractCode`获取对应的`contractId`
   - 如果合同不存在，返回空列表

2. **查询合同支持的一级类目**
   - 根据`contractId`查询`t_contract_sku`表
   - 获取该合同支持的所有一级类目列表
   - 去重处理

3. **查询仓库支持的SKU类型**
   - 根据`warehouseId`和合同的一级类目列表查询`t_warehouse_sku`表
   - 筛选条件：
     - `warehouse_id = #{warehouseId}`
     - `first_category IN (合同支持的一级类目列表)`
     - `valid = 1`
     - `enabled = 1`

4. **数据转换和排序**
   - 转换为响应对象
   - 按一级类目、二级类目排序
   - 生成显示文本

### SQL查询逻辑

```sql
-- 1. 根据合同编号获取合同ID
SELECT id FROM t_contract 
WHERE contract_no = #{contractCode} AND valid = 1 
LIMIT 1;

-- 2. 根据合同ID获取一级类目
SELECT DISTINCT first_category 
FROM t_contract_sku 
WHERE contract_id = #{contractId} AND valid = 1;

-- 3. 根据仓库ID和一级类目获取SKU类型
SELECT warehouse_id, first_category, second_category
FROM t_warehouse_sku 
WHERE warehouse_id = #{warehouseId} 
  AND first_category IN (#{firstCategoryList})
  AND valid = 1 
  AND enabled = 1
ORDER BY first_category, second_category;
```

## 使用场景

### 订单创建页面

在订单创建页面中，当用户选择了合同编号和收货仓库后，调用此接口获取可选的SKU类型：

```javascript
// 当合同编号和仓库都选择后，获取SKU类型
function loadSkuTypes(contractCode, warehouseId) {
    if (contractCode && warehouseId) {
        fetch(`/api/shipping-order/sku-types?contractCode=${contractCode}&warehouseId=${warehouseId}`)
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    // 填充SKU类型下拉框
                    populateSkuTypeSelect(data.data);
                }
            });
    }
}

function populateSkuTypeSelect(skuTypes) {
    const selectElement = document.getElementById('skuTypeSelect');
    selectElement.innerHTML = '<option value="">请选择SKU类型</option>';
    
    skuTypes.forEach(sku => {
        const option = document.createElement('option');
        option.value = `${sku.firstCategoryId}_${sku.secondCategory}`;
        option.textContent = sku.displayText;
        selectElement.appendChild(option);
    });
}
```

## 数据表关系

### 涉及的数据表

1. **t_contract** - 合同表
   - 存储合同基本信息
   - 通过`contract_no`字段关联

2. **t_contract_sku** - 合同SKU表
   - 存储合同支持的SKU类型
   - 通过`contract_id`关联合同表

3. **t_warehouse_sku** - 仓库SKU表
   - 存储仓库支持的SKU类型
   - 通过`warehouse_id`关联仓库表

### 表关系图

```
t_contract (合同表)
    ↓ (contract_id)
t_contract_sku (合同SKU表) → first_category (一级类目)
                                ↓
t_warehouse_sku (仓库SKU表) ← warehouse_id + first_category
    ↓
返回: first_category + second_category
```

## 错误处理

### 参数验证

- 如果`contractCode`为空或null，返回空列表
- 如果`warehouseId`为空或null，返回空列表

### 业务异常

- 合同不存在：返回空列表
- 合同没有配置SKU：返回空列表
- 仓库没有匹配的SKU：返回空列表

### 示例错误响应

```json
{
    "code": 200,
    "message": "操作成功",
    "data": []
}
```

## 使用示例

### 正常查询

```bash
GET /api/shipping-order/sku-types?contractCode=CONTRACT001&warehouseId=1
```

### 响应示例

```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "firstCategoryId": "1",
            "firstCategoryName": "共享托盘",
            "secondCategory": "标准托盘",
            "displayText": "共享托盘 标准托盘"
        },
        {
            "firstCategoryId": "1",
            "firstCategoryName": "共享托盘",
            "secondCategory": "加强托盘",
            "displayText": "共享托盘 加强托盘"
        },
        {
            "firstCategoryId": "1",
            "firstCategoryName": "共享托盘",
            "secondCategory": "轻量托盘",
            "displayText": "共享托盘 轻量托盘"
        }
    ]
}
```

## 注意事项

1. **数据一致性**: 确保合同SKU和仓库SKU数据的一致性
2. **性能优化**: 对于频繁查询的数据，建议添加适当的缓存
3. **权限控制**: 确保用户有权限访问指定的合同和仓库数据
4. **数据有效性**: 只返回有效且启用的SKU类型数据
5. **排序规则**: 按一级类目、二级类目的自然顺序排序
