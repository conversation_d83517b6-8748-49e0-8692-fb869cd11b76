package com.yi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yi.controller.shippingorder.model.ShippingOrderQueryRequest;
import com.yi.controller.shippingorder.model.ShippingOrderPageResponse;
import com.yi.controller.shippingorder.model.ShippingOrderRequest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 发运订单服务测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class TShippingOrderServiceTest {

    @Autowired
    private TShippingOrderService shippingOrderService;

    @Test
    public void testGetShippingOrderPage() {
        // 创建查询请求
        ShippingOrderQueryRequest request = new ShippingOrderQueryRequest();
        request.setCurrent("1");
        request.setSize("10");

        // 执行查询
        IPage<ShippingOrderPageResponse> result = shippingOrderService.getShippingOrderPageResponse(request);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getCurrent() >= 1);
        assertTrue(result.getSize() >= 1);
    }

    @Test
    public void testAddShippingOrder() {
        // 创建新增请求
        ShippingOrderRequest request = new ShippingOrderRequest();
        request.setOrderNo("TEST_ORDER_" + System.currentTimeMillis());
        request.setCustomerCompanyId("1");
        request.setWarehouseId("1");
        request.setFirstCategory("1");
        request.setCount("100");
        request.setDemandTime("2024-01-01");

        // 执行新增
        boolean result = shippingOrderService.addShippingOrder(request);

        // 验证结果
        assertTrue(result);
    }
}
