package com.yi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yi.controller.contract.model.ContractListVO;
import com.yi.entity.TContract;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 合同管理表 Mapper 接口
 */
@Mapper
public interface TContractMapper extends BaseMapper<TContract> {

    /**
     * 分页查询合同列表（联合查询）
     *
     * @param page 分页参数
     * @param contractNo 合同编号（模糊查询）
     * @param customerCompanyName 客户主体名称（模糊查询）
     * @param contractStatus 合同状态
     * @return 分页结果
     */
    IPage<ContractListVO> selectContractPageWithJoin(Page<?> page,
                                                     @Param("contractNo") String contractNo,
                                                     @Param("customerCompanyName") String customerCompanyName,
                                                     @Param("contractStatus") Integer contractStatus);

    /**
     * 查询合同列表（联合查询，用于导出）
     *
     * @param contractNo 合同编号（模糊查询）
     * @param customerCompanyName 客户主体名称（模糊查询）
     * @param contractStatus 合同状态
     * @return 合同列表
     */
    List<ContractListVO> selectContractListWithJoin(@Param("contractNo") String contractNo,
                                                    @Param("customerCompanyName") String customerCompanyName,
                                                    @Param("contractStatus") Integer contractStatus);

    /**
     * 根据合同编号查询合同
     *
     * @param contractNo 合同编号
     * @return 合同信息
     */
    TContract selectByContractNo(@Param("contractNo") String contractNo);

    /**
     * 根据客户公司ID查询有效合同列表
     *
     * @param customerCompanyId 客户公司ID
     * @return 合同列表
     */
    List<TContract> selectValidContractsByCustomerId(@Param("customerCompanyId") Long customerCompanyId);

    /**
     * 根据ID查询合同详情（联合查询）
     *
     * @param id 合同ID
     * @return 合同详情
     */
    ContractListVO selectContractDetailWithJoin(@Param("id") Long id);
}
